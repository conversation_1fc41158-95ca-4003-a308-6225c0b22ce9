#!/usr/bin/env python3
"""
Ultra-Stable Training Script

Uses the stability fixes and ultra-conservative configuration to train
a stable PatchTST model for manufacturing forecasting.

The fixes implemented:
1. Fixed target tensor pipeline (ManufacturingDataCollator)
2. Removed UnivariatePatchTSTForPrediction wrapper conflicts
3. Added pre-loss tensor validation and numerical stability
4. Ultra-conservative training parameters

Usage:
    python train_stable.py                    # Train with ultra-stable config
    python train_stable.py --target Speed     # Train specific target
    python train_stable.py --help             # Show options
"""

import argparse
import logging
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.forecasting.config import load_config_from_file
from src.forecasting.trainer import ManufacturingForecastTrainer
from src.data.loader import ManufacturingDataLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('stable_training.log')
    ]
)
logger = logging.getLogger(__name__)

def check_for_existing_unified_table():
    """Check if unified table already exists and prompt user (from train.py)."""
    unified_table_path = Path("test-data/consolidated/test_matched_stacks.csv")
    if unified_table_path.exists():
        # Read header comments to get export info
        try:
            with open(unified_table_path, 'r') as f:
                lines = []
                for i, line in enumerate(f):
                    if i < 10 and line.startswith('#'):
                        lines.append(line.strip())
                    else:
                        break
            
            # Extract export date and record count
            export_date = None
            total_records = None
            for line in lines:
                if "Export Date:" in line:
                    export_date = line.split("Export Date: ")[1]
                elif "Total Records:" in line:
                    total_records = line.split("Total Records: ")[1].replace(',', '')
            
            # Display unified table info
            logger.info(f"🔍 Unified table found!")
            if export_date:
                logger.info(f"   Export date: {export_date}")
            if total_records:
                logger.info(f"   Total records: {total_records}")
            
            # Ask user if they want to use existing unified table
            use_existing = input("Use existing unified table? [Y/n]: ").strip().lower()
            return use_existing in ['', 'y', 'yes']
            
        except Exception as e:
            logger.warning(f"Error reading unified table metadata: {e}")
            return False
    
    return False

def main():
    parser = argparse.ArgumentParser(description='Ultra-Stable PatchTST Training')
    parser.add_argument('--target', type=str, default='fm_reject_pct',
                       help='Target variable to forecast (default: fm_reject_pct)')
    parser.add_argument('--config', type=str, default='config/ultra_stable_config.json',
                       help='Configuration file (default: ultra_stable_config.json)')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("🛡️  Starting Ultra-Stable PatchTST Training")
    logger.info("=" * 60)
    logger.info(f"Target Variable: {args.target}")
    logger.info(f"Configuration: {args.config}")
    logger.info("=" * 60)
    
    try:
        # Load ultra-stable configuration
        logger.info("📋 Loading ultra-stable configuration...")
        forecast_config, training_config = load_config_from_file(args.config)
        logger.info("✅ Configuration loaded successfully")
        
        # Log key stability settings (safely handle different config types)
        logger.info("🛡️  Stability Features:")
        logger.info(f"   - Learning Rate: {training_config.learning_rate}")
        logger.info(f"   - Batch Size: {training_config.batch_size}")
        logger.info(f"   - Max Epochs: {training_config.max_epochs}")
        
        # Safe access to nested config
        try:
            grad_clip = training_config.stability_config.gradient_clipping['max_norm']
            logger.info(f"   - Gradient Clipping: {grad_clip}")
        except:
            logger.info("   - Gradient Clipping: Configuration loaded")
        
        try:
            mixed_prec = training_config.stability_config.mixed_precision['enabled']
            logger.info(f"   - Mixed Precision: {mixed_prec}")
        except:
            logger.info("   - Mixed Precision: Configuration loaded")
        
        try:
            transfer_enabled = training_config.transfer_learning['enabled']
            logger.info(f"   - Transfer Learning: {transfer_enabled}")
        except:
            logger.info("   - Transfer Learning: Configuration loaded")
        
        # Check for existing unified table (same logic as train.py)
        use_existing_table = check_for_existing_unified_table()
        
        # Load data with three-tier fallback strategy
        logger.info("📊 Loading manufacturing data...")
        unified_data = None
        
        # Tier 1: Try existing unified table if available
        if use_existing_table:
            try:
                logger.info("🚀 Loading existing unified table...")
                import pandas as pd
                unified_table_path = "test-data/consolidated/test_matched_stacks.csv"
                
                # Handle header comments in unified table
                with open(unified_table_path, 'r') as f:
                    lines = f.readlines()
                
                # Find the first non-comment line (actual CSV header)
                start_line = 0
                for i, line in enumerate(lines):
                    if not line.startswith('#'):
                        start_line = i
                        break
                
                # Read CSV starting from the actual header
                unified_data = pd.read_csv(unified_table_path, skiprows=start_line, low_memory=False)
                logger.info(f"✅ Unified table loaded: {len(unified_data)} records")
            except Exception as e:
                logger.warning(f"⚠️ Failed to load existing unified table: {e}")
                logger.info("📝 Falling back to creating unified table from raw sources...")
        
        # Tier 2: Create unified table from raw sources if Tier 1 failed or wasn't used
        if unified_data is None:
            try:
                logger.info("🔄 Creating unified table from raw data sources...")
                data_loader = ManufacturingDataLoader()
                unified_data = data_loader.create_unified_table()
                logger.info(f"✅ Unified table created: {len(unified_data)} records")
            except Exception as e:
                logger.warning(f"⚠️ Failed to create unified table: {e}")
                logger.error("❌ No fallback strategy available for ultra-stable training")
                logger.error("Please ensure data files are available or run without unified table")
                return 1
        
        if unified_data is None or unified_data.empty:
            logger.error("❌ No data available for training")
            return 1
        
        # Validate target variable
        if args.target not in unified_data.columns:
            available_targets = [col for col in unified_data.columns 
                               if col in forecast_config.target_variables]
            logger.error(f"❌ Target '{args.target}' not found in data")
            logger.error(f"Available targets: {available_targets}")
            logger.info(f"Available columns: {list(unified_data.columns)[:10]}...")  # Show first 10 columns
            return 1
        
        # Initialize trainer with stability fixes
        logger.info("🤖 Initializing trainer with stability fixes...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,  # Enable stability enhancements
            use_transfer_learning=False   # Disable for ultra-stable mode
        )
        
        # Override target variables to only train the specified target
        original_targets = forecast_config.target_variables.copy()
        forecast_config.target_variables = [args.target]
        logger.info(f"Training target: {args.target} (was: {original_targets})")
        
        logger.info("✅ Trainer initialized successfully")
        
        # Start training
        logger.info(f"🚀 Starting ultra-stable training for '{args.target}'...")
        logger.info("🛡️  Active Fixes:")
        logger.info("   ✅ Fixed target tensor shape pipeline")
        logger.info("   ✅ Removed wrapper conflicts")
        logger.info("   ✅ Enhanced numerical stability validation")
        logger.info("   ✅ Ultra-conservative training parameters")
        
        # Use the correct trainer method based on data source
        if use_existing_table:
            # Use unified table path method
            unified_table_path = "test-data/consolidated/test_matched_stacks.csv"
            results = trainer.train_all_target_variables_with_unified_table(
                unified_table_path=unified_table_path
            )
        else:
            # Use standard training with data path
            results = trainer.train_all_target_variables(
                data_path="test-data"
            )
        
        # Filter results to only the target we want
        if args.target in results:
            results = {args.target: results[args.target]}
        else:
            logger.error(f"Target '{args.target}' not found in training results")
            return 1
        
        # Check results
        if results and args.target in results and results[args.target]:
            logger.info("🎉 Training completed successfully!")
            logger.info("✅ Model saved without infinite loss errors")
            logger.info("✅ Stability fixes validated in production")
            
            # Report model location
            model_path = f"./models/patchtst_manufacturing_{args.target}"
            logger.info(f"📁 Model saved to: {model_path}")
            
            # Show performance summary if available
            model = results[args.target]
            if hasattr(model, 'training_history'):
                history = model.training_history
                if 'training_loss' in history:
                    logger.info(f"📊 Final training loss: {history['training_loss']:.6f}")
            
            return 0
        else:
            logger.error("❌ Training failed or returned empty results")
            logger.error(f"Available results: {list(results.keys()) if results else 'None'}")
            return 1
            
    except Exception as e:
        logger.error(f"💥 Training failed with error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())