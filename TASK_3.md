# TASK_3.md

## FEATURE:
**Predictive Modeling with Transformers**

Goal: To create a new PatchTST-based forecasting tool that the AI agent can use to predict future values of any configurable manufacturing parameter based on historical time series data.

### Core Implementation Tasks:

#### 1. PatchTST Forecasting Tool Development:
- **Model Architecture**: Implement PatchTST (Patched Time Series Transformer) - a state-of-the-art forecasting model that segments time series into patches for improved efficiency and accuracy
- **Agent Integration**: Create a new agent tool `@correlation_agent.tool` that can be called via natural language requests like "Forecast thickness for the next 2 hours" or "Predict speed variations for tomorrow's shift"
- **Configurable Parameters**: Use config.json to define:
  - Input Variables: Which manufacturing parameters to use as predictors (e.g., thickness, speed, temperature, pressure)
  - Target Variables: Which parameters to forecast (e.g., thickness_avg, scrap_rate, quality_index)
  - Forecast Horizons: Time periods to predict (15 minutes, 1 hour, 4 hours, 24 hours)
  - Lookback Windows: Historical data length for predictions (60 minutes, 4 hours, 24 hours)

#### 2. Dataset Preparation and Splitting:
- **Training Set (70%)**: Historical manufacturing data for model training
- **Validation Set (15%)**: Used for hyperparameter tuning and model selection
- **Test Set (15%)**: Hold-out data for final performance evaluation
- **Temporal Splitting**: Ensure chronological order (train on older data, validate/test on newer data) to simulate real-world forecasting scenarios
- **Data Preprocessing**: Handle missing values, normalize features, create proper time series sequences for PatchTST input format

#### 3. Model Training and Configuration:
**PatchTST Implementation:**
- Configure patch size and stride for optimal time series segmentation
- Set up multi-head attention mechanism for capturing temporal dependencies
- Implement positional encoding for manufacturing time patterns (shift cycles, daily patterns)
- Multi-Variable Forecasting: Train separate models or unified multi-output model based on config.json specifications
- Hyperparameter Optimization: Use validation set to tune learning rate, batch size, number of attention heads, patch parameters

#### 4. Configuration Example (config.json):
```json
{
  "forecasting_config": {
    "input_variables": [
      "thickness_avg", 
      "thickness_uniformity", 
      "speed", 
      "temperature", 
      "pressure",
      "minutes_since_last_stop"
    ],
    "target_variables": [
      "thickness_avg",
      "scrap_rate", 
      "quality_index"
    ],
    "forecast_horizons": [15, 60, 240],
    "lookback_window": 240,
    "patch_size": 16,
    "model_params": {
      "d_model": 128,
      "n_heads": 8,
      "num_layers": 3,
      "dropout": 0.1
    }
  }
}
```

#### 5. Testing & Validation:
**Performance Metrics:**
- Accuracy: RMSE, MAE, MAPE for continuous forecasts
- Directional Accuracy: Percentage of correct trend predictions (up/down/stable)
- Horizon Analysis: Compare accuracy across different forecast windows (15 min vs 4 hours)

**Model Interpretability:**
- Attention Visualization: Show which historical time periods the model focuses on for predictions
- Variable Importance: Rank input variables by their contribution to forecast accuracy
- Temporal Patterns: Identify recurring patterns the model has learned (daily cycles, shift changes, equipment warm-up periods)

**Agent Tool Functionality:**
- Natural Language Interface: "Forecast thickness for next shift", "Predict quality trends for next 4 hours"
- Dynamic Configuration: Ability to modify forecast targets and horizons via conversation
- Confidence Intervals: Provide uncertainty estimates with all forecasts
- Comparative Analysis: Compare forecasts across different time periods or production scenarios

#### 6. Success Criteria:
- A production-ready PatchTST forecasting tool integrated into the correlation agent
- Configurable via config.json for any manufacturing parameter combination
- 15% improvement in forecast accuracy compared to baseline methods (ARIMA, Linear Regression)
- Agent can process natural language forecasting requests and provide actionable insights
- Model provides interpretable attention patterns showing critical time dependencies in manufacturing processes

## EXAMPLES:

### Reference Implementations in Examples Folder:

#### 1. **Agent Integration Patterns** (`examples/agent_examples/`)
- **`correlation_agent.py`**: Current production-ready agent showing tool integration patterns
- **`multi_provider_agent.py`**: Multi-LLM provider support architecture
- **`industrial_data_agent.py`**: Manufacturing-specific analysis patterns

#### 2. **Data Processing Foundations** (`examples/data_processing/`)
- **`time_series_processor.py`**: Advanced time series processing for manufacturing data
  - Resampling, anomaly detection, smoothing, event detection
  - Time feature creation for temporal analysis
  - Manufacturing shift alignment utilities
- **`csv_loader.py`**: Manufacturing-specific CSV data loading and validation
  - Data quality assessment, preprocessing, schema validation
  - Automatic handling of mixed timestamp formats
- **`mixed_frequency_aligner.py`**: Align data streams with different sampling frequencies
  - Event-driven alignment, hierarchical frequency handling
  - Manufacturing shift alignment utilities

#### 3. **Multi-Method Analysis Patterns** (`examples/multi_method_*.py`)
- **`multi_method_agent_example.py`**: AI agent tool integration patterns
  - Natural language queries with structured tool responses
  - Multi-step analysis workflows through agent tools
- **`multi_method_analysis_advanced.py`**: Production-ready analysis patterns
  - Domain-specific variable grouping (Production, Quality, Operations)
  - Manufacturing insights generation

#### 4. **PatchTST Implementation References** (`docs/notebooks`)
- **`patch_tst_getting_started.ipynb`**: Basic PatchTST model setup and training
  - Configuration patterns: context_length=512, forecast_horizon=96, patch_length=12
  - Dataset preparation with ForecastDFDataset
  - Model training with Hugging Face Transformers
- **`patch_tst_transfer.ipynb`**: Transfer learning capabilities
  - Zero-shot forecasting on new datasets
  - Linear probing and full fine-tuning approaches
  - Model saving/loading patterns
- **`patch_tsmixer_transfer.ipynb`**: Alternative architecture patterns
  - Channel-independent mode for manufacturing variables
  - Patch-based segmentation strategies

#### 5. **Visualization Integration** (`examples/visualization/`)
- **`time_series_plots.py`**: Time series visualization and forecasting display
  - Multi-variable plots, forecast overlays, confidence intervals
  - Event overlay on time series for manufacturing context
- **`industrial_dashboard.py`**: Comprehensive manufacturing dashboard
  - Real-time monitoring, alerts, process metrics
  - Integration patterns for forecast displays

#### 6. **Configuration Management Examples**
- **Agent Tool Registration**: Follow `@correlation_agent.tool` decorator patterns from existing tools
- **Structured Outputs**: Use Pydantic models like existing `CorrelationAnalysis` for forecast results
- **Error Handling**: Manufacturing domain validation patterns from correlation tools

### 7. **HuggingFace PatchTST Implementation Examples**

#### **Official Configuration Patterns**
```python
# Basic PatchTST Configuration for Manufacturing
from transformers import PatchTSTConfig, PatchTSTForPrediction
from transformers import Trainer, TrainingArguments, EarlyStoppingCallback

# Manufacturing-optimized configuration
config = PatchTSTConfig(
    num_input_channels=6,           # thickness, speed, temperature, pressure, quality, stops
    context_length=512,             # 8.5 hours of historical data (1 min intervals)
    patch_length=16,                # 16-minute patches for shift-level patterns
    patch_stride=16,                # Non-overlapping patches
    prediction_length=96,           # 1.6 hours forecast horizon
    d_model=128,                    # Transformer dimension
    num_attention_heads=16,         # Multi-head attention
    num_hidden_layers=3,            # Encoder layers
    ffn_dim=512,                    # Feed-forward dimension
    dropout=0.2,                    # Regularization
    head_dropout=0.2,               # Output head dropout
    pooling_type=None,              # No pooling for full temporal resolution
    channel_attention=False,        # Channel independence for manufacturing
    scaling="std",                  # Standard scaling for sensor data
    loss="mse",                     # Mean squared error for regression
    pre_norm=True,                  # Pre-normalization for stability
    norm_type="batchnorm"           # Batch normalization
)

# Initialize model for forecasting
model = PatchTSTForPrediction(config)
```

#### **Manufacturing Data Preprocessing Pipeline**
```python
# Time Series Preprocessing for Manufacturing Data
from tsfm_public.toolkit.dataset import ForecastDFDataset
from tsfm_public.toolkit.time_series_preprocessor import TimeSeriesPreprocessor
import pandas as pd

def prepare_manufacturing_data(data_path: str, config: dict):
    """
    Prepare manufacturing time series data for PatchTST training.
    
    Args:
        data_path: Path to manufacturing CSV files
        config: Forecasting configuration
    """
    # Load unified manufacturing data (from existing Phase 2.1 pipeline)
    data = pd.read_csv(data_path)
    
    # Manufacturing-specific preprocessing
    manufacturing_columns = [
        'thickness_avg', 'thickness_uniformity', 'speed', 
        'temperature', 'pressure', 'minutes_since_last_stop'
    ]
    
    # Temporal data splitting for manufacturing (chronological order)
    num_records = len(data)
    train_end = int(num_records * 0.7)
    valid_end = int(num_records * 0.85)
    
    # Training data: First 70% chronologically
    train_data = data.iloc[:train_end]
    
    # Validation data: Next 15% chronologically  
    valid_data = data.iloc[train_end-config['context_length']:valid_end]
    
    # Test data: Final 15% chronologically
    test_data = data.iloc[valid_end-config['context_length']:]
    
    # Initialize time series preprocessor
    tsp = TimeSeriesPreprocessor(
        timestamp_column='unified_timestamp',
        id_columns=[],  # Single manufacturing line
        target_columns=manufacturing_columns,
        scaling=True    # Essential for sensor data normalization
    )
    
    # Fit on training data only
    tsp.train(train_data)
    
    # Create datasets for PatchTST
    train_dataset = ForecastDFDataset(
        tsp.preprocess(train_data),
        id_columns=[],
        target_columns=manufacturing_columns,
        context_length=config['context_length'],
        prediction_length=config['prediction_length']
    )
    
    valid_dataset = ForecastDFDataset(
        tsp.preprocess(valid_data),
        id_columns=[],
        target_columns=manufacturing_columns,
        context_length=config['context_length'],
        prediction_length=config['prediction_length']
    )
    
    test_dataset = ForecastDFDataset(
        tsp.preprocess(test_data),
        id_columns=[],
        target_columns=manufacturing_columns,
        context_length=config['context_length'],
        prediction_length=config['prediction_length']
    )
    
    return train_dataset, valid_dataset, test_dataset, tsp
```

#### **Production-Ready Training Pipeline**
```python
def train_manufacturing_patchtst(model, train_dataset, valid_dataset, config):
    """
    Production-ready training pipeline for manufacturing forecasting.
    """
    # Training arguments optimized for manufacturing data
    training_args = TrainingArguments(
        output_dir=f"./models/patchtst_manufacturing_{config['target_variable']}/",
        overwrite_output_dir=True,
        learning_rate=0.0001,           # Conservative learning rate for stability
        num_train_epochs=100,           # Early stopping will control actual epochs
        do_eval=True,
        evaluation_strategy="epoch",
        per_device_train_batch_size=32, # Adjust based on GPU memory
        per_device_eval_batch_size=64,  # Larger batch for evaluation
        dataloader_num_workers=4,       # Parallel data loading
        save_strategy="epoch",
        logging_strategy="epoch",
        save_total_limit=3,             # Keep only best 3 models
        logging_dir=f"./logs/patchtst_manufacturing_{config['target_variable']}/",
        load_best_model_at_end=True,    # Load best model for production
        metric_for_best_model="eval_loss",
        greater_is_better=False,        # Lower loss is better
        label_names=["future_values"],  # PatchTST expected label format
        report_to="tensorboard"         # Monitoring integration
    )
    
    # Manufacturing-specific early stopping
    early_stopping = EarlyStoppingCallback(
        early_stopping_patience=10,    # Wait 10 epochs for improvement
        early_stopping_threshold=0.001 # Minimum improvement threshold
    )
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=valid_dataset,
        callbacks=[early_stopping]
    )
    
    # Train model
    trainer.train()
    
    return trainer

# Usage example
config = {
    'context_length': 512,
    'prediction_length': 96,
    'target_variable': 'thickness_avg'
}

train_dataset, valid_dataset, test_dataset, preprocessor = prepare_manufacturing_data(
    'test-data/unified_manufacturing_data.csv', config
)

model = PatchTSTForPrediction(PatchTSTConfig(**manufacturing_config))
trainer = train_manufacturing_patchtst(model, train_dataset, valid_dataset, config)
```

### 8. **Complete Agent Tool Implementation**

#### **PatchTST Forecasting Agent Tool**
```python
# Complete implementation following existing correlation agent patterns
from src.agents.correlation_agent import correlation_agent
from transformers import PatchTSTForPrediction, PatchTSTConfig
from pydantic import BaseModel, Field
import torch
import numpy as np
from typing import List, Dict, Optional, Any
import os

class ForecastResult(BaseModel):
    """Structured forecast result following correlation agent patterns"""
    target_variable: str = Field(description="Variable being forecasted")
    forecast_horizon: int = Field(description="Number of time steps forecasted")
    forecast_values: List[float] = Field(description="Predicted future values")
    confidence_intervals: Dict[str, List[float]] = Field(description="95% confidence intervals")
    forecast_timestamps: List[str] = Field(description="Timestamps for forecast period")
    model_performance: Dict[str, float] = Field(description="Model accuracy metrics")
    attention_insights: Dict[str, Any] = Field(description="Key temporal patterns identified")
    manufacturing_insights: List[str] = Field(description="Process optimization recommendations")

class ForecastConfig(BaseModel):
    """Manufacturing forecasting configuration"""
    input_variables: List[str] = Field(default=[
        "thickness_avg", "thickness_uniformity", "speed", 
        "temperature", "pressure", "minutes_since_last_stop"
    ])
    target_variables: List[str] = Field(default=["thickness_avg", "scrap_rate", "quality_index"])
    forecast_horizons: List[int] = Field(default=[15, 60, 240])  # 15min, 1hr, 4hr
    lookback_window: int = Field(default=240)
    patch_size: int = Field(default=16)
    model_params: Dict[str, Any] = Field(default={
        "d_model": 128,
        "n_heads": 8,
        "num_layers": 3,
        "dropout": 0.1
    })

@correlation_agent.tool
async def forecast_manufacturing_parameter(
    ctx: RunContext,
    target_variable: str,
    forecast_horizon: int,
    lookback_window: int = 240,
    include_confidence_intervals: bool = True
) -> ForecastResult:
    """
    Forecast future values of manufacturing parameters using PatchTST.
    
    Integrates with existing correlation agent infrastructure and provides
    manufacturing domain expertise for process optimization decisions.
    
    Args:
        target_variable: Variable to forecast (e.g., 'thickness_avg', 'scrap_rate', 'quality_index')
        forecast_horizon: Number of time steps to forecast (15, 60, 240)
        lookback_window: Historical data window for prediction (default 240 = 4 hours)
        include_confidence_intervals: Whether to calculate uncertainty estimates
    
    Returns:
        ForecastResult: Structured forecast with manufacturing insights
    """
    try:
        # Access unified data from context (established in Phase 2.1)
        unified_data = ctx.deps.get('unified_data')
        if unified_data is None:
            raise ValueError("No unified manufacturing data available. Load data first.")
        
        # Load pre-trained model (following existing agent patterns)
        model_path = f"./models/patchtst_manufacturing_{target_variable}/"
        if not os.path.exists(model_path):
            # Fallback to pre-trained model or trigger training
            model_path = "./models/patchtst_manufacturing_base/"
            
        model = PatchTSTForPrediction.from_pretrained(model_path)
        model.eval()
        
        # Prepare input data (last lookback_window records)
        input_data = unified_data.tail(lookback_window)
        
        # Validate manufacturing data quality (following Phase 2.1 patterns)
        if len(input_data) < lookback_window:
            raise ValueError(f"Insufficient data: need {lookback_window} records, got {len(input_data)}")
        
        # Manufacturing-specific data validation
        required_columns = ['thickness_avg', 'speed', 'temperature', 'pressure']
        missing_columns = [col for col in required_columns if col not in input_data.columns]
        if missing_columns:
            raise ValueError(f"Missing required manufacturing data: {missing_columns}")
        
        # Preprocess data for PatchTST (normalize and create tensors)
        input_tensor = prepare_input_tensor(input_data, target_variable)
        
        # Generate forecast
        with torch.no_grad():
            outputs = model(past_values=input_tensor)
            forecast_values = outputs.prediction_outputs.squeeze().tolist()
        
        # Calculate confidence intervals if requested
        confidence_intervals = {}
        if include_confidence_intervals:
            confidence_intervals = calculate_manufacturing_confidence_intervals(
                model, input_tensor, forecast_horizon
            )
        
        # Generate forecast timestamps (following manufacturing shift patterns)
        forecast_timestamps = generate_manufacturing_timestamps(
            unified_data['unified_timestamp'].iloc[-1], forecast_horizon
        )
        
        # Extract attention insights for manufacturing interpretation
        attention_insights = extract_attention_insights(model, input_tensor)
        
        # Generate manufacturing-specific insights
        manufacturing_insights = generate_manufacturing_insights(
            target_variable, forecast_values, input_data, attention_insights
        )
        
        # Calculate model performance metrics
        model_performance = {
            "forecast_accuracy": calculate_forecast_confidence(forecast_values),
            "trend_direction": "increasing" if forecast_values[-1] > forecast_values[0] else "decreasing",
            "volatility_score": np.std(forecast_values)
        }
        
        return ForecastResult(
            target_variable=target_variable,
            forecast_horizon=forecast_horizon,
            forecast_values=forecast_values,
            confidence_intervals=confidence_intervals,
            forecast_timestamps=forecast_timestamps,
            model_performance=model_performance,
            attention_insights=attention_insights,
            manufacturing_insights=manufacturing_insights
        )
        
    except Exception as e:
        # Follow Phase 2.1 error handling patterns
        logger.error(f"Forecasting error for {target_variable}: {str(e)}")
        raise ValueError(f"Failed to generate forecast: {str(e)}")

@correlation_agent.tool
async def compare_forecast_scenarios(
    ctx: RunContext,
    target_variable: str,
    scenarios: List[Dict[str, Any]]
) -> Dict[str, ForecastResult]:
    """
    Compare forecasting results under different manufacturing scenarios.
    
    Args:
        target_variable: Variable to forecast
        scenarios: List of scenario configurations with different input conditions
    
    Returns:
        Dictionary mapping scenario names to forecast results
    """
    results = {}
    
    for i, scenario in enumerate(scenarios):
        scenario_name = scenario.get('name', f'scenario_{i+1}')
        
        # Modify input data based on scenario parameters
        modified_data = apply_scenario_modifications(
            ctx.deps.get('unified_data'), scenario
        )
        
        # Generate forecast for this scenario
        forecast = await forecast_manufacturing_parameter(
            ctx, target_variable, 
            forecast_horizon=scenario.get('forecast_horizon', 96),
            lookback_window=scenario.get('lookback_window', 240)
        )
        
        results[scenario_name] = forecast
    
    return results
```

### 9. **Transfer Learning Implementation Examples**

#### **Zero-Shot Forecasting with IBM Granite Models**
```python
def zero_shot_manufacturing_forecast(source_model_path: str, target_data: pd.DataFrame):
    """
    Perform zero-shot forecasting on new manufacturing data using pre-trained model.
    """
    # Load pre-trained model (e.g., IBM Granite pre-trained on electricity data)
    model = PatchTSTForPrediction.from_pretrained(source_model_path)
    
    # Apply to manufacturing data without additional training
    input_tensor = prepare_input_tensor(target_data)
    
    with torch.no_grad():
        outputs = model(past_values=input_tensor)
        predictions = outputs.prediction_outputs.squeeze().tolist()
    
    return predictions

# Usage with IBM Granite pre-trained model
predictions = zero_shot_manufacturing_forecast(
    "ibm-granite/granite-timeseries-patchtst",  # Pre-trained HuggingFace model
    manufacturing_data
)
```

#### **Linear Probing for Manufacturing Adaptation**
```python
def linear_probe_manufacturing(pretrained_model_path: str, manufacturing_data: pd.DataFrame):
    """
    Adapt pre-trained model to manufacturing data using linear probing.
    """
    # Load pre-trained model
    model = PatchTSTForPrediction.from_pretrained(pretrained_model_path)
    
    # Freeze backbone parameters
    for param in model.model.parameters():
        param.requires_grad = False
    
    # Only train the final prediction head
    train_dataset, valid_dataset, _, _ = prepare_manufacturing_data(manufacturing_data)
    
    # Fast fine-tuning configuration
    training_args = TrainingArguments(
        output_dir="./models/linear_probe_manufacturing/",
        learning_rate=0.001,        # Higher learning rate for head only
        num_train_epochs=20,        # Fewer epochs needed
        evaluation_strategy="epoch"
    )
    
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=valid_dataset
    )
    
    trainer.train()
    return trainer
```

### 10. **Advanced Manufacturing Integration Examples**

#### **Multi-Horizon Forecasting**
```python
@correlation_agent.tool
async def multi_horizon_manufacturing_forecast(
    ctx: RunContext,
    target_variable: str,
    forecast_horizons: List[int] = [15, 60, 240]  # 15min, 1hr, 4hr
) -> Dict[str, ForecastResult]:
    """
    Generate forecasts for multiple time horizons simultaneously.
    """
    results = {}
    
    for horizon in forecast_horizons:
        horizon_name = f"{horizon}_minutes"
        forecast = await forecast_manufacturing_parameter(
            ctx, target_variable, horizon
        )
        results[horizon_name] = forecast
    
    return results
```

#### **Manufacturing Domain-Specific Processing**
```python
def prepare_input_tensor(data: pd.DataFrame, target_variable: str) -> torch.Tensor:
    """Prepare manufacturing data for PatchTST input with domain expertise"""
    # Manufacturing-specific feature engineering
    feature_columns = [
        'thickness_avg', 'thickness_uniformity', 'speed',
        'temperature', 'pressure', 'minutes_since_last_stop'
    ]
    
    # Add derived manufacturing features
    data['speed_stability'] = data['speed'].rolling(window=10).std()
    data['temperature_trend'] = data['temperature'].diff()
    data['thickness_quality_score'] = data['thickness_avg'] / data['thickness_uniformity']
    
    # Handle manufacturing-specific missing data patterns
    data = handle_manufacturing_missing_data(data)
    
    # Apply manufacturing-specific normalization
    normalized_data = apply_manufacturing_normalization(data[feature_columns])
    
    # Convert to tensor format expected by PatchTST
    input_tensor = torch.FloatTensor(normalized_data).unsqueeze(0)
    
    return input_tensor

def generate_manufacturing_insights(
    target_variable: str, 
    forecast_values: List[float], 
    historical_data: pd.DataFrame,
    attention_insights: Dict[str, Any]
) -> List[str]:
    """Generate actionable manufacturing insights from forecast"""
    insights = []
    
    # Trend analysis with manufacturing context
    trend_change = forecast_values[-1] - forecast_values[0]
    if abs(trend_change) > 0.1:  # Significant change threshold
        direction = "increasing" if trend_change > 0 else "decreasing"
        insights.append(f"Significant {direction} trend predicted in {target_variable}")
        
        # Manufacturing-specific recommendations
        if target_variable == "thickness_avg" and direction == "increasing":
            insights.append("Consider reducing line speed to maintain thickness control")
        elif target_variable == "scrap_rate" and direction == "increasing":
            insights.append("Elevated scrap rate predicted - investigate process parameters")
    
    # Volatility analysis
    volatility = np.std(forecast_values)
    historical_volatility = historical_data[target_variable].std()
    
    if volatility > historical_volatility * 1.5:
        insights.append(f"High variability predicted in {target_variable} - monitor process stability")
    
    # Manufacturing-specific pattern detection
    if "shift_change_effect" in attention_insights.get("key_patterns", []):
        insights.append("Shift change effects detected - ensure proper handover procedures")
    
    return insights
```

### 11. **Comprehensive Configuration Examples**

#### **Advanced Manufacturing Configuration**
```python
# Enhanced config.json for manufacturing forecasting
{
  "forecasting_config": {
    "data_sources": {
      "primary": "unified_manufacturing_data.csv",
      "backup": "fallback_sensor_data.csv"
    },
    "input_variables": [
      "thickness_avg", 
      "thickness_uniformity", 
      "speed", 
      "temperature", 
      "pressure",
      "minutes_since_last_stop",
      "shift_indicator",
      "maintenance_hours_ago"
    ],
    "target_variables": [
      {
        "name": "thickness_avg",
        "type": "continuous",
        "units": "mm",
        "valid_range": [10.0, 15.0],
        "alert_thresholds": {"low": 11.0, "high": 14.0}
      },
      {
        "name": "scrap_rate",
        "type": "rate",
        "units": "percentage",
        "valid_range": [0.0, 25.0],
        "alert_thresholds": {"high": 15.0}
      },
      {
        "name": "quality_index",
        "type": "score",
        "units": "index",
        "valid_range": [0.0, 100.0],
        "alert_thresholds": {"low": 85.0}
      }
    ],
    "forecast_horizons": [15, 60, 240, 1440],  # 15min, 1hr, 4hr, 24hr
    "lookback_window": 240,                    # 4 hours historical context
    "update_frequency": 15,                    # Update forecasts every 15 minutes
    "patch_size": 16,                          # 16-minute patches
    "model_params": {
      "d_model": 128,
      "n_heads": 8,
      "num_layers": 3,
      "dropout": 0.1,
      "activation": "gelu",
      "norm_first": true
    },
    "training_params": {
      "batch_size": 32,
      "learning_rate": 0.0001,
      "max_epochs": 100,
      "early_stopping_patience": 10,
      "validation_split": 0.15,
      "test_split": 0.15
    },
    "deployment": {
      "model_versioning": true,
      "a_b_testing": true,
      "confidence_intervals": true,
      "explanation_required": true,
      "alert_integration": true
    }
  }
}
```

### Integration Workflow Example:
```python
# Complete workflow integrating with Phase 2.1 infrastructure
async def manufacturing_forecasting_workflow():
    """Complete forecasting workflow integrated with existing correlation agent"""
    
    # 1. Load manufacturing data (using existing Phase 2.1 data loader)
    from src.data.loader import load_unified_manufacturing_data
    unified_data = await load_unified_manufacturing_data()
    
    # 2. Generate multi-horizon forecasts
    forecast_results = await correlation_agent.run(
        "Generate 15-minute, 1-hour, and 4-hour forecasts for thickness and speed",
        deps={'unified_data': unified_data}
    )
    
    # 3. Compare scenarios (e.g., speed adjustments)
    scenarios = [
        {"name": "current_conditions", "speed_adjustment": 0},
        {"name": "increased_speed", "speed_adjustment": 0.1},
        {"name": "decreased_speed", "speed_adjustment": -0.1}
    ]
    
    scenario_results = await correlation_agent.run(
        "Compare forecast scenarios for thickness_avg with different speed settings",
        deps={'unified_data': unified_data, 'scenarios': scenarios}
    )
    
    # 4. Generate visualizations (integrating with Phase 2.1 visualization tools)
    await correlation_agent.run(
        "Create comprehensive forecast visualization dashboard",
        deps={'forecast_results': forecast_results, 'scenario_results': scenario_results}
    )
    
    return forecast_results, scenario_results
```

## DOCUMENTATION:

### Core Technical References:

#### 1. **PatchTST Method Documentation**
- **Primary Source**: `/Users/<USER>/JH/JH PoC/test_1/docs/2211.14730v2.pdf`
  - "A Time Series is Worth 64 Words: Long-term Forecasting with Transformers"
  - Patch-based segmentation theory and architecture details
  - Attention mechanism for time series forecasting
  - Performance benchmarks on industrial datasets

#### 2. **Implementation Repository**
- **Granite TSFM**: `https://github.com/jentrix-au/granite-tsfm/tree/main/tsfm_public`
  - Production-ready PatchTST implementation
  - Time series preprocessing utilities
  - Model configuration examples
  - Training and evaluation frameworks

#### 3. **Jupyter Notebook Examples**
- **Getting Started**: `/Users/<USER>/JH/JH PoC/test_1/docs/patch_tst_getting_started.ipynb`
  - Basic model setup and configuration
  - Dataset preparation with ForecastDFDataset
  - Training loop and evaluation metrics
- **Transfer Learning**: `/Users/<USER>/JH/JH PoC/test_1/docs/patch_tst_transfer.ipynb`
  - Zero-shot forecasting approaches
  - Model fine-tuning strategies
  - Cross-domain adaptation techniques
- **Alternative Architecture**: `/Users/<USER>/JH/JH PoC/test_1/docs/patch_tsmixer_transfer.ipynb`
  - PatchTSMixer implementation patterns
  - Channel-independent processing
  - Manufacturing-specific adaptations

#### 4. **Framework Documentation**
- **HuggingFace Transformers**: `https://huggingface.co/docs/transformers/model_doc/patchtst`
  - PatchTSTConfig and PatchTSTForPrediction classes
  - Integration with Trainer and TrainingArguments
  - Model serialization and deployment patterns
- **PydanticAI Integration**: `https://ai.pydantic.dev/`
  - Agent tool registration patterns
  - Structured output handling for forecasts
  - Async tool execution for model inference

#### 5. **Manufacturing Domain References**
- **Project Documentation**: Existing manufacturing analysis patterns in `PLANNING.md`, `INITIAL.md`
- **Data Architecture**: Established time alignment and processing from Phase 2.1
- **Agent Patterns**: Production-ready agent architecture from correlation analysis system

#### 6. **Time Series Forecasting Best Practices**
- **Industrial Applications**: Manufacturing-specific forecasting considerations
- **Model Validation**: Time series cross-validation techniques
- **Interpretability**: Attention visualization for manufacturing insights

#### 7. **Additional Research-Based References**
- **Official PatchTST Repository**: `https://github.com/yuqinie98/PatchTST`
  - Original implementation with supervised and self-supervised learning
  - Training scripts and configuration examples
  - Performance benchmarks on industrial datasets
- **HuggingFace Blog Post**: `https://huggingface.co/blog/patchtst`
  - Practical implementation examples with electricity data
  - Transfer learning capabilities and zero-shot forecasting
  - Integration with HuggingFace Transformers ecosystem
- **IBM Granite Pre-trained Models**: `https://huggingface.co/ibm-granite/granite-timeseries-patchtst`
  - Production-ready pre-trained models for transfer learning
  - ETTh1 dataset validation (MSE: 0.3881 on test split)
  - 512-hour historical window, 96-hour forecast capabilities
- **NeuralForecast Implementation**: `https://nixtlaverse.nixtla.io/neuralforecast/models.patchtst.html`
  - Alternative PatchTST implementation with different API
  - Custom loss functions and configuration options
  - Manufacturing time series examples
- **Time Series Transformer Architecture**: `https://huggingface.co/docs/transformers/en/model_doc/time_series_transformer`
  - Probabilistic forecasting models for uncertainty estimation
  - Encoder-decoder architecture patterns
  - Distribution-based output handling

### Development Workflow Documentation:
1. **Phase 2.1 Foundation**: Leverage existing production-ready correlation agent architecture
2. **Data Pipeline**: Extend current manufacturing data processing (262K+ records validated)
3. **Agent Integration**: Follow established tool registration and natural language patterns
4. **Configuration Management**: JSON-based configuration following established patterns
5. **Testing Framework**: Extend existing 73-test suite with forecasting validation
6. **Visualization**: Integrate with current 5-tool visualization suite

## OTHER CONSIDERATIONS:

### Manufacturing-Specific Requirements:

#### 1. **Data Characteristics and Challenges**
- **Mixed-Frequency Integration**: Building on existing 99.5% timestamp alignment success
  - Combine high-frequency sensor data (thickness, speed) with low-frequency events (stoppages, quality)
  - Handle 5 different time column formats automatically
  - Maintain 262K+ unified timeline records for training data
- **Manufacturing Patterns**: 
  - Shift cycles (day/night/weekend patterns)
  - Equipment warm-up periods and steady-state operations
  - Planned maintenance windows and startup sequences
- **Domain-Specific Variables**:
  - Thickness = average of 10-sensor arrays with uniformity calculations
  - Speed variations linked to process adjustments
  - Stoppage cascading effects on subsequent production quality

#### 2. **Production Environment Integration**
- **Real-Time Requirements**: 
  - Sub-second inference for operational forecasts
  - Batch processing for longer-term planning horizons
  - Integration with existing manufacturing data streams
- **Scalability Considerations**:
  - Handle enterprise-scale data (295K+ records validated in Phase 2.1)
  - Parallel processing for multiple forecast targets
  - Memory-efficient model serving for continuous operation
- **Reliability and Monitoring**:
  - Model performance degradation detection
  - Forecast accuracy monitoring and alerts
  - Graceful fallback to baseline methods when needed

#### 3. **AI Agent Integration Gotchas**
- **Tool Registration**: Use `output_type` instead of deprecated `result_type` in PydanticAI agents
- **Environment Variables**: Strict configuration management - system fails with clear error messages instead of fallback values
- **Threading Safety**: Apply `matplotlib.use('Agg')` lessons from Phase 2.1 for visualization integration
- **Data Structure Validation**: Implement targeted validation functions to avoid mixed object/dictionary handling errors
- **P-value Formatting**: Maintain both numeric and formatted values for forecasting confidence intervals

#### 4. **Model Deployment and Operations**
- **Container Deployment**: Headless operation requirements for server/cloud environments
- **Cross-Platform Compatibility**: Ensure consistent behavior across macOS, Linux, Windows
- **API Integration**: RESTful endpoints for forecast requests from manufacturing systems
- **Configuration Management**: JSON-based configuration with validation and schema enforcement
- **Model Versioning**: Support for A/B testing and gradual rollout of improved models

#### 5. **Performance and Accuracy Requirements**
- **Baseline Comparison**: 15% improvement over ARIMA/Linear Regression methods
- **Forecast Horizons**: Multi-horizon evaluation (15 min, 1 hour, 4 hours, 24 hours)
- **Uncertainty Quantification**: Confidence intervals and prediction intervals for manufacturing decisions
- **Feature Importance**: Explainable forecasting for process optimization insights

#### 6. **Advanced Manufacturing Integration**
- **Causal Relationships**: Leverage existing correlation discovery for feature engineering
- **Process Context**: Integrate with established manufacturing domain expertise
- **Quality Metrics**: Align forecasting accuracy with business impact (scrap reduction, quality improvement)
- **Stakeholder Communication**: Professional visualization output for engineering and management teams

#### 7. **Technical Debt and Future-Proofing**
- **Model Architecture**: Design for easy extension to other transformer architectures (TimesNet, PatchTSMixer)
- **Data Pipeline**: Maintain compatibility with existing Phase 2.1 data processing infrastructure  
- **Agent Framework**: Follow established PydanticAI patterns for consistency and maintainability
- **Testing Strategy**: Comprehensive test coverage including real API integration (no mocking)
- **Documentation**: Manufacturing domain documentation for model interpretation and troubleshooting

#### 8. **Regulatory and Compliance**
- **Data Privacy**: Maintain anonymization patterns established in Phase 2.1
- **Audit Trails**: Log all forecast decisions and model updates for quality system compliance
- **Validation Documentation**: Comprehensive model validation reports for regulatory approval
- **Change Management**: Structured approach to model updates and performance monitoring

### Success Metrics and Validation:
- **Technical Performance**: RMSE, MAE, MAPE across different forecast horizons
- **Business Impact**: Correlation between forecast accuracy and production optimization decisions
- **User Adoption**: Natural language interface usage and agent interaction patterns
- **System Reliability**: Uptime, response times, and error rates in production environment
- **Model Interpretability**: Ability to explain forecast drivers to manufacturing engineers and process experts