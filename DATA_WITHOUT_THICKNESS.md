# Unified Manufacturing Data Table Creation Guide

## Overview
This document provides a comprehensive methodology for creating a unified data table from multiple manufacturing data sources, enabling analysis of Stack Machine (SM) and Finishing Machine (FM) operations.

## Table of Contents
1. [Data Sources](#data-sources)
2. [Step-by-Step Methodology](#step-by-step-methodology)
3. [Implementation Details](#implementation-details)
4. [Code Examples](#code-examples)
5. [Final Table Structure](#final-table-structure)
6. [Data Validation](#data-validation)

## Data Sources

### Input Files
- **sm_stack.csv**: Stack machine production data
- **fm_stack.csv**: Finishing machine processing data
- **speed.csv**: Machine speed logs (minute-level)
- **stop.csv**: Machine stoppage events
- **VM Capacity Report.csv**: Product specifications

### Key Relationships
```
SM Stack ──Production Order──> FM Stack
    │                             │
    ├── Speed Data (time-based)   │
    ├── Stoppage Data (events)    │
    └── Product Specs (lookup) ───┘
```

## Step-by-Step Methodology

### Phase 1: Data Preparation

#### Step 1.1: Parse Timestamps
Convert all date/time strings to proper datetime objects for accurate time-based operations.

```python
import pandas as pd
import numpy as np
from datetime import datetime, timedel<PERSON>

def parse_datetime(date_str, time_str):
    """Convert date and time strings to datetime object"""
    if isinstance(date_str, str) and isinstance(time_str, str):
        # Handle format: "2025.03.01" + "0:00:09"
        return pd.to_datetime(f"{date_str} {time_str}", format="%Y.%m.%d %H:%M:%S")
    return pd.NaT
```

#### Step 1.2: Prepare SM Stack Data
```python
def prepare_sm_stack_data(sm_stack_df):
    """
    Transform raw SM stack data into analysis-ready format
    
    Input columns:
    - Stack Number, First Sheet Date Time, Last Sheet Date Time
    - Sheet Cut, Good Sheets, Scrap%, Production Order, Product
    
    Output additions:
    - Parsed timestamps
    - Duration in minutes
    - Numeric scrap percentage
    - Base production order (for matching)
    """
    # Parse timestamps
    sm_stack_df['first_sheet_timestamp'] = pd.to_datetime(sm_stack_df['First Sheet Date Time'])
    sm_stack_df['last_sheet_timestamp'] = pd.to_datetime(sm_stack_df['Last Sheet Date Time'])
    
    # Calculate duration
    sm_stack_df['sm_duration_minutes'] = (
        sm_stack_df['last_sheet_timestamp'] - sm_stack_df['first_sheet_timestamp']
    ).dt.total_seconds() / 60
    
    # Convert scrap percentage from string to float
    # "2.33%" -> 2.33
    sm_stack_df['sm_scrap_pct'] = sm_stack_df['Scrap%'].str.rstrip('%').astype(float)
    
    # Extract base production order for FM matching
    # "1508615/1" -> "1508615"
    sm_stack_df['base_production_order'] = sm_stack_df['Production Order'].str.split('/').str[0]
    
    return sm_stack_df
```

#### Step 1.3: Prepare FM Stack Data
```python
def prepare_fm_stack_data(fm_stack_df):
    """
    Transform raw FM stack data
    
    Key transformations:
    - Parse on-load and off-load timestamps
    - Calculate FM processing duration
    - Calculate reject percentage
    """
    # Parse timestamps
    fm_stack_df['on_load_timestamp'] = pd.to_datetime(fm_stack_df['On-Load'])
    fm_stack_df['off_load_timestamp'] = pd.to_datetime(fm_stack_df['Off-Load'])
    
    # Calculate duration
    fm_stack_df['fm_duration_minutes'] = (
        fm_stack_df['off_load_timestamp'] - fm_stack_df['on_load_timestamp']
    ).dt.total_seconds() / 60
    
    # Calculate reject percentage
    total_sheets = fm_stack_df['Ok'] + fm_stack_df['Rej.']
    fm_stack_df['fm_reject_pct'] = np.where(
        total_sheets > 0,
        fm_stack_df['Rej.'] / total_sheets * 100,
        0
    )
    
    return fm_stack_df
```

### Phase 2: Aggregate Time-Series Data

#### Step 2.1: Speed Data Aggregation
```python
def aggregate_speed_data(sm_stack_df, speed_df):
    """
    Calculate speed statistics for each SM stack's production period
    
    For each stack, find all speed readings during production and calculate:
    - Average speed
    - Speed variability (std dev, CV)
    - Min/max speeds
    """
    # Parse speed data timestamps
    speed_df['timestamp'] = speed_df.apply(
        lambda x: parse_datetime(x['Log Date'], x['Log Time']), axis=1
    )
    
    speed_stats_list = []
    
    for _, stack in sm_stack_df.iterrows():
        # Filter speed data to stack production period
        mask = (
            (speed_df['timestamp'] >= stack['first_sheet_timestamp']) & 
            (speed_df['timestamp'] <= stack['last_sheet_timestamp']) &
            (speed_df['Work Center/Resource'] == stack.get('Work Center/Resource', 'SM270'))
        )
        
        relevant_speeds = speed_df[mask]['Speed']
        
        if not relevant_speeds.empty:
            stats = {
                'Stack Number': stack['Stack Number'],
                'speed_avg': relevant_speeds.mean(),
                'speed_std': relevant_speeds.std(),
                'speed_min': relevant_speeds.min(),
                'speed_max': relevant_speeds.max(),
                'speed_cv': relevant_speeds.std() / relevant_speeds.mean() if relevant_speeds.mean() > 0 else 0,
                'speed_readings_count': len(relevant_speeds)
            }
        else:
            # No speed data found for this period
            stats = {
                'Stack Number': stack['Stack Number'],
                'speed_avg': np.nan,
                'speed_std': np.nan,
                'speed_min': np.nan,
                'speed_max': np.nan,
                'speed_cv': np.nan,
                'speed_readings_count': 0
            }
        
        speed_stats_list.append(stats)
    
    return pd.DataFrame(speed_stats_list)
```

#### Step 2.2: Stoppage Data Aggregation
```python
def aggregate_stoppage_data(sm_stack_df, stop_df):
    """
    Calculate stoppage metrics for each stack
    
    Metrics include:
    - Stops during production
    - Stops in the hour before production
    - Total stop duration
    - Production efficiency
    """
    # Parse stop timestamps
    stop_df['stop_timestamp'] = stop_df.apply(
        lambda x: parse_datetime(x['Stop Date'], x['Stop Time']), axis=1
    )
    stop_df['restart_timestamp'] = stop_df.apply(
        lambda x: parse_datetime(x['Restart Date'], x['Restart Time']), axis=1
    )
    
    stoppage_stats_list = []
    
    for _, stack in sm_stack_df.iterrows():
        # Get work center for this stack
        work_center = stack.get('Work Center/Resource', 'SM270')
        work_center_stops = stop_df[stop_df['Work Center/Resource'] == work_center]
        
        # Stops during production
        during_mask = (
            (work_center_stops['stop_timestamp'] >= stack['first_sheet_timestamp']) & 
            (work_center_stops['stop_timestamp'] <= stack['last_sheet_timestamp'])
        )
        stops_during = work_center_stops[during_mask]
        
        # Stops in hour before production
        hour_before = stack['first_sheet_timestamp'] - timedelta(hours=1)
        before_mask = (
            (work_center_stops['stop_timestamp'] >= hour_before) & 
            (work_center_stops['stop_timestamp'] < stack['first_sheet_timestamp'])
        )
        stops_before = work_center_stops[before_mask]
        
        # Calculate statistics
        total_stop_duration = stops_during['MPS Stop Duration'].sum() if not stops_during.empty else 0
        production_efficiency = (
            (stack['sm_duration_minutes'] - total_stop_duration) / stack['sm_duration_minutes'] 
            if stack['sm_duration_minutes'] > 0 else 1
        )
        
        stats = {
            'Stack Number': stack['Stack Number'],
            'stops_during_production': len(stops_during),
            'stop_duration_during': total_stop_duration,
            'stops_hour_before': len(stops_before),
            'stop_duration_hour_before': stops_before['MPS Stop Duration'].sum() if not stops_before.empty else 0,
            'production_efficiency': production_efficiency,
            'longest_stop_during': stops_during['MPS Stop Duration'].max() if not stops_during.empty else 0
        }
        
        # Add stop reason breakdown if stops occurred
        if not stops_during.empty:
            top_reason = stops_during['Stoppage Reason'].mode()[0] if len(stops_during['Stoppage Reason'].mode()) > 0 else 'Unknown'
            stats['primary_stop_reason'] = top_reason
        else:
            stats['primary_stop_reason'] = 'None'
        
        stoppage_stats_list.append(stats)
    
    return pd.DataFrame(stoppage_stats_list)
```

### Phase 3: Stack Matching

#### Step 3.1: Match SM to FM Stacks
```python
def match_sm_fm_stacks(sm_stack_df, fm_stack_df):
    """
    Match SM stacks to their corresponding FM processing
    
    Matching logic:
    1. Same production order (base)
    2. FM processing starts after SM completion
    3. Take earliest FM stack if multiple matches
    """
    matches = []
    
    for _, sm_row in sm_stack_df.iterrows():
        # Find potential FM matches
        potential_fm = fm_stack_df[
            fm_stack_df['Production Order'] == sm_row['base_production_order']
        ]
        
        if not potential_fm.empty:
            # Calculate time gaps
            potential_fm = potential_fm.copy()
            potential_fm['time_gap_hours'] = (
                potential_fm['on_load_timestamp'] - sm_row['last_sheet_timestamp']
            ).dt.total_seconds() / 3600
            
            # Filter to valid matches (FM starts after SM)
            valid_fm = potential_fm[potential_fm['time_gap_hours'] > 0]
            
            if not valid_fm.empty:
                # Take the earliest FM processing
                best_match = valid_fm.loc[valid_fm['time_gap_hours'].idxmin()]
                
                match = {
                    'Stack Number': sm_row['Stack Number'],
                    'fm_mps_id': best_match['MPS ID'],
                    'fm_production_order': best_match['Production Order'],
                    'fm_ok_sheets': best_match['Ok'],
                    'fm_reject_sheets': best_match['Rej.'],
                    'fm_reject_pct': best_match['fm_reject_pct'],
                    'fm_product_description': best_match['Product Description'],
                    'time_lag_hours': best_match['time_gap_hours'],
                    'fm_duration_minutes': best_match['fm_duration_minutes'],
                    'has_fm_match': True
                }
            else:
                # No valid FM match found
                match = create_empty_fm_match(sm_row['Stack Number'])
        else:
            # No FM records for this production order
            match = create_empty_fm_match(sm_row['Stack Number'])
        
        matches.append(match)
    
    return pd.DataFrame(matches)

def create_empty_fm_match(stack_number):
    """Create empty FM match record"""
    return {
        'Stack Number': stack_number,
        'fm_mps_id': None,
        'fm_production_order': None,
        'fm_ok_sheets': None,
        'fm_reject_sheets': None,
        'fm_reject_pct': None,
        'fm_product_description': None,
        'time_lag_hours': None,
        'fm_duration_minutes': None,
        'has_fm_match': False
    }
```

### Phase 4: Add Product Specifications

#### Step 4.1: Extract and Match Product Codes
```python
def add_product_specifications(unified_df, vm_capacity_df):
    """
    Add design specifications from VM Capacity Report
    
    Steps:
    1. Extract product code from product description
    2. Match with VM Capacity Report
    3. Calculate deviation from design
    """
    # Extract 7-digit product code from Product field
    # Example: "6X405301 HARDIE OBLIQUE..." -> "405301"
    unified_df['product_code'] = unified_df['Product'].str.extract(r'(\d{7})')
    
    # Clean VM Capacity column names
    vm_capacity_df = vm_capacity_df.rename(columns={'SAP Code\t': 'SAP_Code'})
    
    # Ensure product codes are strings for matching
    vm_capacity_df['product_code'] = vm_capacity_df['SAP_Code'].astype(str)
    
    # Merge specifications
    unified_df = unified_df.merge(
        vm_capacity_df[['product_code', 'Design Capacity', 'Design Felt Speed', 
                         'Sheet Machine', 'Finishing Machine']],
        on='product_code',
        how='left'
    )
    
    # Calculate operational deviations
    unified_df['speed_deviation_from_design'] = np.where(
        unified_df['Design Felt Speed'].notna() & (unified_df['Design Felt Speed'] > 0),
        (unified_df['speed_avg'] - unified_df['Design Felt Speed']) / unified_df['Design Felt Speed'] * 100,
        np.nan
    )
    
    # Calculate production rate metrics
    unified_df['sheets_per_minute'] = np.where(
        unified_df['sm_duration_minutes'] > 0,
        unified_df['Good Sheets'] / unified_df['sm_duration_minutes'],
        0
    )
    
    unified_df['capacity_utilization'] = np.where(
        unified_df['Design Capacity'].notna() & (unified_df['Design Capacity'] > 0),
        unified_df['sheets_per_minute'] * 60 / unified_df['Design Capacity'] * 100,
        np.nan
    )
    
    return unified_df
```

### Phase 5: Add Temporal Features

#### Step 5.1: Create Time-Based Features
```python
def add_temporal_features(unified_df):
    """
    Add time-based features for pattern analysis
    
    Features:
    - Hour of day, day of week
    - Shift classification
    - Weekend indicator
    """
    # Basic time components
    unified_df['production_hour'] = unified_df['first_sheet_timestamp'].dt.hour
    unified_df['production_day_of_week'] = unified_df['first_sheet_timestamp'].dt.dayofweek
    unified_df['production_date'] = unified_df['first_sheet_timestamp'].dt.date
    
    # Shift classification
    def classify_shift(hour):
        if 6 <= hour < 14:
            return 'morning'
        elif 14 <= hour < 22:
            return 'afternoon'
        else:
            return 'night'
    
    unified_df['production_shift'] = unified_df['production_hour'].apply(classify_shift)
    
    # Weekend indicator
    unified_df['is_weekend'] = unified_df['production_day_of_week'].isin([5, 6])
    
    # Time since start of week/month
    unified_df['week_of_year'] = unified_df['first_sheet_timestamp'].dt.isocalendar().week
    unified_df['month'] = unified_df['first_sheet_timestamp'].dt.month
    
    return unified_df
```

## Complete Implementation

### Main Function to Create Unified Table
```python
def create_unified_table(sm_stack_df, fm_stack_df, speed_df, stop_df, vm_capacity_df):
    """
    Main function to create unified manufacturing data table
    
    Parameters:
    - sm_stack_df: Stack machine data
    - fm_stack_df: Finishing machine data
    - speed_df: Speed logs
    - stop_df: Stoppage events
    - vm_capacity_df: Product specifications
    
    Returns:
    - unified_df: Complete unified dataset
    """
    print("Step 1: Preparing base data...")
    # Prepare individual datasets
    sm_stack_df = prepare_sm_stack_data(sm_stack_df)
    fm_stack_df = prepare_fm_stack_data(fm_stack_df)
    
    # Start with SM stack data as base
    unified_df = sm_stack_df.copy()
    print(f"  Base table: {len(unified_df)} SM stacks")
    
    print("\nStep 2: Aggregating speed data...")
    # Add speed statistics
    speed_stats = aggregate_speed_data(sm_stack_df, speed_df)
    unified_df = unified_df.merge(speed_stats, on='Stack Number', how='left')
    print(f"  Added speed metrics for {speed_stats['Stack Number'].notna().sum()} stacks")
    
    print("\nStep 3: Aggregating stoppage data...")
    # Add stoppage statistics
    stoppage_stats = aggregate_stoppage_data(sm_stack_df, stop_df)
    unified_df = unified_df.merge(stoppage_stats, on='Stack Number', how='left')
    print(f"  Added stoppage metrics for {len(stoppage_stats)} stacks")
    
    print("\nStep 4: Matching FM stacks...")
    # Add FM matches
    fm_matches = match_sm_fm_stacks(sm_stack_df, fm_stack_df)
    unified_df = unified_df.merge(fm_matches, on='Stack Number', how='left')
    match_rate = fm_matches['has_fm_match'].sum() / len(fm_matches) * 100
    print(f"  Matched {fm_matches['has_fm_match'].sum()} stacks to FM ({match_rate:.1f}%)")
    
    print("\nStep 5: Adding product specifications...")
    # Add product specifications
    unified_df = add_product_specifications(unified_df, vm_capacity_df)
    specs_found = unified_df['Design Capacity'].notna().sum()
    print(f"  Found specifications for {specs_found} products")
    
    print("\nStep 6: Adding temporal features...")
    # Add temporal features
    unified_df = add_temporal_features(unified_df)
    
    print("\nStep 7: Final calculations...")
    # Additional calculated fields
    unified_df['total_reject_rate'] = np.where(
        unified_df['has_fm_match'],
        unified_df['sm_scrap_pct'] + unified_df['fm_reject_pct'] - 
        (unified_df['sm_scrap_pct'] * unified_df['fm_reject_pct'] / 100),
        unified_df['sm_scrap_pct']
    )
    
    # Sort by timestamp for time series analysis
    unified_df = unified_df.sort_values('first_sheet_timestamp')
    
    # Add sequential index
    unified_df['stack_sequence'] = range(len(unified_df))
    
    print(f"\nUnified table created successfully!")
    print(f"Final dimensions: {len(unified_df)} rows x {len(unified_df.columns)} columns")
    
    return unified_df
```

## Final Table Structure

### Column Categories

#### 1. Stack Identifiers
- `Stack Number` (primary key)
- `stack_sequence` (sequential index)
- `Production Order`
- `base_production_order`
- `Product`
- `product_code`

#### 2. Time Information
- `first_sheet_timestamp`
- `last_sheet_timestamp`
- `sm_duration_minutes`
- `production_date`
- `production_hour`
- `production_day_of_week`
- `production_shift`
- `is_weekend`
- `week_of_year`
- `month`

#### 3. SM Production Metrics
- `Sheet Cut`
- `Good Sheets`
- `sm_scrap_pct`
- `sheets_per_minute`

#### 4. Speed Metrics
- `speed_avg`
- `speed_std`
- `speed_min`
- `speed_max`
- `speed_cv`
- `speed_readings_count`

#### 5. Stoppage Metrics
- `stops_during_production`
- `stop_duration_during`
- `stops_hour_before`
- `stop_duration_hour_before`
- `production_efficiency`
- `longest_stop_during`
- `primary_stop_reason`

#### 6. FM Processing Data
- `has_fm_match`
- `fm_mps_id`
- `fm_production_order`
- `fm_ok_sheets`
- `fm_reject_sheets`
- `fm_reject_pct`
- `fm_product_description`
- `time_lag_hours`
- `fm_duration_minutes`

#### 7. Design Specifications
- `Design Capacity`
- `Design Felt Speed`
- `Sheet Machine`
- `Finishing Machine`
- `speed_deviation_from_design`
- `capacity_utilization`

#### 8. Calculated Metrics
- `total_reject_rate`

## Data Validation

### Validation Checks
```python
def validate_unified_table(unified_df):
    """
    Perform data quality checks on unified table
    """
    validation_results = {}
    
    # Check 1: Temporal consistency
    temporal_issues = unified_df[
        unified_df['last_sheet_timestamp'] < unified_df['first_sheet_timestamp']
    ]
    validation_results['temporal_consistency'] = len(temporal_issues) == 0
    
    # Check 2: FM matching validity
    fm_temporal_issues = unified_df[
        unified_df['has_fm_match'] & 
        (unified_df['time_lag_hours'] < 0)
    ]
    validation_results['fm_matching_validity'] = len(fm_temporal_issues) == 0
    
    # Check 3: Data completeness
    critical_columns = ['Stack Number', 'first_sheet_timestamp', 'sm_scrap_pct']
    completeness = {}
    for col in critical_columns:
        completeness[col] = unified_df[col].notna().sum() / len(unified_df)
    validation_results['completeness'] = completeness
    
    # Check 4: Value ranges
    range_checks = {
        'scrap_pct_valid': ((unified_df['sm_scrap_pct'] >= 0) & 
                            (unified_df['sm_scrap_pct'] <= 100)).all(),
        'efficiency_valid': ((unified_df['production_efficiency'] >= 0) & 
                            (unified_df['production_efficiency'] <= 1)).all(),
        'positive_durations': (unified_df['sm_duration_minutes'] > 0).all()
    }
    validation_results['range_checks'] = range_checks
    
    # Print validation report
    print("\nData Validation Report")
    print("=" * 50)
    for check, result in validation_results.items():
        if isinstance(result, dict):
            print(f"\n{check}:")
            for subcheck, subresult in result.items():
                print(f"  {subcheck}: {subresult}")
        else:
            print(f"{check}: {'PASS' if result else 'FAIL'}")
    
    return validation_results
```

### Usage Example
```python
# Load data files
print("Loading data files...")
sm_stack_df = pd.read_csv('sm_stack.csv')
fm_stack_df = pd.read_csv('fm_stack.csv')
speed_df = pd.read_csv('speed.csv')
stop_df = pd.read_csv('stop.csv')
vm_capacity_df = pd.read_csv('VM Capacity Report.csv')

# Create unified table
unified_df = create_unified_table(
    sm_stack_df, fm_stack_df, speed_df, stop_df, vm_capacity_df
)

# Validate data
validation_results = validate_unified_table(unified_df)

# Save results
unified_df.to_csv('unified_manufacturing_data.csv', index=False)
print(f"\nSaved unified table to 'unified_manufacturing_data.csv'")

# Display sample
print("\nSample of unified data:")
print(unified_df.head())

# Summary statistics
print("\nSummary Statistics:")
print(f"Total stacks: {len(unified_df)}")
print(f"Date range: {unified_df['first_sheet_timestamp'].min()} to {unified_df['first_sheet_timestamp'].max()}")
print(f"FM match rate: {unified_df['has_fm_match'].mean():.1%}")
print(f"Average SM scrap: {unified_df['sm_scrap_pct'].mean():.2f}%")
print(f"Average FM reject (matched): {unified_df[unified_df['has_fm_match']]['fm_reject_pct'].mean():.2f}%")
```

## Key Considerations

### Performance Optimization
1. **Vectorization**: Use pandas operations instead of loops where possible
2. **Memory efficiency**: Process large datasets in chunks if needed
3. **Index optimization**: Set appropriate indexes for merge operations

### Data Quality
1. **Missing data handling**: Decide on strategy (NaN, interpolation, forward-fill)
2. **Outlier detection**: Flag unusual values for review
3. **Consistency checks**: Ensure related fields align logically

### Extensibility
1. **Modular design**: Each aggregation function can be modified independently
2. **Configuration**: Consider making time windows and thresholds configurable
3. **Additional metrics**: Easy to add new calculated fields as needed