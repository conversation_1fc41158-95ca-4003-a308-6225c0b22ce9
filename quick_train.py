#!/usr/bin/env python3
"""
Quick Training Script for Manufacturing PatchTST Models

A simplified script for quick model training with sensible defaults.
Perfect for testing and development.

Usage:
    python quick_train.py                          # Train all models with defaults
    python quick_train.py thickness_thickness_avg  # Train specific target
    python quick_train.py --help                   # Show help
"""

import sys
import logging
from pathlib import Path

# Add src to path
src_path = str(Path(__file__).parent / "src")
if src_path not in sys.path:
    sys.path.insert(0, src_path)

try:
    from src.forecasting.trainer import ManufacturingForecastTrainer
    from src.forecasting.config import load_config_from_file
except ImportError:
    # Fallback for different import structure
    import os
    os.chdir(str(Path(__file__).parent))
    from src.forecasting.trainer import ManufacturingForecastTrainer
    from src.forecasting.config import load_config_from_file

# Simple logging setup
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def quick_train(target_variable: str = None):
    """Quick training with sensible defaults."""
    try:
        # Load default configuration
        logger.info("Loading configuration...")
        forecast_config, training_config = load_config_from_file("config/forecasting_config.json")
        
        # If specific target requested, update config
        if target_variable:
            if target_variable not in forecast_config.target_variables:
                logger.error(f"Target '{target_variable}' not in config. Available: {forecast_config.target_variables}")
                return False
            forecast_config.target_variables = [target_variable]
            logger.info(f"Training single target: {target_variable}")
        else:
            logger.info(f"Training all targets: {forecast_config.target_variables}")
        
        # Create trainer with stability features enabled
        logger.info("Creating trainer with stability enhancements...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,
            use_transfer_learning=True
        )
        
        # Train models
        logger.info("Starting training...")
        trained_models = trainer.train_all_target_variables(data_path="test-data")
        
        # Report results
        if trained_models:
            logger.info(f"✅ Successfully trained {len(trained_models)} model(s)")
            for target, model in trained_models.items():
                logger.info(f"   {target}: {model.model_save_path}")
            
            # Check improvement validation
            improvement_results = trainer.validate_15_percent_improvement(trainer.performance_comparison)
            passed = all(improvement_results.values())
            logger.info(f"15% improvement validation: {'✅ PASSED' if passed else '❌ FAILED'}")
            
            return True
        else:
            logger.error("❌ No models were trained successfully")
            return False
            
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        return False

def main():
    if len(sys.argv) > 1:
        if sys.argv[1] in ["-h", "--help"]:
            print(__doc__)
            return
        target = sys.argv[1]
    else:
        target = None
    
    logger.info("🚀 Quick PatchTST Training")
    logger.info("=" * 40)
    
    success = quick_train(target)
    
    logger.info("=" * 40)
    if success:
        logger.info("🎉 Training completed successfully!")
    else:
        logger.error("❌ Training failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()