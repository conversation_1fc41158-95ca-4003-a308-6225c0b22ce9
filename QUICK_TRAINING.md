# 🚀 Quick Training Guide

## ✅ Working Training Scripts

You now have **working training scripts** for PatchTST forecasting models!

### 🎯 **Option 1: Simple Script (Recommended)**
```bash
# From project root directory
cd "/Users/<USER>/JH/JH PoC/test_1"

# Activate virtual environment
source venv/bin/activate

# Train all models
python train.py

# Train specific model
python train.py thickness_thickness_avg

# Show help and available targets
python train.py --help
```

### 🔧 **Option 2: Full-Featured Module Script**
```bash
# From project root directory
cd "/Users/<USER>/JH/JH PoC/test_1"
source venv/bin/activate

# Train all models
python -m scripts.train_models --all-targets

# Train specific model  
python -m scripts.train_models --target thickness_thickness_avg

# Advanced options
python -m scripts.train_models --all-targets --verbose
python -m scripts.train_models --target speed_Speed --disable-transfer-learning

# Show help
python -m scripts.train_models --help
```

## 🎯 **Available Target Variables**
- `thickness_thickness_avg` - Average thickness across 10 sensors
- `speed_Speed` - Production line speed
- `fm_stack_Total Sheet Rejected` - Forming machine rejection rate

## 🔮 **Phase 3.1 Features Enabled**
- ✅ **Stability Enhancements**: Gradient clipping, UnitNorm layers
- ✅ **Transfer Learning**: IBM Granite model integration  
- ✅ **Manufacturing Validation**: 15% improvement requirement
- ✅ **Comprehensive Logging**: Detailed training progress

## 📊 **What Happens During Training**
1. **Environment Check**: Validates data files and configuration
2. **Configuration Loading**: Loads from `config/forecasting_config.json`
3. **Model Creation**: Creates PatchTST with stability enhancements
4. **Transfer Learning**: Attempts to load IBM Granite pre-trained model
5. **Training**: Trains with gradient clipping and monitoring
6. **Validation**: Tests performance against baselines
7. **Saving**: Saves models to `models/` directory

## 📁 **Output Files**
```
models/
├── patchtst_manufacturing_thickness_thickness_avg/
│   ├── config.json           # Model configuration
│   ├── model.safetensors     # Trained weights
│   └── metadata.json         # Training metadata
├── training_report.json      # Training summary
└── enhanced_training_report.json  # Detailed metrics
```

## 🏆 **Training Success Indicators**
- ✅ **Models Saved**: Check `models/` directory for saved models
- ✅ **Stability Validation**: "STABLE" status for training
- ✅ **15% Improvement**: Models beat baseline by 15%
- ✅ **No Critical Errors**: Clean training logs

## 🔍 **Troubleshooting**

### 1. **Import Errors**
```bash
# Make sure you're in project root
cd "/Users/<USER>/JH/JH PoC/test_1"

# Activate virtual environment
source venv/bin/activate

# Check environment
python -c "import src.forecasting.config; print('Imports OK')"
```

### 2. **Data Not Found**
```bash
# Check data files exist
ls test-data/
# Should show: thickness.csv, speed.csv, fm_stack.csv, sm_stack.csv, stop.csv
```

### 3. **Configuration Issues**
```bash
# Check config file
cat config/forecasting_config.json
# Should show valid JSON with forecasting_config and training_config sections
```

### 4. **Memory Issues**
If you get memory errors, edit `config/forecasting_config.json`:
```json
{
  "training_config": {
    "batch_size": 8,     // Reduce from 16 to 8
    "max_epochs": 20     // Reduce from 50 to 20
  }
}
```

## 📈 **After Training**

### Use Models with CLI
```bash
# Start interactive CLI with trained models
python -m src.cli interactive

# Ask for forecasts
> Create a 1-hour forecast for thickness
> Generate multi-horizon predictions for all variables
```

### Check Training Results
```bash
# View training logs
tail -20 training.log

# Check model files
ls models/

# View training report
cat models/enhanced_training_report.json | python -m json.tool
```

## 🎯 **Quick Test**
```bash
# Quick test training (should take 5-15 minutes)
python train.py thickness_thickness_avg

# Check if it worked
ls models/patchtst_manufacturing_thickness_thickness_avg/
```

If you see model files in the output, training was successful! 🎉