#!/usr/bin/env python3
"""
Manufacturing PatchTST Model Training Script

This script provides a comprehensive interface for training PatchTST forecasting models
with stability enhancements, transfer learning, and manufacturing-specific validation.

Features:
- Stability-enhanced training with gradient clipping and UnitNorm layers
- Transfer learning with IBM Granite models  
- Manufacturing compliance validation (15% improvement requirement)
- Comprehensive model evaluation and baseline comparisons
- Production-ready model saving and deployment preparation

Usage:
    python train_forecasting_models.py --help
    python train_forecasting_models.py --target thickness_thickness_avg
    python train_forecasting_models.py --all-targets --enable-transfer-learning
    python train_forecasting_models.py --config custom_config.json
"""

import argparse
import logging
import sys
import json
from pathlib import Path
from typing import List, Optional
import os

# Add src to path for imports
src_path = str(Path(__file__).parent / "src")
if src_path not in sys.path:
    sys.path.insert(0, src_path)

try:
    from src.forecasting.trainer import ManufacturingForecastTrainer
    from src.forecasting.config import ForecastConfig, PatchTSTTrainingConfig, load_config_from_file
    from src.data.loader import ManufacturingDataLoader
except ImportError:
    # Fallback for different import structure
    import os
    os.chdir(str(Path(__file__).parent))
    from src.forecasting.trainer import ManufacturingForecastTrainer
    from src.forecasting.config import ForecastConfig, PatchTSTTrainingConfig, load_config_from_file
    from src.data.loader import ManufacturingDataLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('training.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Set up the training environment and validate requirements."""
    logger.info("Setting up training environment...")
    
    # Check if virtual environment is activated
    if sys.prefix == sys.base_prefix:
        logger.warning("Virtual environment not detected. Run 'source venv/bin/activate' first.")
    
    # Validate data directory
    data_dir = Path("test-data")
    if not data_dir.exists():
        logger.error(f"Data directory {data_dir} not found!")
        return False
    
    required_files = ["thickness.csv", "speed.csv", "fm_stack.csv", "sm_stack.csv", "stop.csv"]
    missing_files = [f for f in required_files if not (data_dir / f).exists()]
    if missing_files:
        logger.error(f"Missing required data files: {missing_files}")
        return False
    
    # Create models directory if it doesn't exist
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    logger.info("✅ Environment setup complete")
    return True

def load_training_config(config_path: Optional[str] = None) -> tuple[ForecastConfig, PatchTSTTrainingConfig]:
    """Load forecasting and training configuration."""
    if config_path:
        config_file = Path(config_path)
        if not config_file.exists():
            logger.error(f"Config file {config_path} not found!")
            sys.exit(1)
        logger.info(f"Loading custom config from {config_path}")
        return load_config_from_file(str(config_file))
    else:
        # Use default config
        default_config = Path("config/forecasting_config.json")
        if not default_config.exists():
            logger.error(f"Default config {default_config} not found!")
            sys.exit(1)
        logger.info(f"Loading default config from {default_config}")
        return load_config_from_file(str(default_config))

def train_single_target(
    target_variable: str,
    forecast_config: ForecastConfig,
    training_config: PatchTSTTrainingConfig,
    use_stability_features: bool = True,
    use_transfer_learning: bool = True,
    data_path: str = "test-data"
):
    """Train a model for a single target variable."""
    logger.info(f"🎯 Training model for target variable: {target_variable}")
    
    # Update config to focus on single target
    forecast_config.target_variables = [target_variable]
    
    # Create trainer
    trainer = ManufacturingForecastTrainer(
        forecast_config=forecast_config,
        training_config=training_config,
        use_stability_features=use_stability_features,
        use_transfer_learning=use_transfer_learning
    )
    
    try:
        # Train the model
        trained_models = trainer.train_all_target_variables(data_path=data_path)
        
        if target_variable in trained_models:
            model = trained_models[target_variable]
            logger.info(f"✅ Successfully trained model for {target_variable}")
            logger.info(f"   Model saved to: {model.model_save_path}")
            
            # Display performance summary
            if target_variable in trainer.performance_comparison:
                perf = trainer.performance_comparison[target_variable]
                logger.info(f"   Validation metrics: {perf.get('validation_results', 'N/A')}")
                
                # Check improvement validation
                improvement_validation = trainer.validate_15_percent_improvement(trainer.performance_comparison)
                improvement_passed = improvement_validation.get(target_variable, False)
                logger.info(f"   15% improvement requirement: {'✅ PASSED' if improvement_passed else '❌ FAILED'}")
            
            return model
        else:
            logger.error(f"❌ Failed to train model for {target_variable}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Training failed for {target_variable}: {str(e)}")
        return None

def train_all_targets(
    forecast_config: ForecastConfig,
    training_config: PatchTSTTrainingConfig,
    use_stability_features: bool = True,
    use_transfer_learning: bool = True,
    data_path: str = "test-data"
):
    """Train models for all configured target variables."""
    logger.info(f"🎯 Training models for all target variables: {forecast_config.target_variables}")
    
    # Create trainer
    trainer = ManufacturingForecastTrainer(
        forecast_config=forecast_config,
        training_config=training_config,
        use_stability_features=use_stability_features,
        use_transfer_learning=use_transfer_learning
    )
    
    try:
        # Train all models
        trained_models = trainer.train_all_target_variables(data_path=data_path)
        
        logger.info(f"✅ Training completed for {len(trained_models)} models")
        
        # Display comprehensive summary
        improvement_validation = trainer.validate_15_percent_improvement(trainer.performance_comparison)
        
        for target_variable, model in trained_models.items():
            logger.info(f"📊 {target_variable}:")
            logger.info(f"   Model path: {model.model_save_path}")
            
            if target_variable in trainer.performance_comparison:
                perf = trainer.performance_comparison[target_variable]
                
                # Validation results
                val_results = perf.get('validation_results', {})
                if 'horizon_results' in val_results:
                    for horizon, metrics in val_results['horizon_results'].items():
                        mse = metrics.get('mse', 'N/A')
                        mae = metrics.get('mae', 'N/A')
                        logger.info(f"   {horizon}: MSE={mse}, MAE={mae}")
                
                # Improvement validation
                improvement_passed = improvement_validation.get(target_variable, False)
                logger.info(f"   15% improvement: {'✅ PASSED' if improvement_passed else '❌ FAILED'}")
                
                # Stability validation
                stability_results = perf.get('stability_validation', {})
                is_stable = stability_results.get('is_stable', False)
                stability_score = stability_results.get('stability_score', 0.0)
                logger.info(f"   Stability: {'✅ STABLE' if is_stable else '❌ UNSTABLE'} (score: {stability_score:.3f})")
        
        # Overall assessment
        overall_improvement = all(improvement_validation.values())
        stable_models = sum(1 for target in trained_models.keys() 
                          if trainer.performance_comparison.get(target, {}).get('stability_validation', {}).get('is_stable', False))
        overall_stable = stable_models == len(trained_models)
        
        logger.info("🏆 OVERALL ASSESSMENT:")
        logger.info(f"   Models trained: {len(trained_models)}/{len(forecast_config.target_variables)}")
        logger.info(f"   Stability: {stable_models}/{len(trained_models)} models stable")
        logger.info(f"   Improvement: {'✅ ALL PASSED' if overall_improvement else '❌ SOME FAILED'}")
        logger.info(f"   Production ready: {'✅ YES' if (overall_stable and overall_improvement) else '❌ NO'}")
        
        return trained_models
        
    except Exception as e:
        logger.error(f"❌ Training failed: {str(e)}")
        return {}

def main():
    """Main training script entry point."""
    parser = argparse.ArgumentParser(
        description="Train PatchTST forecasting models for manufacturing data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python train_forecasting_models.py --target thickness_thickness_avg
  python train_forecasting_models.py --all-targets --enable-transfer-learning
  python train_forecasting_models.py --config custom_config.json --disable-stability
  python train_forecasting_models.py --all-targets --data-path my-data/
        """
    )
    
    # Target selection
    target_group = parser.add_mutually_exclusive_group(required=True)
    target_group.add_argument(
        "--target", 
        type=str,
        help="Train model for specific target variable"
    )
    target_group.add_argument(
        "--all-targets",
        action="store_true",
        help="Train models for all configured target variables"
    )
    
    # Configuration options
    parser.add_argument(
        "--config",
        type=str,
        help="Path to custom configuration file (default: config/forecasting_config.json)"
    )
    parser.add_argument(
        "--data-path",
        type=str,
        default="test-data",
        help="Path to manufacturing data directory (default: test-data)"
    )
    
    # Feature toggles
    parser.add_argument(
        "--disable-stability",
        action="store_true",
        help="Disable stability enhancements (gradient clipping, UnitNorm layers)"
    )
    parser.add_argument(
        "--disable-transfer-learning",
        action="store_true",
        help="Disable transfer learning with IBM Granite models"
    )
    parser.add_argument(
        "--enable-transfer-learning",
        action="store_true",
        help="Enable transfer learning (default based on config)"
    )
    
    # Logging options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress most logging output"
    )
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    elif args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    logger.info("🚀 Starting PatchTST Manufacturing Model Training")
    logger.info("=" * 60)
    
    # Setup environment
    if not setup_environment():
        logger.error("❌ Environment setup failed")
        sys.exit(1)
    
    # Load configuration
    try:
        forecast_config, training_config = load_training_config(args.config)
        logger.info(f"📋 Configuration loaded:")
        logger.info(f"   Target variables: {forecast_config.target_variables}")
        logger.info(f"   Forecast horizons: {forecast_config.forecast_horizons}")
        logger.info(f"   Model save path: {training_config.model_save_path}")
    except Exception as e:
        logger.error(f"❌ Failed to load configuration: {str(e)}")
        sys.exit(1)
    
    # Determine feature settings
    use_stability_features = not args.disable_stability
    use_transfer_learning = not args.disable_transfer_learning
    if args.enable_transfer_learning:
        use_transfer_learning = True
    
    logger.info(f"🔧 Training features:")
    logger.info(f"   Stability enhancements: {'✅ ENABLED' if use_stability_features else '❌ DISABLED'}")
    logger.info(f"   Transfer learning: {'✅ ENABLED' if use_transfer_learning else '❌ DISABLED'}")
    logger.info(f"   Data path: {args.data_path}")
    
    # Validate target variable if specified
    if args.target:
        if args.target not in forecast_config.target_variables:
            logger.error(f"❌ Target variable '{args.target}' not in configured targets: {forecast_config.target_variables}")
            sys.exit(1)
    
    # Validate data path
    data_path = Path(args.data_path)
    if not data_path.exists():
        logger.error(f"❌ Data path {data_path} does not exist")
        sys.exit(1)
    
    logger.info("=" * 60)
    
    # Start training
    if args.target:
        # Train single target
        model = train_single_target(
            target_variable=args.target,
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=use_stability_features,
            use_transfer_learning=use_transfer_learning,
            data_path=str(data_path)
        )
        
        if model:
            logger.info("🎉 Single target training completed successfully!")
            logger.info(f"   Model available at: {model.model_save_path}")
        else:
            logger.error("❌ Single target training failed!")
            sys.exit(1)
            
    else:
        # Train all targets
        models = train_all_targets(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=use_stability_features,
            use_transfer_learning=use_transfer_learning,
            data_path=str(data_path)
        )
        
        if models:
            logger.info(f"🎉 All targets training completed! Trained {len(models)} models.")
            logger.info("   Models available in:")
            for target, model in models.items():
                logger.info(f"     {target}: {model.model_save_path}")
        else:
            logger.error("❌ All targets training failed!")
            sys.exit(1)
    
    logger.info("=" * 60)
    logger.info("✅ Training script completed successfully!")

if __name__ == "__main__":
    main()