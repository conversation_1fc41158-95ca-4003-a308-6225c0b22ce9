#!/usr/bin/env python3
"""
Simple Training Script for PatchTST Models

Usage from project root:
    python train.py                          # Train all targets
    python train.py fm_reject_pct             # Train specific target
    python train.py --config improved_config.json  # Use custom config
    python train.py --help                   # Show help
"""

import sys
import logging
import os
from pathlib import Path

# Ensure we're in the right directory and can import modules
project_root = Path(__file__).parent
os.chdir(project_root)

# Import using module structure
try:
    from src.forecasting.trainer import ManufacturingForecastTrainer
    from src.forecasting.config import load_config_from_file
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root directory.")
    print("Also ensure virtual environment is activated: source venv/bin/activate")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if environment is set up correctly."""
    # Check data directory
    if not Path("test-data").exists():
        logger.error("test-data directory not found!")
        return False
    
    # Check required data files
    required_files = ["thickness.csv", "speed.csv", "fm_stack.csv", "sm_stack.csv", "stop.csv"]
    missing = [f for f in required_files if not Path(f"test-data/{f}").exists()]
    if missing:
        logger.error(f"Missing data files: {missing}")
        return False
    
    # Check config file
    if not Path("config/forecasting_config.json").exists():
        logger.error("Config file config/forecasting_config.json not found!")
        return False
    
    return True

def check_for_existing_unified_table():
    """Check if unified table already exists and prompt user."""
    unified_table_path = Path("test-data/consolidated/test_matched_stacks.csv")
    if unified_table_path.exists():
        # Read header comments to get export info
        try:
            with open(unified_table_path, 'r') as f:
                lines = []
                for i, line in enumerate(f):
                    if i < 10 and line.startswith('#'):
                        lines.append(line.strip())
                    else:
                        break
            
            # Extract export date and record count
            export_date = None
            total_records = None
            for line in lines:
                if "Export Date:" in line:
                    export_date = line.split("Export Date: ")[1]
                elif "Total Records:" in line:
                    total_records = line.split("Total Records: ")[1].replace(',', '')
            
            # Display unified table info
            print(f"🔍 Unified table found!")
            if export_date:
                print(f"   Export date: {export_date}")
            if total_records:
                print(f"   Total records: {total_records}")
            
            # Ask user if they want to use existing unified table
            use_existing = input("Use existing unified table? [Y/n]: ").strip().lower()
            return use_existing in ['', 'y', 'yes']
            
        except Exception as e:
            logger.warning(f"Error reading unified table metadata: {e}")
            return False
    
    return False

def train_models(target_variable=None, config_file="config/forecasting_config.json"):
    """Train models with default settings."""
    try:
        # Load configuration
        logger.info(f"Loading configuration from {config_file}...")
        forecast_config, training_config = load_config_from_file(config_file)
        
        # Set target if specified
        if target_variable:
            if target_variable not in forecast_config.target_variables:
                logger.error(f"Target '{target_variable}' not in available targets: {forecast_config.target_variables}")
                return False
            forecast_config.target_variables = [target_variable]
            logger.info(f"Training target: {target_variable}")
        else:
            logger.info(f"Training all targets: {forecast_config.target_variables}")
        
        # Check for existing unified table
        use_existing_table = check_for_existing_unified_table()
        data_path = "test-data"
        
        # Create trainer with Phase 3.1 features
        logger.info("Creating enhanced trainer with stability features...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,  # Enable stability enhancements
            use_transfer_learning=True    # Enable transfer learning
        )
        
        # Start training with three-tier fallback strategy
        trained_models = None
        
        # Tier 1: Try existing unified table if available
        if use_existing_table:
            try:
                logger.info("🚀 Starting model training with existing unified table...")
                trained_models = trainer.train_all_target_variables_with_unified_table(
                    unified_table_path="test-data/consolidated/test_matched_stacks.csv"
                )
                logger.info("✅ Successfully used existing unified table")
            except Exception as e:
                logger.warning(f"⚠️ Failed to use existing unified table: {e}")
                logger.info("📝 Falling back to creating unified table from raw sources...")
        
        # Tier 2: Create unified table from raw sources if Tier 1 failed or wasn't used
        if trained_models is None:
            try:
                logger.info("🚀 Starting model training from raw data sources...")
                trained_models = trainer.train_all_target_variables(data_path=data_path)
                logger.info("✅ Successfully created and used unified table from raw sources")
            except Exception as e:
                logger.warning(f"⚠️ Failed to create unified table: {e}")
                logger.info("📝 Falling back to individual data source loading...")
        
        # Tier 3: Emergency fallback to manual data loading
        if trained_models is None:
            try:
                logger.info("🚀 Emergency fallback: Manual data loading...")
                # Try basic training with simplified data loading
                from src.data.loader import ManufacturingDataLoader
                import pandas as pd
                
                fallback_loader = ManufacturingDataLoader()
                fallback_loader.data_dir = Path(data_path)
                
                # Try to load at least basic data sources
                basic_data = {}
                for file in ["thickness.csv", "speed.csv", "stop.csv"]:
                    try:
                        file_path = Path(data_path) / file
                        if file_path.exists():
                            basic_data[file] = pd.read_csv(file_path, low_memory=False)
                            logger.info(f"✓ Loaded {file}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not load {file}: {e}")
                
                if basic_data:
                    logger.info(f"📊 Loaded {len(basic_data)} data sources for emergency training")
                    # Here you could implement a simplified training with available data
                    # For now, we'll raise an error as this would require significant code changes
                    raise RuntimeError("Emergency fallback not fully implemented - requires basic training mode")
                else:
                    raise RuntimeError("No data sources available for training")
                    
            except Exception as e:
                logger.error(f"❌ All training strategies failed: {e}")
                return False
        
        if trained_models:
            logger.info(f"✅ Successfully trained {len(trained_models)} model(s)!")
            
            # Show results for each model
            for target, model in trained_models.items():
                logger.info(f"📊 Model: {target}")
                # Get save path from training config or model metadata
                save_path = getattr(model.training_config, 'model_save_path', './models/')
                model_dir = f"{save_path}/patchtst_manufacturing_{target}"
                logger.info(f"   Saved to: {model_dir}")
                
                # Check if we have performance data
                if target in trainer.performance_comparison:
                    perf = trainer.performance_comparison[target]
                    
                    # Stability validation
                    stability = perf.get('stability_validation', {})
                    if stability:
                        is_stable = stability.get('is_stable', False)
                        score = stability.get('stability_score', 0.0)
                        logger.info(f"   Stability: {'✅ STABLE' if is_stable else '❌ UNSTABLE'} (score: {score:.3f})")
            
            # Check improvement validation
            improvement_results = trainer.validate_15_percent_improvement(trainer.performance_comparison)
            all_passed = all(improvement_results.values()) if improvement_results else True
            
            logger.info("🏆 Training Summary:")
            logger.info(f"   Models trained: {len(trained_models)}")
            logger.info(f"   15% improvement: {'✅ PASSED' if all_passed else '❌ FAILED'}")
            
            if improvement_results:
                for target, passed in improvement_results.items():
                    status = "✅" if passed else "❌"
                    logger.info(f"     {target}: {status}")
            
            return True
        else:
            logger.error("❌ No models were trained successfully")
            return False
            
    except Exception as e:
        logger.error(f"❌ Training failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main entry point."""
    # Parse simple arguments
    target = None
    config_file = "config/forecasting_config.json"  # Default config
    
    i = 1
    while i < len(sys.argv):
        arg = sys.argv[i]
        if arg in ["-h", "--help", "help"]:
            print(__doc__)
            print("\nAvailable target variables:")
            try:
                from src.forecasting.config import load_config_from_file
                config, _ = load_config_from_file(config_file)
                for target_var in config.target_variables:
                    print(f"  - {target_var}")
            except:
                print("  (run from project directory to see targets)")
            return
        elif arg == "--config":
            if i + 1 < len(sys.argv):
                config_file = sys.argv[i + 1]
                i += 2
            else:
                print("❌ --config requires a config file path")
                return
        else:
            # Assume it's a target variable
            target = arg
            i += 1
    
    print("🔮 PatchTST Manufacturing Model Training")
    print("=" * 50)
    
    # Check environment
    logger.info("Checking environment...")
    if not check_environment():
        logger.error("❌ Environment check failed")
        print("\nTroubleshooting:")
        print("1. Make sure you're in the project root directory")
        print("2. Activate virtual environment: source venv/bin/activate")
        print("3. Check that test-data/ directory exists with CSV files")
        sys.exit(1)
    
    logger.info("✅ Environment check passed")
    
    # Start training with custom config
    success = train_models(target, config_file)
    
    print("=" * 50)
    if success:
        print("🎉 Training completed successfully!")
        print("\nNext steps:")
        print("- Models are saved in models/ directory")
        print("- Use with CLI: python -m src.cli interactive")
        print("- Check training.log for detailed logs")
    else:
        print("❌ Training failed!")
        print("\nCheck training.log for details")
        sys.exit(1)

if __name__ == "__main__":
    main()