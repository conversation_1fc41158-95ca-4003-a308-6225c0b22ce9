#!/usr/bin/env python3
"""
Use Trained Manufacturing Models for Forecasting

Quick script to load and use already trained models without retraining.
"""

import pandas as pd
from pathlib import Path
from src.forecasting.patchtst_model import ManufacturingPatchTSTModel
import argparse

def load_and_predict(model_name: str, data_file: str = None, horizon: int = 15):
    """
    Load trained model and generate predictions
    
    Args:
        model_name: Name of the target variable (Speed, fm_reject_pct, etc.)
        data_file: Optional path to data file, defaults to unified table
        horizon: Forecast horizon in minutes
    """
    print(f"🔮 Loading trained model for: {model_name}")
    
    # Model path
    model_path = f"./models/patchtst_manufacturing_{model_name}"
    
    if not Path(model_path).exists():
        print(f"❌ Model not found at {model_path}")
        print("Available models:")
        models_dir = Path("./models")
        if models_dir.exists():
            for model_dir in models_dir.glob("patchtst_manufacturing_*"):
                target = model_dir.name.replace("patchtst_manufacturing_", "")
                print(f"  - {target}")
        return None
    
    # Load model
    print("📥 Loading model...")
    model = ManufacturingPatchTSTModel.load_pretrained(model_path)
    print("✅ Model loaded successfully")
    
    # Load data
    if data_file:
        print(f"📊 Loading data from {data_file}")
        data = pd.read_csv(data_file, low_memory=False)
    else:
        print("📊 Loading unified table...")
        data = pd.read_csv("test-data/consolidated/test_matched_stacks.csv", 
                          comment='#', low_memory=False)
    
    print(f"✅ Data loaded: {len(data)} records")
    
    # Use recent data for prediction
    recent_data = data.tail(500)  # Use last 500 records for context
    print(f"🎯 Using {len(recent_data)} recent records for forecasting")
    
    # Generate forecast
    print(f"🔮 Generating {horizon}-minute forecast for {model_name}...")
    try:
        result = model.forecast(recent_data, model_name, horizon, include_confidence_intervals=True)
        
        print("✅ Forecast generated successfully!")
        print(f"📈 Predictions for next {horizon} minutes:")
        
        for i, (timestamp, value) in enumerate(zip(result.forecast_timestamps, result.forecast_values)):
            print(f"  {timestamp}: {value:.4f}")
            if i >= 4:  # Show first 5 predictions
                print(f"  ... and {len(result.forecast_values) - 5} more")
                break
        
        # Show statistics
        import numpy as np
        predictions = result.forecast_values
        print(f"\n📊 Forecast Statistics:")
        print(f"  Average: {np.mean(predictions):.4f}")
        print(f"  Min: {np.min(predictions):.4f}")
        print(f"  Max: {np.max(predictions):.4f}")
        print(f"  Std Dev: {np.std(predictions):.4f}")
        
        # Show confidence intervals if available
        if result.confidence_intervals and result.confidence_intervals.get('lower'):
            lower = result.confidence_intervals['lower']
            upper = result.confidence_intervals['upper']
            print(f"\n🎯 Confidence Intervals (95%):")
            for i in range(min(3, len(predictions))):
                print(f"  {result.forecast_timestamps[i]}: {lower[i]:.4f} - {upper[i]:.4f}")
        
        return result
        
    except Exception as e:
        print(f"❌ Forecast failed: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Use trained manufacturing forecasting models")
    parser.add_argument("model", nargs="?", help="Model name (e.g., fm_reject_pct, Speed, sm_scrap_pct)")
    parser.add_argument("--data", help="Path to data file (default: unified table)")
    parser.add_argument("--horizon", type=int, default=15, help="Forecast horizon in minutes (default: 15)")
    parser.add_argument("--list", action="store_true", help="List available trained models")
    
    args = parser.parse_args()
    
    if args.list:
        print("🔮 Available Trained Models:")
        models_dir = Path("./models")
        if models_dir.exists():
            for model_dir in models_dir.glob("patchtst_manufacturing_*"):
                target = model_dir.name.replace("patchtst_manufacturing_", "")
                print(f"  - {target}")
        else:
            print("❌ No models directory found")
        return
    
    if not args.model:
        print("❌ Please specify a model name or use --list to see available models")
        return
    
    load_and_predict(args.model, args.data, args.horizon)

if __name__ == "__main__":
    main()