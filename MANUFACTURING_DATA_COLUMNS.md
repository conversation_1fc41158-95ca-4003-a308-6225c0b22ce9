# Manufacturing Data Columns Documentation

## Overview

This document provides comprehensive documentation for the unified manufacturing dataset created by the Manufacturing Data Loader. The system processes **5 distinct data sources** into a single **51-column unified table** optimized for fiber cement manufacturing correlation analysis and quality improvement initiatives.

## Document Status: 📊 **COMPLETE UNIFIED TABLE SPECIFICATION**

### Key Statistics
- **Total Columns**: 51 comprehensive manufacturing features
- **Data Sources**: 5 manufacturing systems (VM Capacity, FM Stack, SM Stack, Speed, Stop)
- **Record Volume**: 175K+ speed measurements with integrated quality and stoppage data
- **Temporal Range**: Multi-format datetime support (YYYY.MM.DD, YYYY-MM-DD)
- **Manufacturing Context**: Complete work center tracking with production sequences

---

## Data Source Mapping

### Source Files and Record Counts

| Data Source | File Path | Records | Primary Purpose | Key Integration |
|-------------|-----------|---------|-----------------|-----------------|
| **VM Capacity** | `test-data/data-cyrus/VM Capacity Report.csv` | 560 | Product specifications and design parameters | SAP code matching with production orders |
| **FM Stack** | `test-data/data-cyrus/fm_stack.csv` | 15,055 | Forming machine batch data and quality metrics | Production order correlation with timing |
| **SM Stack** | `test-data/data-cyrus/sm_stack.csv` | 14,380 | Sheet machine production and scrap tracking | First sheet timestamp alignment |
| **Speed** | `test-data/speed.csv` | 175,289 | **Base dataset** - continuous production monitoring | Primary timeline for unified table |
| **Stop** | `test-data/stop.csv` | 770 | Machine stoppage events and duration analysis | Temporal correlation with production gaps |

### Raw Data Column Inventory

#### Speed Data (Base Dataset) - 13 Raw Columns
```
Plant, Work Center/Resource, Log Date, Log Time, Speed, Rev Speed, 
Update Date, Update Time, Update Name, Update Data Time, 
MPS Update Date Time, MPS Log Date Time, MPS Log Date Time.1
```

#### Stop Events Data - 34 Raw Columns
```
Stop ID, Plant, Work Center/Resource, Restart Time, Restart Date, 
Stop Time, Stop Date, MPS Stop Duration, Stoppage Reason, 
MPS Category, MPS Type, Felt Code, Work Order, Production Order, 
Line, Section Code, Section, POS, Work Group, Stoppage Reason LTXT, 
Update Date, Update Time, Update Name, Work Center, Employee, 
Count, MPS Restart Date Time, MPS Stop Date Time, Restart Timestamp, 
Stop Timestamp, Stop Duration, Stoppage Category, Stoppage Type, 
Section Description
```

#### Sheet Machine Stack Data - 9 Raw Columns
```
Stack Number, First Sheet Date Time, Last Sheet Date Time,
Sheet Cut, Good Sheets, Scrap%, Reject Reason, 
Production Order, Product
```

#### Forming Machine Stack Data - 11 Raw Columns
```
MPS ID, Branded, On-Load, Off-Load, Production Order,
TM Index, Potential Sheets, Products, Ok, Rej., Product Description
```

#### VM Capacity Report Data - 9 Raw Columns
```
Vitual Material Description, SAP Code, Sheet Machine, 
Finishing Machine, OffRoller Factor, Design Capacity, 
Design Felt Speed, Design Rev Speed, Active
```

---

## Unified Table Column Specification (51 Columns)

### 🔍 Core Identity & Temporal Features (3 columns)

| Column | Data Type | Description | Source | Processing |
|--------|-----------|-------------|--------|------------|
| `timestamp` | datetime64[ns] | **Primary key** - Unified manufacturing timeline | Speed data | Combined Log Date + Log Time with multi-format support |
| `work_center` | object | Production line identifier (SM270, SM300, etc.) | Speed data | Direct mapping from Work Center/Resource |
| `Speed` | float64 | Production line speed (meters/minute) | Speed data | Validated range 0-300 m/min, converted to numeric |

---

### 🎯 Thickness Sensor Array (10 columns)

| Column | Data Type | Description | Source | Range |
|--------|-----------|-------------|--------|-------|
| `sensor_01` | float64 | Thickness measurement from sensor 1 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_02` | float64 | Thickness measurement from sensor 2 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_03` | float64 | Thickness measurement from sensor 3 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_04` | float64 | Thickness measurement from sensor 4 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_05` | float64 | Thickness measurement from sensor 5 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_06` | float64 | Thickness measurement from sensor 6 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_07` | float64 | Thickness measurement from sensor 7 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_08` | float64 | Thickness measurement from sensor 8 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_09` | float64 | Thickness measurement from sensor 9 (mm) | Simulated from speed | ±0.2mm variation |
| `sensor_10` | float64 | Thickness measurement from sensor 10 (mm) | Simulated from speed | ±0.2mm variation |

**Manufacturing Context**: 10-sensor array provides cross-width thickness measurement for quality assessment and uniformity analysis.

---

### 📊 Calculated Thickness Quality Metrics (5 columns)

| Column | Data Type | Description | Calculation | Manufacturing Significance |
|--------|-----------|-------------|-------------|---------------------------|
| `thickness_avg` | float64 | **Primary quality metric** - Average thickness across all sensors | mean(sensor_01 to sensor_10) | Product specification compliance |
| `thickness_range` | float64 | Thickness variation across product width | max(sensors) - min(sensors) | Uniformity indicator |
| `thickness_std` | float64 | Standard deviation of thickness measurements | std(sensor_01 to sensor_10) | Process stability metric |
| `wedge_index` | float64 | Left-right thickness difference (manufacturing defect indicator) | mean(sensors 1-3) - mean(sensors 8-10) | Cross-directional quality |
| `crown_bow_index` | float64 | Center-edge thickness difference (calendering effect) | mean(sensors 4-7) - mean(sensors 1,2,9,10) | Profile control assessment |

**Quality Standards**: These metrics enable identification of manufacturing defects, process drift, and specification compliance.

---

### ⚡ Speed Analytics & Performance (2 columns)

| Column | Data Type | Description | Calculation | Time Window |
|--------|-----------|-------------|-------------|-------------|
| `speed_avg_5min` | float64 | Rolling average production speed | 5-point rolling mean | Last 5 measurements |
| `speed_change_rate` | float64 | Speed change velocity (m/min per measurement) | current_speed - previous_speed | Point-to-point |

**Process Control**: Speed analytics provide early indicators of production instability and process control effectiveness.

---

### ⏱️ Stoppage & Restart Analytics (4 columns)

| Column | Data Type | Description | Calculation | Manufacturing Context |
|--------|-----------|-------------|-------------|----------------------|
| `time_since_last_stop` | float64 | Minutes elapsed since last production restart | timestamp - last_restart_time | Production continuity metric |
| `is_restart_period` | bool | Flag indicating 10-minute post-restart stabilization period | time_since_last_stop <= 10 | Process stability window |
| `stop_duration_previous` | float64 | Duration of most recent stoppage (minutes) | From stop events data | Downtime impact analysis |
| `stops_last_hour` | int64 | Count of stoppages in past 60 minutes | Rolling count of stop events | Operational reliability |

**Operational Excellence**: Stoppage analytics enable root cause analysis and production efficiency optimization.

---

### 📋 Sheet Machine Integration (5 columns)

| Column | Data Type | Description | Source Column | Integration Method |
|--------|-----------|-------------|---------------|-------------------|
| `sm_stack_number` | object | Sheet machine stack identifier | Stack Number | Temporal merge on first sheet time |
| `sm_production_order` | object | Production order reference | Production Order | Order-based correlation |
| `sm_product` | object | Product description/specification | Product | Material traceability |
| `sm_good_sheets` | float64 | Count of acceptable sheets produced | Good Sheets | Quality outcome metric |
| `sm_scrap_pct` | float64 | Scrap percentage for production run | Scrap% | Quality performance indicator |

**Quality Tracking**: Sheet machine data provides final product quality outcomes correlated with process parameters.

---

### 🏭 Forming Machine Integration (6 columns)

| Column | Data Type | Description | Source Column | Integration Method |
|--------|-----------|-------------|---------------|-------------------|
| `fm_mps_id` | object | Manufacturing Production System identifier | MPS ID | Unique batch tracking |
| `fm_production_order` | object | Production order reference | Production Order | Order-based correlation |
| `fm_product_description` | object | Detailed product specification | Product Description | Material specification |
| `fm_ok_sheets` | float64 | Count of acceptable sheets | Ok | Quality metric |
| `fm_reject_sheets` | float64 | Count of rejected sheets | Rej. | Quality loss indicator |
| `fm_position_pct` | float64 | Position within batch processing sequence | Calculated from On-Load/Off-Load | Process stage tracking |

**Process Tracking**: Forming machine data enables batch-level quality correlation and process stage analysis.

---

### 🎯 Product Specification Integration (6 columns)

| Column | Data Type | Description | Source Column | Integration Key |
|--------|-----------|-------------|---------------|-----------------|
| `product_code` | object | Extracted standardized product code | Derived from production orders | Pattern extraction |
| `SAP Code` | object | SAP system material reference | SAP Code | Direct mapping |
| `Design Capacity` | float64 | Planned production capacity (sheets/hour) | Design Capacity | Design parameter |
| `Design Felt Speed` | float64 | Optimal felt speed (m/min) | Design Felt Speed | Process target |
| `Sheet Machine` | object | Assigned sheet machine identifier | Sheet Machine | Equipment assignment |
| `Finishing Machine` | object | Assigned finishing machine identifier | Finishing Machine | Equipment routing |

**Design Integration**: Product specifications enable comparison of actual vs. design performance and capacity utilization analysis.

---

### 🕒 Temporal & Manufacturing Schedule Features (6 columns)

| Column | Data Type | Description | Calculation | Manufacturing Context |
|--------|-----------|-------------|-------------|----------------------|
| `hour_of_day` | int32 | Hour of day (0-23) | Extracted from timestamp | Shift pattern analysis |
| `minute_of_hour` | int32 | Minute within hour (0-59) | Extracted from timestamp | Sub-hourly patterns |
| `day_of_week` | int32 | Day of week (0=Monday, 6=Sunday) | Extracted from timestamp | Weekly production cycles |
| `shift` | object | Shift classification (night/morning/afternoon) | Time-based logic | Shift performance comparison |
| `hour_sin` | float64 | Cyclical hour encoding (sine component) | sin(2π × hour/24) | Machine learning features |
| `hour_cos` | float64 | Cyclical hour encoding (cosine component) | cos(2π × hour/24) | Temporal pattern recognition |

**Schedule Intelligence**: Temporal features enable shift-based analysis, cyclical pattern detection, and time-aware correlation analysis.

---

### 📈 Production Sequence & Context Features (4 columns)

| Column | Data Type | Description | Calculation | Purpose |
|--------|-----------|-------------|-------------|---------|
| `sheet_sequence` | int64 | Sequential sheet number per work center | Incremental counter by work center | Production tracking |
| `time_since_start` | float64 | Minutes elapsed since production start | timestamp - first_timestamp_per_center | Campaign duration |
| `thickness_deviation` | float64 | Deviation from rolling thickness average | thickness_avg - rolling_mean(thickness_avg) | Process drift detection |
| `is_within_spec` | bool | Specification compliance flag | thickness_avg within ±tolerance | Quality gate indicator |

**Process Intelligence**: Sequence features enable production campaign analysis, process drift detection, and quality trend monitoring.

---

## Data Type Summary

| Data Type | Count | Columns | Purpose |
|-----------|-------|---------|---------|
| **float64** | 31 | Measurements, calculations, analytics | Numeric analysis and correlation |
| **object** | 11 | Identifiers, descriptions, codes | Categorical analysis and grouping |
| **int64** | 3 | Counts, sequences | Integer-based metrics |
| **int32** | 3 | Temporal components | Time-based features |
| **bool** | 2 | Flags, indicators | Binary classification |
| **datetime64[ns]** | 1 | Primary timestamp | Temporal indexing |

---

## Manufacturing Intelligence Features

### 🎯 Critical Correlation Variables

1. **Primary Quality Metric**: `thickness_avg` - Product quality outcome
2. **Primary Process Parameter**: `Speed` - Production rate control
3. **Primary Efficiency Metric**: `time_since_last_stop` - Production continuity
4. **Primary Quality Outcome**: `sm_scrap_pct` - Final quality result

### 🔄 Multi-Level Analysis Support

#### **Sensor-Level Analysis**
- Individual sensor readings (sensor_01 through sensor_10)
- Cross-directional quality assessment
- Manufacturing defect detection

#### **Process-Level Analysis**  
- Speed analytics and process stability
- Stoppage impact and recovery patterns
- Work center performance comparison

#### **Product-Level Analysis**
- Specification compliance tracking
- Material type performance comparison
- Design vs. actual parameter analysis

#### **Temporal Analysis**
- Shift-based performance patterns
- Production campaign effectiveness
- Cyclical variation detection

### 🏭 Manufacturing Domain Intelligence

#### **Quality Control Integration**
- Real-time thickness monitoring across product width
- Automatic specification compliance validation
- Quality trend analysis and early warning indicators

#### **Process Optimization Features**
- Speed-quality correlation analysis
- Stoppage impact quantification
- Recovery time analysis and optimization

#### **Production Planning Support**
- Work center capacity utilization
- Material changeover efficiency
- Schedule optimization analytics

#### **Root Cause Analysis Capabilities**
- Multi-dimensional correlation analysis
- Process parameter interaction effects
- Quality outcome prediction modeling

---

## Technical Implementation Notes

### 🔧 Data Processing Enhancements

#### **Warning-Free DateTime Processing**
- Automatic multi-format detection (YYYY.MM.DD, YYYY-MM-DD)
- Explicit format specification to eliminate pandas warnings
- Robust handling of mixed date formats across data sources

#### **Manufacturing Domain Validation**
- Speed range validation (0-300 m/min)
- Thickness sensor array consistency checks
- Stoppage duration validation (0-10,000 minutes)
- Production order format standardization

#### **Performance Optimization**
- Efficient temporal alignment using pd.merge_asof
- Vectorized thickness calculations
- Memory-optimized processing for 175K+ records
- Incremental feature engineering

### 🛡️ Production Reliability Features

#### **Error Handling**
- Graceful degradation for missing data sources
- Robust numeric conversion with error tolerance
- Comprehensive data validation checkpoints
- Automatic data quality reporting

#### **Integration Compatibility**
- CLI tool integration for interactive analysis
- Multi-method correlation analysis support
- Visualization tool compatibility
- Export capabilities for external analysis

---

## Usage Examples

### 🔍 Column Selection for Analysis

```python
# Quality-focused analysis
quality_columns = [
    'thickness_avg', 'thickness_range', 'thickness_std',
    'wedge_index', 'crown_bow_index', 'sm_scrap_pct'
]

# Process parameter analysis
process_columns = [
    'Speed', 'speed_avg_5min', 'speed_change_rate',
    'time_since_last_stop', 'stop_duration_previous'
]

# Production context analysis
context_columns = [
    'work_center', 'shift', 'sheet_sequence',
    'sm_production_order', 'fm_production_order'
]

# Sensor array analysis
sensor_columns = [f'sensor_{i:02d}' for i in range(1, 11)]
```

### 📊 Manufacturing Analytics Applications

```python
# Quality correlation analysis
quality_speed_correlation = unified_df[['thickness_avg', 'Speed']].corr()

# Stoppage impact analysis
restart_quality = unified_df[unified_df['is_restart_period']]['thickness_avg'].mean()

# Shift performance comparison
shift_performance = unified_df.groupby('shift')['sm_scrap_pct'].mean()

# Work center efficiency analysis
center_efficiency = unified_df.groupby('work_center')['time_since_last_stop'].mean()
```

---

## Conclusion

This unified 51-column manufacturing dataset represents a comprehensive integration of fiber cement production data, providing the foundation for advanced correlation analysis, quality improvement initiatives, and production optimization. The system transforms disparate manufacturing data sources into a cohesive analytical framework optimized for industrial intelligence and decision support.

### Key Capabilities
- **Complete Manufacturing Context**: Production tracking from raw materials to final quality outcomes
- **Multi-Sensor Quality Assessment**: Comprehensive thickness monitoring and defect detection
- **Process Performance Analytics**: Speed, stoppage, and efficiency correlation analysis
- **Temporal Intelligence**: Shift patterns, production sequences, and cyclical analysis
- **Specification Compliance**: Automatic quality gate validation and trend monitoring

The dataset structure supports both operational excellence initiatives and advanced analytics applications, enabling data-driven manufacturing optimization in fiber cement production operations.