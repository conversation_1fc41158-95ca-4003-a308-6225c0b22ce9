# Fiber Cement Manufacturing Correlation Analysis

An AI-powered **production-ready multi-method correlation analysis system** for identifying relationships between manufacturing variables and scrap rates in fiber cement production. This tool helps engineers understand how machine stoppages, speed variations, and thickness deviations impact product quality using **<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlation methods** with intelligent method selection and convergence analysis.

## 🚀 **Phase 4.0: 100% COMPLETE - Enhanced Unified Table Analysis with Stratified Intelligence + CLI Import Fixes**

**✅ Enhanced Unified Table Analysis: 83-column manufacturing table support with 5 new specialized analysis types**  
**✅ Stratified Analysis Engine: Multi-dimensional analysis by time lags, shifts, efficiency ranges, and machine types**  
**✅ Pattern Identification System: Quality threshold analysis, operational risk detection, and statistical anomaly identification**  
**✅ ML Quality Prediction: RandomForest-based quality prediction with feature importance and optimization insights**  
**✅ Time Series Analysis: Hourly aggregation, rolling correlations, and temporal pattern discovery**  
**✅ CLI Import Resolution: Fixed critical module import issues for seamless correlation agent functionality**

## 🚀 **Phase 3.1: 100% COMPLETE - Advanced PatchTST Forecasting with Stability Enhancements + Production Fixes**

**✅ Successfully implemented production-ready PatchTST forecasting system with comprehensive stability validation**  
**✅ Stability-enhanced training with gradient clipping, UnitNorm layers, and StabilizedTrainer**  
**✅ Transfer learning integration with IBM Granite models and adaptive fine-tuning**  
**✅ Comprehensive testing suite with manufacturing-specific validation and compliance checks**  
**✅ Full integration with existing correlation analysis, scrap rate analysis, and visualization capabilities**  
**✅ Production-Ready Data Loading: Eliminated all pandas warnings and FutureWarnings for clean enterprise deployment**

The **enhanced unified table analysis agent with stratified intelligence, pattern identification, ML prediction, and time series analysis** is production-ready with complete test coverage, real API integration, and **CLI import resolution**. The system now supports **5 new specialized analysis types**, **83-column unified table processing**, **multi-dimensional stratification**, **quality pattern detection**, **RandomForest prediction**, and **temporal correlation analysis** for comprehensive manufacturing intelligence and process optimization.

## Overview

This project implements an intelligent agent that uses Anthropic Claude or Google Vertex AI to analyze correlations in manufacturing data using **multiple correlation methods** and **advanced time series forecasting**. The agent can:

**Enhanced Unified Table Analysis (Phase 4.0):**
- **83-Column Unified Table Processing** with comprehensive manufacturing variable analysis
- **Stratified Analysis Engine** for multi-dimensional correlation discovery by time lags, production shifts, efficiency ranges, and machine types
- **Pattern Identification System** with quality threshold analysis, operational risk detection, and statistical anomaly identification
- **ML Quality Prediction** using RandomForest with feature importance analysis and process optimization insights
- **Time Series Analysis** with hourly aggregation, rolling correlations, and temporal pattern discovery
- **Intelligent Analysis Type Detection** with automatic tool selection based on natural language queries

**Statistical Analysis & Correlation Discovery:**
- **Load and validate** industrial sensor data from CSV files with mixed time formats
- **Calculate multi-method correlations** using Pearson, Spearman, and Kendall methods with statistical significance
- **Intelligent method selection** based on data distribution characteristics (normality, outliers, linearity)
- **Method convergence analysis** to assess robustness and stability across correlation approaches
- **Side-by-side visualization** comparing all three correlation methods with heatmaps and scatter plots
- **Analyze lag relationships** to understand process delays and cause-effect timing

**Scrap Rate Analysis & Quality Intelligence:**
- **Calculate scrap rates** with off roller factor adjustment using formula: ((Sheets Cut / Off Roller Factor) - Good Sheets) / (Sheets Cut / Off Roller Factor)
- **Material-specific scrap rate analysis** with lookup from off_roller_factor.csv reference data
- **Manufacturing Order mapping** for complex data relationships (SM → FM → Material → Off Roller Factor)
- **Quality assessment thresholds** with manufacturing best practices (10% high, 20% critical scrap rates)
- **Comprehensive scrap rate metrics** including statistical analysis and material-specific insights
- **Correlation analysis integration** for scrap rate relationships with process variables

**Advanced Time Series Forecasting:**
- **PatchTST-based forecasting** using state-of-the-art transformer architecture for manufacturing parameters
- **Multi-horizon predictions** (15 minutes, 1 hour, 4 hours, 24 hours) for production planning
- **Natural language forecasting** with queries like "create a 1-hour forecast for thickness"
- **Manufacturing domain expertise** with process-specific insights and recommendations
- **Uncertainty quantification** with confidence intervals and prediction reliability
- **Automatic model training** and intelligent fallback for robust forecasting

**Process Intelligence & Optimization:**
- **Filter by time periods** to identify patterns in specific production windows
- **Provide natural language insights** about manufacturing relationships and optimal correlation methods
- **Generate comprehensive visualizations** including multi-method heatmaps, convergence plots, and interactive dashboards
- **Predictive maintenance** insights through forecasting of critical manufacturing parameters

## Features

### ✅ **Current Implementation (Phase 4.0 - COMPLETE)**

#### **Enhanced Unified Table Analysis System (Phase 4.0)**
- **🏭 83-Column Unified Manufacturing Table**: Complete variable analysis across all manufacturing processes
- **🔬 5 New Specialized Analysis Types**: Unified table, stratified, pattern identification, ML prediction, time series
- **📊 Stratified Analysis Engine**: Multi-dimensional analysis by time lag bins, production shifts, efficiency ranges, machine types
- **🎯 Pattern Identification System**: Quality threshold analysis, operational risk detection, statistical anomaly identification
- **🤖 ML Quality Prediction**: RandomForest-based quality prediction with feature importance and optimization insights
- **⏰ Time Series Analysis**: Hourly aggregation, rolling correlations, temporal pattern discovery with manufacturing context
- **🧠 Intelligent Auto-Detection**: Automatic analysis type selection based on natural language query patterns
- **🔧 CLI Import Resolution**: Fixed critical module import issues for seamless correlation agent functionality

#### **Multi-Method Correlation Analysis System**
- **🔬 Three Correlation Methods**: Pearson, Spearman, and Kendall with statistical significance testing
- **🧠 Intelligent Method Selection**: Automatic data distribution assessment and method recommendation
- **📊 Method Convergence Analysis**: Robustness assessment across different correlation approaches
- **📈 Side-by-Side Visualization**: Comparative heatmaps and scatter plots for all three methods
- **🎯 Bootstrap Robustness**: Stability analysis with confidence intervals and reliability metrics
- **📏 Data Distribution Assessment**: Normality tests, outlier detection, linearity analysis for optimal method selection

#### **Advanced PatchTST Forecasting System (Phase 3.1)**
- **🔮 Stability-Enhanced Transformer Architecture**: PatchTST with gradient clipping and UnitNorm layers
- **⏰ Multi-Horizon Predictions**: 15-minute, 1-hour, 4-hour, and 24-hour forecasting capabilities
- **🗣️ Natural Language Interface**: "Create a 1-hour forecast for thickness" - direct CLI integration
- **🏭 Manufacturing Domain Intelligence**: Process-specific insights and optimization recommendations
- **📈 Uncertainty Quantification**: Confidence intervals and prediction reliability metrics
- **🚀 Transfer Learning**: IBM Granite model integration with adaptive fine-tuning strategies
- **✅ StabilizedTrainer**: Comprehensive gradient monitoring and training stability validation
- **🛡️ Manufacturing Compliance**: Automatic validation of 15% improvement requirement over baselines
- **⚡ Production-Ready Performance**: Enterprise-scale forecasting with robust error handling
- **📊 Multi-Variable Forecasting**: Simultaneous prediction of thickness, speed, scrap rates, and quality metrics

#### **Core AI Agent System**
- **🤖 Enhanced Unified Table Analysis Agent**: Production-ready PydanticAI agent with 5 new specialized analysis types
- **🔧 30+ Specialized Tools**: Multi-method analysis, stratified analysis, pattern identification, ML prediction, time series analysis, **5 visualization tools**, **4 forecasting tools**, **scrap rate metrics**
- **🏭 83-Column Manufacturing Intelligence**: Complete analysis across unified manufacturing table with 122K+ records
- **📊 Stratified Multi-variable Analysis**: Correlations by time lags, production shifts, efficiency ranges, machine types
- **🎯 Pattern Recognition**: Quality threshold analysis, operational risk detection, statistical anomaly identification
- **🤖 ML Prediction Engine**: RandomForest quality prediction with feature importance and optimization insights
- **⏰ Temporal Analysis**: Hourly aggregation, rolling correlations, time series pattern discovery
- **🧠 Auto-Detection System**: Intelligent analysis type selection from natural language queries
- **🎯 High-Precision Display**: 8-decimal internal precision, 6-decimal display, scientific notation for small p-values
- **📏 Thickness Integration**: Automatic calculation from 10-sensor readings with uniformity metrics
- **🏭 Scrap Rate Intelligence**: Comprehensive scrap rate calculation with off roller factor adjustment and material-specific analysis
- **⏱️ Time-lagged Analysis**: Discovers optimal lag times for cause-effect relationships
- **📈 Statistical Rigor**: P-values, confidence intervals, significance testing for all methods
- **🎨 Integrated Visualization**: Agent automatically generates visualizations as part of correlation analysis
- **🔮 Forecasting Integration**: Seamless transition from correlation discovery to predictive modeling

#### **Data Processing Pipeline**
- **📁 Advanced CSV Loader**: Handles 6 different data sources including off_roller_factor.csv
- **🔄 Time Alignment**: Unifies mixed timestamp formats across manufacturing datasets
- **✅ Warning-Free Processing**: Eliminated all pandas datetime warnings and FutureWarnings for production deployment
- **📏 Thickness Processing**: Automatic averaging of 10 sensor readings (Sensor 01-10) with uniformity calculations
- **🏭 Scrap Rate Processing**: Automatic calculation with off roller factor lookup and material mapping
- **✅ Data Validation**: Quality scoring, outlier detection, completeness assessment
- **🏭 Manufacturing-Specific**: Domain knowledge for fiber cement processes

#### **User Interfaces**
- **💻 Interactive CLI**: Rich console interface with natural language queries
- **⚡ Batch Processing**: Single-command analysis for automation
- **📋 Command System**: `/help`, `/load`, `/analyze`, `/export` commands
- **🎨 Rich Output**: High-precision correlation tables, progress bars, color-coded significance levels
- **🔍 Enhanced Display**: Scientific notation for small p-values, 6-decimal correlation precision

#### **Complete Visualization Suite (Phase 2.0)**
- **🔥 Multi-Method Heatmaps**: Side-by-side correlation matrices for Pearson, Spearman, and Kendall
- **📊 Method Comparison Plots**: Scatter plots comparing correlation methods with convergence scoring
- **📈 Convergence Analysis**: Interactive dashboards showing method agreement and stability
- **🎯 Robustness Visualizations**: Bootstrap stability plots and confidence regions
- **🕸️ Network Graphs**: Interactive correlation networks with method-specific filtering
- **📈 Time Series**: Multi-variable plots with event markers and method comparisons
- **📋 Comprehensive Dashboards**: Multi-method analysis with method selection guidance
- **🤖 Agent-Integrated Visualizations**: 5 visualization tools fully integrated into correlation agent
- **📁 Automatic Generation**: Agent creates visualizations automatically during analysis
- **🎨 Professional Output**: PNG and HTML formats with manufacturing-specific styling

### 📋 **Validated Capabilities - Production Ready**

**Real Data Performance:**
- ✅ **262,028 unified records** processed across 5 manufacturing datasets
- ✅ **99.5% timestamp success rate** with automatic time alignment
- ✅ **Multi-method correlations discovered** with >0.99 convergence scores
- ✅ **Sub-second response times** for all three correlation methods
- ✅ **Enterprise-scale processing** with intelligent method selection

**Multi-Method Statistical Analysis:**
- ✅ **All three correlation methods** (Pearson, Spearman, Kendall) with comparative analysis
- ✅ **Method convergence analysis** with >0.99 overall convergence scores
- ✅ **Intelligent method selection** based on data characteristics (normality, outliers, linearity)
- ✅ **Bootstrap robustness analysis** with >0.99 stability scores
- ✅ **High-precision correlations** (8-decimal internal, 6-decimal display)
- ✅ **Scientific notation** for small p-values (< 0.001)
- ✅ **Automatic thickness metrics** from 10-sensor arrays with uniformity calculations
- ✅ **Lag correlation analysis** up to 60 time periods for all methods
- ✅ **Comprehensive significance testing** with p-values and confidence intervals for all methods
- ✅ **Data distribution assessment** for optimal method recommendation

**Testing & Quality Assurance:**
- ✅ **73 comprehensive tests** with real API integration (no mocking)
- ✅ **73 tests passing** (100% success rate)
- ✅ **Multi-method validation** with actual manufacturing data
- ✅ **Method convergence testing** with bootstrap robustness analysis
- ✅ **Multi-provider testing** (Anthropic Claude & Vertex AI)
- ✅ **Error handling validation** for data anomalies and API failures
- ✅ **Visualization integration testing** with agent tool validation
- ✅ **Complete visualization suite testing** with 5 integrated tools

## 🔧 **Phase 2.1 Critical Fixes & Enhancements + Latest Production Fixes**

### **Latest Production Fixes (Data Loading)**
- **✅ Pandas DateTime Warnings Eliminated**: Complete removal of UserWarnings about datetime format inference
- **✅ FutureWarning Resolution**: Fixed dtype compatibility issues in stoppage features processing
- **✅ Enhanced DateTime Parser**: Added `parse_single_datetime()` function with explicit format specifications
- **✅ Clean Enterprise Deployment**: Zero warnings or deprecation notices during data processing
- **✅ Robust Type Conversion**: Proper numeric conversion for all manufacturing data types

### **Previous Critical Fixes (Phase 2.1)**
- **✅ Matplotlib GUI Threading**: Resolved macOS NSWindow thread errors with proper backend configuration
- **✅ Data Structure Validation**: Fixed warnings for mixed object/dictionary handling in correlation results
- **✅ P-value Display Formatting**: Enhanced professional statistical output with proper scientific notation
- **✅ String vs Float Comparisons**: Separated formatting concerns from calculation logic
- **✅ Visualization Pipeline**: Added None/NoneType checks to prevent mathematical operation errors
- **✅ Error Handling**: Comprehensive error messages and fallback values for edge cases

### **Enhanced Statistical Output**
- **Professional P-value Formatting**: `< 1e-15` instead of `0.00e+00` for extremely small values
- **Robust Data Processing**: Handles both MultiMethodCorrelationResult objects and dictionaries
- **Safe Visualization Generation**: Prevents crashes from missing or invalid correlation data
- **Improved Error Messages**: Detailed debugging information with type and structure details

### **Production Reliability**
- **262,028+ Records Processed**: Validated with real manufacturing data at enterprise scale
- **Zero Critical Warnings**: Clean execution without data structure or threading errors
- **Professional Output**: Suitable for manufacturing reports and process optimization decisions
- **Cross-Platform Compatibility**: Works reliably on macOS, Linux, and Windows environments

### 🔮 **Planned Features (Phase 4)**
- Advanced causal inference with PCMCI
- Multi-agent system (Research + Diagnostic agents)
- Transfer learning for cross-facility forecasting
- Real-time monitoring and alerting capabilities
- Automated root cause analysis with causal discovery

## Project Structure

```
test_1/
├── src/                           # Production source code
│   ├── agents/                    # AI agents and tools
│   │   ├── correlation_agent.py   # Main PydanticAI correlation agent
│   │   ├── tools.py               # Original 8 specialized analysis tools
│   │   ├── multi_tools.py         # Multi-method correlation agent tools (4 tools)
│   │   ├── visualization_tools.py # Visualization agent tools (5 tools)
│   │   ├── forecasting_tools.py   # PatchTST forecasting agent tools (4 tools)
│   │   └── prompts.py             # Manufacturing domain prompts with multi-method and forecasting guidance
│   ├── data/                      # Data processing pipeline
│   │   ├── loader.py              # Advanced CSV loader with thickness processing
│   │   ├── correlations.py        # High-precision statistical correlation engine
│   │   └── multi_correlations.py  # Multi-method correlation analyzer
│   ├── forecasting/               # Advanced time series forecasting (Phase 3.1)
│   │   ├── patchtst_model.py      # PatchTST model training and inference
│   │   ├── preprocessing.py       # Time series preprocessing for manufacturing data
│   │   ├── trainer.py             # Enhanced training pipeline with stability validation
│   │   ├── config.py              # Forecasting configuration management
│   │   ├── stability/             # NEW: Stability validation framework
│   │   │   ├── __init__.py        # Stability validator exports
│   │   │   ├── validator.py       # ManufacturingStabilityValidator
│   │   │   ├── gradient_clipping.py # Gradient clipping utilities
│   │   │   └── normalization.py   # UnitNorm and RobustScaler
│   │   └── transfer_learning/     # NEW: Transfer learning capabilities
│   │       ├── __init__.py        # Transfer learning exports
│   │       ├── granite_loader.py  # IBM Granite model integration
│   │       └── fine_tuning.py     # Adaptive fine-tuning strategies
│   ├── visualization/             # Plotting and dashboard suite
│   │   ├── plots.py               # Original comprehensive visualization tools
│   │   └── multi_plots.py         # Multi-method visualization suite
│   └── cli.py                     # Interactive CLI with high-precision display and forecasting
├── tests/                         # Comprehensive unit tests (100+ tests)
│   ├── test_data_loader.py        # Data loading validation tests (22 tests)
│   ├── test_correlations.py       # Statistical analysis tests (32 tests)
│   ├── test_agent.py              # Agent functionality tests (17 tests)
│   ├── test_multi_correlations.py # Multi-method analysis tests (24 tests)
│   ├── test_multi_tools.py        # Multi-method tool tests (22 tests)
│   ├── test_multi_plots.py        # Multi-method visualization tests (27 tests)
│   ├── test_forecasting_model.py  # PatchTST model tests
│   ├── test_forecasting_tools.py  # Forecasting agent tool tests
│   ├── test_forecasting_agent.py  # Complete forecasting workflow tests
│   ├── test_stability/            # NEW: Stability testing suite (Phase 3.1)
│   │   ├── test_gradient_clipping.py # Gradient clipping validation
│   │   ├── test_normalization.py  # UnitNorm and RobustScaler tests
│   │   ├── test_training_stability.py # End-to-end training stability
│   │   └── test_manufacturing_metrics.py # Manufacturing compliance tests
│   └── test_transfer_learning/    # NEW: Transfer learning testing suite
│       ├── test_pretrained_loading.py # IBM Granite model loading
│       └── test_fine_tuning.py    # Fine-tuning strategies validation
├── test-data/                     # Legacy manufacturing data (336K+ records)
│   ├── stop.csv                   # Stoppage events (47K records)
│   ├── speed.csv                  # Production speed (78K records)
│   ├── thickness.csv              # Quality measurements (78K records)
│   ├── fm_stack.csv               # Forming machine scrap (60K records)
│   ├── sm_stack.csv               # Sheet machine scrap (71K records)
│   └── off_roller_factor.csv      # Off roller correction factors (1.3K records)
├── test_data_3_to_6_2025/         # Current manufacturing data (501K+ records)
│   ├── stop.csv                   # Stoppage events (771 records)
│   ├── speed.csv                  # Production speed (175K records)
│   ├── thickness.csv              # Quality measurements (298K records)
│   ├── fm_stack.csv               # Forming machine scrap (12K records)
│   ├── sm_stack.csv               # Sheet machine scrap (14K records)
│   └── off_roller_factor.csv      # Off roller correction factors (721 records)
├── config/                        # Configuration files
│   └── forecasting_config.json   # NEW: PatchTST model configuration
├── models/                        # NEW: Trained model storage
│   ├── patchtst_manufacturing_thickness/
│   └── patchtst_manufacturing_speed/
├── examples/                      # Reference implementations
├── docs/                          # Technical documentation
├── requirements.txt               # Production dependencies
├── .env.example                   # Configuration template
└── README.md                      # This file
```

## Installation

### Prerequisites
- Python 3.8+ (tested with Python 3.13)
- Virtual environment (already configured)
- API key for Anthropic Claude or Google Vertex AI

### Quick Start

1. **Clone and navigate:**
```bash
git clone <repository-url>
cd test_1
```

2. **Activate virtual environment:**
```bash
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Configure API access:**
```bash
cp .env.example .env
# Edit .env with your API keys (see Configuration section)
```

5. **Verify installation:**
```bash
python -c "from src.data.loader import ManufacturingDataLoader; print('✅ Installation successful!')"
```

6. **Test with current dataset:**
```bash
python -c "
from src.data.loader import ManufacturingDataLoader
loader = ManufacturingDataLoader()  # Uses test_data_3_to_6_2025 by default
data = loader.load_all_manufacturing_data()
print(f'✅ Loaded {len(data)} datasets with {sum(len(df) for df in data.values())} total records')
"
```

## Configuration

### API Keys & Model Configuration

Choose your preferred LLM provider and specify the model:

#### **Option 1: Anthropic Claude (Recommended)**
```env
LLM_PROVIDER=ANTHROPIC
ANTHROPIC_API_KEY=your_claude_api_key_here
ANTHROPIC_MODEL=claude-sonnet-4-20250514
```

#### **Option 2: Google Vertex AI**
```env
LLM_PROVIDER=VERTEX_AI
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json
VERTEX_AI_PROJECT=your-project-id
VERTEX_AI_LOCATION=us-central1
GEMINI_MODEL=gemini-2.5-pro
```

### 🎯 **High-Precision Correlation Display**

The system now provides **enhanced precision** for detecting small but industrially significant correlations:

**Display Improvements:**
- **6-Decimal Precision**: Correlations displayed as 0.047451 (vs. previous 0.000)
- **Scientific Notation**: P-values < 0.001 shown as 7.90e-04 for clarity
- **Robust Significance**: Automatic fallback calculation ensures no "unknown" significance
- **Lowered Thresholds**: Default minimum correlation reduced from 0.3 to 0.01

**Benefits for Manufacturing:**
- **Small Correlations Visible**: Can now detect weak relationships (0.01-0.1) that may be process-relevant
- **Statistical Precision**: Very small p-values clearly distinguished from zero
- **Process Sensitivity**: Identifies subtle relationships between variables like thickness uniformity and scrap rates
- **Industrial Relevance**: Small correlations often indicate early warning signals in manufacturing

**Before vs. After:**
```
BEFORE: Correlation: 0.000, P-value: 0.0000, Significance: unknown
AFTER:  Correlation: 0.047451, P-value: 7.90e-04, Significance: highly_significant
```

### ⚠️ **Important: All Environment Variables Required**

**The system now requires strict configuration** - all environment variables must be set:

- **For Anthropic**: Both `ANTHROPIC_API_KEY` and `ANTHROPIC_MODEL` are required
- **For Vertex AI**: `VERTEX_AI_PROJECT`, `GEMINI_MODEL`, and `GOOGLE_APPLICATION_CREDENTIALS` are required

**If any required variable is missing, the system will fail with a clear error message instead of using fallback values.**

### Analysis Settings
```env
DEFAULT_SIGNIFICANCE_THRESHOLD=0.05
DEFAULT_MIN_CORRELATION=0.01    # Lowered to capture small correlations
DEFAULT_CORRELATION_METHOD=pearson
DEFAULT_TIME_WINDOW=60
```

## Usage

### 🎯 **Quick Start Examples**

#### **1. Interactive Analysis Session**
```bash
python -m src.cli interactive
```
This opens the rich CLI interface where you can:
- Type `/help` for commands
- Type `/load` to load manufacturing data
- Ask questions like: *"What correlations exist between speed and thickness?"*

#### **2. Single Query Analysis**
```bash
python -m src.cli analyze --query "Find correlations between stoppages and scrap rates" --output results.json
```

#### **3. Load and Explore Data**
```bash
python -c "
from src.data.loader import ManufacturingDataLoader
loader = ManufacturingDataLoader('test-data')
data = loader.load_all_manufacturing_data()
print(f'Loaded {len(data)} datasets with {sum(len(df) for df in data.values())} total records')
"
```

### 💬 **Example Queries**

The AI agent understands natural language questions about multi-method manufacturing correlations:

**Multi-Method Analysis:**
- *"Compare Pearson, Spearman, and Kendall correlations for all manufacturing variables"*
- *"Which correlation method should I use for speed vs thickness analysis?"*
- *"Show me side-by-side correlation heatmaps for all three methods"*
- *"Calculate multi-method correlations and recommend the best method for each variable pair"*
- *"Analyze method convergence patterns in this dataset"*

**Process Optimization with Method Selection:**
- *"What is the correlation between machine stoppages and scrap rates using the optimal method?"*
- *"How does production speed affect thickness variation? Recommend the best correlation method."*
- *"Analyze thickness uniformity vs. scrap rates with method convergence analysis"*
- *"Calculate scrap rates with off roller factor adjustment and find correlations with process variables"*
- *"Show me material-specific scrap rate patterns and their correlation with production parameters"*
- *"Show me robust correlations that are consistent across all three methods"*
- *"Find small but significant correlations above 0.01 using the most reliable method"*

**Time-based Analysis:**
- *"Analyze correlations for the last 24 hours"*
- *"What patterns emerge after machine restarts?"*
- *"Compare correlations between day and night shifts"*

**Statistical Deep-dive with Multi-Method:**
- *"Find significant correlations above 0.01 with p-value < 0.05 for all three methods"*
- *"Show me correlations with 6-decimal precision and method convergence scores"*
- *"Calculate robustness metrics using bootstrap sampling for all methods"*
- *"Assess data distribution and recommend optimal correlation methods"*
- *"Generate method comparison matrices with convergence analysis"*

**Multi-Method Visualization Requests:**
- *"Create side-by-side correlation heatmaps for Pearson, Spearman, and Kendall"*
- *"Plot method convergence analysis with interactive dashboard"*
- *"Generate comprehensive multi-method correlation dashboard"*
- *"Show method comparison scatter plots with convergence scoring"*
- *"Create robustness visualization with bootstrap confidence intervals"*

### 📊 **CLI Commands Reference**

| Command | Description | Example |
|---------|-------------|---------|
| `/help` | Show detailed help and commands | `/help` |
| `/load` | Load manufacturing data files | `/load` |
| `/data` | Show loaded data summary | `/data` |
| `/settings` | View/modify analysis settings | `/settings` |
| `/analyze [type]` | Run correlation analysis | `/analyze lag` |
| `/export [file]` | Export results to JSON | `/export results.json` |
| `/clear` | Clear conversation history | `/clear` |
| `/quit` | Exit the CLI | `/quit` |

**Analysis Types:**
- `general` - Comprehensive single-method correlation discovery
- `multi_method` - Multi-method correlation analysis with Pearson, Spearman, Kendall
- `method_convergence` - Method convergence and robustness analysis
- `method_selection` - Data-driven correlation method recommendation
- `visualization` - **NEW:** Complete visualization suite with multi-method dashboards
- `lag` - Time-lagged correlation analysis
- `quality` - Quality-focused correlation analysis
- `optimization` - Process optimization analysis
- `rca` - Root cause analysis

## Data Format

### 📁 **Real Manufacturing Data Included**

The system includes **837,397+ records** of real fiber cement manufacturing data across two datasets:

#### **Current Dataset (test_data_3_to_6_2025) - 501,714 records**
| Dataset | Records | Description | Key Variables | Unified Coverage |
|---------|---------|-------------|---------------|--------------------|
| **thickness.csv** | 298,532 | Quality measurements | 10 sensor readings (Sensor 01-10), auto-calculated averages | 298,742 (100%) |
| **speed.csv** | 175,290 | Production speed | Speed values, Timestamps | 175,402 (100%) |
| **sm_stack.csv** | 14,432 | Sheet machine scrap | Material tracking, Quality, **scrap_rate** | 14,431 (100%) |
| **fm_stack.csv** | 11,968 | Forming machine scrap | Sheet quantities, Accept/Reject, **fm_scrap_rate** | 11,985 (100%) |
| **stop.csv** | 771 | Machine stoppages | Duration, Reason, Work Center | 771 (100%) |
| **off_roller_factor.csv** | 721 | Off roller factors | Material, Off Roller Factor, Work Center | N/A |

**📊 Unified Dataset**: 495,154 rows (98.8% source data preservation)

#### **Legacy Dataset (test-data) - 336,083 records**
| Dataset | Records | Description | Key Variables |
|---------|---------|-------------|---------------|
| **sm_stack.csv** | 70,829 | Sheet machine scrap | Material tracking, Quality, **scrap_rate** |
| **speed.csv** | 78,448 | Production speed | Speed values, Timestamps |
| **thickness.csv** | 78,002 | Quality measurements | 10 sensor readings (Sensor 01-10), auto-calculated averages |
| **fm_stack.csv** | 60,009 | Forming machine scrap | Sheet quantities, Accept/Reject, **fm_scrap_rate** |
| **stop.csv** | 47,386 | Machine stoppages | Duration, Reason, Work Center |
| **off_roller_factor.csv** | 1,281 | Off roller factors | Material, Off Roller Factor, Work Center |

### ⚠️ **Critical Time Alignment (Handled Automatically)**

The system automatically handles inconsistent time column formats and date formats across datasets:

#### **Date Format Support**
- **Legacy Format**: `YYYY-MM-DD` (e.g., `2025-01-13`) - **test-data** directory
- **Current Format**: `YYYY.MM.DD` (e.g., `2025.03.02`) - **test_data_3_to_6_2025** directory
- **Automatic Detection**: The system detects and handles both formats seamlessly

#### **Column Alignment**
- **fm_stack.csv**: `'Finish Start Date'` + `'Finish Start ime'` *(note typo - missing 'T')*
- **sm_stack.csv**: `'First Sheet Date'` + `'First Sheet Time'`
- **speed.csv**: `'Log Date'` + `'Log Time'`
- **thickness.csv**: `'Sensor Date'` + `'Sensor Time'`
- **stop.csv**: `'Stop Date'` + `'Stop Time'`

✅ **The data loader automatically creates unified timestamp columns with multi-format support for proper temporal correlation analysis.**

### 📏 **Automated Thickness Processing**

The system provides **intelligent thickness data processing** that automatically handles industrial sensor arrays:

**Thickness Sensor Integration:**
- **10 Sensor Array**: Processes thickness measurements from Sensor 01 through Sensor 10
- **Automatic Averaging**: Calculates `thickness_avg` as the mean of all sensor readings
- **Uniformity Metrics**: Computes `thickness_uniformity` (standard deviation across sensors)
- **Range Analysis**: Determines `thickness_range` (max - min across sensors)

**Manufacturing Intelligence:**
- **Domain Knowledge**: Understands that thickness = average of 10 sensors across product width
- **Quality Metrics**: Automatically calculates thickness variation and uniformity indicators
- **Correlation Ready**: All thickness metrics available for correlation analysis with scrap rates

**Example Thickness Variables Created:**
```
thickness_avg          # Average of Sensor 01-10 (primary thickness measure)
thickness_uniformity    # Standard deviation across sensors (quality indicator)
thickness_range         # Max-min variation across sensors (consistency measure)
Sensor 01-10           # Individual sensor readings (granular analysis)
```

### 🏭 **Automated Scrap Rate Processing with Off Roller Factor Integration**

The system provides **intelligent scrap rate calculation** that automatically handles off roller factor adjustments and material-specific analysis:

**Scrap Rate Calculation Formula:**
- **Corrected Formula**: `scrap_rate = ((Sheets Cut / Off Roller Factor) - Good Sheets) / (Sheets Cut / Off Roller Factor)`
- **Accounts for Production Efficiency**: Off roller factors adjust expected production for accurate scrap measurement
- **Material-Specific**: Different materials have different off roller factors affecting production expectations

**Manufacturing Intelligence:**
- **Material Lookup**: Automatically maps SM → Manufacturing Order → FM → Material → Off Roller Factor
- **Data Relationships**: Handles complex manufacturing data relationships across multiple systems
- **Quality Thresholds**: Applies manufacturing best practices (10% high scrap, 20% critical scrap rates)
- **Correlation Ready**: All scrap rate metrics available for correlation analysis with process variables

**Example Scrap Rate Variables Created:**
```
fm_scrap_rate          # Forming machine scrap rate with off roller factor adjustment
scrap_rate            # Sheet machine scrap rate with off roller factor adjustment  
overall_scrap_rate    # Combined scrap rate across all sources
scrap_rate_variation  # Variation between different scrap rate sources
```

## Development

### 🧪 **Running Tests**
```bash
# IMPORTANT: Tests use real API keys - ensure .env is configured
# Run all tests (73 comprehensive tests)
pytest tests/ -v

# Run specific test modules
pytest tests/test_data_loader.py -v      # 22 data processing tests
pytest tests/test_correlations.py -v    # 32 statistical analysis tests  
pytest tests/test_agent.py -v           # 17 agent functionality tests (real API)

# NEW: Run multi-method correlation tests
pytest tests/test_multi_correlations.py -v  # 24 multi-method analysis tests
pytest tests/test_multi_tools.py -v         # 22 multi-method tool tests
pytest tests/test_multi_plots.py -v         # 27 multi-method visualization tests

# Run with coverage
pytest tests/ --cov=src/ --cov-report=html

# Quick validation (non-agent tests only)
pytest tests/test_data_loader.py tests/test_correlations.py tests/test_multi_correlations.py -v

# Multi-method specific testing
pytest tests/test_multi_* -v  # Run all multi-method tests (73 total tests)

# Visualization testing
pytest tests/test_multi_plots.py -v  # Test visualization tools integration
```

### 🔍 **Code Quality**
```bash
# Format code
ruff check src/ --fix

# Type checking
mypy src/

# Style validation
ruff format src/
```

### 🚀 **Performance Testing**
```bash
# Test with real data
python -c "
from src.data.loader import ManufacturingDataLoader
from src.data.correlations import ManufacturingCorrelationAnalyzer
import time

print('Loading 295K+ manufacturing records...')
start = time.time()
loader = ManufacturingDataLoader('test-data')
data = loader.load_all_manufacturing_data()
unified = loader.create_unified_dataset()
print(f'✅ Loaded in {time.time()-start:.2f}s: {unified.shape[0]:,} unified records')

print('Running correlation analysis...')
start = time.time()
analyzer = ManufacturingCorrelationAnalyzer()
correlations = analyzer.find_significant_correlations(unified, min_correlation=0.3)
print(f'✅ Found {len(correlations)} correlations in {time.time()-start:.2f}s')
"
```

### 📝 **Adding New Features**

1. **Check current tasks**: See `TASK.md` for development roadmap
2. **Follow guidelines**: Review `CLAUDE.md` for coding standards
3. **Write tests**: Create unit tests for new functionality
4. **Update docs**: Maintain documentation consistency

## Architecture

### 🏗️ **System Design**

```mermaid
graph TD
    A[CLI Interface] --> B[Correlation Agent]
    B --> C[Agent Tools]
    C --> D[Data Loader]
    C --> E[Correlation Engine]
    C --> F[Visualization Suite]
    D --> G[Manufacturing Data]
    E --> H[Statistical Analysis]
    F --> I[Plots & Dashboards]
```

### 🔧 **Key Components**

**Agent Layer:**
- `correlation_agent.py` - Main PydanticAI agent with manufacturing domain knowledge
- `tools.py` - 8 specialized tools including thickness metrics and high-precision analysis
- `multi_tools.py` - 4 multi-method correlation tools with method convergence analysis
- `visualization_tools.py` - 5 visualization tools integrated with correlation agent
- `prompts.py` - Manufacturing-specific system prompts with multi-method and visualization guidance

**Data Layer:**
- `loader.py` - Advanced CSV loader with automatic thickness processing from 10-sensor arrays
- `correlations.py` - High-precision statistical correlation engine (8-decimal internal precision)
- `multi_correlations.py` - Multi-method correlation analyzer with convergence analysis

**Interface Layer:**
- `cli.py` - Rich interactive CLI with 6-decimal correlation display and scientific notation
- `plots.py` - Comprehensive visualization suite
- `multi_plots.py` - Multi-method visualization suite with side-by-side comparisons

## Documentation

### 📚 **Available Documentation**

- **`INITIAL.md`** - Original project requirements and specifications
- **`PLANNING.md`** - Architecture decisions and technical implementation
- **`CLAUDE.md`** - Development guidelines and coding standards
- **`TASK.md`** - Development roadmap and task tracking
- **`docs/`** - Additional technical documentation and domain knowledge

### 🔗 **External References**

- **Pydantic AI**: https://ai.pydantic.dev/
- **Anthropic Claude**: https://docs.anthropic.com/claude/
- **Statistical Methods**: https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.corr.html
- **Visualization**: https://seaborn.pydata.org/generated/seaborn.heatmap.html

## Validation Results

### ✅ **Production Readiness Verified**

**Data Processing:**
- ✅ Loads 295,373 manufacturing records successfully
- ✅ Handles 5 different time column formats automatically
- ✅ Creates unified timeline with 262,028 aligned records
- ✅ Achieves 99.5% timestamp alignment success rate

**Statistical Analysis:**
- ✅ Discovers significant correlations (r=0.306, p<0.0001)
- ✅ Provides confidence intervals and significance testing
- ✅ Handles lag analysis up to 60 time periods
- ✅ Supports multiple correlation methods (Pearson, Spearman, Kendall)

**AI Agent Performance:**
- ✅ Natural language query processing working
- ✅ Manufacturing domain knowledge integrated (including thickness sensor understanding)
- ✅ Multi-provider support (Claude/Vertex AI) functional
- ✅ 8 specialized tools operational (including thickness metrics calculator)
- ✅ High-precision correlation analysis (6-decimal display, 8-decimal internal)

**User Interface:**
- ✅ Interactive CLI with high-precision correlation tables
- ✅ Scientific notation for small p-values (< 0.001)
- ✅ Robust significance level display (no more "unknown")
- ✅ Command system fully functional
- ✅ Batch processing capabilities
- ✅ Export functionality working

## Contributing

### 🤝 **How to Contribute**

1. **Check open tasks** in `TASK.md`
2. **Follow coding standards** in `CLAUDE.md`
3. **Write comprehensive tests** for new features
4. **Update documentation** as needed
5. **Validate with real data** using the test-data folder

### 🛠️ **Development Workflow**

```bash
# 1. Set up development environment
source venv/bin/activate
pip install -r requirements.txt

# 2. Run tests before changes
pytest tests/ -v

# 3. Make your changes following CLAUDE.md guidelines

# 4. Validate with real data
python -c "from src.data.loader import ManufacturingDataLoader; print('✅ Tests pass')"

# 5. Run tests after changes
pytest tests/ -v

# 6. Update documentation if needed
```

## License

[Your License Here]

## Support

### 🆘 **Getting Help**

1. **Documentation**: Check `docs/` folder for technical details
2. **Examples**: Review `examples/` for reference implementations
3. **Issues**: Create repository issues for bugs or feature requests
4. **Validation**: Use the included test data to verify functionality

### 🎯 **Quick Troubleshooting**

**Installation Issues:**
```bash
# Verify Python version (3.8+ required)
python --version

# Check virtual environment
which python

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

**API Configuration:**
```bash
# Check environment variables (strict validation)
python -c "
import os
provider = os.getenv('LLM_PROVIDER', 'ANTHROPIC').upper()
if provider == 'ANTHROPIC':
    if os.getenv('ANTHROPIC_API_KEY') and os.getenv('ANTHROPIC_MODEL'):
        print('✅ Anthropic configuration complete')
    else:
        print('❌ Missing: ANTHROPIC_API_KEY and/or ANTHROPIC_MODEL')
elif provider == 'VERTEX_AI':
    if os.getenv('VERTEX_AI_PROJECT') and os.getenv('GEMINI_MODEL'):
        print('✅ Vertex AI configuration complete')
    else:
        print('❌ Missing: VERTEX_AI_PROJECT and/or GEMINI_MODEL')
else:
    print('❌ Unknown LLM_PROVIDER')
"
```

**Data Loading:**
```bash
# Test data loading
python -c "from src.data.loader import ManufacturingDataLoader; loader = ManufacturingDataLoader('test-data'); data = loader.load_all_manufacturing_data(); print(f'✅ Loaded {len(data)} datasets')"
```

---

## 🎉 **Phase 2.0: 100% COMPLETE - Multi-Method Analysis with Complete Visualization Suite Ready for Production**

The **Multi-Method Correlation Analysis System with Complete Visualization Suite** is **production-ready** and comprehensively validated with real manufacturing data. The system successfully processes 262,028+ unified records, compares Pearson, Spearman, and Kendall correlation methods, provides intelligent method selection, and generates comprehensive visualizations for actionable insights in fiber cement manufacturing process improvement.

### ✅ **Final Validation Results**
- **262,028 unified manufacturing records** processed successfully
- **73 comprehensive tests** with 73 passing (100% success rate)
- **Multi-method correlation analysis** fully validated with all three methods
- **Method convergence scores** >0.99 demonstrating robust relationships
- **Bootstrap robustness analysis** with >0.99 stability scores
- **99.5% timestamp alignment** across mixed-frequency datasets
- **Real API integration** with Anthropic Claude (no mocking)
- **Enterprise-scale performance** with sub-second response times for all methods
- **Statistical rigor** with p-values, confidence intervals, and significance testing for all methods
- **Intelligent method selection** validated with data distribution assessment
- **Complete visualization suite** with 5 agent-integrated tools for automatic visualization generation
- **Professional visualization output** with PNG and HTML formats

### 🔮 **Next Steps: Phase 3**
Phase 3 will implement the multi-agent system with Research and Diagnostic agents for advanced causal inference, predictive modeling with Temporal Fusion Transformer, and real-time monitoring capabilities. The multi-method correlation analysis foundation with complete visualization suite provides robust statistical analysis and visual insights for these advanced features.