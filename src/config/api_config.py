"""
API Configuration Management for Manufacturing Correlation Analysis

Provides centralized configuration for API rate limiting, monitoring,
and performance optimization based on environment and usage patterns.

Features:
    - Environment-based configuration (dev/staging/prod)
    - Dynamic rate limit adjustment based on API tier
    - Usage tracking and quota management
    - Health check and monitoring configuration
    - Fallback and retry policy configuration

Usage:
    ```python
    from src.config.api_config import get_api_config, APIConfig
    
    # Get configuration for current environment
    config = get_api_config()
    
    # Use in rate limit manager
    rate_manager = RateLimitManager(
        requests_per_minute=config.requests_per_minute,
        tokens_per_minute=config.tokens_per_minute
    )
    ```
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class RetryPolicy:
    """Configuration for retry behavior"""
    max_retries: int = 5
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    backoff_on_rate_limit: bool = True
    backoff_on_connection_error: bool = True


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5
    timeout_seconds: float = 60.0
    half_open_max_calls: int = 3


@dataclass
class MonitoringConfig:
    """Configuration for monitoring and health checks"""
    enable_stats_tracking: bool = True
    stats_reset_interval_hours: int = 24
    health_check_interval_seconds: int = 30
    alert_on_error_rate_percent: float = 20.0
    alert_on_rate_limit_percent: float = 15.0
    log_level: str = "INFO"


@dataclass
class APIConfig:
    """Complete API configuration"""
    # Rate limiting
    requests_per_minute: int = 50
    tokens_per_minute: int = 40000
    burst_capacity_multiplier: float = 2.0
    
    # Adaptive throttling
    enable_adaptive_throttling: bool = True
    throttle_min_factor: float = 0.3
    throttle_adjustment_interval_seconds: int = 30
    
    # Fallback behavior
    enable_fallbacks: bool = True
    fallback_cache_ttl_seconds: int = 300
    
    # Sub-configurations
    retry_policy: RetryPolicy = field(default_factory=RetryPolicy)
    circuit_breaker: CircuitBreakerConfig = field(default_factory=CircuitBreakerConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    
    # Environment info
    environment: str = "development"
    api_provider: str = "anthropic"
    model_name: str = "claude-3-sonnet-20240229"


# Predefined configurations for different environments
ENVIRONMENT_CONFIGS = {
    "development": APIConfig(
        requests_per_minute=30,
        tokens_per_minute=20000,
        enable_adaptive_throttling=True,
        enable_fallbacks=True,
        environment="development"
    ),
    
    "testing": APIConfig(
        requests_per_minute=20,
        tokens_per_minute=15000,
        enable_adaptive_throttling=False,  # More predictable for tests
        enable_fallbacks=False,  # Want to catch API issues in tests
        environment="testing"
    ),
    
    "staging": APIConfig(
        requests_per_minute=40,
        tokens_per_minute=30000,
        enable_adaptive_throttling=True,
        enable_fallbacks=True,
        environment="staging"
    ),
    
    "production": APIConfig(
        requests_per_minute=50,
        tokens_per_minute=40000,
        enable_adaptive_throttling=True,
        enable_fallbacks=True,
        retry_policy=RetryPolicy(max_retries=3, max_delay=30.0),  # Faster recovery in prod
        circuit_breaker=CircuitBreakerConfig(failure_threshold=3),
        environment="production"
    )
}


# API tier configurations (based on Anthropic pricing tiers)
API_TIER_CONFIGS = {
    "free": {
        "requests_per_minute": 20,
        "tokens_per_minute": 10000,
        "burst_capacity_multiplier": 1.5
    },
    
    "pro": {
        "requests_per_minute": 50,
        "tokens_per_minute": 40000,
        "burst_capacity_multiplier": 2.0
    },
    
    "team": {
        "requests_per_minute": 100,
        "tokens_per_minute": 80000,
        "burst_capacity_multiplier": 2.5
    },
    
    "enterprise": {
        "requests_per_minute": 200,
        "tokens_per_minute": 160000,
        "burst_capacity_multiplier": 3.0
    }
}


def get_environment() -> str:
    """Get current environment from environment variables"""
    return os.getenv('ENVIRONMENT', os.getenv('ENV', 'development')).lower()


def get_api_tier() -> str:
    """Get API tier from environment variables"""
    return os.getenv('ANTHROPIC_API_TIER', os.getenv('API_TIER', 'pro')).lower()


def get_api_config(
    environment: Optional[str] = None,
    api_tier: Optional[str] = None,
    override_config: Optional[Dict[str, Any]] = None
) -> APIConfig:
    """
    Get API configuration for the current environment and tier
    
    Args:
        environment: Environment name (dev/test/staging/prod)
        api_tier: API tier (free/pro/team/enterprise)
        override_config: Dictionary of config overrides
        
    Returns:
        Complete API configuration
    """
    # Get environment and tier
    env = environment or get_environment()
    tier = api_tier or get_api_tier()
    
    # Start with environment config
    config = ENVIRONMENT_CONFIGS.get(env, ENVIRONMENT_CONFIGS["development"])
    
    # Apply tier-specific settings
    if tier in API_TIER_CONFIGS:
        tier_config = API_TIER_CONFIGS[tier]
        config.requests_per_minute = tier_config["requests_per_minute"]
        config.tokens_per_minute = tier_config["tokens_per_minute"] 
        config.burst_capacity_multiplier = tier_config["burst_capacity_multiplier"]
    
    # Apply environment variable overrides
    env_overrides = {}
    
    # Rate limiting overrides
    if os.getenv('ANTHROPIC_REQUESTS_PER_MINUTE'):
        env_overrides['requests_per_minute'] = int(os.getenv('ANTHROPIC_REQUESTS_PER_MINUTE'))
    
    if os.getenv('ANTHROPIC_TOKENS_PER_MINUTE'):
        env_overrides['tokens_per_minute'] = int(os.getenv('ANTHROPIC_TOKENS_PER_MINUTE'))
    
    # Feature toggles
    if os.getenv('ENABLE_ADAPTIVE_THROTTLING'):
        env_overrides['enable_adaptive_throttling'] = os.getenv('ENABLE_ADAPTIVE_THROTTLING').lower() == 'true'
    
    if os.getenv('ENABLE_FALLBACKS'):
        env_overrides['enable_fallbacks'] = os.getenv('ENABLE_FALLBACKS').lower() == 'true'
    
    # Model configuration
    if os.getenv('ANTHROPIC_MODEL'):
        env_overrides['model_name'] = os.getenv('ANTHROPIC_MODEL')
    
    # Apply overrides to config
    for key, value in env_overrides.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    # Apply custom overrides
    if override_config:
        for key, value in override_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
    
    return config


def get_monitoring_config() -> MonitoringConfig:
    """Get monitoring configuration with environment overrides"""
    config = MonitoringConfig()
    
    # Environment variable overrides
    if os.getenv('LOG_LEVEL'):
        config.log_level = os.getenv('LOG_LEVEL').upper()
    
    if os.getenv('ENABLE_STATS_TRACKING'):
        config.enable_stats_tracking = os.getenv('ENABLE_STATS_TRACKING').lower() == 'true'
    
    if os.getenv('HEALTH_CHECK_INTERVAL'):
        config.health_check_interval_seconds = int(os.getenv('HEALTH_CHECK_INTERVAL'))
    
    return config


def validate_api_credentials() -> Dict[str, bool]:
    """
    Validate API credentials and configuration
    
    Returns:
        Dictionary with validation results
    """
    validation = {
        "anthropic_api_key": bool(os.getenv('ANTHROPIC_API_KEY')),
        "anthropic_model": bool(os.getenv('ANTHROPIC_MODEL')),
        "environment_set": bool(os.getenv('ENVIRONMENT') or os.getenv('ENV')),
        "config_valid": True
    }
    
    # Additional validation
    try:
        config = get_api_config()
        validation["config_valid"] = config.requests_per_minute > 0 and config.tokens_per_minute > 0
    except Exception:
        validation["config_valid"] = False
    
    return validation


def get_config_summary() -> Dict[str, Any]:
    """Get a summary of current configuration for monitoring/debugging"""
    config = get_api_config()
    validation = validate_api_credentials()
    
    return {
        "environment": config.environment,
        "api_tier": get_api_tier(),
        "rate_limits": {
            "requests_per_minute": config.requests_per_minute,
            "tokens_per_minute": config.tokens_per_minute,
            "burst_capacity": int(config.requests_per_minute * config.burst_capacity_multiplier)
        },
        "features": {
            "adaptive_throttling": config.enable_adaptive_throttling,
            "fallbacks": config.enable_fallbacks,
            "stats_tracking": config.monitoring.enable_stats_tracking
        },
        "retry_policy": {
            "max_retries": config.retry_policy.max_retries,
            "max_delay": config.retry_policy.max_delay
        },
        "validation": validation,
        "model": config.model_name
    }


# Global configuration instance
_config_cache: Optional[APIConfig] = None


def get_cached_config() -> APIConfig:
    """Get cached configuration (creates if not exists)"""
    global _config_cache
    if _config_cache is None:
        _config_cache = get_api_config()
    return _config_cache


def clear_config_cache():
    """Clear configuration cache (useful for testing)"""
    global _config_cache
    _config_cache = None