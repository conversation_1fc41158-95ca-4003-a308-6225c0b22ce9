"""
Manufacturing PatchTST Model Implementation

HuggingFace transformers-based PatchTST model for manufacturing time series forecasting.
Handles model initialization, training, inference, and manufacturing domain expertise.
"""

import torch
import torch.nn
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from pathlib import Path
import json

# HuggingFace transformers
from transformers import (
    PatchTSTConfig, 
    PatchTSTForPrediction,
    Trainer,
    TrainingArguments,
    EarlyStoppingCallback
)

# CRITICAL FIX: HuggingFace PatchTST ignores num_targets=1 and outputs all channels
# We need a wrapper that properly extracts the target channel for univariate prediction

class UnivariatePatchTSTWrapper(torch.nn.Module):
    """
    Wrapper for HuggingFace PatchTST that properly handles univariate prediction
    
    FIXES: Despite setting num_targets=1, HuggingFace PatchTST outputs all input channels.
    This wrapper extracts only the target channel during both training and inference.
    """
    
    def __init__(self, base_model, target_channel_idx=0):
        super().__init__()
        self.base_model = base_model
        self.target_channel_idx = target_channel_idx
        
    def forward(self, past_values, future_values=None, **kwargs):
        """Forward pass with univariate output extraction"""
        outputs = self.base_model(past_values=past_values, future_values=future_values, **kwargs)
        
        # Extract only the target channel from prediction_outputs
        if hasattr(outputs, 'prediction_outputs'):
            # Extract target channel: [batch, pred_len, channels] -> [batch, pred_len, 1]
            univariate_predictions = outputs.prediction_outputs[:, :, self.target_channel_idx:self.target_channel_idx+1]
            
            # Create new output dict that maintains HuggingFace compatibility
            corrected_outputs = dict(outputs)
            corrected_outputs['prediction_outputs'] = univariate_predictions
            
            # Recalculate loss if training and future_values provided
            if future_values is not None and self.training:
                import torch.nn.functional as F
                corrected_outputs['loss'] = F.mse_loss(univariate_predictions, future_values)
            
            # Return as the same type as original outputs but with corrected values
            output_type = type(outputs)
            if hasattr(output_type, '_fields'):  # NamedTuple
                return output_type(**corrected_outputs)
            else:
                # Return as dict-like object that supports both dict and attribute access
                class DictLikeOutput(dict):
                    def __getattr__(self, key):
                        try:
                            return self[key]
                        except KeyError:
                            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{key}'")
                    def __setattr__(self, key, value):
                        self[key] = value
                
                return DictLikeOutput(corrected_outputs)
        
        return outputs

# Local imports
from .config import ForecastConfig, PatchTSTTrainingConfig, ForecastResult
from .preprocessing import ManufacturingTimeSeriesPreprocessor
from .stability import StabilizedTrainer, ManufacturingStabilityValidator

logger = logging.getLogger(__name__)

class ManufacturingDataCollator:
    """
    Custom data collator for manufacturing PatchTST training
    Handles proper target shape for univariate prediction (DOUBLE-FIXED)
    """
    
    def __init__(self, target_channel_idx=0):
        self.target_channel_idx = target_channel_idx
    
    def __call__(self, batch):
        """
        Convert batch data to proper format for PatchTST training
        DOUBLE-CRITICAL FIX: Ensure targets are shaped correctly for HuggingFace PatchTST
        """
        past_values = torch.stack([item['past_values'] for item in batch])
        future_values = torch.stack([item['future_values'] for item in batch])
        
        # DOUBLE-CRITICAL FIX: HuggingFace PatchTST with num_targets=1 expects
        # future_values shape [batch, prediction_length, 1] for univariate prediction
        
        # Validate tensor shapes
        logger.debug(f"Collator input shapes: past_values={past_values.shape}, future_values={future_values.shape}")
        
        # Ensure future_values is correctly shaped for num_targets=1
        if len(future_values.shape) == 2:
            # Convert 2D [batch, pred_len] to 3D [batch, pred_len, 1] for univariate
            future_values = future_values.unsqueeze(-1)
            logger.debug(f"Converted 2D targets to 3D univariate: {future_values.shape}")
        elif len(future_values.shape) == 3:
            # If already 3D, ensure it's [batch, pred_len, 1]
            if future_values.shape[-1] != 1:
                # Extract target channel and reshape to [batch, pred_len, 1]
                future_values = future_values[:, :, self.target_channel_idx:self.target_channel_idx+1]
                logger.debug(f"Extracted target channel to univariate: {future_values.shape}")
        
        # Final validation for HuggingFace PatchTST univariate format
        expected_shape = (future_values.shape[0], future_values.shape[1], 1)
        if future_values.shape != expected_shape:
            raise ValueError(f"Invalid target shape: {future_values.shape}, expected [batch, pred_len, 1] for num_targets=1")
        
        logger.debug(f"Collator output shapes: past_values={past_values.shape}, future_values={future_values.shape}")
        
        return {
            'past_values': past_values,
            'future_values': future_values
        }

class ManufacturingDataset(torch.utils.data.Dataset):
    """PyTorch dataset for manufacturing time series data"""
    
    def __init__(self, sequences: List[Dict[str, Any]], max_horizon: int):
        self.sequences = sequences
        self.max_horizon = max_horizon
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        seq = self.sequences[idx]
        
        # Features (historical window) - shape: [sequence_length, num_features]
        features = torch.tensor(seq['features'], dtype=torch.float32)
        
        # Ensure features have correct shape
        if len(features.shape) != 2:
            raise ValueError(f"Features must be 2D tensor, got shape {features.shape}")
        
        # Target (forecast horizon) - CRITICAL FIX for infinite loss
        max_horizon_key = f'horizon_{self.max_horizon}'
        target_values = seq['targets'][max_horizon_key]
        
        # Ensure target values are valid and not causing numerical issues
        if not isinstance(target_values, (list, np.ndarray)):
            target_values = [target_values] * self.max_horizon
        
        target = torch.tensor(target_values, dtype=torch.float32)
        
        # Ensure target is 1D and finite
        if len(target.shape) != 1:
            target = target.flatten()
        
        # CRITICAL: Check for and fix invalid target values
        if torch.any(torch.isnan(target)) or torch.any(torch.isinf(target)):
            logger.warning(f"Invalid target values detected in sequence {idx}, replacing with zeros")
            target = torch.zeros_like(target)
        
        # For Fixed PatchTST: targets should be [prediction_length] for univariate prediction
        # Keep target as 1D - the trainer will handle shape conversion
        
        # Validate final shapes
        expected_feature_shape = (len(seq['features']), len(seq['features'][0]))
        expected_target_shape = (self.max_horizon,)  # Keep 1D target shape
        
        if features.shape != expected_feature_shape:
            raise ValueError(f"Feature shape mismatch: got {features.shape}, expected {expected_feature_shape}")
        
        if target.shape != expected_target_shape:
            raise ValueError(f"Target shape mismatch: got {target.shape}, expected {expected_target_shape}")
        
        return {
            'past_values': features,
            'future_values': target
        }

class ManufacturingPatchTSTModel:
    """
    Manufacturing-optimized PatchTST model for time series forecasting
    
    Integrates with existing manufacturing data infrastructure and provides
    manufacturing domain expertise for process optimization.
    """
    
    def __init__(self, forecast_config: ForecastConfig, training_config: Optional[PatchTSTTrainingConfig] = None):
        """
        Initialize the manufacturing PatchTST model with stability enhancements
        
        Args:
            forecast_config: Forecasting configuration
            training_config: Training configuration (optional)
        """
        self.forecast_config = forecast_config
        self.training_config = training_config or PatchTSTTrainingConfig()
        
        self.model: Optional[Any] = None
        self.preprocessor = ManufacturingTimeSeriesPreprocessor(forecast_config)
        self.model_metadata: Dict[str, Any] = {}
        
        # Performance tracking
        self.training_history: Dict[str, Any] = {}
        self.best_model_metrics: Dict[str, Any] = {}
        
        # Stability infrastructure
        self.stability_validator = ManufacturingStabilityValidator(
            manufacturing_params=forecast_config.manufacturing_params
        )
        self.use_stability_features = getattr(training_config, 'use_stability_features', True)
        
    def initialize_model(self, num_input_channels: int) -> None:
        """
        Initialize PatchTST model with manufacturing-optimized configuration
        
        Args:
            num_input_channels: Number of input feature channels
        """
        try:
            # Create PatchTST configuration with CORRECTED univariate setup
            config = PatchTSTConfig(
                num_input_channels=num_input_channels,
                context_length=self.forecast_config.lookback_window,
                prediction_length=max(self.forecast_config.forecast_horizons),
                patch_length=self.forecast_config.patch_size,
                patch_stride=self.forecast_config.patch_stride,
                d_model=self.forecast_config.model_params.get('d_model', 128),
                num_attention_heads=self.forecast_config.model_params.get('num_attention_heads', 8),
                num_hidden_layers=self.forecast_config.model_params.get('num_hidden_layers', 3),
                dropout=self.forecast_config.model_params.get('dropout', 0.1),
                channel_attention=self.forecast_config.model_params.get('channel_attention', False),
                scaling=self.forecast_config.model_params.get('scaling', 'std'),
                loss=self.forecast_config.model_params.get('loss', 'mse'),
                # CRITICAL FIXES for univariate prediction:
                num_targets=1,  # Only forecast 1 target variable
                # ADDITIONAL FIX: Ensure output matches expected target shape
                num_parallel_samples=1,  # Single prediction path
                # Manufacturing-specific parameters
                distribution_output=None,  # Use None for regression (not "mse")
                head_dropout=self.forecast_config.model_params.get('head_dropout', 0.1)
            )
            
            # Log critical configuration for debugging
            logger.info(f"PatchTST Config: num_input_channels={config.num_input_channels}, num_targets={config.num_targets}")
            logger.info(f"PatchTST Config: prediction_length={config.prediction_length}, context_length={config.context_length}")
            
            # CRITICAL FIX: HuggingFace PatchTST ignores num_targets=1, use wrapper to extract target channel
            base_model = PatchTSTForPrediction(config)
            
            # Find target channel index for univariate extraction
            target_channel_idx = 0  # Default to first channel
            if hasattr(self, '_current_target_variable'):
                if self._current_target_variable in self.forecast_config.input_variables:
                    target_channel_idx = self.forecast_config.input_variables.index(self._current_target_variable)
                    logger.info(f"Target variable '{self._current_target_variable}' found at channel index {target_channel_idx}")
                else:
                    logger.warning(f"Target variable '{self._current_target_variable}' not found in input variables, using channel 0")
            
            # Wrap with univariate extraction
            self.model = UnivariatePatchTSTWrapper(base_model, target_channel_idx)
            logger.info(f"Model wrapped for univariate prediction on channel {target_channel_idx}")
            
            # Store model metadata
            self.model_metadata['standard_patchtst'] = True
            self.model_metadata['target_channel_idx'] = target_channel_idx
            self.model_metadata['univariate_mode'] = True
            
            # Store model metadata
            self.model_metadata = {
                'num_input_channels': num_input_channels,
                'context_length': self.forecast_config.lookback_window,
                'prediction_length': max(self.forecast_config.forecast_horizons),
                'patch_length': self.forecast_config.patch_size,
                'model_params': self.forecast_config.model_params,
                'created_at': datetime.now().isoformat()
            }
            
            logger.info(f"PatchTST model initialized with {num_input_channels} input channels")
            
        except Exception as e:
            logger.error(f"Error initializing PatchTST model: {str(e)}")
            raise
    
    def train(self, unified_data: pd.DataFrame, target_variable: str) -> 'ManufacturingPatchTSTModel':
        """
        Train the PatchTST model on manufacturing data
        
        Args:
            unified_data: Unified manufacturing dataset
            target_variable: Target variable to forecast
            
        Returns:
            Self for method chaining
        """
        try:
            logger.info(f"Starting model training for target: {target_variable}")
            
            # Validate input data
            if unified_data.empty:
                raise ValueError("Cannot train on empty dataset")
            
            if target_variable not in unified_data.columns:
                available_cols = list(unified_data.columns)
                raise ValueError(f"Target variable '{target_variable}' not found. Available: {available_cols}")
            
            # Prepare data for training
            prepared_data = self.preprocessor.prepare_forecasting_data(
                unified_data, target_variable
            )
            
            # Validate prepared data
            if not prepared_data['feature_columns']:
                raise ValueError("No valid feature columns found in prepared data")
            
            # Initialize model with correct feature count
            if self.model is None:
                num_channels = len(prepared_data['feature_columns'])
                logger.info(f"Initializing model with {num_channels} input channels: {prepared_data['feature_columns']}")
                
                # Store target variable for model initialization
                self._current_target_variable = target_variable
                
                # Validate that model config matches actual features
                available_features = [col for col in self.forecast_config.input_variables 
                                    if col in unified_data.columns]
                if len(available_features) != num_channels:
                    logger.warning(f"Config input_variables mismatch: expected {len(self.forecast_config.input_variables)}, found {num_channels}")
                
                self.initialize_model(num_channels)
            else:
                # Validate existing model matches data
                existing_channels = self.model_metadata.get('num_input_channels', 0)
                current_channels = len(prepared_data['feature_columns'])
                if existing_channels != current_channels:
                    raise ValueError(f"Model channel mismatch: model has {existing_channels}, data has {current_channels}")
            
            # Validate training data availability
            train_sequences = prepared_data['train_data']['sequences']
            if not train_sequences:
                raise ValueError("No training sequences available")
            
            logger.info(f"Training data: {len(train_sequences)} sequences, {len(prepared_data['feature_columns'])} features")
            
            # Create PyTorch datasets
            train_dataset = self._create_pytorch_dataset(prepared_data['train_data'])
            val_dataset = self._create_pytorch_dataset(prepared_data['val_data'])
            
            # Set up training arguments
            training_args = TrainingArguments(
                output_dir=self.training_config.model_save_path,
                num_train_epochs=self.training_config.max_epochs,
                per_device_train_batch_size=self.training_config.batch_size,
                per_device_eval_batch_size=self.training_config.batch_size,
                warmup_steps=100,
                weight_decay=0.01,
                logging_dir=f"{self.training_config.model_save_path}/logs",
                logging_steps=10,
                eval_strategy=self.training_config.evaluation_strategy,
                save_strategy=self.training_config.save_strategy,
                load_best_model_at_end=True,
                metric_for_best_model="eval_runtime",  # Use available metric
                greater_is_better=False,  # Lower loss is better
                learning_rate=self.training_config.learning_rate,
                dataloader_num_workers=self.training_config.num_workers,
                dataloader_pin_memory=self.training_config.pin_memory,
                report_to=None,  # Disable wandb/tensorboard
                seed=42
            )
            
            # Create custom data collator for proper target shape handling
            # Note: target_channel_idx still used for potential 3D->2D conversion during validation
            target_channel_idx = self.model_metadata.get('target_channel_idx', 0)
            data_collator = ManufacturingDataCollator(target_channel_idx=target_channel_idx)
            
            # Initialize trainer with stability enhancements
            if self.use_stability_features:
                trainer = StabilizedTrainer(
                    model=self.model,
                    args=training_args,
                    train_dataset=train_dataset,
                    eval_dataset=val_dataset,
                    data_collator=data_collator,
                    gradient_clip_norm=getattr(self.training_config, 'gradient_clip_norm', 1.0),
                    adaptive_clipping=getattr(self.training_config, 'adaptive_clipping', True),
                    mixed_precision=training_args.fp16,
                    stability_monitoring=True,
                    callbacks=[EarlyStoppingCallback(early_stopping_patience=self.training_config.early_stopping_patience)]
                )
                logger.info("Using StabilizedTrainer with gradient clipping and monitoring")
            else:
                trainer = Trainer(
                    model=self.model,
                    args=training_args,
                    train_dataset=train_dataset,
                    eval_dataset=val_dataset,
                    data_collator=data_collator,
                    callbacks=[EarlyStoppingCallback(early_stopping_patience=self.training_config.early_stopping_patience)]
                )
            
            # Train model
            logger.info("Starting model training...")
            # Use getattr to safely access train method
            train_method = getattr(trainer, 'train', None)
            if train_method:
                training_output = train_method()
            else:
                raise AttributeError("Trainer does not have train method")
            
            # Save training history with stability metrics
            self.training_history = {
                'target_variable': target_variable,
                'training_loss': training_output.training_loss,
                'epochs_trained': training_output.global_step,
                'training_time': training_output.metrics.get('train_runtime', 0),
                'prepared_data_metadata': prepared_data['metadata']
            }
            
            # Add stability metrics if using StabilizedTrainer
            if self.use_stability_features and hasattr(trainer, 'stability_metrics'):
                self.training_history.update({
                    'gradient_norms': trainer.stability_metrics.get('gradient_norms', []),
                    'losses': trainer.stability_metrics.get('losses', []),
                    'stability_scores': trainer.stability_metrics.get('stability_scores', [])
                })
            
            # Evaluate model
            evaluate_method = getattr(trainer, 'evaluate', None)
            if evaluate_method:
                eval_results = evaluate_method()
            else:
                eval_results = {}
            self.best_model_metrics = {
                'eval_loss': eval_results.get('eval_loss', float('inf')),
                'eval_runtime': eval_results.get('eval_runtime', 0),
                'target_variable': target_variable
            }
            
            # Save model and metadata
            self._save_model_and_metadata(target_variable)
            
            # Perform stability validation if enabled
            if self.use_stability_features:
                self._validate_model_stability(prepared_data, target_variable)
            
            logger.info(f"Model training completed. Final eval loss: {eval_results.get('eval_loss', 'N/A')}")
            
            return self
            
        except Exception as e:
            logger.error(f"Error during model training: {str(e)}")
            raise
    
    def forecast(self, 
                historical_data: pd.DataFrame,
                target_variable: str,
                forecast_horizon: int,
                include_confidence_intervals: bool = True) -> ForecastResult:
        """
        Generate forecasts for manufacturing parameters
        
        Args:
            historical_data: Historical manufacturing data
            target_variable: Variable to forecast
            forecast_horizon: Number of time steps to forecast
            include_confidence_intervals: Whether to include confidence intervals
            
        Returns:
            ForecastResult with predictions and insights
        """
        try:
            if self.model is None:
                raise ValueError("Model not initialized. Load a pretrained model or train first.")
            
            # Validate historical data
            if historical_data.empty:
                raise ValueError("Historical data cannot be empty")
            
            if target_variable not in historical_data.columns:
                available_cols = list(historical_data.columns)
                raise ValueError(f"Target variable '{target_variable}' not found in historical data. Available: {available_cols}")
            
            logger.info(f"Generating forecast for {target_variable} with {forecast_horizon} steps")
            
            # Prepare input data
            input_data = self._prepare_inference_data(historical_data, target_variable)
            logger.info(f"Input tensor shape for inference: {input_data.shape}")
            
            # Generate predictions
            with torch.no_grad():
                self.model.eval()
                
                # Validate model input compatibility
                model_config = self.model.config if hasattr(self.model, 'config') else None
                if model_config:
                    expected_channels = getattr(model_config, 'num_input_channels', None)
                    actual_channels = input_data.shape[-1]
                    if expected_channels and expected_channels != actual_channels:
                        raise ValueError(f"Model expects {expected_channels} channels, got {actual_channels}")
                
                # Run model inference
                logger.info("Running model inference...")
                predictions = self.model(past_values=input_data)
                
                # Debug prediction output
                if hasattr(predictions, 'prediction_outputs'):
                    pred_tensor = predictions.prediction_outputs
                    logger.info(f"Model prediction output shape: {pred_tensor.shape}")
                elif hasattr(predictions, 'last_hidden_state'):
                    pred_tensor = predictions.last_hidden_state
                    logger.info(f"Model last hidden state shape: {pred_tensor.shape}")
                else:
                    # Handle different output formats
                    pred_tensor = predictions
                    logger.info(f"Raw prediction shape: {pred_tensor.shape}")
                
                # Extract forecast values safely
                # PatchTST outputs shape [batch, prediction_length, num_features]
                if len(pred_tensor.shape) == 3:  # [batch, time, features]
                    # Find which feature index corresponds to our target variable
                    feature_columns = [col for col in self.forecast_config.input_variables 
                                     if col in historical_data.columns]
                    
                    if target_variable in feature_columns:
                        target_idx = feature_columns.index(target_variable)
                        forecast_values = pred_tensor[0, :forecast_horizon, target_idx].cpu().numpy()
                        logger.info(f"Extracted target {target_variable} at feature index {target_idx}")
                    else:
                        # Fallback: use first feature
                        forecast_values = pred_tensor[0, :forecast_horizon, 0].cpu().numpy()
                        logger.warning(f"Target {target_variable} not found in features, using first feature")
                elif len(pred_tensor.shape) == 2:  # [batch, time]
                    forecast_values = pred_tensor[0, :forecast_horizon].cpu().numpy()
                else:
                    # Fallback: squeeze and take first forecast_horizon values
                    forecast_values = pred_tensor.squeeze().cpu().numpy()
                    if len(forecast_values.shape) > 1:
                        forecast_values = forecast_values[:forecast_horizon, 0]
                    else:
                        forecast_values = forecast_values[:forecast_horizon]
                
                logger.info(f"Extracted forecast values shape: {forecast_values.shape}")
                
                # Ensure we have the right number of forecast points
                if len(forecast_values) < forecast_horizon:
                    logger.warning(f"Got {len(forecast_values)} forecast points, expected {forecast_horizon}")
                    # Pad with last value if necessary
                    padding = np.full(forecast_horizon - len(forecast_values), forecast_values[-1])
                    forecast_values = np.concatenate([forecast_values, padding])
                elif len(forecast_values) > forecast_horizon:
                    forecast_values = forecast_values[:forecast_horizon]
                
                # Inverse transform predictions
                try:
                    forecast_values = self.preprocessor.inverse_transform_predictions(
                        forecast_values, forecast_horizon
                    )
                    logger.info("Applied inverse transform to predictions")
                except Exception as e:
                    logger.warning(f"Inverse transform failed: {e}, using raw predictions")
                    # Convert to reasonable scale if inverse transform fails
                    forecast_values = forecast_values.astype(float)
            
            # Generate timestamps
            last_timestamp = historical_data['timestamp'].iloc[-1]
            forecast_timestamps = self._generate_forecast_timestamps(
                last_timestamp, forecast_horizon
            )
            
            # Calculate confidence intervals if requested
            confidence_intervals: Dict[str, List[float]] = {"lower": [], "upper": []}
            if include_confidence_intervals:
                confidence_intervals = self._calculate_confidence_intervals(
                    forecast_values, forecast_horizon
                )
            
            # Generate manufacturing insights
            manufacturing_insights = self._generate_manufacturing_insights(
                target_variable, forecast_values, historical_data
            )
            
            # Calculate model performance metrics
            performance_metrics = self._calculate_performance_metrics(
                historical_data, target_variable, forecast_values
            )
            
            return ForecastResult(
                target_variable=target_variable,
                forecast_horizon=forecast_horizon,
                forecast_values=forecast_values.tolist(),
                confidence_intervals=confidence_intervals,
                forecast_timestamps=forecast_timestamps,
                model_performance=performance_metrics,
                attention_insights=self._extract_attention_insights(),
                manufacturing_insights=manufacturing_insights
            )
            
        except Exception as e:
            logger.error(f"Error during forecasting: {str(e)}")
            raise
    
    def _validate_model_stability(self, prepared_data: Dict[str, Any], target_variable: str) -> None:
        """
        Validate model stability using manufacturing stability validator
        
        Args:
            prepared_data: Prepared training data
            target_variable: Target variable being forecasted
        """
        try:
            logger.info(f"Performing stability validation for {target_variable}")
            
            # Create test data from prepared data
            test_sequences = prepared_data.get('test_data', {}).get('sequences', [])
            if not test_sequences:
                logger.warning("No test data available for stability validation")
                return
            
            # Convert test sequences back to DataFrame format for validation
            test_df_data = []
            for seq in test_sequences[:100]:  # Use first 100 sequences for validation
                for i, feature_row in enumerate(seq['features']):
                    row_data = {}
                    for j, col_name in enumerate(prepared_data['feature_columns']):
                        row_data[col_name] = feature_row[j]
                    row_data['timestamp'] = pd.Timestamp.now() + pd.Timedelta(minutes=i)
                    test_df_data.append(row_data)
            
            test_data = pd.DataFrame(test_df_data)
            
            # Run stability validation
            validation_result = self.stability_validator.validate_model_stability(
                model=self,
                training_history=self.training_history,
                test_data=test_data,
                target_variable=target_variable
            )
            
            # Log validation results
            if validation_result.is_stable:
                logger.info(f"✓ Model stability validation PASSED (score: {validation_result.stability_score:.3f})")
            else:
                logger.warning(f"✗ Model stability validation FAILED (score: {validation_result.stability_score:.3f})")
                for recommendation in validation_result.recommendations:
                    logger.warning(f"  - {recommendation}")
            
            # Save validation results to training history
            self.training_history['stability_validation'] = {
                'is_stable': validation_result.is_stable,
                'stability_score': validation_result.stability_score,
                'prediction_variance': validation_result.prediction_variance,
                'manufacturing_compliance': validation_result.manufacturing_compliance,
                'recommendations': validation_result.recommendations
            }
            
        except Exception as e:
            logger.error(f"Error during stability validation: {str(e)}")
            # Don't fail the training due to validation errors
    
    def _create_pytorch_dataset(self, data: Dict[str, Any]) -> torch.utils.data.Dataset:
        """Create PyTorch dataset from processed sequences"""
        return ManufacturingDataset(
            data['sequences'], 
            max(self.forecast_config.forecast_horizons)
        )
    
    def _prepare_inference_data(self, historical_data: pd.DataFrame, target_variable: str) -> torch.Tensor:
        """Prepare historical data for model inference"""
        try:
            # Extract features for the lookback window
            lookback = self.forecast_config.lookback_window
            recent_data = historical_data.tail(lookback)
            
            # Validate data length
            if len(recent_data) < lookback:
                logger.warning(f"Limited data: {len(recent_data)} records, expected {lookback}")
                # Pad with zeros if necessary
                padding_size = lookback - len(recent_data)
                padding = pd.DataFrame(0, index=range(padding_size), columns=recent_data.columns)
                recent_data = pd.concat([padding, recent_data], ignore_index=True)
            
            # Get feature columns that exist in both config and data
            available_columns = list(recent_data.columns)
            feature_columns = [col for col in self.forecast_config.input_variables 
                              if col in available_columns]
            
            if not feature_columns:
                raise ValueError(f"No valid feature columns found. Expected: {self.forecast_config.input_variables}, Available: {available_columns}")
            
            logger.info(f"Using {len(feature_columns)} feature columns: {feature_columns}")
            
            # Extract features
            features = recent_data[feature_columns].values
            
            # Validate feature array shape
            expected_shape = (lookback, len(feature_columns))
            if features.shape != expected_shape:
                logger.error(f"Feature shape mismatch: got {features.shape}, expected {expected_shape}")
                raise ValueError(f"Feature shape mismatch: got {features.shape}, expected {expected_shape}")
            
            # Handle missing values
            features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
            
            # Scale features using the preprocessor's scaler if available
            if hasattr(self.preprocessor, 'feature_scaler') and self.preprocessor.feature_scaler is not None:
                try:
                    features = self.preprocessor.feature_scaler.transform(features)
                    logger.info("Applied feature scaling")
                except Exception as e:
                    logger.warning(f"Feature scaling failed: {e}, using unscaled features")
            
            # Convert to tensor with proper shape [batch_size, sequence_length, num_features]
            input_tensor = torch.tensor(features, dtype=torch.float32).unsqueeze(0)
            
            # Move tensor to same device as model if possible
            if hasattr(self.model, 'device'):
                input_tensor = input_tensor.to(self.model.device)
            elif next(self.model.parameters(), None) is not None:
                device = next(self.model.parameters()).device
                input_tensor = input_tensor.to(device)
            
            # Validate final tensor shape
            expected_tensor_shape = (1, lookback, len(feature_columns))
            if input_tensor.shape != expected_tensor_shape:
                logger.error(f"Input tensor shape mismatch: got {input_tensor.shape}, expected {expected_tensor_shape}")
                raise ValueError(f"Input tensor shape mismatch: got {input_tensor.shape}, expected {expected_tensor_shape}")
            
            logger.info(f"Prepared input tensor with shape: {input_tensor.shape}")
            return input_tensor
            
        except Exception as e:
            logger.error(f"Error preparing inference data: {str(e)}")
            raise
    
    def _generate_forecast_timestamps(self, last_timestamp, horizon: int) -> List[str]:
        """Generate timestamps for forecast period"""
        timestamps = []
        
        # Ensure we have a proper datetime object
        if isinstance(last_timestamp, str):
            current_time = pd.to_datetime(last_timestamp)
        elif hasattr(last_timestamp, 'to_pydatetime'):
            current_time = last_timestamp.to_pydatetime()
        else:
            current_time = pd.to_datetime(last_timestamp)
        
        for i in range(horizon):
            current_time += timedelta(minutes=1)  # Assuming 1-minute intervals
            timestamps.append(current_time.strftime('%Y-%m-%d %H:%M:%S'))
        
        return timestamps
    
    def _calculate_confidence_intervals(self, forecast_values: np.ndarray, horizon: int) -> Dict[str, List[float]]:
        """Calculate confidence intervals for forecasts"""
        # Simple confidence interval calculation
        # In production, this could use bootstrap sampling or model uncertainty
        std_dev = np.std(forecast_values) * 0.1  # 10% uncertainty
        
        return {
            "lower": (forecast_values - 1.96 * std_dev).tolist(),
            "upper": (forecast_values + 1.96 * std_dev).tolist()
        }
    
    def _generate_manufacturing_insights(self, 
                                       target_variable: str,
                                       forecast_values: np.ndarray,
                                       historical_data: pd.DataFrame) -> List[str]:
        """Generate manufacturing-specific insights from forecasts"""
        insights = []
        
        # Analyze forecast trends
        trend = "increasing" if forecast_values[-1] > forecast_values[0] else "decreasing"
        insights.append(f"{target_variable} is predicted to be {trend} over the forecast horizon")
        
        # Manufacturing-specific insights
        if target_variable == "thickness_avg":
            avg_thickness = np.mean(forecast_values)
            thickness_range = self.forecast_config.manufacturing_params.get('thickness_range', [2.0, 20.0])
            
            if avg_thickness < thickness_range[0] + 1:
                insights.append("Warning: Thickness approaching lower specification limit")
            elif avg_thickness > thickness_range[1] - 1:
                insights.append("Warning: Thickness approaching upper specification limit")
            else:
                insights.append("Thickness forecast within acceptable manufacturing range")
        
        elif target_variable == "scrap_rate":
            max_scrap = np.max(forecast_values)
            threshold = self.forecast_config.manufacturing_params.get('scrap_rate_threshold', 0.05)
            
            if max_scrap > threshold:
                insights.append(f"Alert: Scrap rate may exceed {threshold*100:.1f}% threshold")
            else:
                insights.append("Scrap rate forecast within acceptable limits")
        
        elif target_variable == "speed":
            speed_variance = np.var(forecast_values)
            if speed_variance > 10:  # High variance threshold
                insights.append("High speed variability predicted - consider process stability review")
            else:
                insights.append("Stable production speed forecast")
        
        return insights
    
    def _calculate_performance_metrics(self, 
                                     historical_data: pd.DataFrame,
                                     target_variable: str,
                                     forecast_values: np.ndarray) -> Dict[str, float]:
        """Calculate model performance metrics"""
        # Use recent historical data for validation
        recent_actual = np.array(historical_data[target_variable].tail(len(forecast_values)).values)
        
        if len(recent_actual) >= len(forecast_values):
            # Calculate basic metrics
            mse = np.mean((recent_actual[:len(forecast_values)] - forecast_values) ** 2)
            mae = np.mean(np.abs(recent_actual[:len(forecast_values)] - forecast_values))
            
            # Calculate MAPE (avoid division by zero)
            mape = np.mean(np.abs((recent_actual[:len(forecast_values)] - forecast_values) / 
                                (recent_actual[:len(forecast_values)] + 1e-8))) * 100
            
            return {
                "mse": float(mse),
                "mae": float(mae),
                "mape": float(mape)
            }
        else:
            return {
                "mse": 0.0,
                "mae": 0.0,
                "mape": 0.0
            }
    
    def _extract_attention_insights(self) -> Dict[str, Any]:
        """Extract attention-based insights from the model"""
        # Placeholder for attention analysis
        # In production, this would analyze attention weights
        return {
            "key_time_periods": "Recent 30 minutes most influential",
            "important_features": "Speed and thickness show highest attention",
            "temporal_patterns": "Daily cycle patterns detected"
        }
    
    def _save_model_and_metadata(self, target_variable: str) -> None:
        """Save trained model and metadata"""
        try:
            # Create model directory
            model_dir = Path(self.training_config.model_save_path) / f"patchtst_manufacturing_{target_variable}"
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Save model
            if self.model and hasattr(self.model, 'save_pretrained'):
                self.model.save_pretrained(str(model_dir))
            else:
                logger.warning("Model does not support save_pretrained method")
            
            # Save metadata
            metadata = {
                'model_metadata': self.model_metadata,
                'training_history': self.training_history,
                'best_model_metrics': self.best_model_metrics,
                'forecast_config': self.forecast_config.dict(),
                'training_config': self.training_config.dict()
            }
            
            with open(model_dir / 'metadata.json', 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"Model saved to {model_dir}")
            
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
            raise
    
    @classmethod
    def load_pretrained(cls, model_path: str) -> 'ManufacturingPatchTSTModel':
        """Load a pretrained model"""
        try:
            model_dir = Path(model_path)
            
            # Load metadata
            with open(model_dir / 'metadata.json', 'r') as f:
                metadata = json.load(f)
            
            # Recreate configs
            forecast_config = ForecastConfig(**metadata['forecast_config'])
            training_config = PatchTSTTrainingConfig(**metadata['training_config'])
            
            # Create model instance
            model_instance = cls(forecast_config, training_config)
            
            # Load pretrained model
            model_instance.model = PatchTSTForPrediction.from_pretrained(str(model_dir))
            model_instance.model_metadata = metadata['model_metadata']
            model_instance.training_history = metadata['training_history']
            model_instance.best_model_metrics = metadata['best_model_metrics']
            
            logger.info(f"Pretrained model loaded from {model_path}")
            
            return model_instance
            
        except Exception as e:
            logger.error(f"Error loading pretrained model: {str(e)}")
            raise
    
    @classmethod
    def from_config(cls, config_path: str) -> 'ManufacturingPatchTSTModel':
        """Create model instance from configuration file"""
        forecast_config = ForecastConfig.from_json(config_path)
        return cls(forecast_config)