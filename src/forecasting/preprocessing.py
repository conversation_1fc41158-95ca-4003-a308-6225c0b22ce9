"""
Manufacturing Time Series Preprocessing for PatchTST

Preprocessing pipeline for converting manufacturing data into PatchTST-compatible
format. Handles temporal alignment, feature engineering, and dataset creation
following established patterns from the data loader infrastructure.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import logging

# Import existing data processing infrastructure
from ..data.loader import ManufacturingDataLoader
from .config import ForecastConfig

logger = logging.getLogger(__name__)

class ManufacturingTimeSeriesPreprocessor:
    """
    Preprocessing pipeline for manufacturing time series data
    Following patterns from src/data/loader.py ManufacturingDataLoader
    """
    
    def __init__(self, config: ForecastConfig):
        """
        Initialize preprocessor with forecasting configuration
        
        Args:
            config: ForecastConfig object with preprocessing parameters
        """
        self.config = config
        self.data_loader = ManufacturingDataLoader()
        self.feature_scalers: Dict[str, Any] = {}
        self.target_scalers: Dict[str, Any] = {}
        
    def prepare_forecasting_data(self, 
                               unified_data: pd.DataFrame,
                               target_variable: str) -> Dict[str, Any]:
        """
        Prepare manufacturing data for PatchTST training/inference
        
        Args:
            unified_data: Unified manufacturing dataset from data loader
            target_variable: Target variable to forecast
            
        Returns:
            Dictionary containing prepared datasets and metadata
        """
        try:
            # Validate input data
            if unified_data.empty:
                raise ValueError("Input data is empty")
            
            if target_variable not in unified_data.columns:
                raise ValueError(f"Target variable '{target_variable}' not found in data")
            
            # Ensure timestamp column exists
            if 'timestamp' not in unified_data.columns:
                raise ValueError("Timestamp column required for time series processing")
            
            # Sort by timestamp for proper temporal ordering
            unified_data = unified_data.sort_values('timestamp').reset_index(drop=True)
            
            # Create feature matrix
            feature_columns = [col for col in self.config.input_variables 
                             if col in unified_data.columns]
            
            if not feature_columns:
                available_cols = list(unified_data.columns)
                expected_cols = self.config.input_variables
                raise ValueError(
                    f"No valid input variables found in data. "
                    f"Expected: {expected_cols}, "
                    f"Available: {available_cols}"
                )
            
            # Extract features and target
            features = unified_data[feature_columns].copy()
            target = unified_data[target_variable].copy()
            timestamps = unified_data['timestamp'].copy()
            
            # Handle missing values
            features = self._handle_missing_values(features)
            target = self._handle_missing_target(target)
            
            # Apply manufacturing domain validation
            features = self._validate_manufacturing_ranges(features)
            
            # Store target variable for sequence creation
            self._target_variable = target_variable
            
            # Create time series sequences
            sequences = self._create_time_series_sequences(
                features, target, timestamps
            )
            
            # Split data chronologically
            train_data, val_data, test_data = self._split_data_chronologically(
                sequences, unified_data
            )
            
            # Scale features and targets
            train_data = self._scale_features_and_targets(train_data, fit=True)
            val_data = self._scale_features_and_targets(val_data, fit=False)
            test_data = self._scale_features_and_targets(test_data, fit=False)
            
            return {
                'train_data': train_data,
                'val_data': val_data,
                'test_data': test_data,
                'feature_columns': feature_columns,
                'target_variable': target_variable,
                'metadata': {
                    'total_samples': len(unified_data),
                    'train_samples': len(train_data['sequences']),
                    'val_samples': len(val_data['sequences']),
                    'test_samples': len(test_data['sequences']),
                    'sequence_length': self.config.lookback_window,
                    'feature_count': len(feature_columns)
                }
            }
            
        except Exception as e:
            logger.error(f"Error in prepare_forecasting_data: {str(e)}")
            raise
    
    def _handle_missing_values(self, features: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values in feature data"""
        # Use forward fill then backward fill for time series continuity
        features = features.ffill().bfill().fillna(0.0)
        
        # For remaining NaN values, use column means
        for col in features.columns:
            if features[col].isna().any():
                features[col] = features[col].fillna(features[col].mean())
        
        return features
    
    def _handle_missing_target(self, target: pd.Series) -> pd.Series:
        """Handle missing values in target variable"""
        # Use forward fill for target continuity
        target = target.ffill().bfill().fillna(0.0)
        
        # For remaining NaN, use mean
        if target.isna().any():
            target = target.fillna(target.mean()).fillna(0.0)
        
        return target
    
    def _validate_manufacturing_ranges(self, features: pd.DataFrame) -> pd.DataFrame:
        """Apply manufacturing domain validation to features"""
        params = self.config.manufacturing_params
        
        # Validate thickness ranges
        if 'thickness_avg' in features.columns:
            thickness_range = params.get('thickness_range', [0, 50])
            features['thickness_avg'] = features['thickness_avg'].clip(
                thickness_range[0], thickness_range[1]
            )
        
        # Validate speed ranges
        if 'speed' in features.columns:
            speed_range = params.get('speed_range', [0, 200])
            features['speed'] = features['speed'].clip(
                speed_range[0], speed_range[1]
            )
        
        # Ensure non-negative values for certain variables
        non_negative_vars = ['thickness_uniformity', 'minutes_since_last_stop']
        for var in non_negative_vars:
            if var in features.columns:
                features[var] = features[var].clip(lower=0)
        
        return features
    
    def _create_time_series_sequences(self, 
                                    features: pd.DataFrame,
                                    target: pd.Series,
                                    timestamps: pd.Series) -> List[Dict[str, Any]]:
        """Create time series sequences for PatchTST"""
        sequences = []
        
        lookback = self.config.lookback_window
        max_horizon = max(self.config.forecast_horizons)
        
        # Ensure we have enough data for sequences
        if len(features) < lookback + max_horizon:
            raise ValueError(f"Insufficient data: need {lookback + max_horizon}, got {len(features)}")
        
        # Create sequences with sliding window
        for i in range(len(features) - lookback - max_horizon + 1):
            # Historical features (lookback window)
            hist_features = features.iloc[i:i + lookback].values
            
            # Target values for different horizons
            targets = {}
            for horizon in self.config.forecast_horizons:
                targets[f'horizon_{horizon}'] = target.iloc[i + lookback:i + lookback + horizon].values
            
            # Timestamps for sequence
            hist_timestamps = timestamps.iloc[i:i + lookback].tolist()
            future_timestamps = timestamps.iloc[i + lookback:i + lookback + max_horizon].tolist()
            
            # Find target variable index in feature columns  
            target_feature_idx = 0  # Default
            if hasattr(self, '_target_variable'):
                feature_columns = [col for col in self.config.input_variables 
                                 if col in features.columns]
                if self._target_variable in feature_columns:
                    target_feature_idx = feature_columns.index(self._target_variable)
            
            sequences.append({
                'features': hist_features,
                'targets': targets,
                'hist_timestamps': hist_timestamps,
                'future_timestamps': future_timestamps,
                'sequence_id': i,
                'target_feature_idx': target_feature_idx
            })
        
        return sequences
    
    def _split_data_chronologically(self, 
                                  sequences: List[Dict[str, Any]],
                                  original_data: pd.DataFrame) -> Tuple[Dict, Dict, Dict]:
        """Split sequences chronologically for time series validation"""
        total_sequences = len(sequences)
        
        # Calculate split points (chronological order)
        train_end = int(total_sequences * 0.7)
        val_end = int(total_sequences * 0.85)
        
        # Split sequences
        train_sequences = sequences[:train_end]
        val_sequences = sequences[train_end:val_end]
        test_sequences = sequences[val_end:]
        
        return (
            {'sequences': train_sequences, 'split': 'train'},
            {'sequences': val_sequences, 'split': 'validation'},
            {'sequences': test_sequences, 'split': 'test'}
        )
    
    def _scale_features_and_targets(self, data: Dict[str, Any], fit: bool = False) -> Dict[str, Any]:
        """Scale features and targets using StandardScaler"""
        from sklearn.preprocessing import StandardScaler
        
        sequences = data['sequences']
        if not sequences:
            return data
        
        # Initialize scalers on first fit
        if fit:
            # Feature scaler
            all_features = np.vstack([seq['features'] for seq in sequences])
            self.feature_scaler = StandardScaler()
            self.feature_scaler.fit(all_features.reshape(-1, all_features.shape[-1]))
            
            # Target scalers for each horizon
            for horizon in self.config.forecast_horizons:
                horizon_key = f'horizon_{horizon}'
                all_targets = np.concatenate([seq['targets'][horizon_key] for seq in sequences])
                scaler = StandardScaler()
                scaler.fit(all_targets.reshape(-1, 1))
                self.target_scalers[horizon_key] = scaler
        
        # Apply scaling
        scaled_sequences = []
        for seq in sequences:
            scaled_seq = seq.copy()
            
            # Scale features
            scaled_features = self.feature_scaler.transform(
                seq['features'].reshape(-1, seq['features'].shape[-1])
            ).reshape(seq['features'].shape)
            scaled_seq['features'] = scaled_features
            
            # Scale targets
            scaled_targets = {}
            for horizon in self.config.forecast_horizons:
                horizon_key = f'horizon_{horizon}'
                if horizon_key in seq['targets']:
                    scaled_targets[horizon_key] = self.target_scalers[horizon_key].transform(
                        seq['targets'][horizon_key].reshape(-1, 1)
                    ).flatten()
            scaled_seq['targets'] = scaled_targets
            
            scaled_sequences.append(scaled_seq)
        
        data['sequences'] = scaled_sequences
        return data
    
    def inverse_transform_predictions(self, 
                                    predictions: np.ndarray,
                                    horizon: int) -> np.ndarray:
        """Inverse transform scaled predictions back to original scale"""
        horizon_key = f'horizon_{horizon}'
        if horizon_key not in self.target_scalers:
            raise ValueError(f"No scaler found for horizon {horizon}")
        
        return self.target_scalers[horizon_key].inverse_transform(
            predictions.reshape(-1, 1)
        ).flatten()
    
    def validate_preprocessing_output(self, processed_data: Dict[str, Any]) -> bool:
        """Validate preprocessing output for quality and consistency"""
        try:
            # Check required keys
            required_keys = ['train_data', 'val_data', 'test_data', 'feature_columns', 'metadata']
            for key in required_keys:
                if key not in processed_data:
                    logger.error(f"Missing required key: {key}")
                    return False
            
            # Check data splits have sequences
            for split in ['train_data', 'val_data', 'test_data']:
                if not processed_data[split]['sequences']:
                    logger.error(f"No sequences found in {split}")
                    return False
            
            # Check feature dimensions
            train_sequences = processed_data['train_data']['sequences']
            expected_features = len(processed_data['feature_columns'])
            
            for i, seq in enumerate(train_sequences[:3]):  # Check first 3 sequences
                if seq['features'].shape[1] != expected_features:
                    logger.error(f"Feature dimension mismatch in sequence {i}")
                    return False
            
            logger.info("Preprocessing validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Preprocessing validation error: {str(e)}")
            return False