"""
Linear Probing for Fast Adaptation

Implements fast adaptation techniques by freezing the backbone and only
training the prediction head on manufacturing data.
"""

import logging
from typing import Dict, Any, List, Optional
from transformers import PatchTSTForPrediction, TrainingArguments
from dataclasses import dataclass

from ..stability import StabilizedTrainer

logger = logging.getLogger(__name__)

@dataclass
class LinearProbingConfig:
    """Configuration for linear probing training"""
    learning_rate: float = 1e-3  # Higher LR for head-only training
    batch_size: int = 32
    max_epochs: int = 10  # Fewer epochs needed
    warmup_ratio: float = 0.1
    weight_decay: float = 0.01
    gradient_clip_norm: float = 1.0
    early_stopping_patience: int = 5
    freeze_backbone: bool = True
    freeze_embeddings: bool = True
    trainable_layers: List[str] = None  # Specific layers to keep trainable

def freeze_backbone_parameters(
    model: PatchTSTForPrediction,
    freeze_embeddings: bool = True,
    freeze_encoder: bool = True,
    freeze_normalization: bool = False,
    trainable_layers: Optional[List[str]] = None
) -> int:
    """
    Freeze backbone parameters for linear probing
    
    Args:
        model: PatchTST model to freeze
        freeze_embeddings: Whether to freeze embedding layers
        freeze_encoder: Whether to freeze encoder layers
        freeze_normalization: Whether to freeze normalization layers
        trainable_layers: Specific layer names to keep trainable
        
    Returns:
        Number of parameters frozen
    """
    frozen_params = 0
    total_params = 0
    
    trainable_layers = trainable_layers or []
    
    for name, param in model.named_parameters():
        total_params += param.numel()
        
        # Keep specific layers trainable if specified
        if any(layer_name in name for layer_name in trainable_layers):
            param.requires_grad = True
            continue
        
        # Freeze embedding layers
        if freeze_embeddings and ('embed' in name.lower() or 'patch' in name.lower()):
            param.requires_grad = False
            frozen_params += param.numel()
            continue
        
        # Freeze encoder layers
        if freeze_encoder and ('encoder' in name.lower() or 'transformer' in name.lower()):
            param.requires_grad = False
            frozen_params += param.numel()
            continue
        
        # Freeze normalization layers
        if freeze_normalization and ('norm' in name.lower() or 'layer_norm' in name.lower()):
            param.requires_grad = False
            frozen_params += param.numel()
            continue
        
        # Keep prediction head trainable by default
        if 'head' in name.lower() or 'prediction' in name.lower() or 'output' in name.lower():
            param.requires_grad = True
            continue
    
    trainable_params = total_params - frozen_params
    freeze_percentage = (frozen_params / total_params) * 100
    
    logger.info(f"Frozen {frozen_params:,} parameters ({freeze_percentage:.1f}%)")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    
    return frozen_params

def unfreeze_top_layers(
    model: PatchTSTForPrediction,
    num_layers: int = 1
) -> int:
    """
    Unfreeze top N encoder layers for gradual unfreezing
    
    Args:
        model: Model to unfreeze layers in
        num_layers: Number of top layers to unfreeze
        
    Returns:
        Number of parameters unfrozen
    """
    unfrozen_params = 0
    
    # Find encoder layers (model-specific logic)
    encoder_layers = []
    for name, param in model.named_parameters():
        if 'encoder' in name.lower() and 'layer' in name.lower():
            # Extract layer number
            try:
                layer_parts = name.split('.')
                for i, part in enumerate(layer_parts):
                    if 'layer' in part.lower() or part.isdigit():
                        layer_num = int(part) if part.isdigit() else int(layer_parts[i+1])
                        encoder_layers.append((layer_num, name, param))
                        break
            except (ValueError, IndexError):
                continue
    
    # Sort by layer number (descending to get top layers)
    encoder_layers.sort(key=lambda x: x[0], reverse=True)
    
    # Unfreeze top N layers
    unfrozen_layers = set()
    for layer_num, name, param in encoder_layers:
        if len(unfrozen_layers) < num_layers:
            unfrozen_layers.add(layer_num)
            param.requires_grad = True
            unfrozen_params += param.numel()
            logger.debug(f"Unfroze parameter: {name}")
    
    logger.info(f"Unfroze top {len(unfrozen_layers)} encoder layers ({unfrozen_params:,} parameters)")
    
    return unfrozen_params

class LinearProbingTrainer:
    """
    Trainer for linear probing with manufacturing data
    
    Implements fast adaptation by freezing backbone and training only
    the prediction head with manufacturing-specific data.
    """
    
    def __init__(self, 
                 model: PatchTSTForPrediction,
                 config: LinearProbingConfig):
        """
        Initialize linear probing trainer
        
        Args:
            model: Pre-trained PatchTST model
            config: Linear probing configuration
        """
        self.model = model
        self.config = config
        self.original_state = None  # Store original parameter states
        
        # Freeze backbone if configured
        if config.freeze_backbone:
            self.freeze_model_backbone()
    
    def freeze_model_backbone(self) -> None:
        """Freeze model backbone for linear probing"""
        # Store original parameter states for restoration
        self.original_state = {
            name: param.requires_grad 
            for name, param in self.model.named_parameters()
        }
        
        # Freeze backbone parameters
        frozen_count = freeze_backbone_parameters(
            self.model,
            freeze_embeddings=self.config.freeze_embeddings,
            freeze_encoder=True,
            freeze_normalization=False,
            trainable_layers=self.config.trainable_layers
        )
        
        logger.info(f"Frozen {frozen_count:,} backbone parameters for linear probing")
    
    def train_linear_probe(self,
                          train_dataset,
                          val_dataset,
                          output_dir: str = "./linear_probe_models/") -> Dict[str, Any]:
        """
        Train linear probe on manufacturing data
        
        Args:
            train_dataset: Training dataset
            val_dataset: Validation dataset  
            output_dir: Directory to save trained model
            
        Returns:
            Training results and metrics
        """
        logger.info("Starting linear probing training")
        
        # Create training arguments optimized for linear probing
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=self.config.max_epochs,
            per_device_train_batch_size=self.config.batch_size,
            per_device_eval_batch_size=self.config.batch_size,
            learning_rate=self.config.learning_rate,
            warmup_ratio=self.config.warmup_ratio,
            weight_decay=self.config.weight_decay,
            logging_steps=10,
            eval_strategy="epoch",
            save_strategy="epoch",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            fp16=True,  # Use mixed precision for efficiency
            dataloader_num_workers=2,
            save_total_limit=3,
            report_to=None,
            seed=42
        )
        
        # Use stabilized trainer for reliability
        trainer = StabilizedTrainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            gradient_clip_norm=self.config.gradient_clip_norm,
            adaptive_clipping=True,
            mixed_precision=True,
            stability_monitoring=True
        )
        
        # Train the model
        training_output = trainer.train()
        
        # Evaluate final performance
        eval_results = trainer.evaluate()
        
        # Prepare results
        results = {
            'training_loss': training_output.training_loss,
            'eval_loss': eval_results.get('eval_loss', float('inf')),
            'training_time': training_output.metrics.get('train_runtime', 0),
            'epochs_trained': training_output.global_step,
            'linear_probing_config': self.config.__dict__,
            'frozen_parameters': self._count_frozen_parameters(),
            'trainable_parameters': self._count_trainable_parameters()
        }
        
        logger.info(f"Linear probing completed. Final eval loss: {results['eval_loss']:.4f}")
        
        return results
    
    def gradual_unfreezing(self,
                          train_dataset,
                          val_dataset,
                          num_layers_per_stage: int = 1,
                          max_stages: int = 3,
                          output_dir: str = "./gradual_unfreeze_models/") -> List[Dict[str, Any]]:
        """
        Perform gradual unfreezing of model layers
        
        Args:
            train_dataset: Training dataset
            val_dataset: Validation dataset
            num_layers_per_stage: Number of layers to unfreeze per stage
            max_stages: Maximum number of unfreezing stages
            output_dir: Directory to save models
            
        Returns:
            List of results for each stage
        """
        logger.info(f"Starting gradual unfreezing with {max_stages} stages")
        
        stage_results = []
        
        for stage in range(max_stages):
            logger.info(f"Gradual unfreezing stage {stage + 1}/{max_stages}")
            
            # Unfreeze additional layers (except first stage)
            if stage > 0:
                unfrozen_count = unfreeze_top_layers(self.model, num_layers_per_stage)
                logger.info(f"Stage {stage + 1}: Unfroze {unfrozen_count:,} additional parameters")
            
            # Adjust learning rate for later stages (lower LR for unfrozen layers)
            stage_lr = self.config.learning_rate * (0.5 ** stage)
            
            # Create stage-specific config
            stage_config = LinearProbingConfig(
                learning_rate=stage_lr,
                batch_size=self.config.batch_size,
                max_epochs=max(5, self.config.max_epochs // 2),  # Fewer epochs per stage
                warmup_ratio=0.05,  # Shorter warmup for later stages
                weight_decay=self.config.weight_decay,
                gradient_clip_norm=self.config.gradient_clip_norm,
                early_stopping_patience=3,
                freeze_backbone=False  # Don't re-freeze
            )
            
            # Train stage
            stage_output_dir = f"{output_dir}/stage_{stage + 1}"
            stage_trainer = LinearProbingTrainer(self.model, stage_config)
            stage_trainer.original_state = self.original_state  # Preserve original state
            
            stage_results_dict = stage_trainer.train_linear_probe(
                train_dataset, val_dataset, stage_output_dir
            )
            
            stage_results_dict['stage'] = stage + 1
            stage_results_dict['stage_learning_rate'] = stage_lr
            stage_results.append(stage_results_dict)
            
            logger.info(f"Stage {stage + 1} completed. Eval loss: {stage_results_dict['eval_loss']:.4f}")
        
        # Log overall results
        final_eval_loss = stage_results[-1]['eval_loss']
        initial_eval_loss = stage_results[0]['eval_loss']
        improvement = ((initial_eval_loss - final_eval_loss) / initial_eval_loss) * 100
        
        logger.info("Gradual unfreezing completed:")
        logger.info(f"  Initial eval loss: {initial_eval_loss:.4f}")
        logger.info(f"  Final eval loss: {final_eval_loss:.4f}")
        logger.info(f"  Improvement: {improvement:.1f}%")
        
        return stage_results
    
    def restore_original_parameters(self) -> None:
        """Restore original parameter freeze states"""
        if self.original_state is not None:
            for name, param in self.model.named_parameters():
                if name in self.original_state:
                    param.requires_grad = self.original_state[name]
            logger.info("Restored original parameter freeze states")
    
    def _count_frozen_parameters(self) -> int:
        """Count number of frozen parameters"""
        return sum(
            param.numel() for param in self.model.parameters() 
            if not param.requires_grad
        )
    
    def _count_trainable_parameters(self) -> int:
        """Count number of trainable parameters"""
        return sum(
            param.numel() for param in self.model.parameters() 
            if param.requires_grad
        )
    
    def get_parameter_summary(self) -> Dict[str, Any]:
        """Get summary of parameter freeze states"""
        total_params = sum(param.numel() for param in self.model.parameters())
        frozen_params = self._count_frozen_parameters()
        trainable_params = self._count_trainable_parameters()
        
        return {
            'total_parameters': total_params,
            'frozen_parameters': frozen_params,
            'trainable_parameters': trainable_params,
            'freeze_percentage': (frozen_params / total_params) * 100,
            'trainable_percentage': (trainable_params / total_params) * 100
        }

def create_linear_probing_config(
    adaptation_strategy: str = "conservative",
    manufacturing_specific: bool = True
) -> LinearProbingConfig:
    """
    Create linear probing configuration for different strategies
    
    Args:
        adaptation_strategy: "conservative", "moderate", or "aggressive"
        manufacturing_specific: Whether to use manufacturing-specific settings
        
    Returns:
        Configured LinearProbingConfig
    """
    if adaptation_strategy == "conservative":
        return LinearProbingConfig(
            learning_rate=5e-4,  # Lower learning rate
            batch_size=16,       # Smaller batch size
            max_epochs=5,        # Fewer epochs
            warmup_ratio=0.2,    # Longer warmup
            weight_decay=0.05,   # Higher regularization
            gradient_clip_norm=0.5,  # Aggressive clipping
            early_stopping_patience=3,
            freeze_backbone=True,
            freeze_embeddings=True
        )
    
    elif adaptation_strategy == "moderate":
        return LinearProbingConfig(
            learning_rate=1e-3,
            batch_size=32,
            max_epochs=10,
            warmup_ratio=0.1,
            weight_decay=0.01,
            gradient_clip_norm=1.0,
            early_stopping_patience=5,
            freeze_backbone=True,
            freeze_embeddings=True,
            trainable_layers=['norm', 'head']  # Allow some normalization layers
        )
    
    elif adaptation_strategy == "aggressive":
        return LinearProbingConfig(
            learning_rate=2e-3,  # Higher learning rate
            batch_size=64,       # Larger batch size
            max_epochs=15,       # More epochs
            warmup_ratio=0.05,   # Shorter warmup
            weight_decay=0.001,  # Lower regularization
            gradient_clip_norm=2.0,  # Less aggressive clipping
            early_stopping_patience=7,
            freeze_backbone=True,
            freeze_embeddings=False,  # Allow embedding adaptation
            trainable_layers=['norm', 'head', 'embed']
        )
    
    else:
        raise ValueError(f"Unknown adaptation strategy: {adaptation_strategy}")

def quick_linear_probe(
    model: PatchTSTForPrediction,
    train_dataset,
    val_dataset,
    adaptation_strategy: str = "moderate"
) -> Dict[str, Any]:
    """
    Quick linear probing for fast evaluation
    
    Args:
        model: Pre-trained model
        train_dataset: Training data
        val_dataset: Validation data
        adaptation_strategy: Adaptation strategy to use
        
    Returns:
        Training results
    """
    config = create_linear_probing_config(adaptation_strategy)
    config.max_epochs = 3  # Very quick training
    
    trainer = LinearProbingTrainer(model, config)
    results = trainer.train_linear_probe(train_dataset, val_dataset)
    
    return results