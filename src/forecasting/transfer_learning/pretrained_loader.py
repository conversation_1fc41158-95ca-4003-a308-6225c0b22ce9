"""
Pre-trained Model Loader

Handles loading and adaptation of pre-trained PatchTST models,
particularly IBM Granite time series models, for manufacturing forecasting.
"""

import torch
import logging
from typing import Optional, Dict, Any, Tuple
from transformers import PatchTSTForPrediction
from pathlib import Path
import json

logger = logging.getLogger(__name__)

def load_granite_model(
    model_name: str = "ibm-granite/granite-timeseries-patchtst",
    cache_dir: Optional[str] = None,
    force_download: bool = False
) -> Optional[PatchTSTForPrediction]:
    """
    Load IBM Granite pre-trained PatchTST model
    
    Args:
        model_name: HuggingFace model identifier
        cache_dir: Directory to cache downloaded models
        force_download: Force re-download of model
        
    Returns:
        Loaded PatchTST model or None if loading fails
    """
    try:
        logger.info(f"Loading pre-trained model: {model_name}")
        
        # Load the pre-trained model
        model = PatchTSTForPrediction.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            force_download=force_download,
            trust_remote_code=True  # May be needed for custom models
        )
        
        # Log model information
        if hasattr(model, 'config'):
            config = model.config
            logger.info("Loaded model with config:")
            logger.info(f"  - Input channels: {getattr(config, 'num_input_channels', 'N/A')}")
            logger.info(f"  - Context length: {getattr(config, 'context_length', 'N/A')}")
            logger.info(f"  - Prediction length: {getattr(config, 'prediction_length', 'N/A')}")
            logger.info(f"  - d_model: {getattr(config, 'd_model', 'N/A')}")
        
        # Set model to evaluation mode initially
        model.eval()
        
        logger.info("Successfully loaded pre-trained model")
        return model
        
    except Exception as e:
        logger.error(f"Failed to load pre-trained model {model_name}: {str(e)}")
        return None

def validate_model_compatibility(
    pretrained_model: PatchTSTForPrediction,
    target_config: Dict[str, Any]
) -> Tuple[bool, Dict[str, Any]]:
    """
    Validate compatibility between pre-trained model and target configuration
    
    Args:
        pretrained_model: Loaded pre-trained model
        target_config: Target configuration for manufacturing data
        
    Returns:
        Tuple of (is_compatible, compatibility_report)
    """
    compatibility_report = {
        'compatible': False,
        'issues': [],
        'adaptations_needed': [],
        'model_info': {},
        'target_info': {}
    }
    
    try:
        # Extract model configuration
        model_config = pretrained_model.config
        
        # Store model info
        compatibility_report['model_info'] = {
            'num_input_channels': getattr(model_config, 'num_input_channels', None),
            'context_length': getattr(model_config, 'context_length', None),
            'prediction_length': getattr(model_config, 'prediction_length', None),
            'd_model': getattr(model_config, 'd_model', None),
            'num_attention_heads': getattr(model_config, 'num_attention_heads', None),
            'num_hidden_layers': getattr(model_config, 'num_hidden_layers', None)
        }
        
        # Store target info
        compatibility_report['target_info'] = target_config
        
        # Check input channels compatibility
        model_channels = getattr(model_config, 'num_input_channels', 1)
        target_channels = target_config.get('num_input_channels', 1)
        
        if model_channels != target_channels:
            compatibility_report['issues'].append(
                f"Input channel mismatch: model has {model_channels}, need {target_channels}"
            )
            compatibility_report['adaptations_needed'].append('input_projection_layer')
        
        # Check context length compatibility
        model_context = getattr(model_config, 'context_length', 32)
        target_context = target_config.get('context_length', 32)
        
        if model_context != target_context:
            compatibility_report['issues'].append(
                f"Context length mismatch: model has {model_context}, need {target_context}"
            )
            compatibility_report['adaptations_needed'].append('context_length_adaptation')
        
        # Check prediction length compatibility  
        model_pred_len = getattr(model_config, 'prediction_length', 24)
        target_pred_len = target_config.get('prediction_length', 24)
        
        if model_pred_len != target_pred_len:
            compatibility_report['issues'].append(
                f"Prediction length mismatch: model has {model_pred_len}, need {target_pred_len}"
            )
            compatibility_report['adaptations_needed'].append('prediction_head_adaptation')
        
        # Check architecture size compatibility (for stability)
        model_d_model = getattr(model_config, 'd_model', 128)
        target_d_model = target_config.get('d_model', 128)
        
        if abs(model_d_model - target_d_model) > 64:  # Significant difference
            compatibility_report['issues'].append(
                f"Model dimension difference: model has {model_d_model}, target {target_d_model}"
            )
            compatibility_report['adaptations_needed'].append('dimension_adaptation')
        
        # Determine overall compatibility
        # Compatible if no major structural issues
        major_issues = [
            'input_projection_layer',
            'context_length_adaptation', 
            'prediction_head_adaptation'
        ]
        
        has_major_issues = any(adapt in compatibility_report['adaptations_needed'] 
                              for adapt in major_issues)
        
        compatibility_report['compatible'] = not has_major_issues
        
        if compatibility_report['compatible']:
            logger.info("Pre-trained model is compatible with target configuration")
        else:
            logger.warning("Pre-trained model requires adaptations for target configuration")
            for issue in compatibility_report['issues']:
                logger.warning(f"  - {issue}")
        
        return compatibility_report['compatible'], compatibility_report
        
    except Exception as e:
        logger.error(f"Error validating model compatibility: {str(e)}")
        compatibility_report['issues'].append(f"Validation error: {str(e)}")
        return False, compatibility_report

def adapt_model_for_manufacturing(
    pretrained_model: PatchTSTForPrediction,
    target_config: Dict[str, Any],
    adaptation_strategy: str = "minimal"
) -> PatchTSTForPrediction:
    """
    Adapt pre-trained model for manufacturing data requirements
    
    Args:
        pretrained_model: Pre-trained model to adapt
        target_config: Target configuration for manufacturing
        adaptation_strategy: Strategy for adaptation ("minimal", "moderate", "full")
        
    Returns:
        Adapted model
    """
    logger.info(f"Adapting pre-trained model with strategy: {adaptation_strategy}")
    
    # Clone the model to avoid modifying the original
    adapted_model = PatchTSTForPrediction(pretrained_model.config)
    adapted_model.load_state_dict(pretrained_model.state_dict())
    
    try:
        # Get compatibility analysis
        is_compatible, compatibility_report = validate_model_compatibility(
            pretrained_model, target_config
        )
        
        adaptations_needed = compatibility_report.get('adaptations_needed', [])
        
        if not adaptations_needed:
            logger.info("No adaptations needed for manufacturing data")
            return adapted_model
        
        # Adapt input projection if needed
        if 'input_projection_layer' in adaptations_needed:
            adapted_model = _adapt_input_channels(adapted_model, target_config)
        
        # Adapt prediction head if needed
        if 'prediction_head_adaptation' in adaptations_needed:
            adapted_model = _adapt_prediction_head(adapted_model, target_config)
        
        # Adapt context length if needed (requires more complex changes)
        if 'context_length_adaptation' in adaptations_needed:
            logger.warning("Context length adaptation not implemented - using positional interpolation")
            adapted_model = _adapt_context_length(adapted_model, target_config)
        
        logger.info("Model adaptation completed successfully")
        return adapted_model
        
    except Exception as e:
        logger.error(f"Error adapting model: {str(e)}")
        # Return original model if adaptation fails
        return pretrained_model

def _adapt_input_channels(
    model: PatchTSTForPrediction,
    target_config: Dict[str, Any]
) -> PatchTSTForPrediction:
    """Adapt model input channels for different number of features"""
    try:
        target_channels = target_config.get('num_input_channels', 1)
        current_channels = model.config.num_input_channels
        
        logger.info(f"Adapting input channels: {current_channels} -> {target_channels}")
        
        # For PatchTST, we need to adapt the input embedding layer
        # This is a simplified adaptation - in practice, might need more sophisticated approach
        if hasattr(model, 'model') and hasattr(model.model, 'encoder'):
            # Update config
            model.config.num_input_channels = target_channels
            
            # Note: This is a simplified adaptation
            # In practice, you might need to reinitialize or interpolate embedding weights
            logger.warning("Input channel adaptation is simplified - consider retraining")
        
        return model
        
    except Exception as e:
        logger.error(f"Error adapting input channels: {str(e)}")
        raise

def _adapt_prediction_head(
    model: PatchTSTForPrediction,
    target_config: Dict[str, Any]
) -> PatchTSTForPrediction:
    """Adapt model prediction head for different prediction length"""
    try:
        target_pred_len = target_config.get('prediction_length', 24)
        current_pred_len = model.config.prediction_length
        
        logger.info(f"Adapting prediction length: {current_pred_len} -> {target_pred_len}")
        
        # Update config
        model.config.prediction_length = target_pred_len
        
        # For prediction head adaptation, we typically need to reinitialize
        # the final linear layer to match the new prediction length
        if hasattr(model, 'prediction_head'):
            # This would need to be implemented based on the specific model architecture
            logger.warning("Prediction head adaptation requires model-specific implementation")
        
        return model
        
    except Exception as e:
        logger.error(f"Error adapting prediction head: {str(e)}")
        raise

def _adapt_context_length(
    model: PatchTSTForPrediction,
    target_config: Dict[str, Any]
) -> PatchTSTForPrediction:
    """Adapt model for different context length"""
    try:
        target_context = target_config.get('context_length', 32)
        current_context = model.config.context_length
        
        logger.info(f"Adapting context length: {current_context} -> {target_context}")
        
        # Update config
        model.config.context_length = target_context
        
        # Context length adaptation often requires positional encoding adjustments
        # This is a placeholder for more sophisticated adaptation
        logger.warning("Context length adaptation is simplified - may need positional encoding updates")
        
        return model
        
    except Exception as e:
        logger.error(f"Error adapting context length: {str(e)}")
        raise

def save_adapted_model(
    model: PatchTSTForPrediction,
    save_path: str,
    adaptation_info: Dict[str, Any]
) -> None:
    """
    Save adapted model with adaptation metadata
    
    Args:
        model: Adapted model to save
        save_path: Path to save the model
        adaptation_info: Information about adaptations performed
    """
    try:
        save_dir = Path(save_path)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # Save the model
        model.save_pretrained(save_dir)
        
        # Save adaptation metadata
        metadata = {
            'adaptation_info': adaptation_info,
            'model_config': model.config.to_dict() if hasattr(model.config, 'to_dict') else {},
            'adaptation_timestamp': str(torch.datetime.now())
        }
        
        metadata_path = save_dir / 'adaptation_metadata.json'
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Adapted model saved to {save_path}")
        
    except Exception as e:
        logger.error(f"Error saving adapted model: {str(e)}")
        raise

def load_adapted_model(load_path: str) -> Tuple[Optional[PatchTSTForPrediction], Dict[str, Any]]:
    """
    Load adapted model with adaptation metadata
    
    Args:
        load_path: Path to load the adapted model from
        
    Returns:
        Tuple of (loaded_model, adaptation_metadata)
    """
    try:
        load_dir = Path(load_path)
        
        # Load the model
        model = PatchTSTForPrediction.from_pretrained(load_dir)
        
        # Load adaptation metadata
        metadata_path = load_dir / 'adaptation_metadata.json'
        adaptation_metadata = {}
        
        if metadata_path.exists():
            with open(metadata_path, 'r') as f:
                adaptation_metadata = json.load(f)
        
        logger.info(f"Loaded adapted model from {load_path}")
        
        return model, adaptation_metadata
        
    except Exception as e:
        logger.error(f"Error loading adapted model: {str(e)}")
        return None, {}

def quick_compatibility_check(
    model_name: str = "ibm-granite/granite-timeseries-patchtst",
    num_features: int = 6,
    context_length: int = 240,
    prediction_length: int = 60
) -> bool:
    """
    Quick compatibility check for common manufacturing configurations
    
    Args:
        model_name: Pre-trained model to check
        num_features: Number of input features  
        context_length: Historical context length
        prediction_length: Forecast horizon
        
    Returns:
        True if compatible with minimal adaptation
    """
    try:
        # Load model for quick check
        model = load_granite_model(model_name)
        if model is None:
            return False
        
        # Define target configuration
        target_config = {
            'num_input_channels': num_features,
            'context_length': context_length,
            'prediction_length': prediction_length
        }
        
        # Check compatibility
        is_compatible, _ = validate_model_compatibility(model, target_config)
        
        return is_compatible
        
    except Exception as e:
        logger.error(f"Error in quick compatibility check: {str(e)}")
        return False