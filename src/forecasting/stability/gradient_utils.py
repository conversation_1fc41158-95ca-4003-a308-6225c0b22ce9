"""
Gradient Stabilization Utilities

Implements gradient clipping, monitoring, and visualization utilities for stable
PatchTST training with manufacturing data.
"""

import torch.nn.utils as nn_utils
import numpy as np
import logging
from typing import Dict, List, Optional, Any, Callable
from functools import wraps
import matplotlib.pyplot as plt
from pathlib import Path

logger = logging.getLogger(__name__)

class GradientClipper:
    """
    Advanced gradient clipping with norm monitoring and adaptive thresholds
    
    Implements multiple gradient clipping strategies to prevent gradient explosions
    and ensure stable training for manufacturing time series models.
    """
    
    def __init__(self, 
                 max_norm: float = 1.0,
                 norm_type: float = 2.0,
                 adaptive: bool = True,
                 warmup_steps: int = 100,
                 history_size: int = 1000):
        """
        Initialize gradient clipper
        
        Args:
            max_norm: Maximum allowed gradient norm
            norm_type: Type of norm to calculate (2.0 for L2 norm)
            adaptive: Whether to use adaptive clipping based on gradient history
            warmup_steps: Number of steps for warmup phase
            history_size: Size of gradient norm history buffer
        """
        self.max_norm = max_norm
        self.norm_type = norm_type
        self.adaptive = adaptive
        self.warmup_steps = warmup_steps
        self.history_size = history_size
        
        # Gradient norm tracking
        self.gradient_norms: List[float] = []
        self.step_count = 0
        
        # Adaptive clipping parameters
        self.adaptive_threshold = max_norm
        self.moving_avg_window = 50
        
    def clip_gradients(self, parameters, clip_norm: Optional[float] = None) -> float:
        """
        Clip gradients with optional adaptive threshold
        
        Args:
            parameters: Model parameters to clip
            clip_norm: Override norm threshold (optional)
            
        Returns:
            Actual gradient norm before clipping
        """
        # Calculate current gradient norm
        total_norm = nn_utils.clip_grad_norm_(
            parameters, 
            float('inf'),  # Don't clip yet, just calculate norm
            norm_type=self.norm_type
        )
        
        # Update step count and history
        self.step_count += 1
        self.gradient_norms.append(float(total_norm))
        
        # Keep history bounded
        if len(self.gradient_norms) > self.history_size:
            self.gradient_norms = self.gradient_norms[-self.history_size:]
        
        # Determine clipping threshold
        clip_threshold = clip_norm if clip_norm is not None else self._get_clip_threshold()
        
        # Apply clipping
        if total_norm > clip_threshold:
            nn_utils.clip_grad_norm_(parameters, clip_threshold, norm_type=self.norm_type)
            logger.debug(f"Clipped gradients: norm {total_norm:.4f} -> {clip_threshold:.4f}")
        
        return float(total_norm)
    
    def _get_clip_threshold(self) -> float:
        """Calculate adaptive or fixed clipping threshold"""
        if not self.adaptive or self.step_count < self.warmup_steps:
            return self.max_norm
        
        # Use moving average to adapt threshold
        recent_norms = self.gradient_norms[-self.moving_avg_window:]
        if len(recent_norms) < 10:  # Need minimum history
            return self.max_norm
        
        # Calculate 95th percentile as adaptive threshold
        percentile_95 = np.percentile(recent_norms, 95)
        
        # Ensure threshold doesn't exceed maximum or fall below minimum
        self.adaptive_threshold = max(
            self.max_norm * 0.1,  # Minimum threshold
            min(percentile_95, self.max_norm * 2.0)  # Maximum threshold
        )
        
        return self.adaptive_threshold
    
    def get_gradient_statistics(self) -> Dict[str, float]:
        """Get comprehensive gradient statistics"""
        if not self.gradient_norms:
            return {
                'mean_norm': 0.0,
                'max_norm': 0.0,
                'min_norm': 0.0,
                'std_norm': 0.0,
                'recent_mean': 0.0,
                'clipping_frequency': 0.0
            }
        
        norms = np.array(self.gradient_norms)
        recent_norms = norms[-100:] if len(norms) > 100 else norms
        
        # Calculate clipping frequency
        threshold = self._get_clip_threshold()
        clipping_frequency = np.mean(norms > threshold) * 100
        
        return {
            'mean_norm': float(np.mean(norms)),
            'max_norm': float(np.max(norms)),
            'min_norm': float(np.min(norms)),
            'std_norm': float(np.std(norms)),
            'recent_mean': float(np.mean(recent_norms)),
            'clipping_frequency': clipping_frequency,
            'adaptive_threshold': self.adaptive_threshold,
            'total_steps': len(norms)
        }
    
    def visualize_gradient_norms(self, save_path: Optional[str] = None) -> None:
        """Visualize gradient norm evolution over training"""
        if len(self.gradient_norms) < 10:
            logger.warning("Insufficient gradient history for visualization")
            return
        
        plt.figure(figsize=(12, 6))
        
        # Plot gradient norms
        plt.subplot(1, 2, 1)
        plt.plot(self.gradient_norms, alpha=0.7, label='Gradient Norm')
        plt.axhline(y=self.max_norm, color='r', linestyle='--', label=f'Max Norm ({self.max_norm})')
        if self.adaptive:
            plt.axhline(y=self.adaptive_threshold, color='orange', linestyle='--', 
                       label=f'Adaptive Threshold ({self.adaptive_threshold:.3f})')
        plt.xlabel('Training Step')
        plt.ylabel('Gradient Norm')
        plt.title('Gradient Norm Evolution')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot gradient norm histogram
        plt.subplot(1, 2, 2)
        plt.hist(self.gradient_norms, bins=50, alpha=0.7, density=True)
        plt.axvline(x=self.max_norm, color='r', linestyle='--', label=f'Max Norm ({self.max_norm})')
        plt.xlabel('Gradient Norm')
        plt.ylabel('Density')
        plt.title('Gradient Norm Distribution')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            logger.info(f"Gradient visualization saved to {save_path}")
        else:
            plt.show()
        
        plt.close()

def gradient_norm_logging(log_frequency: int = 10):
    """
    Decorator for logging gradient norms during training
    
    Args:
        log_frequency: Log every N training steps
    """
    def decorator(training_function: Callable) -> Callable:
        @wraps(training_function)
        def wrapper(self, *args, **kwargs):
            # Check if object has gradient clipper
            if hasattr(self, 'gradient_clipper') and hasattr(self, 'state'):
                if self.state.global_step % log_frequency == 0:
                    stats = self.gradient_clipper.get_gradient_statistics()
                    
                    # Log key statistics
                    self.log({
                        "grad_norm_mean": stats['mean_norm'],
                        "grad_norm_max": stats['max_norm'],
                        "grad_norm_recent": stats['recent_mean'],
                        "grad_clipping_freq": stats['clipping_frequency'],
                        "grad_adaptive_threshold": stats.get('adaptive_threshold', 0.0)
                    })
                    
                    # Log warning if high clipping frequency
                    if stats['clipping_frequency'] > 50:
                        logger.warning(f"High gradient clipping frequency: {stats['clipping_frequency']:.1f}%")
            
            return training_function(self, *args, **kwargs)
        return wrapper
    return decorator

class GradientNormCallback:
    """
    Callback for monitoring gradient norms during HuggingFace training
    
    Integrates with HuggingFace Trainer callbacks to provide gradient monitoring.
    """
    
    def __init__(self, 
                 clipper: GradientClipper,
                 log_frequency: int = 10,
                 save_visualization: bool = True,
                 output_dir: str = "./gradient_logs"):
        """
        Initialize gradient norm callback
        
        Args:
            clipper: GradientClipper instance
            log_frequency: Frequency of logging (every N steps)
            save_visualization: Whether to save gradient visualizations
            output_dir: Directory for saving logs and visualizations
        """
        self.clipper = clipper
        self.log_frequency = log_frequency
        self.save_visualization = save_visualization
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def on_log(self, logs: Dict[str, Any]) -> None:
        """Called when training logs are generated"""
        # Add gradient statistics to logs
        stats = self.clipper.get_gradient_statistics()
        logs.update({
            "grad_norm_mean": stats['mean_norm'],
            "grad_norm_recent": stats['recent_mean'],
            "grad_clipping_freq": stats['clipping_frequency']
        })
    
    def on_train_end(self) -> None:
        """Called at the end of training"""
        if self.save_visualization and len(self.clipper.gradient_norms) > 10:
            viz_path = self.output_dir / "gradient_norms_final.png"
            self.clipper.visualize_gradient_norms(str(viz_path))
            
            # Save gradient statistics
            stats = self.clipper.get_gradient_statistics()
            stats_path = self.output_dir / "gradient_statistics.json"
            
            import json
            with open(stats_path, 'w') as f:
                json.dump(stats, f, indent=2)
            
            logger.info(f"Gradient monitoring results saved to {self.output_dir}")

def detect_gradient_explosion(gradient_norms: List[float], 
                             threshold_multiplier: float = 10.0,
                             window_size: int = 10) -> bool:
    """
    Detect potential gradient explosion based on recent gradient norms
    
    Args:
        gradient_norms: History of gradient norms
        threshold_multiplier: Multiplier for baseline to detect explosion
        window_size: Window size for calculating baseline
        
    Returns:
        True if gradient explosion is detected
    """
    if len(gradient_norms) < window_size * 2:
        return False
    
    # Calculate baseline from early training
    baseline_norms = gradient_norms[:window_size]
    baseline_mean = np.mean(baseline_norms)
    baseline_std = np.std(baseline_norms)
    
    # Check recent norms
    recent_norms = gradient_norms[-window_size:]
    recent_max = np.max(recent_norms)
    
    # Explosion threshold
    explosion_threshold = baseline_mean + threshold_multiplier * baseline_std
    
    return recent_max > explosion_threshold

def calculate_gradient_health_score(gradient_norms: List[float]) -> float:
    """
    Calculate a health score for gradient behavior (0-1, higher is better)
    
    Args:
        gradient_norms: History of gradient norms
        
    Returns:
        Health score between 0 and 1
    """
    if len(gradient_norms) < 10:
        return 0.5  # Neutral score for insufficient data
    
    norms = np.array(gradient_norms)
    
    # Factors for health score
    # 1. Stability (low variance relative to mean)
    stability_score = 1.0 / (1.0 + np.std(norms) / (np.mean(norms) + 1e-8))
    
    # 2. Reasonable magnitude (not too large or too small)
    mean_norm = np.mean(norms)
    magnitude_score = 1.0 - min(1.0, abs(np.log10(mean_norm + 1e-8)) / 2.0)
    
    # 3. Trend stability (no explosion or vanishing)
    if len(norms) > 50:
        early_mean = np.mean(norms[:25])
        late_mean = np.mean(norms[-25:])
        trend_ratio = late_mean / (early_mean + 1e-8)
        trend_score = 1.0 - min(1.0, abs(np.log10(trend_ratio)) / 1.0)
    else:
        trend_score = 1.0
    
    # Combine scores (weighted average)
    health_score = 0.4 * stability_score + 0.3 * magnitude_score + 0.3 * trend_score
    
    return float(np.clip(health_score, 0.0, 1.0))