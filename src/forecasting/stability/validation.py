"""
Manufacturing Stability Validation

Provides comprehensive validation for training stability, prediction consistency,
and manufacturing domain compliance for PatchTST models.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
import logging
from dataclasses import dataclass
from pathlib import Path
import json

logger = logging.getLogger(__name__)

@dataclass
class StabilityValidationResult:
    """Results from stability validation testing"""
    is_stable: bool
    stability_score: float
    gradient_health: Dict[str, float]
    prediction_variance: float
    manufacturing_compliance: Dict[str, bool]
    improvement_over_baseline: float
    validation_details: Dict[str, Any]
    recommendations: List[str]

class ManufacturingStabilityValidator:
    """
    Comprehensive validator for manufacturing PatchTST model stability
    
    Validates training stability, prediction consistency, gradient health,
    and manufacturing domain compliance.
    """
    
    def __init__(self, 
                 manufacturing_params: Dict[str, Any],
                 stability_thresholds: Optional[Dict[str, float]] = None):
        """
        Initialize stability validator
        
        Args:
            manufacturing_params: Manufacturing domain parameters
            stability_thresholds: Custom thresholds for stability metrics
        """
        self.manufacturing_params = manufacturing_params
        
        # Default stability thresholds
        default_thresholds = {
            'min_prediction_variance': 0.1,
            'max_gradient_norm': 1.0,
            'min_stability_score': 0.7,
            'max_loss_variance': 0.5,
            'min_improvement_threshold': 15.0,  # 15% improvement requirement
            'max_nan_ratio': 0.01,  # Max 1% NaN predictions
            'min_manufacturing_compliance': 0.95  # 95% compliance with domain constraints
        }
        
        self.thresholds = {**default_thresholds, **(stability_thresholds or {})}
        
        logger.info("ManufacturingStabilityValidator initialized")
    
    def validate_model_stability(self, 
                                model,
                                training_history: Dict[str, Any],
                                test_data: pd.DataFrame,
                                target_variable: str) -> StabilityValidationResult:
        """
        Comprehensive stability validation for a trained model
        
        Args:
            model: Trained PatchTST model
            training_history: Training history from model
            test_data: Test dataset for validation
            target_variable: Target variable being forecasted
            
        Returns:
            Comprehensive stability validation results
        """
        logger.info(f"Starting stability validation for {target_variable}")
        
        validation_details = {}
        recommendations = []
        
        # 1. Gradient health validation
        gradient_health = self._validate_gradient_health(training_history)
        validation_details['gradient_health'] = gradient_health
        
        # 2. Prediction consistency validation
        prediction_results = self._validate_prediction_consistency(
            model, test_data, target_variable
        )
        validation_details['prediction_consistency'] = prediction_results
        
        # 3. Training stability validation
        training_stability = self._validate_training_stability(training_history)
        validation_details['training_stability'] = training_stability
        
        # 4. Manufacturing compliance validation
        manufacturing_compliance = self._validate_manufacturing_compliance(
            prediction_results['predictions'], target_variable
        )
        validation_details['manufacturing_compliance'] = manufacturing_compliance
        
        # 5. Performance improvement validation
        improvement_score = self._validate_performance_improvement(training_history)
        validation_details['performance_improvement'] = improvement_score
        
        # Calculate overall stability score
        stability_score = self._calculate_overall_stability_score(
            gradient_health, prediction_results, training_stability, 
            manufacturing_compliance, improvement_score
        )
        
        # Determine if model is stable
        is_stable = self._determine_stability_status(
            gradient_health, prediction_results, training_stability,
            manufacturing_compliance, improvement_score
        )
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            gradient_health, prediction_results, training_stability,
            manufacturing_compliance, improvement_score
        )
        
        return StabilityValidationResult(
            is_stable=is_stable,
            stability_score=stability_score,
            gradient_health=gradient_health,
            prediction_variance=prediction_results.get('variance', 0.0),
            manufacturing_compliance=manufacturing_compliance,
            improvement_over_baseline=improvement_score.get('improvement_percentage', 0.0),
            validation_details=validation_details,
            recommendations=recommendations
        )
    
    def _validate_gradient_health(self, training_history: Dict[str, Any]) -> Dict[str, float]:
        """Validate gradient health during training"""
        gradient_health = {
            'mean_norm': 0.0,
            'max_norm': 0.0,
            'variance': 0.0,
            'health_score': 0.0,
            'explosion_detected': False
        }
        
        # Extract gradient norms from training history
        if 'gradient_norms' in training_history:
            norms = np.array(training_history['gradient_norms'])
            
            gradient_health['mean_norm'] = float(np.mean(norms))
            gradient_health['max_norm'] = float(np.max(norms))
            gradient_health['variance'] = float(np.var(norms))
            
            # Calculate health score
            if len(norms) > 10:
                stability = 1.0 / (1.0 + np.std(norms) / (np.mean(norms) + 1e-8))
                magnitude = 1.0 - min(1.0, abs(np.log10(np.mean(norms) + 1e-8)) / 2.0)
                gradient_health['health_score'] = float(0.6 * stability + 0.4 * magnitude)
            
            # Detect gradient explosion
            if len(norms) > 20:
                recent_max = np.max(norms[-10:])
                early_mean = np.mean(norms[:10])
                gradient_health['explosion_detected'] = recent_max > early_mean * 10
        
        return gradient_health
    
    def _validate_prediction_consistency(self, 
                                       model, 
                                       test_data: pd.DataFrame,
                                       target_variable: str) -> Dict[str, Any]:
        """Validate prediction consistency and variance"""
        predictions = []
        prediction_results = {
            'variance': 0.0,
            'mean_prediction': 0.0,
            'nan_ratio': 0.0,
            'consistency_score': 0.0,
            'predictions': []
        }
        
        try:
            # Generate multiple predictions on test windows
            num_windows = min(10, len(test_data) - 240)  # Use lookback window of 240
            
            for i in range(0, num_windows, 20):  # Sample every 20 steps
                window_data = test_data.iloc[i:i+240]
                if len(window_data) >= 240:
                    try:
                        forecast_result = model.forecast(
                            window_data, target_variable, 15, include_confidence_intervals=False
                        )
                        predictions.extend(forecast_result.forecast_values)
                    except Exception as e:
                        logger.warning(f"Prediction failed for window {i}: {e}")
                        continue
            
            if predictions:
                predictions_array = np.array(predictions)
                prediction_results['predictions'] = predictions
                
                # Calculate variance
                prediction_results['variance'] = float(np.var(predictions_array))
                prediction_results['mean_prediction'] = float(np.mean(predictions_array))
                
                # Check for NaN values
                nan_count = np.sum(np.isnan(predictions_array))
                prediction_results['nan_ratio'] = float(nan_count / len(predictions_array))
                
                # Calculate consistency score (higher variance is better for non-constant predictions)
                if prediction_results['variance'] > self.thresholds['min_prediction_variance']:
                    unique_predictions = len(np.unique(np.round(predictions_array, 3)))
                    consistency_score = min(1.0, unique_predictions / 10.0)  # Expect at least 10 unique values
                    prediction_results['consistency_score'] = consistency_score
                else:
                    prediction_results['consistency_score'] = 0.0
        
        except Exception as e:
            logger.error(f"Error in prediction consistency validation: {e}")
        
        return prediction_results
    
    def _validate_training_stability(self, training_history: Dict[str, Any]) -> Dict[str, float]:
        """Validate training process stability"""
        stability_results = {
            'loss_convergence': 0.0,
            'loss_variance': 0.0,
            'final_loss_finite': True,
            'training_completed': True,
            'stability_score': 0.0
        }
        
        # Check if training completed
        stability_results['training_completed'] = 'training_loss' in training_history
        
        if 'training_loss' in training_history:
            final_loss = training_history['training_loss']
            stability_results['final_loss_finite'] = np.isfinite(final_loss)
        
        # Analyze loss trajectory if available
        if 'losses' in training_history:
            losses = np.array(training_history['losses'])
            if len(losses) > 10:
                # Check convergence (decreasing trend)
                early_losses = losses[:len(losses)//3]
                late_losses = losses[-len(losses)//3:]
                improvement = np.mean(early_losses) - np.mean(late_losses)
                stability_results['loss_convergence'] = float(max(0.0, improvement))
                
                # Calculate loss variance (stability indicator)
                stability_results['loss_variance'] = float(np.var(losses))
                
                # Overall stability score
                convergence_score = min(1.0, improvement / (np.mean(early_losses) + 1e-8))
                variance_score = 1.0 / (1.0 + stability_results['loss_variance'])
                stability_results['stability_score'] = float(0.7 * convergence_score + 0.3 * variance_score)
        
        return stability_results
    
    def _validate_manufacturing_compliance(self, 
                                         predictions: List[float],
                                         target_variable: str) -> Dict[str, bool]:
        """Validate compliance with manufacturing domain constraints"""
        compliance = {
            'within_range': True,
            'reasonable_values': True,
            'no_extreme_outliers': True,
            'physical_feasibility': True
        }
        
        if not predictions:
            return {key: False for key in compliance.keys()}
        
        predictions_array = np.array(predictions)
        predictions_clean = predictions_array[~np.isnan(predictions_array)]
        
        if len(predictions_clean) == 0:
            return {key: False for key in compliance.keys()}
        
        # Check manufacturing range compliance
        if target_variable == "thickness_avg":
            range_limits = self.manufacturing_params.get('thickness_range', [2.0, 20.0])
            compliance['within_range'] = bool(
                np.all(predictions_clean >= range_limits[0] * 0.5) and
                np.all(predictions_clean <= range_limits[1] * 1.5)
            )
        elif target_variable == "speed":
            range_limits = self.manufacturing_params.get('speed_range', [0.0, 100.0])
            compliance['within_range'] = bool(
                np.all(predictions_clean >= 0.0) and
                np.all(predictions_clean <= range_limits[1] * 1.2)
            )
        elif "scrap" in target_variable.lower():
            compliance['within_range'] = bool(
                np.all(predictions_clean >= 0.0) and
                np.all(predictions_clean <= 1.0)
            )
        
        # Check for reasonable values (no extreme outliers)
        q99 = np.percentile(predictions_clean, 99)
        q1 = np.percentile(predictions_clean, 1)
        iqr = np.percentile(predictions_clean, 75) - np.percentile(predictions_clean, 25)
        
        compliance['no_extreme_outliers'] = bool(
            (q99 - q1) < 10 * iqr  # No values beyond 10x IQR
        )
        
        # Check physical feasibility (no negative values for inherently positive quantities)
        if target_variable in ["thickness_avg", "speed", "scrap_rate"]:
            compliance['physical_feasibility'] = bool(np.all(predictions_clean >= 0))
        
        # Reasonable value check (not constant or too extreme)
        std_pred = np.std(predictions_clean)
        mean_pred = np.mean(predictions_clean)
        cv = std_pred / (abs(mean_pred) + 1e-8)  # Coefficient of variation
        compliance['reasonable_values'] = bool(0.01 < cv < 2.0)  # Reasonable variation
        
        return compliance
    
    def _validate_performance_improvement(self, training_history: Dict[str, Any]) -> Dict[str, float]:
        """Validate that model achieves required performance improvement"""
        improvement_results = {
            'improvement_percentage': 0.0,
            'meets_15_percent_threshold': False,
            'baseline_comparison_available': False
        }
        
        # Check if baseline comparison is available
        if 'baseline_comparison' in training_history:
            improvement_results['baseline_comparison_available'] = True
            
            # Calculate average improvement across baselines
            improvements = []
            baseline_data = training_history['baseline_comparison']
            
            if 'baseline_results' in baseline_data:
                for horizon_key, horizon_data in baseline_data['baseline_results'].items():
                    if 'linear_regression' in horizon_data:
                        lr_improvement = horizon_data['linear_regression'].get('improvement_vs_patchtst', 0)
                        improvements.append(lr_improvement)
                    
                    if 'persistence' in horizon_data:
                        persist_improvement = horizon_data['persistence'].get('improvement_vs_patchtst', 0)
                        improvements.append(persist_improvement)
            
            if improvements:
                avg_improvement = np.mean(improvements)
                improvement_results['improvement_percentage'] = float(avg_improvement)
                improvement_results['meets_15_percent_threshold'] = bool(avg_improvement >= 15.0)
        
        return improvement_results
    
    def _calculate_overall_stability_score(self, 
                                         gradient_health: Dict[str, float],
                                         prediction_results: Dict[str, Any],
                                         training_stability: Dict[str, float],
                                         manufacturing_compliance: Dict[str, bool],
                                         improvement_score: Dict[str, float]) -> float:
        """Calculate overall stability score (0-1)"""
        
        # Component scores
        scores = []
        
        # Gradient health (25% weight)
        grad_score = gradient_health.get('health_score', 0.0)
        if gradient_health.get('explosion_detected', False):
            grad_score *= 0.5  # Penalize gradient explosions
        scores.append(0.25 * grad_score)
        
        # Prediction consistency (25% weight)
        pred_score = prediction_results.get('consistency_score', 0.0)
        if prediction_results.get('nan_ratio', 1.0) > 0.01:
            pred_score *= 0.5  # Penalize NaN predictions
        scores.append(0.25 * pred_score)
        
        # Training stability (20% weight)
        train_score = training_stability.get('stability_score', 0.0)
        if not training_stability.get('final_loss_finite', False):
            train_score = 0.0  # Zero score for infinite loss
        scores.append(0.20 * train_score)
        
        # Manufacturing compliance (20% weight)
        compliance_count = sum(manufacturing_compliance.values())
        compliance_score = compliance_count / len(manufacturing_compliance)
        scores.append(0.20 * compliance_score)
        
        # Performance improvement (10% weight)
        perf_score = 1.0 if improvement_score.get('meets_15_percent_threshold', False) else 0.0
        scores.append(0.10 * perf_score)
        
        return float(sum(scores))
    
    def _determine_stability_status(self, 
                                  gradient_health: Dict[str, float],
                                  prediction_results: Dict[str, Any],
                                  training_stability: Dict[str, float],
                                  manufacturing_compliance: Dict[str, bool],
                                  improvement_score: Dict[str, float]) -> bool:
        """Determine if model meets all stability requirements"""
        
        # Critical requirements (must all pass)
        critical_checks = [
            training_stability.get('final_loss_finite', False),  # Finite loss
            prediction_results.get('variance', 0.0) > self.thresholds['min_prediction_variance'],  # Non-constant predictions
            gradient_health.get('mean_norm', float('inf')) < self.thresholds['max_gradient_norm'],  # Reasonable gradients
            not gradient_health.get('explosion_detected', True),  # No gradient explosion
            prediction_results.get('nan_ratio', 1.0) < self.thresholds['max_nan_ratio'],  # Few NaN predictions
        ]
        
        # Manufacturing compliance (majority must pass)
        compliance_ratio = sum(manufacturing_compliance.values()) / len(manufacturing_compliance)
        manufacturing_ok = compliance_ratio >= self.thresholds['min_manufacturing_compliance']
        
        # Performance improvement requirement
        improvement_ok = improvement_score.get('meets_15_percent_threshold', False)
        
        return all(critical_checks) and manufacturing_ok and improvement_ok
    
    def _generate_recommendations(self, 
                                gradient_health: Dict[str, float],
                                prediction_results: Dict[str, Any],
                                training_stability: Dict[str, float],
                                manufacturing_compliance: Dict[str, bool],
                                improvement_score: Dict[str, float]) -> List[str]:
        """Generate actionable recommendations for improvement"""
        recommendations = []
        
        # Gradient-related recommendations
        if gradient_health.get('explosion_detected', False):
            recommendations.append("Reduce learning rate and increase gradient clipping to prevent gradient explosion")
        
        if gradient_health.get('mean_norm', 0.0) > 1.0:
            recommendations.append("Consider stronger gradient clipping (max_norm < 1.0)")
        
        if gradient_health.get('health_score', 1.0) < 0.5:
            recommendations.append("Improve gradient stability with learning rate warmup and weight decay")
        
        # Prediction-related recommendations
        if prediction_results.get('variance', 0.0) < self.thresholds['min_prediction_variance']:
            recommendations.append("Model produces constant predictions - check data preprocessing and model architecture")
        
        if prediction_results.get('nan_ratio', 0.0) > 0.01:
            recommendations.append("Address NaN predictions with input validation and model stability improvements")
        
        # Training stability recommendations
        if not training_stability.get('final_loss_finite', True):
            recommendations.append("CRITICAL: Fix infinite loss issue - check dataset target tensor shaping")
        
        if training_stability.get('loss_variance', 0.0) > 0.5:
            recommendations.append("Reduce training instability with smaller batch size and learning rate")
        
        # Manufacturing compliance recommendations
        if not manufacturing_compliance.get('within_range', True):
            recommendations.append("Predictions outside manufacturing range - add domain constraints to loss function")
        
        if not manufacturing_compliance.get('physical_feasibility', True):
            recommendations.append("Ensure predictions are physically feasible for manufacturing processes")
        
        # Performance improvement recommendations
        if not improvement_score.get('meets_15_percent_threshold', False):
            recommendations.append("Model doesn't meet 15% improvement requirement - consider architecture changes or transfer learning")
        
        return recommendations
    
    def save_validation_report(self, 
                             results: StabilityValidationResult,
                             output_path: str) -> None:
        """Save comprehensive validation report"""
        report = {
            'validation_summary': {
                'is_stable': results.is_stable,
                'overall_stability_score': results.stability_score,
                'meets_production_requirements': results.is_stable
            },
            'detailed_results': {
                'gradient_health': results.gradient_health,
                'prediction_variance': results.prediction_variance,
                'manufacturing_compliance': results.manufacturing_compliance,
                'improvement_over_baseline': results.improvement_over_baseline
            },
            'validation_details': results.validation_details,
            'recommendations': results.recommendations,
            'thresholds_used': self.thresholds
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Validation report saved to {output_path}")

def quick_stability_check(model, 
                         test_data: pd.DataFrame,
                         target_variable: str,
                         manufacturing_params: Dict[str, Any]) -> bool:
    """
    Quick stability check for production deployment
    
    Args:
        model: Trained model to check
        test_data: Test dataset
        target_variable: Target variable
        manufacturing_params: Manufacturing constraints
        
    Returns:
        True if model passes basic stability checks
    """
    try:
        # Quick prediction test
        forecast_result = model.forecast(
            test_data.tail(240), target_variable, 15, include_confidence_intervals=False
        )
        
        predictions = np.array(forecast_result.forecast_values)
        
        # Basic checks
        checks = [
            len(predictions) == 15,  # Correct length
            not np.any(np.isnan(predictions)),  # No NaN values
            not np.any(np.isinf(predictions)),  # No infinite values
            np.var(predictions) > 0.01,  # Non-constant predictions
            np.all(predictions > 0) if target_variable in ["thickness_avg", "speed"] else True  # Positive values
        ]
        
        return all(checks)
        
    except Exception as e:
        logger.error(f"Quick stability check failed: {e}")
        return False