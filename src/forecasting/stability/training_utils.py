"""
Stabilized Training Utilities

Implements enhanced trainer with gradient clipping, mixed precision training,
learning rate scheduling, and manufacturing-specific stability monitoring.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict
from transformers import (
    Trainer, 
    TrainingArguments, 
    TrainerCallback
)
import logging
import json
from pathlib import Path

from .gradient_utils import GradientClipper, calculate_gradient_health_score

logger = logging.getLogger(__name__)

class StabilizedTrainer(Trainer):
    """
    Enhanced HuggingFace Trainer with gradient stabilization and monitoring
    
    Provides training stability improvements specifically designed for
    manufacturing time series forecasting with PatchTST models.
    """
    
    def __init__(self, 
                 gradient_clip_norm: float = 1.0,
                 adaptive_clipping: bool = True,
                 mixed_precision: bool = True,
                 gradient_accumulation_steps: int = 1,
                 warmup_ratio: float = 0.1,
                 weight_decay: float = 0.01,
                 stability_monitoring: bool = True,
                 **kwargs):
        """
        Initialize stabilized trainer
        
        Args:
            gradient_clip_norm: Maximum gradient norm for clipping
            adaptive_clipping: Whether to use adaptive gradient clipping
            mixed_precision: Whether to use mixed precision training
            gradient_accumulation_steps: Steps for gradient accumulation
            warmup_ratio: Ratio of training for warmup
            weight_decay: Weight decay for regularization
            stability_monitoring: Whether to monitor training stability
            **kwargs: Additional arguments for base Trainer
        """
        super().__init__(**kwargs)
        
        # Stability configuration
        self.gradient_clip_norm = gradient_clip_norm
        self.adaptive_clipping = adaptive_clipping
        self.mixed_precision = mixed_precision
        self.stability_monitoring = stability_monitoring
        
        # Initialize gradient clipper
        self.gradient_clipper = GradientClipper(
            max_norm=gradient_clip_norm,
            adaptive=adaptive_clipping,
            warmup_steps=max(100, int(0.1 * self.args.max_steps)) if hasattr(self.args, 'max_steps') else 100
        )
        
        # Training stability tracking
        self.stability_metrics = {
            'gradient_norms': [],
            'losses': [],
            'learning_rates': [],
            'stability_scores': []
        }
        
        # Loss tracking for stability
        self.recent_losses = []
        self.loss_window = 50
        
        logger.info(f"StabilizedTrainer initialized with gradient clipping: {gradient_clip_norm}")
    
    def training_step(self, model: nn.Module, inputs: Dict[str, torch.Tensor], num_items_in_batch=None) -> torch.Tensor:
        """
        Enhanced training step with gradient clipping and monitoring
        ENHANCED: Added pre-loss tensor validation and numerical stability
        
        Args:
            model: The model to train
            inputs: Dictionary of input tensors
            num_items_in_batch: Number of items in batch (for compatibility)
            
        Returns:
            Loss tensor
        """
        model.train()
        inputs = self._prepare_inputs(inputs)
        
        # CRITICAL: Pre-loss tensor validation
        self._validate_input_tensors(inputs)
        
        # Enable mixed precision if configured
        if self.mixed_precision and hasattr(self.args, 'fp16') and self.args.fp16:
            with self.compute_loss_context_manager():
                loss = self.compute_loss(model, inputs)
        else:
            loss = self.compute_loss(model, inputs)
        
        # CRITICAL: Post-loss validation and clamping
        loss = self._validate_and_clamp_loss(loss)
        
        # Scale loss for gradient accumulation
        if self.args.gradient_accumulation_steps > 1:
            loss = loss / self.args.gradient_accumulation_steps
        
        # Backward pass with mixed precision support
        if self.mixed_precision and hasattr(self.args, 'fp16') and self.args.fp16:
            self.scaler.scale(loss).backward()
        else:
            loss.backward()
        
        # Apply gradient clipping and monitoring
        if (self.state.global_step + 1) % self.args.gradient_accumulation_steps == 0:
            # Clip gradients and get norm
            if self.mixed_precision and hasattr(self.args, 'fp16') and self.args.fp16:
                # Unscale gradients before clipping
                self.scaler.unscale_(self.optimizer)
            
            grad_norm = self.gradient_clipper.clip_gradients(model.parameters())
            
            # Update stability metrics
            self._update_stability_metrics(loss.item(), grad_norm)
            
            # Log gradient information periodically
            if self.state.global_step % 10 == 0:
                self._log_stability_metrics()
        
        return loss.detach()
    
    def _validate_input_tensors(self, inputs: Dict[str, torch.Tensor]) -> None:
        """
        Validate input tensors for shape consistency and numerical stability
        CRITICAL: Prevents tensor shape mismatches that cause infinite loss
        """
        if 'past_values' in inputs and 'future_values' in inputs:
            past_values = inputs['past_values']
            future_values = inputs['future_values']
            
            # Log tensor shapes for debugging
            logger.debug(f"Training step tensor shapes: past_values={past_values.shape}, future_values={future_values.shape}")
            
            # Validate past_values shape [batch, sequence_length, num_features]
            if len(past_values.shape) != 3:
                raise ValueError(f"Invalid past_values shape: {past_values.shape}, expected 3D [batch, seq_len, features]")
            
            # CRITICAL: Validate future_values shape for HuggingFace PatchTST univariate prediction
            # Should be [batch, prediction_length, 1] for num_targets=1
            if len(future_values.shape) != 3 or future_values.shape[-1] != 1:
                logger.error(f"CRITICAL: Invalid future_values shape: {future_values.shape}, expected 3D [batch, pred_len, 1]")
                logger.error("HuggingFace PatchTST with num_targets=1 requires [batch, pred_len, 1] target shape!")
                raise ValueError(f"Invalid future_values shape: {future_values.shape}, expected 3D [batch, pred_len, 1] for univariate prediction")
            
            # Check for NaN/inf in input tensors
            if torch.any(torch.isnan(past_values)) or torch.any(torch.isinf(past_values)):
                logger.error("NaN/inf detected in past_values")
                raise ValueError("Invalid values in past_values tensor")
            
            if torch.any(torch.isnan(future_values)) or torch.any(torch.isinf(future_values)):
                logger.error("NaN/inf detected in future_values")
                raise ValueError("Invalid values in future_values tensor")
            
            logger.debug(f"✓ Input tensor validation passed: past={past_values.shape}, future={future_values.shape}")
    
    def _validate_and_clamp_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """
        Validate and clamp loss to prevent numerical instability
        CRITICAL: Prevents infinite loss from propagating through training
        """
        # Check for NaN/inf loss
        if torch.isnan(loss) or torch.isinf(loss):
            logger.error(f"CRITICAL: Invalid loss detected: {loss.item()}")
            logger.error("Replacing with large finite value to prevent training collapse")
            # Replace with large but finite value
            loss = torch.tensor(1000.0, device=loss.device, dtype=loss.dtype, requires_grad=True)
        
        # Clamp loss to reasonable range
        max_loss = 1000.0
        if loss.item() > max_loss:
            logger.warning(f"Loss clamped from {loss.item():.6f} to {max_loss}")
            loss = torch.clamp(loss, max=max_loss)
        
        # Log loss value for monitoring
        if hasattr(self, '_step_count'):
            self._step_count += 1
        else:
            self._step_count = 1
        
        if self._step_count % 10 == 0:
            logger.debug(f"Step {self._step_count}: Loss = {loss.item():.6f}")
        
        return loss
    
    def _update_stability_metrics(self, loss: float, grad_norm: float) -> None:
        """Update training stability metrics"""
        self.stability_metrics['gradient_norms'].append(grad_norm)
        self.stability_metrics['losses'].append(loss)
        
        # Update recent losses for stability calculation
        self.recent_losses.append(loss)
        if len(self.recent_losses) > self.loss_window:
            self.recent_losses = self.recent_losses[-self.loss_window:]
        
        # Calculate stability score
        if len(self.stability_metrics['gradient_norms']) > 10:
            stability_score = calculate_gradient_health_score(
                self.stability_metrics['gradient_norms']
            )
            self.stability_metrics['stability_scores'].append(stability_score)
    
    def _log_stability_metrics(self) -> None:
        """Log training stability metrics"""
        if not self.stability_monitoring:
            return
        
        # Get gradient statistics
        grad_stats = self.gradient_clipper.get_gradient_statistics()
        
        # Calculate loss stability
        loss_stability = 1.0
        if len(self.recent_losses) > 5:
            loss_std = np.std(self.recent_losses)
            loss_mean = np.mean(self.recent_losses)
            loss_stability = 1.0 / (1.0 + loss_std / (loss_mean + 1e-8))
        
        # Log to trainer
        logs = {
            "stability/grad_norm_mean": grad_stats['mean_norm'],
            "stability/grad_norm_recent": grad_stats['recent_mean'],
            "stability/grad_clipping_freq": grad_stats['clipping_frequency'],
            "stability/loss_stability": loss_stability,
            "stability/adaptive_threshold": grad_stats.get('adaptive_threshold', 0.0)
        }
        
        # Add current learning rate
        if self.lr_scheduler:
            logs["stability/learning_rate"] = self.lr_scheduler.get_last_lr()[0]
        
        self.log(logs)
        
        # Warn about potential instability
        if grad_stats['clipping_frequency'] > 75:
            logger.warning(f"High gradient clipping frequency: {grad_stats['clipping_frequency']:.1f}%")
        
        if loss_stability < 0.3:
            logger.warning(f"Low loss stability detected: {loss_stability:.3f}")
    
    def evaluate(self, 
                eval_dataset=None, 
                ignore_keys=None, 
                metric_key_prefix: str = "eval") -> Dict[str, float]:
        """
        Enhanced evaluation with stability checks
        
        Args:
            eval_dataset: Evaluation dataset
            ignore_keys: Keys to ignore in metrics
            metric_key_prefix: Prefix for metric names
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Run standard evaluation
        eval_results = super().evaluate(eval_dataset, ignore_keys, metric_key_prefix)
        
        # Add stability metrics to evaluation
        if self.stability_monitoring and len(self.stability_metrics['stability_scores']) > 0:
            eval_results[f"{metric_key_prefix}_stability_score"] = np.mean(
                self.stability_metrics['stability_scores'][-10:]  # Recent stability
            )
            
            # Check for numerical stability
            eval_loss = eval_results.get(f"{metric_key_prefix}_loss", float('inf'))
            if np.isnan(eval_loss) or np.isinf(eval_loss):
                logger.error(f"Unstable evaluation loss detected: {eval_loss}")
                logger.error("Training will be stopped due to numerical instability")
                eval_results[f"{metric_key_prefix}_numerical_stable"] = False
                # Force early stopping on infinite/NaN loss
                eval_results[f"{metric_key_prefix}_loss"] = float('inf')
                self.control.should_training_stop = True
            else:
                eval_results[f"{metric_key_prefix}_numerical_stable"] = True
        
        return eval_results
    
    def save_stability_report(self, output_dir: str) -> None:
        """
        Save comprehensive stability report
        
        Args:
            output_dir: Directory to save report
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Gradient statistics
        grad_stats = self.gradient_clipper.get_gradient_statistics()
        
        # Loss statistics
        loss_stats = {}
        if self.stability_metrics['losses']:
            losses = np.array(self.stability_metrics['losses'])
            loss_stats = {
                'mean_loss': float(np.mean(losses)),
                'std_loss': float(np.std(losses)),
                'min_loss': float(np.min(losses)),
                'max_loss': float(np.max(losses)),
                'final_loss': float(losses[-1]) if len(losses) > 0 else None
            }
        
        # Stability summary
        stability_summary = {
            'training_stability': {
                'gradient_statistics': grad_stats,
                'loss_statistics': loss_stats,
                'stability_scores': self.stability_metrics['stability_scores'][-10:] if self.stability_metrics['stability_scores'] else [],
                'total_training_steps': len(self.stability_metrics['gradient_norms']),
                'configuration': {
                    'gradient_clip_norm': self.gradient_clip_norm,
                    'adaptive_clipping': self.adaptive_clipping,
                    'mixed_precision': self.mixed_precision
                }
            }
        }
        
        # Save report
        report_path = output_path / 'stability_report.json'
        with open(report_path, 'w') as f:
            json.dump(stability_summary, f, indent=2)
        
        # Save gradient visualization
        try:
            viz_path = output_path / 'gradient_norms.png'
            self.gradient_clipper.visualize_gradient_norms(str(viz_path))
        except Exception as e:
            logger.warning(f"Failed to save gradient visualization: {e}")
        
        logger.info(f"Stability report saved to {output_path}")

class StabilityCallback(TrainerCallback):
    """
    Callback for monitoring training stability during HuggingFace training
    
    Provides early intervention for training instability and comprehensive logging.
    """
    
    def __init__(self, 
                 instability_threshold: float = 0.2,
                 patience: int = 5,
                 save_frequency: int = 100):
        """
        Initialize stability callback
        
        Args:
            instability_threshold: Threshold for detecting instability
            patience: Number of unstable steps before intervention
            save_frequency: Frequency of saving stability reports
        """
        self.instability_threshold = instability_threshold
        self.patience = patience
        self.save_frequency = save_frequency
        
        self.unstable_steps = 0
        self.stability_history = []
    
    def on_log(self, args, state, control, logs=None, **kwargs):
        """Called when training logs are generated"""
        if logs and 'stability/loss_stability' in logs:
            stability = logs['stability/loss_stability']
            self.stability_history.append(stability)
            
            # Check for instability
            if stability < self.instability_threshold:
                self.unstable_steps += 1
                logger.warning(f"Training instability detected (step {self.unstable_steps}/{self.patience})")
                
                if self.unstable_steps >= self.patience:
                    logger.error("Training instability threshold exceeded - consider stopping")
                    control.should_training_stop = True
            else:
                self.unstable_steps = 0  # Reset counter
    
    def on_step_end(self, args, state, control, **kwargs):
        """Called at the end of each training step"""
        # Periodic stability reporting
        if state.global_step % self.save_frequency == 0:
            if hasattr(kwargs.get('model'), 'trainer') and hasattr(kwargs['model'].trainer, 'save_stability_report'):
                kwargs['model'].trainer.save_stability_report(args.output_dir)

class GradientMonitoringCallback(TrainerCallback):
    """
    Specialized callback for gradient monitoring and visualization
    """
    
    def __init__(self, log_frequency: int = 10):
        """
        Initialize gradient monitoring callback
        
        Args:
            log_frequency: Frequency of detailed gradient logging
        """
        self.log_frequency = log_frequency
        
    def on_step_end(self, args, state, control, model=None, **kwargs):
        """Monitor gradients at step end"""
        if state.global_step % self.log_frequency == 0 and model is not None:
            # Calculate gradient statistics
            total_norm = 0.0
            param_count = 0
            
            for name, param in model.named_parameters():
                if param.grad is not None:
                    param_norm = param.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
                    param_count += 1
            
            total_norm = total_norm ** (1. / 2)
            
            # Log gradient health
            logs = {
                "gradient_monitoring/total_norm": total_norm,
                "gradient_monitoring/param_count": param_count
            }
            
            # Log per-layer gradient norms for transformer layers
            layer_norms = {}
            for name, param in model.named_parameters():
                if param.grad is not None and 'encoder.layers' in name:
                    layer_idx = name.split('.')[2] if len(name.split('.')) > 2 else 'unknown'
                    if layer_idx not in layer_norms:
                        layer_norms[layer_idx] = 0.0
                    layer_norms[layer_idx] += param.grad.data.norm(2).item() ** 2
            
            for layer_idx, norm in layer_norms.items():
                logs[f"gradient_monitoring/layer_{layer_idx}_norm"] = norm ** 0.5
            
            # Add to trainer logs if possible
            if hasattr(kwargs.get('model'), 'trainer'):
                kwargs['model'].trainer.log(logs)

def create_stabilized_training_args(
    output_dir: str,
    num_train_epochs: int = 50,
    per_device_train_batch_size: int = 16,
    gradient_accumulation_steps: int = 2,
    warmup_ratio: float = 0.1,
    weight_decay: float = 0.01,
    learning_rate: float = 5e-5,
    fp16: bool = True,
    dataloader_num_workers: int = 2,
    save_strategy: str = "steps",
    save_steps: int = 500,
    eval_strategy: str = "steps", 
    eval_steps: int = 100,
    logging_steps: int = 10,
    load_best_model_at_end: bool = True,
    metric_for_best_model: str = "eval_loss",
    greater_is_better: bool = False,
    **kwargs
) -> TrainingArguments:
    """
    Create training arguments optimized for stability
    
    Args:
        output_dir: Output directory for models and logs
        num_train_epochs: Number of training epochs
        per_device_train_batch_size: Batch size per device
        gradient_accumulation_steps: Gradient accumulation steps
        warmup_ratio: Warmup ratio for learning rate
        weight_decay: Weight decay for regularization
        learning_rate: Initial learning rate
        fp16: Whether to use mixed precision
        dataloader_num_workers: Number of data loading workers
        save_strategy: Strategy for saving checkpoints
        save_steps: Steps between saves
        eval_strategy: Strategy for evaluation
        eval_steps: Steps between evaluations
        logging_steps: Steps between logging
        load_best_model_at_end: Whether to load best model at end
        metric_for_best_model: Metric to use for best model selection
        greater_is_better: Whether higher metric is better
        **kwargs: Additional arguments
        
    Returns:
        Configured TrainingArguments for stable training
    """
    return TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=num_train_epochs,
        per_device_train_batch_size=per_device_train_batch_size,
        per_device_eval_batch_size=per_device_train_batch_size,
        gradient_accumulation_steps=gradient_accumulation_steps,
        warmup_ratio=warmup_ratio,
        weight_decay=weight_decay,
        learning_rate=learning_rate,
        fp16=fp16,
        dataloader_num_workers=dataloader_num_workers,
        dataloader_pin_memory=True,
        save_strategy=save_strategy,
        save_steps=save_steps,
        eval_strategy=eval_strategy,
        eval_steps=eval_steps,
        logging_steps=logging_steps,
        load_best_model_at_end=load_best_model_at_end,
        metric_for_best_model=metric_for_best_model,
        greater_is_better=greater_is_better,
        report_to=None,  # Disable external reporting
        seed=42,
        **kwargs
    )