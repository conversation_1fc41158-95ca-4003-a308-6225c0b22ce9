"""
Advanced Normalization Layers for Time Series Stability

Implements UnitNorm and RobustScaler specifically designed for time series
transformers to improve training stability and convergence.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Optional, Tuple
from sklearn.preprocessing import RobustScaler as SklearnRobustScaler
import logging

logger = logging.getLogger(__name__)

class UnitNorm(nn.Module):
    """
    UnitNorm layer specifically designed for time series transformers
    
    Normalizes input vectors by their L2 norm and applies learnable scaling.
    This is more stable than BatchNorm for time series data with varying scales.
    
    Based on research showing that UnitNorm provides better stability for
    transformer architectures on sequential data.
    """
    
    def __init__(self, 
                 d_model: int, 
                 eps: float = 1e-5,
                 elementwise_affine: bool = True):
        """
        Initialize UnitNorm layer
        
        Args:
            d_model: Model dimension (feature size)
            eps: Small value to avoid division by zero
            elementwise_affine: Whether to include learnable affine transformation
        """
        super(UnitNorm, self).__init__()
        self.d_model = d_model
        self.eps = eps
        self.elementwise_affine = elementwise_affine
        
        if self.elementwise_affine:
            # Learnable scale and shift parameters
            self.scale = nn.Parameter(torch.ones(d_model))
            self.shift = nn.Parameter(torch.zeros(d_model))
        else:
            self.register_parameter('scale', None)
            self.register_parameter('shift', None)
        
        # Initialize parameters
        self.reset_parameters()
    
    def reset_parameters(self) -> None:
        """Initialize parameters to stable defaults"""
        if self.elementwise_affine:
            nn.init.ones_(self.scale)
            nn.init.zeros_(self.shift)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply UnitNorm normalization
        
        Args:
            x: Input tensor of shape [batch, sequence, features]
            
        Returns:
            Normalized tensor of same shape
        """
        # Calculate L2 norm along feature dimension
        # Shape: [batch, sequence, 1]
        norm = torch.norm(x, p=2, dim=-1, keepdim=True)
        
        # Clamp norm to avoid division by zero
        norm = torch.clamp(norm, min=self.eps)
        
        # Normalize by L2 norm
        normalized = x / norm
        
        # Apply learnable affine transformation if enabled
        if self.elementwise_affine:
            # Broadcast scale and shift across batch and sequence dimensions
            scale = self.scale.unsqueeze(0).unsqueeze(0)  # [1, 1, features]
            shift = self.shift.unsqueeze(0).unsqueeze(0)  # [1, 1, features]
            normalized = normalized * scale + shift
        
        return normalized
    
    def extra_repr(self) -> str:
        """String representation for debugging"""
        return f'd_model={self.d_model}, eps={self.eps}, elementwise_affine={self.elementwise_affine}'

class AdaptiveUnitNorm(nn.Module):
    """
    Adaptive UnitNorm with learnable normalization strategy
    
    Dynamically adapts between L1, L2, and infinity norms based on data characteristics.
    Useful for handling diverse time series patterns in manufacturing data.
    """
    
    def __init__(self, 
                 d_model: int,
                 eps: float = 1e-5,
                 norm_types: Tuple[float, ...] = (1.0, 2.0, float('inf'))):
        """
        Initialize adaptive UnitNorm
        
        Args:
            d_model: Model dimension
            eps: Small value for numerical stability
            norm_types: Tuple of norm types to choose from
        """
        super(AdaptiveUnitNorm, self).__init__()
        self.d_model = d_model
        self.eps = eps
        self.norm_types = norm_types
        
        # Learnable weights for combining different norms
        self.norm_weights = nn.Parameter(torch.ones(len(norm_types)))
        self.scale = nn.Parameter(torch.ones(d_model))
        self.shift = nn.Parameter(torch.zeros(d_model))
        
        self.reset_parameters()
    
    def reset_parameters(self) -> None:
        """Initialize parameters"""
        nn.init.ones_(self.norm_weights)
        nn.init.ones_(self.scale)
        nn.init.zeros_(self.shift)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply adaptive normalization
        
        Args:
            x: Input tensor [batch, sequence, features]
            
        Returns:
            Normalized tensor
        """
        # Softmax normalize the weights
        weights = torch.softmax(self.norm_weights, dim=0)
        
        # Calculate different norms
        normalized_outputs = []
        for i, norm_type in enumerate(self.norm_types):
            if norm_type == float('inf'):
                # Infinity norm (max absolute value)
                norm = torch.max(torch.abs(x), dim=-1, keepdim=True)[0]
            else:
                # Lp norm
                norm = torch.norm(x, p=norm_type, dim=-1, keepdim=True)
            
            norm = torch.clamp(norm, min=self.eps)
            normalized = x / norm
            normalized_outputs.append(normalized * weights[i])
        
        # Combine normalized outputs
        combined = sum(normalized_outputs)
        
        # Apply learnable affine transformation
        scale = self.scale.unsqueeze(0).unsqueeze(0)
        shift = self.shift.unsqueeze(0).unsqueeze(0)
        
        return combined * scale + shift

class RobustScaler:
    """
    Robust scaler wrapper for manufacturing time series data
    
    Provides more stable scaling than StandardScaler by using median and
    interquartile range instead of mean and standard deviation.
    """
    
    def __init__(self, 
                 quantile_range: Tuple[float, float] = (25.0, 75.0),
                 copy: bool = True,
                 with_centering: bool = True,
                 with_scaling: bool = True):
        """
        Initialize robust scaler
        
        Args:
            quantile_range: Range for scaling (default: IQR)
            copy: Whether to copy input data
            with_centering: Whether to center data
            with_scaling: Whether to scale data
        """
        self.quantile_range = quantile_range
        self.copy = copy
        self.with_centering = with_centering
        self.with_scaling = with_scaling
        
        self._scaler = SklearnRobustScaler(
            quantile_range=quantile_range,
            copy=copy,
            with_centering=with_centering,
            with_scaling=with_scaling
        )
        
        self.is_fitted = False
    
    def fit(self, X: np.ndarray) -> 'RobustScaler':
        """
        Fit the scaler to data
        
        Args:
            X: Input data of shape [n_samples, n_features]
            
        Returns:
            Self for method chaining
        """
        # Handle NaN values
        if np.any(np.isnan(X)):
            logger.warning("Input data contains NaN values, using nanquantile for fitting")
            X_clean = self._handle_nan_values(X)
        else:
            X_clean = X
        
        self._scaler.fit(X_clean)
        self.is_fitted = True
        
        logger.info(f"RobustScaler fitted on data with shape {X.shape}")
        return self
    
    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        Transform data using fitted scaler
        
        Args:
            X: Input data to transform
            
        Returns:
            Transformed data
        """
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before transform")
        
        # Handle NaN values
        if np.any(np.isnan(X)):
            logger.warning("Input data contains NaN values during transform")
            X_clean = self._handle_nan_values(X)
        else:
            X_clean = X
        
        return self._scaler.transform(X_clean)
    
    def fit_transform(self, X: np.ndarray) -> np.ndarray:
        """
        Fit and transform data in one step
        
        Args:
            X: Input data
            
        Returns:
            Transformed data
        """
        return self.fit(X).transform(X)
    
    def inverse_transform(self, X: np.ndarray) -> np.ndarray:
        """
        Inverse transform data back to original scale
        
        Args:
            X: Transformed data
            
        Returns:
            Data in original scale
        """
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before inverse_transform")
        
        return self._scaler.inverse_transform(X)
    
    def _handle_nan_values(self, X: np.ndarray) -> np.ndarray:
        """
        Handle NaN values in input data
        
        Args:
            X: Input data potentially containing NaN
            
        Returns:
            Data with NaN values handled
        """
        # Strategy: Replace NaN with median of each column
        X_clean = X.copy()
        
        for col in range(X.shape[1]):
            col_data = X[:, col]
            if np.any(np.isnan(col_data)):
                median_val = np.nanmedian(col_data)
                X_clean[:, col] = np.where(np.isnan(col_data), median_val, col_data)
        
        return X_clean
    
    def get_scale_info(self) -> Optional[dict]:
        """
        Get information about fitted scaling parameters
        
        Returns:
            Dictionary with scaling information
        """
        if not self.is_fitted:
            return None
        
        return {
            'center': getattr(self._scaler, 'center_', None),
            'scale': getattr(self._scaler, 'scale_', None),
            'quantile_range': self.quantile_range,
            'n_features': len(self._scaler.scale_) if hasattr(self._scaler, 'scale_') else None
        }

class TimeSeriesNormalizer(nn.Module):
    """
    Specialized normalizer for time series with temporal awareness
    
    Applies normalization that respects temporal dependencies and
    manufacturing process characteristics.
    """
    
    def __init__(self, 
                 d_model: int,
                 normalization_type: str = "unit_norm",
                 temporal_smoothing: bool = True,
                 smoothing_window: int = 5):
        """
        Initialize time series normalizer
        
        Args:
            d_model: Model dimension
            normalization_type: Type of normalization ("unit_norm", "adaptive", "batch_norm")
            temporal_smoothing: Whether to apply temporal smoothing
            smoothing_window: Window size for temporal smoothing
        """
        super(TimeSeriesNormalizer, self).__init__()
        self.d_model = d_model
        self.normalization_type = normalization_type
        self.temporal_smoothing = temporal_smoothing
        self.smoothing_window = smoothing_window
        
        # Choose normalization layer
        if normalization_type == "unit_norm":
            self.norm_layer = UnitNorm(d_model)
        elif normalization_type == "adaptive":
            self.norm_layer = AdaptiveUnitNorm(d_model)
        elif normalization_type == "batch_norm":
            self.norm_layer = nn.BatchNorm1d(d_model)
        else:
            raise ValueError(f"Unknown normalization type: {normalization_type}")
        
        # Temporal smoothing parameters
        if temporal_smoothing:
            self.register_buffer('smoothing_weights', 
                               torch.ones(smoothing_window) / smoothing_window)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply time series normalization
        
        Args:
            x: Input tensor [batch, sequence, features]
            
        Returns:
            Normalized tensor
        """
        # Apply temporal smoothing if enabled
        if self.temporal_smoothing and x.size(1) >= self.smoothing_window:
            x = self._apply_temporal_smoothing(x)
        
        # Apply normalization
        if self.normalization_type == "batch_norm":
            # BatchNorm expects [batch, features, sequence]
            x = x.transpose(1, 2)
            x = self.norm_layer(x)
            x = x.transpose(1, 2)
        else:
            x = self.norm_layer(x)
        
        return x
    
    def _apply_temporal_smoothing(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply temporal smoothing to reduce noise
        
        Args:
            x: Input tensor [batch, sequence, features]
            
        Returns:
            Smoothed tensor
        """
        # Simple moving average smoothing
        batch_size, seq_len, features = x.shape
        smoothed = torch.zeros_like(x)
        
        for i in range(seq_len):
            start_idx = max(0, i - self.smoothing_window + 1)
            end_idx = i + 1
            window_data = x[:, start_idx:end_idx, :]
            
            # Apply weighted average
            window_size = end_idx - start_idx
            weights = self.smoothing_weights[-window_size:].unsqueeze(0).unsqueeze(-1)
            smoothed[:, i, :] = (window_data * weights).sum(dim=1)
        
        return smoothed