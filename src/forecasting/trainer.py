"""
Manufacturing Forecast Training Pipeline

Comprehensive training pipeline for PatchTST models with manufacturing-specific
validation, model evaluation, and performance benchmarking against baseline methods.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
import json

# Machine learning imports
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error

# Local imports
from .patchtst_model import ManufacturingPatchTSTModel
from .config import ForecastConfig, PatchTSTTrainingConfig, get_stabilized_training_config
from .stability import ManufacturingStabilityValidator
from .transfer_learning import load_granite_model, validate_model_compatibility, quick_fine_tune
from ..data.loader import ManufacturingDataLoader

logger = logging.getLogger(__name__)

class ManufacturingForecastTrainer:
    """
    Enhanced comprehensive training pipeline for manufacturing forecasting models
    
    Handles model training, validation, performance benchmarking, stability validation,
    transfer learning, and deployment preparation with manufacturing domain expertise.
    
    Enhanced Features:
    - Stability validation and monitoring
    - Transfer learning with IBM Granite models
    - StabilizedTrainer with gradient clipping
    - Manufacturing-specific compliance checks
    - Comprehensive baseline comparisons
    """
    
    def __init__(self, 
                 forecast_config: ForecastConfig, 
                 training_config: Optional[PatchTSTTrainingConfig] = None,
                 use_stability_features: bool = True,
                 use_transfer_learning: bool = True):
        """
        Initialize the enhanced forecast trainer
        
        Args:
            forecast_config: Forecasting configuration
            training_config: Training configuration (optional, will use stabilized defaults)
            use_stability_features: Whether to use stability enhancements
            use_transfer_learning: Whether to attempt transfer learning
        """
        self.forecast_config = forecast_config
        self.training_config = training_config or get_stabilized_training_config()
        self.use_stability_features = use_stability_features
        self.use_transfer_learning = use_transfer_learning
        
        self.data_loader = ManufacturingDataLoader()
        
        # Training state
        self.trained_models: Dict[str, Any] = {}
        self.baseline_models: Dict[str, Any] = {}
        self.performance_comparison: Dict[str, Any] = {}
        self.training_logs: List[Dict[str, Any]] = []
        
        # Enhanced stability features
        self.stability_validator = ManufacturingStabilityValidator(
            manufacturing_params=forecast_config.manufacturing_params
        )
        self.stability_reports: Dict[str, Any] = {}
        self.transfer_learning_results: Dict[str, Any] = {}
        
        logger.info("Enhanced ManufacturingForecastTrainer initialized:")
        logger.info(f"  - Stability features: {use_stability_features}")
        logger.info(f"  - Transfer learning: {use_transfer_learning}")
        logger.info(f"  - Training config: {type(self.training_config).__name__}")
    
    def _serialize_config(self, config) -> dict:
        """
        Serialize configuration object with compatibility for Pydantic v1 and v2
        
        Args:
            config: Configuration object to serialize
            
        Returns:
            Dictionary representation of the config
        """
        try:
            # Try Pydantic v2 method first
            if hasattr(config, 'model_dump'):
                return config.model_dump()
            # Fall back to Pydantic v1 method
            elif hasattr(config, 'dict'):
                return config.dict()
            # If neither works, try to convert to dict manually
            elif hasattr(config, '__dict__'):
                return config.__dict__
            else:
                # Last resort: string representation
                return {'config_str': str(config)}
        except Exception as e:
            logger.warning(f"Failed to serialize config: {e}")
            return {'config_str': str(config), 'serialization_error': str(e)}
        
    def train_all_target_variables(self, 
                                 data_path: str = 'test-data') -> Dict[str, ManufacturingPatchTSTModel]:
        """
        Train enhanced PatchTST models for all configured target variables with stability features
        
        Args:
            data_path: Path to manufacturing data
            
        Returns:
            Dictionary of trained models by target variable
        """
        try:
            logger.info("Starting enhanced comprehensive model training for all target variables")
            
            # Load manufacturing data
            logger.info("Loading manufacturing data...")
            self.data_loader.data_dir = Path(data_path)
            self.data_loader.load_all_data_sources()
            unified_data = self.data_loader.create_unified_table()
            
            logger.info(f"Loaded data with {len(unified_data)} records")
            
            # Attempt transfer learning setup if enabled
            pretrained_model = None
            if self.use_transfer_learning:
                pretrained_model = self._setup_transfer_learning(unified_data)
            
            # Train models for each target variable
            trained_models = {}
            
            for target_variable in self.forecast_config.target_variables:
                logger.info(f"Training enhanced model for target variable: {target_variable}")
                
                try:
                    # Create model instance with stability features
                    if pretrained_model and self.use_transfer_learning:
                        trained_model = self._train_with_transfer_learning(
                            pretrained_model, unified_data, target_variable
                        )
                    else:
                        trained_model = self._train_from_scratch(
                            unified_data, target_variable
                        )
                    
                    # Enhanced validation with stability checks
                    validation_results = self._enhanced_model_validation(
                        trained_model, unified_data, target_variable
                    )
                    
                    # Compare with baseline methods
                    baseline_comparison = self._compare_with_baselines(
                        trained_model, unified_data, target_variable
                    )
                    
                    # Perform stability validation
                    stability_results = self._validate_model_stability(
                        trained_model, unified_data, target_variable
                    )
                    
                    # Store comprehensive results
                    trained_models[target_variable] = trained_model
                    self.performance_comparison[target_variable] = {
                        'validation_results': validation_results,
                        'baseline_comparison': baseline_comparison,
                        'stability_validation': stability_results
                    }
                    
                    logger.info(f"✓ Successfully trained enhanced model for {target_variable}")
                    
                except Exception as e:
                    logger.error(f"✗ Error training model for {target_variable}: {str(e)}")
                    continue
            
            # Generate enhanced training report
            self._generate_enhanced_training_report(trained_models)
            
            # Validate 15% improvement requirement
            improvement_validation = self.validate_15_percent_improvement(self.performance_comparison)
            
            logger.info("Enhanced training completed:")
            logger.info(f"  - Successfully trained: {len(trained_models)} models")
            logger.info(f"  - 15% improvement validation: {improvement_validation}")
            logger.info(f"  - Stability features: {self.use_stability_features}")
            logger.info(f"  - Transfer learning: {self.use_transfer_learning}")
            
            return trained_models
            
        except Exception as e:
            logger.error(f"Error in enhanced comprehensive training: {str(e)}")
            raise
    
    def train_all_target_variables_with_unified_table(self, 
                                                    unified_table_path: str) -> Dict[str, ManufacturingPatchTSTModel]:
        """
        Train models using existing unified table instead of creating from raw data sources
        
        Args:
            unified_table_path: Path to existing unified table CSV file
            
        Returns:
            Dictionary of trained models by target variable
        """
        try:
            logger.info("Starting model training with existing unified table")
            
            # Load unified data directly
            logger.info(f"Loading unified table from {unified_table_path}")
            import pandas as pd
            unified_data = pd.read_csv(unified_table_path, comment='#', low_memory=False)
            
            logger.info(f"Loaded unified data with {len(unified_data)} records and {len(unified_data.columns)} columns")
            
            # Attempt transfer learning setup if enabled
            pretrained_model = None
            if self.use_transfer_learning:
                pretrained_model = self._setup_transfer_learning(unified_data)
            
            # Train models for each target variable
            trained_models = {}
            
            for target_variable in self.forecast_config.target_variables:
                logger.info(f"Training enhanced model for target variable: {target_variable}")
                
                try:
                    # Create model instance with stability features
                    if pretrained_model and self.use_transfer_learning:
                        trained_model = self._train_with_transfer_learning(
                            pretrained_model, unified_data, target_variable
                        )
                    else:
                        trained_model = self._train_from_scratch(
                            unified_data, target_variable
                        )
                    
                    # Enhanced validation with stability checks
                    validation_results = self._enhanced_model_validation(
                        trained_model, unified_data, target_variable
                    )
                    
                    # Compare with baseline methods
                    baseline_comparison = self._compare_with_baselines(
                        trained_model, unified_data, target_variable
                    )
                    
                    # Perform stability validation
                    stability_results = self._validate_model_stability(
                        trained_model, unified_data, target_variable
                    )
                    
                    # Store comprehensive results
                    trained_models[target_variable] = trained_model
                    self.performance_comparison[target_variable] = {
                        'validation_results': validation_results,
                        'baseline_comparison': baseline_comparison,
                        'stability_validation': stability_results
                    }
                    
                    logger.info(f"✓ Successfully trained enhanced model for {target_variable}")
                    
                except Exception as e:
                    logger.error(f"✗ Error training model for {target_variable}: {str(e)}")
                    continue
            
            # Generate enhanced training report
            self._generate_enhanced_training_report(trained_models)
            
            # Validate 15% improvement requirement
            improvement_validation = self.validate_15_percent_improvement(self.performance_comparison)
            
            logger.info("Enhanced training with unified table completed:")
            logger.info(f"  - Successfully trained: {len(trained_models)} models")
            logger.info(f"  - 15% improvement validation: {improvement_validation}")
            logger.info(f"  - Stability features: {self.use_stability_features}")
            logger.info(f"  - Transfer learning: {self.use_transfer_learning}")
            
            return trained_models
            
        except Exception as e:
            logger.error(f"Error in training with unified table: {str(e)}")
            raise
    
    def _setup_transfer_learning(self, unified_data: pd.DataFrame) -> Optional[Any]:
        """
        Set up transfer learning with pre-trained models
        
        Args:
            unified_data: Manufacturing data for compatibility checking
            
        Returns:
            Pre-trained model if compatible, None otherwise
        """
        try:
            logger.info("Setting up transfer learning with IBM Granite model")
            
            # Load pre-trained model
            pretrained_model = load_granite_model()
            
            if pretrained_model is None:
                logger.warning("Failed to load pre-trained model, using training from scratch")
                return None
            
            # Validate compatibility
            target_config = {
                'num_input_channels': len(self.forecast_config.input_variables),
                'context_length': self.forecast_config.lookback_window,
                'prediction_length': max(self.forecast_config.forecast_horizons)
            }
            
            is_compatible, compatibility_report = validate_model_compatibility(
                pretrained_model, target_config
            )
            
            self.transfer_learning_results['compatibility_check'] = {
                'is_compatible': is_compatible,
                'report': compatibility_report
            }
            
            if is_compatible:
                logger.info("✓ Pre-trained model is compatible with manufacturing data")
                return pretrained_model
            else:
                logger.warning("✗ Pre-trained model compatibility issues detected")
                logger.warning("Will attempt training from scratch")
                return None
                
        except Exception as e:
            logger.error(f"Error setting up transfer learning: {str(e)}")
            return None
    
    def _train_with_transfer_learning(self, 
                                    pretrained_model: Any,
                                    unified_data: pd.DataFrame,
                                    target_variable: str) -> ManufacturingPatchTSTModel:
        """
        Train model using transfer learning approach
        
        Args:
            pretrained_model: Pre-trained model to fine-tune
            unified_data: Manufacturing data
            target_variable: Target variable to forecast
            
        Returns:
            Fine-tuned model
        """
        logger.info(f"Training {target_variable} with transfer learning")
        
        # Create model instance with pre-trained weights
        model = ManufacturingPatchTSTModel(
            self.forecast_config, 
            self.training_config
        )
        
        # Initialize with pre-trained model
        model.model = pretrained_model
        
        # Prepare data for fine-tuning
        prepared_data = model.preprocessor.prepare_forecasting_data(
            unified_data, target_variable
        )
        
        # Create datasets
        train_dataset = model._create_pytorch_dataset(prepared_data['train_data'])
        val_dataset = model._create_pytorch_dataset(prepared_data['val_data'])
        
        # Perform quick fine-tuning
        fine_tuning_results = quick_fine_tune(
            pretrained_model,
            train_dataset,
            val_dataset,
            self.forecast_config.manufacturing_params,
            strategy="conservative"
        )
        
        # Store fine-tuning results
        self.transfer_learning_results[target_variable] = fine_tuning_results
        
        # Update model training history
        model.training_history.update({
            'transfer_learning': True,
            'fine_tuning_results': fine_tuning_results
        })
        
        logger.info(f"✓ Transfer learning completed for {target_variable}")
        
        return model
    
    def _train_from_scratch(self, 
                          unified_data: pd.DataFrame,
                          target_variable: str) -> ManufacturingPatchTSTModel:
        """
        Train model from scratch with stability features
        
        Args:
            unified_data: Manufacturing data
            target_variable: Target variable to forecast
            
        Returns:
            Trained model
        """
        logger.info(f"Training {target_variable} from scratch with stability features")
        
        # Create model instance with stability features
        model = ManufacturingPatchTSTModel(
            self.forecast_config, 
            self.training_config
        )
        
        # Train model with enhanced stability
        trained_model = model.train(unified_data, target_variable)
        
        return trained_model
    
    def _enhanced_model_validation(self,
                                 model: ManufacturingPatchTSTModel,
                                 unified_data: pd.DataFrame,
                                 target_variable: str) -> Dict[str, Any]:
        """
        Enhanced model validation with stability checks
        
        Args:
            model: Trained model
            unified_data: Full dataset
            target_variable: Target variable
            
        Returns:
            Enhanced validation results
        """
        # Run standard validation
        standard_results = self._validate_model_performance(model, unified_data, target_variable)
        
        # Add stability-specific validations
        enhanced_results = standard_results.copy()
        
        # Check for finite loss
        eval_loss = model.best_model_metrics.get('eval_loss', float('inf'))
        enhanced_results['finite_loss'] = np.isfinite(eval_loss)
        
        # Check prediction variance
        try:
            test_data = unified_data.tail(300)  # Use last 300 records for testing
            forecast_result = model.forecast(test_data, target_variable, 15)
            predictions = np.array(forecast_result.forecast_values)
            enhanced_results['prediction_variance'] = float(np.var(predictions))
            enhanced_results['prediction_mean'] = float(np.mean(predictions))
        except Exception as e:
            logger.warning(f"Could not calculate prediction variance: {e}")
            enhanced_results['prediction_variance'] = 0.0
        
        # Check gradient health if available
        if hasattr(model, 'training_history') and 'gradient_norms' in model.training_history:
            gradient_norms = model.training_history['gradient_norms']
            if gradient_norms:
                enhanced_results['gradient_health'] = {
                    'mean_norm': float(np.mean(gradient_norms)),
                    'max_norm': float(np.max(gradient_norms)),
                    'stable_gradients': all(norm < 2.0 for norm in gradient_norms[-10:])
                }
        
        return enhanced_results
    
    def _validate_model_stability(self,
                                model: ManufacturingPatchTSTModel,
                                unified_data: pd.DataFrame,
                                target_variable: str) -> Dict[str, Any]:
        """
        Comprehensive stability validation using ManufacturingStabilityValidator
        
        Args:
            model: Trained model
            unified_data: Full dataset
            target_variable: Target variable
            
        Returns:
            Stability validation results
        """
        try:
            logger.info(f"Performing stability validation for {target_variable}")
            
            # Prepare test data
            test_data = unified_data.tail(1000)  # Use last 1000 records
            
            # Run comprehensive stability validation
            stability_result = self.stability_validator.validate_model_stability(
                model=model,
                training_history=model.training_history,
                test_data=test_data,
                target_variable=target_variable
            )
            
            # Store stability report
            self.stability_reports[target_variable] = stability_result
            
            # Return summary
            return {
                'is_stable': stability_result.is_stable,
                'stability_score': stability_result.stability_score,
                'prediction_variance': stability_result.prediction_variance,
                'manufacturing_compliance': stability_result.manufacturing_compliance,
                'improvement_over_baseline': stability_result.improvement_over_baseline,
                'recommendations': stability_result.recommendations
            }
            
        except Exception as e:
            logger.error(f"Error in stability validation: {str(e)}")
            return {
                'is_stable': False,
                'error': str(e)
            }
    
    def _generate_enhanced_training_report(self, trained_models: Dict[str, ManufacturingPatchTSTModel]) -> None:
        """Generate enhanced training report with stability metrics"""
        try:
            # Get standard report
            self._generate_training_report(trained_models)
            
            # Add enhanced metrics
            report_path = Path(self.training_config.model_save_path) / 'enhanced_training_report.json'
            
            enhanced_report = {
                'training_summary': {
                    'timestamp': datetime.now().isoformat(),
                    'total_models_trained': len(trained_models),
                    'target_variables': list(trained_models.keys()),
                    'stability_features_used': self.use_stability_features,
                    'transfer_learning_used': self.use_transfer_learning,
                    'training_config': self._serialize_config(self.training_config)
                },
                'model_performance': self.performance_comparison,
                'stability_reports': self.stability_reports,
                'transfer_learning_results': self.transfer_learning_results,
                'improvement_validation': self.validate_15_percent_improvement(self.performance_comparison)
            }
            
            # Custom JSON serializer for enhanced report
            def convert_types_enhanced(obj):
                """Convert complex types to JSON serializable format"""
                import numpy as np
                # Handle numpy boolean types
                if hasattr(np, 'bool_') and isinstance(obj, np.bool_):
                    return bool(obj)
                # Handle numpy integer types
                elif isinstance(obj, (np.integer, np.int8, np.int16, np.int32, np.int64, np.uint8, np.uint16, np.uint32, np.uint64)):
                    return int(obj)
                # Handle numpy float types
                elif isinstance(obj, (np.floating, np.float16, np.float32, np.float64)):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif hasattr(obj, '__dict__'):
                    # Convert dataclass objects to dict
                    return obj.__dict__
                return str(obj)
            
            # Save enhanced report
            with open(report_path, 'w') as f:
                json.dump(enhanced_report, f, indent=2, default=convert_types_enhanced)
            
            logger.info(f"Enhanced training report saved to {report_path}")
            
            # Log enhanced summary
            self._log_enhanced_training_summary(enhanced_report)
            
        except Exception as e:
            logger.error(f"Error generating enhanced training report: {str(e)}")
    
    def _log_enhanced_training_summary(self, report: Dict[str, Any]) -> None:
        """Log enhanced training summary to console"""
        logger.info("="*60)
        logger.info("ENHANCED TRAINING SUMMARY")
        logger.info("="*60)
        
        summary = report['training_summary']
        logger.info(f"Total models trained: {summary['total_models_trained']}")
        logger.info(f"Target variables: {', '.join(summary['target_variables'])}")
        logger.info(f"Stability features: {summary['stability_features_used']}")
        logger.info(f"Transfer learning: {summary['transfer_learning_used']}")
        
        # Stability validation summary
        logger.info("\nSTABILITY VALIDATION:")
        stable_models = 0
        for target, stability_report in self.stability_reports.items():
            # stability_report is a StabilityValidationResult object with direct attributes
            is_stable = getattr(stability_report, 'is_stable', False)
            stability_score = getattr(stability_report, 'stability_score', 0.0)
            status = "✓ STABLE" if is_stable else "✗ UNSTABLE"
            logger.info(f"  {target}: {status} (score: {stability_score:.3f})")
            if is_stable:
                stable_models += 1
        
        # Improvement validation
        logger.info("\nIMPROVEMENT VALIDATION (15% threshold):")
        improvement_results = report['improvement_validation']
        for target, passed in improvement_results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            logger.info(f"  {target}: {status}")
        
        # Overall assessment
        overall_stable = stable_models == len(self.stability_reports)
        overall_improvement = all(improvement_results.values())
        overall_pass = overall_stable and overall_improvement
        
        logger.info("\nOVERALL ASSESSMENT:")
        logger.info(f"  Stability: {stable_models}/{len(self.stability_reports)} models stable")
        logger.info(f"  Improvement: {'✓ PASS' if overall_improvement else '✗ FAIL'}")
        logger.info(f"  Production Ready: {'✓ YES' if overall_pass else '✗ NO'}")
        logger.info("="*60)
    
    def _validate_model_performance(self, 
                                  model: ManufacturingPatchTSTModel,
                                  unified_data: pd.DataFrame,
                                  target_variable: str) -> Dict[str, Any]:
        """
        Validate model performance on held-out test data
        
        Args:
            model: Trained PatchTST model
            unified_data: Full dataset
            target_variable: Target variable being forecasted
            
        Returns:
            Validation results dictionary
        """
        try:
            logger.info(f"Validating model performance for {target_variable}")
            
            # Use last 15% of data for validation (chronological split)
            test_size = int(len(unified_data) * 0.15)
            test_data = unified_data.tail(test_size)
            
            # Generate forecasts for different horizons
            horizon_results = {}
            
            for horizon in self.forecast_config.forecast_horizons:
                # Ensure we have enough data for the forecast horizon
                if len(test_data) < self.forecast_config.lookback_window + horizon:
                    logger.warning(f"Insufficient data for horizon {horizon}")
                    continue
                
                # Generate forecasts
                forecasts = []
                actuals = []
                
                # Test on multiple windows
                num_windows = min(10, len(test_data) - self.forecast_config.lookback_window - horizon)
                
                for i in range(num_windows):
                    # Historical window
                    start_idx = i
                    end_idx = start_idx + self.forecast_config.lookback_window
                    hist_data = test_data.iloc[start_idx:end_idx]
                    
                    # Actual future values
                    actual_future = test_data.iloc[end_idx:end_idx + horizon][target_variable].values
                    
                    if len(actual_future) == horizon:
                        # Generate forecast
                        forecast_result = model.forecast(
                            hist_data, target_variable, horizon, include_confidence_intervals=False
                        )
                        
                        forecasts.append(forecast_result.forecast_values)
                        actuals.append(actual_future)
                
                # Calculate metrics
                if forecasts and actuals:
                    forecasts_array = np.array(forecasts)
                    actuals_array = np.array(actuals)
                    
                    mse = mean_squared_error(actuals_array.flatten(), forecasts_array.flatten())
                    mae = mean_absolute_error(actuals_array.flatten(), forecasts_array.flatten())
                    
                    # Calculate MAPE
                    mape = np.mean(np.abs((actuals_array.flatten() - forecasts_array.flatten()) / 
                                        (actuals_array.flatten() + 1e-8))) * 100
                    
                    horizon_results[f'horizon_{horizon}'] = {
                        'mse': float(mse),
                        'mae': float(mae),
                        'mape': float(mape),
                        'num_forecasts': len(forecasts)
                    }
                else:
                    logger.warning(f"No valid forecasts generated for horizon {horizon}")
            
            return {
                'target_variable': target_variable,
                'horizon_results': horizon_results,
                'validation_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in model validation: {str(e)}")
            return {'error': str(e)}
    
    def _compare_with_baselines(self, 
                              model: ManufacturingPatchTSTModel,
                              unified_data: pd.DataFrame,
                              target_variable: str) -> Dict[str, Any]:
        """
        Compare PatchTST model performance with baseline methods
        
        Args:
            model: Trained PatchTST model
            unified_data: Full dataset
            target_variable: Target variable
            
        Returns:
            Baseline comparison results
        """
        try:
            logger.info(f"Comparing with baseline methods for {target_variable}")
            
            # Prepare baseline data
            test_size = int(len(unified_data) * 0.15)
            test_data = unified_data.tail(test_size)
            
            baseline_results = {}
            
            # Test each forecast horizon
            for horizon in self.forecast_config.forecast_horizons:
                if len(test_data) < self.forecast_config.lookback_window + horizon:
                    continue
                
                # Collect data for baseline comparison
                features_list = []
                targets_list = []
                
                num_windows = min(10, len(test_data) - self.forecast_config.lookback_window - horizon)
                
                for i in range(num_windows):
                    # Historical window
                    start_idx = i
                    end_idx = start_idx + self.forecast_config.lookback_window
                    hist_data = test_data.iloc[start_idx:end_idx]
                    
                    # Features (recent values)
                    features = hist_data[target_variable].tail(5).values  # Last 5 values
                    if len(features) == 5:
                        features_list.append(features)
                        
                        # Target (next values)
                        actual_future = test_data.iloc[end_idx:end_idx + horizon][target_variable].values
                        if len(actual_future) == horizon:
                            targets_list.append(actual_future)
                
                if len(features_list) > 2:  # Need at least 3 samples for training
                    X = np.array(features_list[:-1])  # All but last for training
                    y = np.array(targets_list[:-1])
                    X_test = np.array(features_list[-1:])  # Last for testing
                    y_test = np.array(targets_list[-1:])
                    
                    # Linear Regression Baseline
                    lr_model = LinearRegression()
                    lr_model.fit(X, y)
                    lr_pred = lr_model.predict(X_test)
                    
                    lr_mse = mean_squared_error(y_test.flatten(), lr_pred.flatten())
                    lr_mae = mean_absolute_error(y_test.flatten(), lr_pred.flatten())
                    
                    # Simple persistence baseline (last value)
                    persist_pred = np.full_like(y_test, X_test[0, -1])
                    persist_mse = mean_squared_error(y_test.flatten(), persist_pred.flatten())
                    persist_mae = mean_absolute_error(y_test.flatten(), persist_pred.flatten())
                    
                    # Get PatchTST performance from validation results
                    # Use the validation results instead of training metrics
                    try:
                        if hasattr(model, 'validation_results') and model.validation_results:
                            horizon_key = f'horizon_{horizon}'
                            if horizon_key in model.validation_results.get('horizon_results', {}):
                                patchtst_mse = model.validation_results['horizon_results'][horizon_key]['mse']
                            else:
                                # Fall back to overall MSE if available
                                patchtst_mse = model.validation_results.get('mse', float('inf'))
                        else:
                            # Last resort: use training metrics
                            patchtst_metrics = getattr(model, 'best_model_metrics', {})
                            patchtst_mse = patchtst_metrics.get('eval_loss', float('inf'))
                    except Exception:
                        patchtst_mse = float('inf')
                    
                    # Calculate improvement
                    lr_improvement = (lr_mse - patchtst_mse) / lr_mse * 100 if lr_mse > 0 else 0
                    persist_improvement = (persist_mse - patchtst_mse) / persist_mse * 100 if persist_mse > 0 else 0
                    
                    baseline_results[f'horizon_{horizon}'] = {
                        'linear_regression': {
                            'mse': float(lr_mse),
                            'mae': float(lr_mae),
                            'improvement_vs_patchtst': float(lr_improvement)
                        },
                        'persistence': {
                            'mse': float(persist_mse),
                            'mae': float(persist_mae),
                            'improvement_vs_patchtst': float(persist_improvement)
                        },
                        'patchtst': {
                            'mse': float(patchtst_mse),
                            'mae': 0.0  # Not available from training
                        }
                    }
            
            return {
                'target_variable': target_variable,
                'baseline_results': baseline_results,
                'comparison_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in baseline comparison: {str(e)}")
            return {'error': str(e)}
    
    def validate_15_percent_improvement(self, 
                                      performance_comparison: Dict[str, Any]) -> Dict[str, bool]:
        """
        Validate that PatchTST achieves 15% improvement over baseline methods
        
        Args:
            performance_comparison: Performance comparison results
            
        Returns:
            Dictionary indicating which models meet the 15% improvement threshold
        """
        validation_results = {}
        
        for target_variable, results in performance_comparison.items():
            if 'baseline_comparison' not in results:
                validation_results[target_variable] = False
                continue
            
            baseline_results = results['baseline_comparison'].get('baseline_results', {})
            
            # Check improvement for each horizon
            improvements = []
            for horizon_key, horizon_data in baseline_results.items():
                if 'linear_regression' in horizon_data:
                    lr_improvement = horizon_data['linear_regression'].get('improvement_vs_patchtst', 0)
                    improvements.append(lr_improvement)
                
                if 'persistence' in horizon_data:
                    persist_improvement = horizon_data['persistence'].get('improvement_vs_patchtst', 0)
                    improvements.append(persist_improvement)
            
            # Model passes if average improvement >= 15%
            avg_improvement = np.mean(improvements) if improvements else 0
            validation_results[target_variable] = avg_improvement >= 15.0
            
            logger.info(f"{target_variable}: Average improvement {avg_improvement:.1f}% "
                       f"(threshold: 15.0%) - {'PASS' if validation_results[target_variable] else 'FAIL'}")
        
        return validation_results
    
    def _generate_training_report(self, trained_models: Dict[str, ManufacturingPatchTSTModel]) -> None:
        """Generate comprehensive training report"""
        try:
            report = {
                'training_summary': {
                    'timestamp': datetime.now().isoformat(),
                    'total_models_trained': len(trained_models),
                    'target_variables': list(trained_models.keys()),
                    'forecast_horizons': self.forecast_config.forecast_horizons,
                    'training_config': self._serialize_config(self.training_config)
                },
                'model_performance': self.performance_comparison,
                'improvement_validation': self.validate_15_percent_improvement(self.performance_comparison)
            }
            
            # Save report
            report_path = Path(self.training_config.model_save_path) / 'training_report.json'
            report_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Custom JSON serializer to handle numpy types
            def convert_numpy_types(obj):
                """Convert numpy types to Python native types for JSON serialization"""
                import numpy as np
                # Handle numpy boolean types
                if hasattr(np, 'bool_') and isinstance(obj, np.bool_):
                    return bool(obj)
                # Handle numpy integer types  
                elif isinstance(obj, (np.integer, np.int8, np.int16, np.int32, np.int64, np.uint8, np.uint16, np.uint32, np.uint64)):
                    return int(obj)
                # Handle numpy float types
                elif isinstance(obj, (np.floating, np.float16, np.float32, np.float64)):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return str(obj)
            
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2, default=convert_numpy_types)
            
            logger.info(f"Training report saved to {report_path}")
            
            # Log summary
            self._log_training_summary(report)
            
        except Exception as e:
            logger.error(f"Error generating training report: {str(e)}")
    
    def _log_training_summary(self, report: Dict[str, Any]) -> None:
        """Log training summary to console"""
        logger.info("="*50)
        logger.info("TRAINING SUMMARY")
        logger.info("="*50)
        
        summary = report['training_summary']
        logger.info(f"Total models trained: {summary['total_models_trained']}")
        logger.info(f"Target variables: {', '.join(summary['target_variables'])}")
        logger.info(f"Forecast horizons: {summary['forecast_horizons']}")
        
        logger.info("\nIMPROVEMENT VALIDATION (15% threshold):")
        improvement_results = report['improvement_validation']
        for target, passed in improvement_results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            logger.info(f"  {target}: {status}")
        
        overall_pass = all(improvement_results.values())
        logger.info(f"\nOverall validation: {'✓ PASS' if overall_pass else '✗ FAIL'}")
        logger.info("="*50)
    
    def load_trained_models(self, model_directory: str) -> Dict[str, ManufacturingPatchTSTModel]:
        """Load all trained models from directory"""
        trained_models: Dict[str, ManufacturingPatchTSTModel] = {}
        
        model_dir = Path(model_directory)
        if not model_dir.exists():
            logger.warning(f"Model directory {model_directory} does not exist")
            return trained_models
        
        # Look for model subdirectories
        for target_var in self.forecast_config.target_variables:
            model_path = model_dir / f"patchtst_manufacturing_{target_var}"
            if model_path.exists():
                try:
                    model = ManufacturingPatchTSTModel.load_pretrained(str(model_path))
                    trained_models[target_var] = model
                    logger.info(f"Loaded model for {target_var}")
                except Exception as e:
                    logger.error(f"Error loading model for {target_var}: {str(e)}")
        
        return trained_models