"""
Configuration Management for Manufacturing PatchTST Forecasting

Handles forecasting configuration with manufacturing-specific parameters,
model hyperparameters, and deployment settings. Follows existing patterns
from the correlation analysis infrastructure.
"""

import json
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ForecastResult(BaseModel):
    """Structured forecast result following correlation agent patterns"""
    target_variable: str = Field(description="Variable being forecasted")
    forecast_horizon: int = Field(description="Number of time steps forecasted")
    forecast_values: List[float] = Field(description="Predicted future values")
    confidence_intervals: Dict[str, List[float]] = Field(
        description="95% confidence intervals", 
        default_factory=dict
    )
    forecast_timestamps: List[str] = Field(description="Timestamps for forecast period")
    model_performance: Dict[str, float] = Field(
        description="Model accuracy metrics",
        default_factory=dict
    )
    attention_insights: Dict[str, Any] = Field(
        description="Key temporal patterns identified",
        default_factory=dict
    )
    manufacturing_insights: List[str] = Field(
        description="Process optimization recommendations",
        default_factory=list
    )
    
    @validator('forecast_values')
    def validate_forecast_values(cls, v):
        """Validate forecast values are reasonable"""
        if not v:
            raise ValueError("Forecast values cannot be empty")
        if any(x is None for x in v):
            raise ValueError("Forecast values cannot contain None")
        return v
    
    @validator('forecast_horizon')
    def validate_forecast_horizon(cls, v):
        """Validate forecast horizon is positive"""
        if v <= 0:
            raise ValueError("Forecast horizon must be positive")
        if v > 1440:  # 24 hours at 1-minute intervals
            raise ValueError("Forecast horizon cannot exceed 24 hours")
        return v

class TrainingStabilityConfig(BaseModel):
    """Training stability configuration for enhanced PatchTST training"""
    
    # Gradient Stabilization
    gradient_clipping: Dict[str, Any] = Field(
        default={
            "enabled": True,
            "max_norm": 1.0,
            "norm_type": 2.0,
            "adaptive": True,
            "warmup_steps": 100
        },
        description="Gradient clipping configuration"
    )
    
    # Learning Rate Scheduling
    learning_rate_schedule: Dict[str, Any] = Field(
        default={
            "initial_lr": 5e-5,  # Conservative initial learning rate
            "warmup_ratio": 0.1,
            "decay_factor": 0.95,
            "decay_patience": 5,
            "min_lr": 1e-6
        },
        description="Learning rate scheduling configuration"
    )
    
    # Mixed Precision Training
    mixed_precision: Dict[str, Any] = Field(
        default={
            "enabled": True,
            "loss_scale": "dynamic",
            "init_loss_scale": 65536.0,
            "growth_factor": 2.0,
            "backoff_factor": 0.5,
            "growth_interval": 2000
        },
        description="Mixed precision training configuration"
    )
    
    # Early Stopping
    early_stopping: Dict[str, Any] = Field(
        default={
            "patience": 15,
            "min_delta": 0.001,
            "restore_best_weights": True,
            "monitor": "eval_loss"
        },
        description="Early stopping configuration"
    )
    
    # Stability Monitoring
    stability_monitoring: Dict[str, Any] = Field(
        default={
            "enabled": True,
            "log_frequency": 10,
            "save_frequency": 100,
            "instability_threshold": 0.2,
            "intervention_patience": 5
        },
        description="Training stability monitoring configuration"
    )
    
    @validator('gradient_clipping')
    def validate_gradient_clipping(cls, v):
        """Validate gradient clipping configuration"""
        if v.get('enabled', True):
            max_norm = v.get('max_norm', 1.0)
            if max_norm <= 0:
                raise ValueError("max_norm must be positive")
            if max_norm > 10.0:
                logger.warning(f"Large gradient clipping norm: {max_norm}")
        return v
    
    @validator('learning_rate_schedule')
    def validate_learning_rate_schedule(cls, v):
        """Validate learning rate scheduling configuration"""
        initial_lr = v.get('initial_lr', 5e-5)
        min_lr = v.get('min_lr', 1e-6)
        if initial_lr <= 0 or min_lr <= 0:
            raise ValueError("Learning rates must be positive")
        if min_lr >= initial_lr:
            raise ValueError("min_lr must be less than initial_lr")
        return v

class StabilizedPatchTSTConfig(BaseModel):
    """Enhanced PatchTST configuration with stability-focused parameters"""
    
    # Conservative model architecture for stability
    d_model: int = Field(default=64, description="Reduced model dimension for stability")
    num_attention_heads: int = Field(default=4, description="Reduced attention heads")
    num_hidden_layers: int = Field(default=2, description="Reduced layers for stability")
    
    # Enhanced dropout for regularization
    dropout: float = Field(default=0.3, description="Increased dropout for stability")
    head_dropout: float = Field(default=0.3, description="Head dropout rate")
    attention_dropout: float = Field(default=0.2, description="Attention dropout rate")
    
    # Normalization strategy
    norm_type: str = Field(default='unit_norm', description="Normalization type")
    norm_first: bool = Field(default=True, description="Apply normalization first")
    
    # Activation and other parameters
    activation: str = Field(default='gelu', description="Activation function")
    channel_attention: bool = Field(default=False, description="Channel attention")
    scaling: str = Field(default='std', description="Input scaling method")
    
    # Stability-specific parameters
    use_residual_connections: bool = Field(default=True, description="Use residual connections")
    gradient_checkpointing: bool = Field(default=False, description="Use gradient checkpointing")
    
    @validator('d_model')
    def validate_d_model(cls, v):
        """Validate model dimension"""
        if v <= 0:
            raise ValueError("d_model must be positive")
        if v > 512:
            logger.warning(f"Large d_model may cause stability issues: {v}")
        return v
    
    @validator('num_attention_heads')
    def validate_attention_heads(cls, v):
        """Validate number of attention heads"""
        if v <= 0:
            raise ValueError("num_attention_heads must be positive")
        if v > 16:
            logger.warning(f"Many attention heads may cause instability: {v}")
        return v
    
    @validator('dropout', 'head_dropout', 'attention_dropout')
    def validate_dropout_rates(cls, v):
        """Validate dropout rates"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Dropout rates must be between 0 and 1")
        return v
    
    @validator('norm_type')
    def validate_norm_type(cls, v):
        """Validate normalization type"""
        valid_types = ['unit_norm', 'batch_norm', 'layer_norm', 'adaptive_unit_norm']
        if v not in valid_types:
            raise ValueError(f"norm_type must be one of {valid_types}")
        return v

class ForecastConfig(BaseModel):
    """Manufacturing forecasting configuration"""
    
    # Input/Output Configuration
    input_variables: List[str] = Field(
        default=[
            "thickness_thickness_avg", "thickness_thickness_uniformity", "speed_Speed", 
            "stop_MPS Stop Duration", "fm_stack_Sheet Quantity", "fm_stack_Sheet Acceped"
        ],
        description="Input variables for forecasting model"
    )
    target_variables: List[str] = Field(
        default=["thickness_thickness_avg", "speed_Speed", "fm_stack_Total Sheet Rejected"],
        description="Target variables to forecast"
    )
    
    # Time Series Configuration
    forecast_horizons: List[int] = Field(
        default=[15, 60, 240],  # 15min, 1hr, 4hr
        description="Forecast horizons in minutes"
    )
    lookback_window: int = Field(
        default=240,
        description="Historical data window in minutes"
    )
    
    # Model Architecture
    patch_size: int = Field(
        default=16,
        description="Patch size for time series segmentation"
    )
    patch_stride: int = Field(
        default=16,
        description="Patch stride (use patch_size for non-overlapping)"
    )
    
    # PatchTST Model Parameters
    model_params: Dict[str, Any] = Field(
        default={
            "d_model": 128,
            "num_attention_heads": 8,
            "num_hidden_layers": 3,
            "dropout": 0.1,
            "channel_attention": False,
            "scaling": "std",
            "loss": "mse"
        },
        description="PatchTST model configuration"
    )
    
    # Manufacturing Domain Parameters
    manufacturing_params: Dict[str, Any] = Field(
        default={
            "thickness_range": [2.0, 20.0],  # mm
            "speed_range": [0.0, 100.0],     # m/min
            "quality_threshold": 0.95,
            "scrap_rate_threshold": 0.05
        },
        description="Manufacturing domain constraints"
    )
    
    @validator('forecast_horizons')
    def validate_forecast_horizons(cls, v):
        """Validate forecast horizons are reasonable"""
        if not v:
            raise ValueError("At least one forecast horizon must be specified")
        if any(h <= 0 for h in v):
            raise ValueError("All forecast horizons must be positive")
        if max(v) > 1440:  # 24 hours
            raise ValueError("Forecast horizons cannot exceed 24 hours")
        return sorted(v)
    
    @validator('lookback_window')
    def validate_lookback_window(cls, v):
        """Validate lookback window is reasonable"""
        if v <= 0:
            raise ValueError("Lookback window must be positive")
        if v > 10080:  # 1 week
            raise ValueError("Lookback window cannot exceed 1 week")
        return v
    
    @validator('patch_size')
    def validate_patch_size(cls, v):
        """Validate patch size is reasonable"""
        if v <= 0:
            raise ValueError("Patch size must be positive")
        if v > 64:
            raise ValueError("Patch size too large, may cause memory issues")
        return v
    
    @classmethod
    def from_json(cls, config_path: str) -> 'ForecastConfig':
        """Load configuration from JSON file"""
        config_path_obj = Path(config_path)
        if not config_path_obj.exists():
            logger.warning(f"Config file {config_path} not found, using defaults")
            return cls()
        
        try:
            with open(config_path_obj, 'r') as f:
                config_data = json.load(f)
            
            # Extract forecasting config if nested
            if 'forecasting_config' in config_data:
                config_data = config_data['forecasting_config']
            
            return cls(**config_data)
            
        except Exception as e:
            logger.error(f"Error loading config from {config_path}: {e}")
            logger.warning("Using default configuration")
            return cls()
    
    def to_json(self, config_path: str) -> None:
        """Save configuration to JSON file"""
        config_path_obj = Path(config_path)
        config_path_obj.parent.mkdir(parents=True, exist_ok=True)
        
        config_data = {
            "forecasting_config": self.dict()
        }
        
        with open(config_path_obj, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        logger.info(f"Configuration saved to {config_path}")

class PatchTSTTrainingConfig(BaseModel):
    """Enhanced PatchTST training configuration with stability features"""
    
    # Training Parameters
    batch_size: int = Field(default=16, description="Conservative batch size for stability")
    learning_rate: float = Field(default=5e-5, description="Conservative learning rate")
    max_epochs: int = Field(default=50, description="Maximum training epochs")
    early_stopping_patience: int = Field(default=15, description="Increased patience for stability")
    
    # Data Splitting
    validation_split: float = Field(default=0.15, description="Validation data proportion")
    test_split: float = Field(default=0.15, description="Test data proportion")
    
    # Model Checkpointing
    save_strategy: str = Field(default="steps", description="Model save strategy")
    evaluation_strategy: str = Field(default="steps", description="Evaluation strategy")
    metric_for_best_model: str = Field(default="eval_loss", description="Metric for best model")
    
    # Computational Resources
    num_workers: int = Field(default=2, description="Conservative number of workers")
    pin_memory: bool = Field(default=True, description="Pin memory for GPU training")
    
    # Model Storage
    model_save_path: str = Field(default="./models/", description="Model save directory")
    
    # Stability Configuration
    stability_config: Optional[TrainingStabilityConfig] = Field(
        default_factory=TrainingStabilityConfig,
        description="Training stability configuration"
    )
    
    # Stability Features
    use_stability_features: bool = Field(default=True, description="Enable stability enhancements")
    gradient_clip_norm: float = Field(default=1.0, description="Gradient clipping norm")
    adaptive_clipping: bool = Field(default=True, description="Use adaptive gradient clipping")
    
    # Enhanced Training Arguments
    warmup_ratio: float = Field(default=0.1, description="Learning rate warmup ratio")
    weight_decay: float = Field(default=0.01, description="Weight decay for regularization")
    gradient_accumulation_steps: int = Field(default=2, description="Gradient accumulation steps")
    
    # Mixed Precision
    fp16: bool = Field(default=True, description="Use mixed precision training")
    fp16_opt_level: str = Field(default="O1", description="Mixed precision optimization level")
    
    # Logging and Monitoring
    logging_steps: int = Field(default=10, description="Steps between logging")
    eval_steps: int = Field(default=100, description="Steps between evaluations")
    save_steps: int = Field(default=500, description="Steps between saves")
    
    @validator('batch_size')
    def validate_batch_size(cls, v):
        """Validate batch size is reasonable"""
        if v <= 0:
            raise ValueError("Batch size must be positive")
        if v > 256:
            logger.warning(f"Large batch size {v} may cause memory issues")
        return v
    
    @validator('learning_rate')
    def validate_learning_rate(cls, v):
        """Validate learning rate is reasonable"""
        if v <= 0:
            raise ValueError("Learning rate must be positive")
        if v > 0.1:
            logger.warning(f"High learning rate {v} may cause instability")
        return v
    
    @validator('validation_split', 'test_split')
    def validate_split(cls, v):
        """Validate data split proportions"""
        if v < 0 or v > 0.5:
            raise ValueError("Data split must be between 0 and 0.5")
        return v

def get_default_forecast_config() -> ForecastConfig:
    """Get default forecasting configuration optimized for manufacturing"""
    return ForecastConfig(
        input_variables=[
            "thickness_avg", "thickness_uniformity", "speed",
            "temperature", "pressure", "minutes_since_last_stop"
        ],
        target_variables=["thickness_avg", "scrap_rate", "quality_index"],
        forecast_horizons=[15, 60, 240],  # 15min, 1hr, 4hr
        lookback_window=240,  # 4 hours
        patch_size=16,
        model_params={
            "d_model": 128,
            "num_attention_heads": 8,
            "num_hidden_layers": 3,
            "dropout": 0.1,
            "channel_attention": False,
            "scaling": "std",
            "loss": "mse"
        }
    )

def get_default_training_config() -> PatchTSTTrainingConfig:
    """Get default enhanced training configuration with stability features"""
    return PatchTSTTrainingConfig(
        batch_size=16,
        learning_rate=5e-5,
        max_epochs=50,
        early_stopping_patience=15,
        validation_split=0.15,
        test_split=0.15,
        use_stability_features=True,
        gradient_clip_norm=1.0,
        adaptive_clipping=True,
        warmup_ratio=0.1,
        weight_decay=0.01,
        fp16=True
    )

def get_stabilized_training_config() -> PatchTSTTrainingConfig:
    """Get training configuration optimized for maximum stability"""
    stability_config = TrainingStabilityConfig(
        gradient_clipping={
            "enabled": True,
            "max_norm": 0.5,  # Very conservative
            "norm_type": 2.0,
            "adaptive": True,
            "warmup_steps": 200
        },
        learning_rate_schedule={
            "initial_lr": 1e-5,  # Very low learning rate
            "warmup_ratio": 0.2,  # Longer warmup
            "decay_factor": 0.9,
            "decay_patience": 3,
            "min_lr": 1e-7
        },
        early_stopping={
            "patience": 20,  # More patience
            "min_delta": 0.0001,
            "restore_best_weights": True,
            "monitor": "eval_loss"
        }
    )
    
    return PatchTSTTrainingConfig(
        batch_size=8,  # Very small batch size
        learning_rate=1e-5,
        max_epochs=30,
        early_stopping_patience=20,
        gradient_clip_norm=0.5,
        gradient_accumulation_steps=4,  # Larger effective batch size
        warmup_ratio=0.2,
        weight_decay=0.05,  # Higher regularization
        stability_config=stability_config,
        use_stability_features=True,
        fp16=True,
        save_strategy="steps",
        eval_steps=50,
        save_steps=250,
        logging_steps=5
    )

def get_stabilized_model_config() -> StabilizedPatchTSTConfig:
    """Get model configuration optimized for stability"""
    return StabilizedPatchTSTConfig(
        d_model=32,  # Very small for maximum stability
        num_attention_heads=2,
        num_hidden_layers=1,
        dropout=0.4,  # High dropout
        head_dropout=0.4,
        attention_dropout=0.3,
        norm_type='unit_norm',
        norm_first=True,
        activation='gelu',
        channel_attention=False,
        use_residual_connections=True,
        gradient_checkpointing=False
    )

def load_config_from_file(config_path: str) -> tuple[ForecastConfig, PatchTSTTrainingConfig]:
    """
    Load both forecasting and training configuration from JSON file
    
    Args:
        config_path: Path to configuration JSON file
        
    Returns:
        Tuple of (ForecastConfig, PatchTSTTrainingConfig)
    """
    config_path_obj = Path(config_path)
    if not config_path_obj.exists():
        logger.warning(f"Config file {config_path} not found, using defaults")
        return ForecastConfig(), PatchTSTTrainingConfig()
    
    try:
        with open(config_path_obj, 'r') as f:
            config_data = json.load(f)
        
        # Load forecasting config
        forecast_config_data = config_data.get('forecasting_config', {})
        forecast_config = ForecastConfig(**forecast_config_data)
        
        # Load training config  
        training_config_data = config_data.get('training_config', {})
        training_config = PatchTSTTrainingConfig(**training_config_data)
        
        logger.info(f"Configuration loaded from {config_path}")
        return forecast_config, training_config
        
    except Exception as e:
        logger.error(f"Error loading config from {config_path}: {e}")
        logger.warning("Using default configuration")
        return ForecastConfig(), PatchTSTTrainingConfig()