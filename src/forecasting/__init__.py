"""
Manufacturing PatchTST Forecasting Module (Phase 3.1 - Enhanced Stability)

Advanced time series forecasting capabilities for fiber cement manufacturing data
using PatchTST transformer architecture with stability enhancements. Integrates 
with existing correlation analysis infrastructure for comprehensive manufacturing intelligence.

Key Components:
    - PatchTST model training and inference with stability features
    - Manufacturing-specific preprocessing
    - Agent tool integration
    - Multi-horizon forecasting
    - Comprehensive model stability validation
    - Gradient clipping and normalization enhancements
    - Transfer learning capabilities

Key Stability Features:
    - UnitNorm layers for transformer stability
    - StabilizedTrainer with gradient clipping
    - Manufacturing-specific stability validation
    - Robust scaling and preprocessing
    - Conservative training configurations

Usage:
    ```python
    from src.forecasting import (
        ManufacturingPatchTSTModel, 
        ForecastConfig, 
        get_stabilized_training_config
    )
    
    config = ForecastConfig.from_json('config/forecasting_config.json')
    training_config = get_stabilized_training_config()
    model = ManufacturingPatchTSTModel(config, training_config)
    forecasts = model.forecast(historical_data, horizon=96)
    ```
"""

from .config import (
    ForecastConfig, 
    PatchTSTTrainingConfig,
    ForecastResult,
    TrainingStabilityConfig,
    StabilizedPatchTSTConfig,
    get_default_training_config,
    get_stabilized_training_config,
    get_stabilized_model_config
)
from .patchtst_model import ManufacturingPatchTSTModel
from .preprocessing import ManufacturingTimeSeriesPreprocessor
from .trainer import ManufacturingForecastTrainer
from .stability import (
    UnitNorm,
    StabilizedTrainer,
    ManufacturingStabilityValidator,
    GradientClipper,
    RobustScaler
)

__all__ = [
    'ForecastConfig',
    'PatchTSTTrainingConfig',
    'ForecastResult', 
    'TrainingStabilityConfig',
    'StabilizedPatchTSTConfig',
    'ManufacturingPatchTSTModel',
    'ManufacturingTimeSeriesPreprocessor',
    'ManufacturingForecastTrainer',
    'UnitNorm',
    'StabilizedTrainer',
    'ManufacturingStabilityValidator',
    'GradientClipper',
    'RobustScaler',
    'get_default_training_config',
    'get_stabilized_training_config',
    'get_stabilized_model_config'
]