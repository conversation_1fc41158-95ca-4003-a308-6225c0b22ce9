"""
Anthropic API Rate Limit Manager

Implements token bucket algorithm and comprehensive rate limiting strategies
for robust API interaction with Anthropic's Claude models.

Features:
    - Token bucket algorithm for proactive rate limiting
    - Request tracking with sliding window
    - Exponential backoff with jitter
    - Usage monitoring and error rate tracking
    - Adaptive throttling based on response headers
    - Circuit breaker pattern for repeated failures

Usage:
    ```python
    from src.utils.rate_limit_manager import RateLimitManager, api_retry
    
    # Create rate limit manager
    rate_manager = RateLimitManager(
        requests_per_minute=50,
        tokens_per_minute=40000
    )
    
    # Use as decorator
    @api_retry(rate_manager)
    async def make_api_call():
        return await client.messages.create(...)
    
    # Or use directly
    async with rate_manager.acquire():
        response = await client.messages.create(...)
    ```
"""

import asyncio
import time
import random
import logging
from collections import deque
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, Callable, TypeVar, Awaitable
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
import functools

logger = logging.getLogger(__name__)

T = TypeVar('T')

@dataclass
class RateLimitStats:
    """Statistics for rate limit monitoring"""
    total_requests: int = 0
    successful_requests: int = 0
    rate_limited_requests: int = 0
    connection_errors: int = 0
    other_errors: int = 0
    total_wait_time: float = 0.0
    last_reset_time: datetime = field(default_factory=datetime.now)
    
    def success_rate(self) -> float:
        """Calculate success rate as percentage"""
        if self.total_requests == 0:
            return 100.0
        return (self.successful_requests / self.total_requests) * 100.0
    
    def error_rate(self) -> float:
        """Calculate error rate as percentage"""
        if self.total_requests == 0:
            return 0.0
        return ((self.rate_limited_requests + self.connection_errors + self.other_errors) / self.total_requests) * 100.0
    
    def rate_limit_rate(self) -> float:
        """Calculate rate limit error rate as percentage"""
        if self.total_requests == 0:
            return 0.0
        return (self.rate_limited_requests / self.total_requests) * 100.0


class CircuitBreaker:
    """Circuit breaker for API calls"""
    
    def __init__(self, failure_threshold: int = 5, timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def can_proceed(self) -> bool:
        """Check if request can proceed"""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if time.time() - (self.last_failure_time or 0) > self.timeout:
                self.state = "HALF_OPEN"
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Record successful request"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def record_failure(self):
        """Record failed request"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class RateLimitManager:
    """
    Comprehensive rate limit manager for Anthropic API
    
    Implements token bucket algorithm with adaptive throttling
    and comprehensive error handling strategies.
    """
    
    def __init__(
        self,
        requests_per_minute: int = 50,
        tokens_per_minute: int = 40000,
        burst_capacity: Optional[int] = None,
        enable_adaptive_throttling: bool = True
    ):
        """
        Initialize rate limit manager
        
        Args:
            requests_per_minute: Maximum requests per minute
            tokens_per_minute: Maximum tokens per minute (input + output)
            burst_capacity: Burst capacity for requests (defaults to 2x rate)
            enable_adaptive_throttling: Enable adaptive throttling based on error rates
        """
        self.requests_per_minute = requests_per_minute
        self.tokens_per_minute = tokens_per_minute
        self.burst_capacity = burst_capacity or (requests_per_minute * 2)
        self.enable_adaptive_throttling = enable_adaptive_throttling
        
        # Token bucket for requests
        self.request_tokens = self.burst_capacity
        self.last_request_refill = time.time()
        
        # Token bucket for API tokens
        self.api_tokens = tokens_per_minute
        self.last_token_refill = time.time()
        
        # Request tracking
        self.request_times = deque()
        self.token_usage = deque()
        
        # Statistics and monitoring
        self.stats = RateLimitStats()
        self.circuit_breaker = CircuitBreaker()
        
        # Adaptive throttling
        self.throttle_factor = 1.0
        self.last_throttle_update = time.time()
        
        # Lock for thread safety
        self._lock = asyncio.Lock()
    
    def _refill_tokens(self):
        """Refill token buckets based on elapsed time"""
        now = time.time()
        
        # Refill request tokens
        elapsed = now - self.last_request_refill
        request_refill = (elapsed / 60.0) * self.requests_per_minute * self.throttle_factor
        self.request_tokens = min(self.burst_capacity, self.request_tokens + request_refill)
        self.last_request_refill = now
        
        # Refill API tokens
        token_elapsed = now - self.last_token_refill
        token_refill = (token_elapsed / 60.0) * self.tokens_per_minute * self.throttle_factor
        self.api_tokens = min(self.tokens_per_minute, self.api_tokens + token_refill)
        self.last_token_refill = now
    
    def _update_adaptive_throttling(self):
        """Update throttling factor based on recent error rates"""
        if not self.enable_adaptive_throttling:
            return
        
        now = time.time()
        if now - self.last_throttle_update < 30:  # Update every 30 seconds
            return
        
        recent_rate_limit_rate = self.stats.rate_limit_rate()
        
        if recent_rate_limit_rate > 20:  # High rate limiting
            self.throttle_factor = max(0.3, self.throttle_factor * 0.8)
            logger.info(f"Adaptive throttling: reducing to {self.throttle_factor:.2f}")
        elif recent_rate_limit_rate < 5:  # Low rate limiting
            self.throttle_factor = min(1.0, self.throttle_factor * 1.1)
            logger.debug(f"Adaptive throttling: increasing to {self.throttle_factor:.2f}")
        
        self.last_throttle_update = now
    
    async def _wait_for_capacity(self, estimated_tokens: int = 1000) -> float:
        """
        Wait until sufficient capacity is available
        
        Args:
            estimated_tokens: Estimated token usage for the request
            
        Returns:
            Time waited in seconds
        """
        wait_start = time.time()
        
        while True:
            self._refill_tokens()
            
            if self.request_tokens >= 1 and self.api_tokens >= estimated_tokens:
                break
            
            # Calculate wait time needed
            request_wait = 0
            if self.request_tokens < 1:
                request_wait = 60.0 / (self.requests_per_minute * self.throttle_factor)
            
            token_wait = 0
            if self.api_tokens < estimated_tokens:
                tokens_needed = estimated_tokens - self.api_tokens
                token_wait = (tokens_needed / self.tokens_per_minute) * 60.0 / self.throttle_factor
            
            wait_time = max(request_wait, token_wait, 0.1)
            logger.debug(f"Rate limit wait: {wait_time:.2f}s (requests: {self.request_tokens:.1f}, tokens: {self.api_tokens:.0f})")
            
            await asyncio.sleep(wait_time)
        
        return time.time() - wait_start
    
    @asynccontextmanager
    async def acquire(self, estimated_tokens: int = 1000):
        """
        Acquire rate limit tokens for API request
        
        Args:
            estimated_tokens: Estimated token usage for the request
        """
        async with self._lock:
            # Check circuit breaker
            if not self.circuit_breaker.can_proceed():
                raise Exception("Circuit breaker is open - too many recent failures")
            
            # Update adaptive throttling
            self._update_adaptive_throttling()
            
            # Wait for capacity
            wait_time = await self._wait_for_capacity(estimated_tokens)
            
            # Consume tokens
            self.request_tokens -= 1
            self.api_tokens -= estimated_tokens
            
            # Track request
            self.request_times.append(time.time())
            self.token_usage.append(estimated_tokens)
            
            # Clean old entries
            cutoff = time.time() - 60
            while self.request_times and self.request_times[0] < cutoff:
                self.request_times.popleft()
            while self.token_usage and len(self.token_usage) > len(self.request_times):
                self.token_usage.popleft()
            
            if wait_time > 0:
                self.stats.total_wait_time += wait_time
        
        try:
            yield
        except Exception:
            raise
    
    def record_request_result(self, success: bool, rate_limited: bool = False, 
                            connection_error: bool = False, actual_tokens: Optional[int] = None):
        """
        Record the result of an API request
        
        Args:
            success: Whether the request was successful
            rate_limited: Whether the request was rate limited
            connection_error: Whether there was a connection error
            actual_tokens: Actual tokens used (for refund if estimate was wrong)
        """
        self.stats.total_requests += 1
        
        if success:
            self.stats.successful_requests += 1
            self.circuit_breaker.record_success()
        else:
            self.circuit_breaker.record_failure()
            
            if rate_limited:
                self.stats.rate_limited_requests += 1
            elif connection_error:
                self.stats.connection_errors += 1
            else:
                self.stats.other_errors += 1
        
        # Refund tokens if actual usage was lower than estimate
        if actual_tokens is not None and len(self.token_usage) > 0:
            estimated = self.token_usage[-1]
            if actual_tokens < estimated:
                refund = estimated - actual_tokens
                self.api_tokens = min(self.tokens_per_minute, self.api_tokens + refund)
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current rate limit status"""
        self._refill_tokens()
        
        return {
            "request_tokens_available": self.request_tokens,
            "api_tokens_available": self.api_tokens,
            "requests_per_minute_limit": self.requests_per_minute,
            "tokens_per_minute_limit": self.tokens_per_minute,
            "throttle_factor": self.throttle_factor,
            "circuit_breaker_state": self.circuit_breaker.state,
            "stats": {
                "total_requests": self.stats.total_requests,
                "success_rate": self.stats.success_rate(),
                "error_rate": self.stats.error_rate(),
                "rate_limit_rate": self.stats.rate_limit_rate(),
                "total_wait_time": self.stats.total_wait_time
            }
        }


def calculate_exponential_backoff(attempt: int, base_delay: float = 1.0, 
                                max_delay: float = 60.0, jitter: bool = True) -> float:
    """
    Calculate exponential backoff delay with optional jitter
    
    Args:
        attempt: Attempt number (0-based)
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        jitter: Whether to add jitter
        
    Returns:
        Delay in seconds
    """
    delay = min(max_delay, base_delay * (2 ** attempt))
    
    if jitter:
        delay += random.uniform(0, 1)
    
    return delay


def api_retry(
    rate_manager: RateLimitManager,
    max_retries: int = 5,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_on_rate_limit: bool = True
):
    """
    Decorator for API calls with rate limiting and retry logic
    
    Args:
        rate_manager: Rate limit manager instance
        max_retries: Maximum number of retries
        base_delay: Base delay for exponential backoff
        max_delay: Maximum delay between retries
        backoff_on_rate_limit: Whether to use exponential backoff on rate limits
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    # Estimate token usage (could be improved with actual analysis)
                    estimated_tokens = kwargs.get('estimated_tokens', 1000)
                    
                    async with rate_manager.acquire(estimated_tokens):
                        result = await func(*args, **kwargs)
                        rate_manager.record_request_result(success=True)
                        return result
                
                except Exception as e:
                    last_exception = e
                    
                    # Determine error type
                    error_name = type(e).__name__
                    is_rate_limit = 'RateLimitError' in error_name or 'rate_limit' in str(e).lower()
                    is_connection = 'ConnectionError' in error_name or 'connection' in str(e).lower()
                    
                    rate_manager.record_request_result(
                        success=False,
                        rate_limited=is_rate_limit,
                        connection_error=is_connection
                    )
                    
                    # Don't retry on final attempt
                    if attempt >= max_retries:
                        break
                    
                    # Calculate delay
                    if is_rate_limit and backoff_on_rate_limit:
                        delay = calculate_exponential_backoff(attempt, base_delay, max_delay)
                        logger.warning(f"Rate limit hit, retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries})")
                    elif is_connection:
                        delay = min(5.0, base_delay * (attempt + 1))
                        logger.warning(f"Connection error, retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries})")
                    else:
                        # Don't retry other errors
                        break
                    
                    await asyncio.sleep(delay)
            
            # Re-raise the last exception if all retries failed
            if last_exception:
                raise last_exception
            
            # This should never be reached, but just in case
            raise Exception("Unknown error occurred during API call")
        
        return wrapper
    return decorator