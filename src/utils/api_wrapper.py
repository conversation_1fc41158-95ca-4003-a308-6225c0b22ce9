"""
Rate-Limited API Wrapper for Anthropic Claude

Provides a wrapper around PydanticAI agents with integrated rate limiting,
error handling, and fallback strategies for production-ready AI applications.

Features:
    - Automatic rate limiting with token bucket algorithm
    - Exponential backoff retry logic for API failures
    - Circuit breaker pattern for repeated failures
    - Fallback responses for critical operations
    - Comprehensive error logging and monitoring
    - Usage statistics and performance tracking

Usage:
    ```python
    from src.utils.api_wrapper import RateLimitedAgent
    
    # Create rate-limited agent wrapper
    wrapped_agent = RateLimitedAgent(
        agent=correlation_agent,
        requests_per_minute=50,
        enable_fallbacks=True
    )
    
    # Use normally
    result = await wrapped_agent.run(query, deps=deps)
    ```
"""

import asyncio
import logging
from typing import Any, Dict, Optional, TypeVar, Generic, Callable, Awaitable
from datetime import datetime
from pydantic_ai import Agent, RunContext

from .rate_limit_manager import RateLimitManager, api_retry

logger = logging.getLogger(__name__)

T = TypeVar('T')

class RateLimitedAgent(Generic[T]):
    """
    Wrapper for PydanticAI agents with integrated rate limiting and error handling
    """
    
    def __init__(
        self,
        agent: Agent[Any, T],
        requests_per_minute: int = 50,
        tokens_per_minute: int = 40000,
        enable_fallbacks: bool = True,
        enable_adaptive_throttling: bool = True,
        max_retries: int = 5
    ):
        """
        Initialize rate-limited agent wrapper
        
        Args:
            agent: PydanticAI agent to wrap
            requests_per_minute: Maximum requests per minute
            tokens_per_minute: Maximum tokens per minute
            enable_fallbacks: Enable fallback responses on failures
            enable_adaptive_throttling: Enable adaptive rate limiting
            max_retries: Maximum retry attempts
        """
        self.agent = agent
        self.enable_fallbacks = enable_fallbacks
        self.max_retries = max_retries
        
        # Create rate limit manager
        self.rate_manager = RateLimitManager(
            requests_per_minute=requests_per_minute,
            tokens_per_minute=tokens_per_minute,
            enable_adaptive_throttling=enable_adaptive_throttling
        )
        
        # Performance tracking
        self.total_calls = 0
        self.successful_calls = 0
        self.fallback_responses = 0
        
        logger.info(f"Initialized RateLimitedAgent with {requests_per_minute} RPM, {tokens_per_minute} TPM")
    
    def _estimate_tokens(self, query: str, deps: Any = None) -> int:
        """
        Estimate token usage for a query
        
        Args:
            query: Query string
            deps: Dependencies object
            
        Returns:
            Estimated token count
        """
        # Simple estimation based on query length
        # This could be improved with more sophisticated analysis
        base_tokens = len(query.split()) * 1.3  # ~1.3 tokens per word
        
        # Add overhead for system prompts and context
        overhead = 500
        
        # Add data context if available
        if deps and hasattr(deps, 'data') and hasattr(deps.data, 'shape'):
            # Add tokens for data description
            data_context = deps.data.shape[0] * 0.01  # Small factor for data size
            overhead += min(data_context, 1000)  # Cap at 1000 tokens
        
        return int(base_tokens + overhead)
    
    def _create_fallback_response(self, query: str, error: Exception, deps: Any = None) -> Any:
        """
        Create fallback response when API calls fail
        
        Args:
            query: Original query
            error: Error that occurred
            deps: Dependencies object
            
        Returns:
            Fallback response object
        """
        if not self.enable_fallbacks:
            raise error
        
        self.fallback_responses += 1
        logger.warning(f"Creating fallback response due to: {error}")
        
        # Import here to avoid circular imports
        from ..agents.correlation_agent import CorrelationAnalysis, CorrelationInsight
        
        # Create basic fallback based on the expected output type
        if hasattr(self.agent, 'output_type'):
            output_type = self.agent.output_type
            
            if output_type.__name__ == 'CorrelationAnalysis':
                return CorrelationAnalysis(
                    dataset_summary={
                        "shape": list(deps.data.shape) if deps and hasattr(deps, 'data') else [0, 0],
                        "columns": list(deps.data.columns) if deps and hasattr(deps, 'data') else [],
                        "error": str(error),
                        "fallback_response": True
                    },
                    significant_correlations=[],
                    correlation_matrix=None,
                    insights=[
                        CorrelationInsight(
                            insight_type="error",
                            description=f"Analysis temporarily unavailable due to API limitations: {str(error)}",
                            supporting_evidence={"error_details": str(error), "fallback": True},
                            confidence_level="low",
                            actionable_recommendation="Please try again in a few minutes or check your API quota"
                        )
                    ],
                    recommendations=[
                        "Check API rate limits and quota",
                        "Verify network connectivity", 
                        "Try again with a simpler query",
                        "Contact support if issue persists"
                    ],
                    data_quality_score=0.0,
                    analysis_metadata={
                        "analysis_timestamp": datetime.now().isoformat(),
                        "analysis_type": "fallback",
                        "error": str(error),
                        "fallback_response": True,
                        "rate_limit_status": self.rate_manager.get_current_status()
                    }
                )
        
        # Generic fallback
        return {
            "error": str(error),
            "fallback_response": True,
            "timestamp": datetime.now().isoformat(),
            "rate_limit_status": self.rate_manager.get_current_status(),
            "suggestions": [
                "Check API rate limits and quota",
                "Verify network connectivity",
                "Try again in a few minutes"
            ]
        }
    
    @api_retry(rate_manager=None, max_retries=5)  # Will be set dynamically
    async def _make_api_call(self, query: str, deps: Any = None, **kwargs) -> T:
        """
        Make API call with rate limiting
        
        Args:
            query: Query string
            deps: Dependencies object
            **kwargs: Additional arguments
            
        Returns:
            Agent response
        """
        # Estimate token usage
        estimated_tokens = self._estimate_tokens(query, deps)
        
        # Use rate manager to control access
        async with self.rate_manager.acquire(estimated_tokens):
            result = await self.agent.run(query, deps=deps, **kwargs)
            return result.output
    
    async def run(self, query: str, deps: Any = None, **kwargs) -> T:
        """
        Run agent with rate limiting and error handling
        
        Args:
            query: Query string
            deps: Dependencies object
            **kwargs: Additional arguments for agent.run()
            
        Returns:
            Agent response or fallback response
        """
        self.total_calls += 1
        
        # Update the decorator with our rate manager
        decorated_call = api_retry(
            rate_manager=self.rate_manager,
            max_retries=self.max_retries
        )(self._make_api_call_raw)
        
        try:
            result = await decorated_call(query, deps, **kwargs)
            self.successful_calls += 1
            return result
            
        except Exception as e:
            logger.error(f"Agent call failed after retries: {e}")
            
            # Check if we should provide fallback
            if self.enable_fallbacks:
                return self._create_fallback_response(query, e, deps)
            else:
                raise e
    
    async def _make_api_call_raw(self, query: str, deps: Any = None, **kwargs) -> T:
        """Raw API call without rate limiting (used by decorator)"""
        result = await self.agent.run(query, deps=deps, **kwargs)
        return result.output
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status including rate limiting and performance"""
        status = self.rate_manager.get_current_status()
        
        status.update({
            "agent_stats": {
                "total_calls": self.total_calls,
                "successful_calls": self.successful_calls,
                "success_rate": (self.successful_calls / self.total_calls * 100) if self.total_calls > 0 else 100.0,
                "fallback_responses": self.fallback_responses,
                "fallback_rate": (self.fallback_responses / self.total_calls * 100) if self.total_calls > 0 else 0.0
            },
            "configuration": {
                "enable_fallbacks": self.enable_fallbacks,
                "max_retries": self.max_retries,
                "requests_per_minute": self.rate_manager.requests_per_minute,
                "tokens_per_minute": self.rate_manager.tokens_per_minute
            }
        })
        
        return status
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.total_calls = 0
        self.successful_calls = 0
        self.fallback_responses = 0
        self.rate_manager.stats = type(self.rate_manager.stats)()
        logger.info("Reset agent statistics")


# Global rate-limited agent instances
_rate_limited_agents: Dict[str, RateLimitedAgent] = {}


def get_rate_limited_agent(
    agent: Agent[Any, T],
    agent_name: str = "default",
    requests_per_minute: int = 50,
    tokens_per_minute: int = 40000,
    enable_fallbacks: bool = True,
    force_recreate: bool = False
) -> RateLimitedAgent[T]:
    """
    Get or create a rate-limited agent wrapper
    
    Args:
        agent: PydanticAI agent to wrap
        agent_name: Name for the agent (for caching)
        requests_per_minute: Maximum requests per minute
        tokens_per_minute: Maximum tokens per minute
        enable_fallbacks: Enable fallback responses
        force_recreate: Force recreation of existing agent
        
    Returns:
        Rate-limited agent wrapper
    """
    if agent_name in _rate_limited_agents and not force_recreate:
        return _rate_limited_agents[agent_name]
    
    wrapped_agent = RateLimitedAgent(
        agent=agent,
        requests_per_minute=requests_per_minute,
        tokens_per_minute=tokens_per_minute,
        enable_fallbacks=enable_fallbacks
    )
    
    _rate_limited_agents[agent_name] = wrapped_agent
    return wrapped_agent


def get_all_agent_status() -> Dict[str, Dict[str, Any]]:
    """Get status for all rate-limited agents"""
    return {
        name: agent.get_status() 
        for name, agent in _rate_limited_agents.items()
    }