"""
Interactive Multi-Method Manufacturing Correlation Analysis CLI (Phase 1.5)

Advanced command-line interface providing comprehensive multi-method correlation analysis
capabilities for fiber cement manufacturing data using AI agents. Supports interactive
and batch analysis modes with intelligent method selection and convergence analysis.

Features:
    - Interactive conversational interface with AI agents
    - Multi-method correlation analysis (<PERSON>, <PERSON><PERSON>, <PERSON>)
    - Batch analysis mode for automation and scripting
    - High-precision correlation display (6-decimal precision)
    - Scientific notation for small p-values (< 0.001)
    - Enhanced visualization export (PNG, HTML, JSON)
    - Manufacturing domain expertise with thickness sensor integration
    - Automatic data loading and validation for 295K+ manufacturing records

Commands:
    - `interactive`: Start conversational analysis session
    - `analyze`: Single-query batch analysis with export
    - Built-in commands: /help, /load, /data, /settings, /analyze, /export, /clear, /quit

Usage:
    ```bash
    # Interactive multi-method analysis
    python -m src.cli interactive
    
    # Batch multi-method query
    python -m src.cli analyze -q "Compare all three correlation methods" -t multi_method
    
    # Method selection guidance
    python -m src.cli analyze -q "Which method for speed vs thickness?" -t method_selection
    ```

Analysis Types:
    - general: Comprehensive correlation discovery
    - multi_method: Comp<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> methods
    - method_convergence: Assess method stability and agreement  
    - method_selection: Get method recommendations based on data characteristics
    - unified_table: 83-column unified manufacturing table analysis
    - stratified: Stratified analysis by shift, lag, efficiency, machine type
    - pattern_identification: Quality patterns and operational risk factors
    - ml_prediction: Machine learning quality prediction and feature importance
    - time_series: Temporal patterns and rolling correlation analysis
    - lag: Time-lagged correlation analysis
    - quality: Quality-focused manufacturing analysis
    - optimization: Process optimization opportunities
    - rca: Root cause analysis

Environment Requirements:
    - ANTHROPIC_API_KEY and ANTHROPIC_MODEL (for Claude)
    - OR Vertex AI credentials (for Gemini)
    - All required environment variables must be set (strict validation)
"""

import asyncio
import click
import pandas as pd
import numpy as np
from pathlib import Path
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.markdown import Markdown
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
import os
from datetime import datetime
from typing import Optional
import json
from dotenv import load_dotenv

# Import our modules
try:
    from .data.loader import ManufacturingDataLoader
except ImportError:
    from data.loader import ManufacturingDataLoader
# Skip correlation agent import for now - will be loaded on demand
analyze_manufacturing_correlations = None

def get_correlation_agent():
    """Lazy import of correlation agent to avoid import issues."""
    global analyze_manufacturing_correlations
    if analyze_manufacturing_correlations is None:
        try:
            from .agents.correlation_agent import analyze_manufacturing_correlations as _analyze
            analyze_manufacturing_correlations = _analyze
        except ImportError as e:
            console.print(f"[red]Error importing correlation agent: {e}[/red]")
            console.print("[yellow]Correlation analysis is not available[/yellow]")
            return None
    return analyze_manufacturing_correlations

# Load environment variables
load_dotenv()

console = Console()

class ManufacturingCorrelationCLI:
    """
    Interactive CLI for manufacturing correlation analysis
    """
    
    def __init__(self, data_dir: str = "test-data"):
        """
        Initialize the CLI interface.
        
        Args:
            data_dir: Directory containing manufacturing data files
        """
        self.data_dir = Path(data_dir)
        self.loader = ManufacturingDataLoader(str(data_dir))
        self.loaded_data = {}
        self.unified_data = None
        self.conversation_history = []
        self.running = True
        
        # Analysis settings
        self.settings = {
            'significance_threshold': float(os.getenv('DEFAULT_SIGNIFICANCE_THRESHOLD', 0.05)),
            'min_correlation': float(os.getenv('DEFAULT_MIN_CORRELATION', 0.01)),  # Lowered to capture small correlations
            'correlation_method': os.getenv('DEFAULT_CORRELATION_METHOD', 'pearson'),
            'time_window': int(os.getenv('DEFAULT_TIME_WINDOW', 60))
        }
    
    async def start(self):
        """Start the interactive CLI session"""
        
        # Welcome message
        welcome_text = """
# 🏭 Manufacturing Correlation Analysis CLI

Welcome to the AI-powered correlation analysis system for fiber cement manufacturing!

**Available Commands:**
- `/help` - Show detailed help and commands
- `/load` - Load manufacturing data files
- `/data` - Show loaded data summary
- `/settings` - View and modify analysis settings
- `/analyze` - Run comprehensive correlation analysis
- `/export` - Export results to file
- `/clear` - Clear conversation history
- `/quit` - Exit the CLI

**Analysis Types:**
- General correlation discovery
- Lag-based correlation analysis  
- Quality-focused analysis
- Process optimization analysis
- Root cause analysis

Type your questions about manufacturing correlations, or use commands to manage data and settings.
        """
        
        console.print(Panel(Markdown(welcome_text), title="Welcome", border_style="blue"))
        
        # Auto-load data if available
        if self.data_dir.exists():
            await self._auto_load_data()
        
        # Check for API key
        self._check_api_configuration()
        
        # Main interaction loop
        while self.running:
            try:
                # Get user input
                user_input = Prompt.ask(
                    "[bold cyan]You[/bold cyan]",
                    default=""
                )
                
                if not user_input.strip():
                    continue
                
                # Handle commands
                if user_input.startswith('/'):
                    await self._handle_command(user_input)
                else:
                    # Process with AI agent
                    await self._process_with_agent(user_input)
                    
            except KeyboardInterrupt:
                if Confirm.ask("\nDo you want to exit?"):
                    break
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
        
        console.print("\n[bold green]Thank you for using Manufacturing Correlation Analysis![/bold green]")
    
    def _check_api_configuration(self):
        """Check if API keys are configured"""
        provider = os.getenv('LLM_PROVIDER', 'ANTHROPIC').upper()
        
        if provider == 'ANTHROPIC':
            if not os.getenv('ANTHROPIC_API_KEY'):
                console.print("[yellow]⚠️  ANTHROPIC_API_KEY not configured. Please set in .env file.[/yellow]")
        elif provider == 'VERTEX_AI':
            if not os.getenv('VERTEX_AI_PROJECT'):
                console.print("[yellow]⚠️  VERTEX_AI_PROJECT not configured. Please set in .env file.[/yellow]")
    
    async def _auto_load_data(self):
        """Automatically load available data files"""
        try:
            # Check if unified table already exists
            unified_table_path = Path("test-data/consolidated/test_matched_stacks.csv")
            if unified_table_path.exists():
                # Read header comments to get export info
                try:
                    with open(unified_table_path, 'r') as f:
                        lines = []
                        for i, line in enumerate(f):
                            if i < 10 and line.startswith('#'):
                                lines.append(line.strip())
                            else:
                                break
                    
                    # Extract export date and record count
                    export_date = None
                    total_records = None
                    for line in lines:
                        if "Export Date:" in line:
                            export_date = line.split("Export Date: ")[1]
                        elif "Total Records:" in line:
                            total_records = line.split("Total Records: ")[1].replace(',', '')
                    
                    # Ask user if they want to use existing unified table
                    console.print(f"[cyan]Unified table found![/cyan]")
                    if export_date:
                        console.print(f"Export date: {export_date}")
                    if total_records:
                        console.print(f"Total records: {total_records}")
                    
                    use_existing = Confirm.ask("Use existing unified table?", default=True)
                    
                    if use_existing:
                        # Load unified table directly
                        with console.status("[bold green]Loading unified table..."):
                            import pandas as pd
                            self.unified_data = pd.read_csv(unified_table_path, comment='#', low_memory=False)
                        console.print(f"[green]✓ Loaded unified table: {self.unified_data.shape}[/green]")
                        console.print(f"[green]✓ Skipped individual dataset loading (using unified table)[/green]")
                        
                        # Set loaded_data to indicate we have data loaded
                        self.loaded_data = {"unified_table": self.unified_data}
                        return
                        
                except Exception as e:
                    console.print(f"[yellow]Could not read unified table info: {e}[/yellow]")
            
            # Proceed with normal loading if unified table not used
            with console.status("[bold green]Loading manufacturing data..."):
                self.loaded_data = self.loader.load_all_data_sources()
            
            if self.loaded_data:
                console.print(f"[green]✓ Automatically loaded {len(self.loaded_data)} datasets[/green]")
                
                # Show quick summary
                for data_type, df in self.loaded_data.items():
                    console.print(f"  {data_type}: {len(df)} records")
                
                # Create unified dataset
                try:
                    with console.status("[bold green]Creating unified timeline..."):
                        self.unified_data = self.loader.create_unified_table()
                    console.print(f"[green]✓ Created unified dataset: {self.unified_data.shape}[/green]")
                except Exception as e:
                    console.print(f"[yellow]Could not create unified dataset: {e}[/yellow]")
            else:
                console.print("[yellow]No data files found in the specified directory[/yellow]")
                
        except Exception as e:
            console.print(f"[red]Error loading data: {e}[/red]")
    
    async def _handle_command(self, command: str):
        """Handle special commands"""
        parts = command[1:].split()  # Remove '/' and split
        cmd = parts[0].lower()
        
        if cmd == 'help':
            await self._show_help()
        
        elif cmd == 'load':
            await self._load_data()
        
        elif cmd == 'data':
            await self._show_data_summary()
        
        elif cmd == 'settings':
            await self._manage_settings()
        
        elif cmd == 'config':
            await self._show_config_status()
        
        elif cmd == 'status':
            await self._show_api_status()
        
        elif cmd == 'analyze':
            analysis_type = parts[1] if len(parts) > 1 else 'general'
            await self._run_analysis(analysis_type)
        
        elif cmd == 'export':
            filename = parts[1] if len(parts) > 1 else None
            await self._export_results(filename)
        
        elif cmd == 'clear':
            self.conversation_history = []
            console.print("[yellow]Conversation history cleared[/yellow]")
        
        elif cmd == 'quit' or cmd == 'exit':
            self.running = False
        
        else:
            console.print(f"[red]Unknown command: {command}[/red]")
            console.print("Type `/help` for available commands")
    
    async def _show_help(self):
        """Show detailed help information"""
        help_text = """
# 📚 Manufacturing Correlation Analysis Help

## Commands:
- `/help` - Show this help message
- `/load` - (Re)load manufacturing data files
- `/config` - Show API configuration and environment settings
- `/status` - Show API rate limit and performance status
- `/data` - Show summary of loaded datasets
- `/settings` - View and modify analysis settings
- `/analyze [type]` - Run correlation analysis (types: general, lag, quality, optimization, rca)
- `/export [filename]` - Export analysis results to JSON file
- `/clear` - Clear conversation history
- `/quit` - Exit the CLI

## Analysis Types:
- **general** - Comprehensive correlation discovery
- **lag** - Time-lagged correlation analysis
- **quality** - Quality-focused correlation analysis
- **optimization** - Process optimization analysis
- **rca** - Root cause analysis

## Example Questions:
- "What correlations exist between speed and thickness?"
- "Analyze the impact of stoppages on scrap rates"
- "Show me lag correlations between temperature and quality"
- "What process variables predict quality issues?"
- "Find correlations above 0.5 with high significance"

## Data Requirements:
The system expects CSV files in the data directory:
- `stop.csv` - Machine stoppage events
- `speed.csv` - Production line speed data
- `thickness.csv` - Product thickness measurements
- `fm_stack.csv`, `sm_stack.csv` - Scrap/stack data

## Settings:
- Significance threshold: {significance_threshold}
- Minimum correlation: {min_correlation}
- Correlation method: {correlation_method}
- Time window: {time_window} minutes

Use `/settings` to modify these values.
        """.format(**self.settings)
        
        console.print(Panel(Markdown(help_text), title="Help", border_style="green"))
    
    async def _load_data(self):
        """Load or reload manufacturing data"""
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Loading manufacturing data...", total=None)
                self.loaded_data = self.loader.load_all_data_sources()
                progress.update(task, description="Creating unified dataset...")
                
                if self.loaded_data:
                    try:
                        self.unified_data = self.loader.create_unified_table()
                    except Exception as e:
                        console.print(f"[yellow]Could not create unified dataset: {e}[/yellow]")
            
            if self.loaded_data:
                console.print(f"[green]✓ Loaded {len(self.loaded_data)} datasets[/green]")
                
                # Create summary table
                table = Table(title="Loaded Datasets", show_header=True, header_style="bold magenta")
                table.add_column("Dataset", style="cyan")
                table.add_column("Records", justify="right", style="green")
                table.add_column("Variables", justify="right", style="yellow")
                table.add_column("Time Range", style="blue")
                
                for data_type, df in self.loaded_data.items():
                    # Get time range
                    time_range = "No timestamps"
                    if 'timestamp' in df.columns:
                        try:
                            timestamps = pd.to_datetime(df['timestamp'], errors='coerce').dropna()
                            if len(timestamps) > 0:
                                time_range = f"{timestamps.min().date()} to {timestamps.max().date()}"
                        except Exception:
                            time_range = "Invalid timestamps"
                    
                    table.add_row(data_type, str(len(df)), str(len(df.columns)), time_range)
                
                console.print(table)
                
                if self.unified_data is not None:
                    console.print(f"[green]✓ Unified dataset: {self.unified_data.shape[0]} rows, {self.unified_data.shape[1]} columns[/green]")
            else:
                console.print("[yellow]No data files found[/yellow]")
                
        except Exception as e:
            console.print(f"[red]Error loading data: {e}[/red]")
    
    async def _show_data_summary(self):
        """Show summary of loaded data"""
        if not self.loaded_data:
            console.print("[yellow]No data loaded. Use `/load` to load data.[/yellow]")
            return
        
        console.print("[bold]Data Summary:[/bold]")
        
        # Overall summary
        total_records = sum(len(df) for df in self.loaded_data.values())
        console.print(f"Total datasets: {len(self.loaded_data)}")
        console.print(f"Total records: {total_records:,}")
        
        if self.unified_data is not None:
            console.print(f"Unified dataset: {self.unified_data.shape[0]:,} rows, {self.unified_data.shape[1]} columns")
            
            # Show numeric variables available for correlation
            numeric_cols = self.unified_data.select_dtypes(include=[pd.api.types.is_numeric_dtype]).columns
            console.print(f"Numeric variables for correlation: {len(numeric_cols)}")
            
            if len(numeric_cols) > 0:
                console.print("Variables:", ", ".join(numeric_cols.tolist()[:10]))
                if len(numeric_cols) > 10:
                    console.print(f"... and {len(numeric_cols) - 10} more")
        
        # Individual dataset details
        for data_type, df in self.loaded_data.items():
            console.print(f"\n[cyan]{data_type.upper()}:[/cyan]")
            console.print(f"  Records: {len(df):,}")
            console.print(f"  Variables: {len(df.columns)}")
            
            # Data quality
            missing_pct = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
            quality_color = "green" if missing_pct < 5 else "yellow" if missing_pct < 20 else "red"
            console.print(f"  Missing data: [{quality_color}]{missing_pct:.1f}%[/{quality_color}]")
            
            # Time range
            if 'timestamp' in df.columns:
                try:
                    timestamps = pd.to_datetime(df['timestamp'], errors='coerce').dropna()
                    if len(timestamps) > 0:
                        console.print(f"  Time range: {timestamps.min()} to {timestamps.max()}")
                        console.print(f"  Valid timestamps: {len(timestamps)}/{len(df)}")
                except Exception:
                    console.print("  Time range: Invalid timestamps")
    
    async def _manage_settings(self):
        """View and modify analysis settings"""
        console.print("[bold]Current Analysis Settings:[/bold]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Setting", style="cyan")
        table.add_column("Current Value", style="green")
        table.add_column("Description", style="white")
        
        table.add_row("significance_threshold", str(self.settings['significance_threshold']), "P-value threshold for statistical significance")
        table.add_row("min_correlation", str(self.settings['min_correlation']), "Minimum correlation coefficient to report")
        table.add_row("correlation_method", self.settings['correlation_method'], "Correlation method (pearson, spearman, kendall)")
        table.add_row("time_window", str(self.settings['time_window']), "Default time window for analysis (minutes)")
        
        console.print(table)
        
        if Confirm.ask("Do you want to modify any settings?"):
            # Modify settings interactively
            for setting, current_value in self.settings.items():
                new_value = Prompt.ask(
                    f"Enter new value for {setting} (current: {current_value})",
                    default=str(current_value)
                )
                
                # Type conversion
                try:
                    if setting in ['significance_threshold', 'min_correlation']:
                        self.settings[setting] = float(new_value)
                    elif setting == 'time_window':
                        self.settings[setting] = int(new_value)
                    else:
                        self.settings[setting] = new_value
                except ValueError:
                    console.print(f"[red]Invalid value for {setting}, keeping current value[/red]")
            
            console.print("[green]Settings updated![/green]")
    
    async def _show_config_status(self):
        """Show API configuration and environment settings"""
        try:
            # Import here to avoid circular imports
            from .config.api_config import get_config_summary, validate_api_credentials
            
            console.print("[bold]API Configuration Status:[/bold]")
            
            # Get configuration summary
            config_summary = get_config_summary()
            validation = validate_api_credentials()
            
            # Environment and basic info
            console.print(f"\n[cyan]Environment Configuration:[/cyan]")
            env_table = Table(show_header=True, header_style="bold magenta")
            env_table.add_column("Setting", style="cyan")
            env_table.add_column("Value", style="green")
            env_table.add_column("Status", style="white")
            
            # Environment
            env_name = config_summary["environment"]
            env_status = "🟢 Valid" if env_name in ["development", "testing", "staging", "production"] else "🟡 Custom"
            env_table.add_row("Environment", env_name, env_status)
            
            # API Tier
            api_tier = config_summary["api_tier"]
            tier_status = "🟢 Recognized" if api_tier in ["free", "pro", "team", "enterprise"] else "🟡 Custom"
            env_table.add_row("API Tier", api_tier, tier_status)
            
            # Model
            model = config_summary["model"]
            env_table.add_row("Model", model, "🟢 Set")
            
            console.print(env_table)
            
            # Rate limits
            console.print(f"\n[cyan]Rate Limit Configuration:[/cyan]")
            rate_table = Table(show_header=True, header_style="bold magenta")
            rate_table.add_column("Limit Type", style="cyan")
            rate_table.add_column("Value", style="green")
            rate_table.add_column("Recommendation", style="white")
            
            rate_limits = config_summary["rate_limits"]
            rpm = rate_limits["requests_per_minute"]
            tpm = rate_limits["tokens_per_minute"]
            burst = rate_limits["burst_capacity"]
            
            rpm_rec = "🟢 Appropriate" if rpm >= 20 else "🟡 Low for production"
            tpm_rec = "🟢 Appropriate" if tpm >= 10000 else "🟡 Low for complex analysis"
            burst_rec = "🟢 Good burst capacity" if burst >= rpm else "🟡 Limited burst capacity"
            
            rate_table.add_row("Requests/Minute", str(rpm), rpm_rec)
            rate_table.add_row("Tokens/Minute", f"{tpm:,}", tpm_rec)
            rate_table.add_row("Burst Capacity", str(burst), burst_rec)
            
            console.print(rate_table)
            
            # Features
            console.print(f"\n[cyan]Feature Configuration:[/cyan]")
            feature_table = Table(show_header=True, header_style="bold magenta")
            feature_table.add_column("Feature", style="cyan")
            feature_table.add_column("Status", style="green")
            feature_table.add_column("Description", style="white")
            
            features = config_summary["features"]
            adaptive_status = "🟢 Enabled" if features["adaptive_throttling"] else "🔴 Disabled"
            fallback_status = "🟢 Enabled" if features["fallbacks"] else "🔴 Disabled"
            stats_status = "🟢 Enabled" if features["stats_tracking"] else "🔴 Disabled"
            
            feature_table.add_row("Adaptive Throttling", adaptive_status, "Automatically adjusts rates based on errors")
            feature_table.add_row("Fallback Responses", fallback_status, "Provides degraded service during outages")
            feature_table.add_row("Stats Tracking", stats_status, "Monitors performance and usage")
            
            console.print(feature_table)
            
            # Validation results
            console.print(f"\n[cyan]Credential Validation:[/cyan]")
            val_table = Table(show_header=True, header_style="bold magenta")
            val_table.add_column("Credential", style="cyan")
            val_table.add_column("Status", style="green")
            val_table.add_column("Required", style="white")
            
            api_key_status = "🟢 Set" if validation["anthropic_api_key"] else "🔴 Missing"
            model_status = "🟢 Set" if validation["anthropic_model"] else "🔴 Missing"
            env_val_status = "🟢 Set" if validation["environment_set"] else "🟡 Using Default"
            config_val_status = "🟢 Valid" if validation["config_valid"] else "🔴 Invalid"
            
            val_table.add_row("ANTHROPIC_API_KEY", api_key_status, "Yes")
            val_table.add_row("ANTHROPIC_MODEL", model_status, "Yes")
            val_table.add_row("ENVIRONMENT", env_val_status, "No")
            val_table.add_row("Config Validation", config_val_status, "Yes")
            
            console.print(val_table)
            
            # Recommendations
            console.print(f"\n[bold]Configuration Recommendations:[/bold]")
            
            issues = []
            if not validation["anthropic_api_key"]:
                issues.append("🔴 Set ANTHROPIC_API_KEY environment variable")
            if not validation["anthropic_model"]:
                issues.append("🔴 Set ANTHROPIC_MODEL environment variable")
            if not validation["config_valid"]:
                issues.append("🔴 Check configuration values for validity")
            if rpm < 30 and env_name == "production":
                issues.append("🟡 Consider upgrading API tier for production use")
            if not features["fallbacks"] and env_name == "production":
                issues.append("🟡 Enable fallbacks for production reliability")
            
            if issues:
                for issue in issues:
                    console.print(issue)
            else:
                console.print("🟢 Configuration looks healthy!")
                
            # Environment variable guide
            console.print(f"\n[bold]Key Environment Variables:[/bold]")
            console.print("• ANTHROPIC_API_KEY - Your Anthropic API key")
            console.print("• ANTHROPIC_MODEL - Model to use (e.g., claude-3-sonnet-20240229)")
            console.print("• ANTHROPIC_REQUESTS_PER_MINUTE - Override default rate limit")
            console.print("• ANTHROPIC_TOKENS_PER_MINUTE - Override token limit")
            console.print("• ENVIRONMENT - Set to dev/test/staging/prod")
            console.print("• ANTHROPIC_API_TIER - Set to free/pro/team/enterprise")
        
        except Exception as e:
            console.print(f"[red]Error getting configuration status: {e}[/red]")
    
    async def _show_api_status(self):
        """Show API rate limit and performance status"""
        try:
            # Import here to avoid circular imports
            from .agents.correlation_agent import get_correlation_agent_status
            from .utils.api_wrapper import get_all_agent_status
            
            console.print("[bold]API Rate Limit Status:[/bold]")
            
            # Get all agent statuses
            all_status = get_all_agent_status()
            
            if not all_status:
                console.print("[yellow]No API calls made yet - status not available[/yellow]")
                console.print("\nTo see status after making API calls:")
                console.print("1. Run an analysis with `/analyze`")
                console.print("2. Check status again with `/status`")
                return
            
            for agent_name, status in all_status.items():
                console.print(f"\n[cyan]{agent_name.title()} Agent:[/cyan]")
                
                # Rate limiting status
                if 'request_tokens_available' in status:
                    table = Table(show_header=True, header_style="bold magenta")
                    table.add_column("Metric", style="cyan")
                    table.add_column("Current", style="green")
                    table.add_column("Limit", style="yellow")
                    table.add_column("Status", style="white")
                    
                    # Request tokens
                    req_tokens = status.get('request_tokens_available', 0)
                    req_limit = status.get('requests_per_minute_limit', 50)
                    req_status = "🟢 Available" if req_tokens > 5 else "🟡 Low" if req_tokens > 0 else "🔴 Exhausted"
                    table.add_row("Request Tokens", f"{req_tokens:.1f}", str(req_limit), req_status)
                    
                    # API tokens
                    api_tokens = status.get('api_tokens_available', 0)
                    api_limit = status.get('tokens_per_minute_limit', 40000)
                    api_status = "🟢 Available" if api_tokens > 1000 else "🟡 Low" if api_tokens > 0 else "🔴 Exhausted"
                    table.add_row("API Tokens", f"{api_tokens:.0f}", str(api_limit), api_status)
                    
                    # Throttle factor
                    throttle = status.get('throttle_factor', 1.0)
                    throttle_status = "🟢 Normal" if throttle > 0.8 else "🟡 Reduced" if throttle > 0.5 else "🔴 Heavily Throttled"
                    table.add_row("Throttle Factor", f"{throttle:.2f}", "1.00", throttle_status)
                    
                    # Circuit breaker
                    cb_state = status.get('circuit_breaker_state', 'CLOSED')
                    cb_status = "🟢 Operational" if cb_state == 'CLOSED' else "🟡 Testing" if cb_state == 'HALF_OPEN' else "🔴 Open"
                    table.add_row("Circuit Breaker", cb_state, "CLOSED", cb_status)
                    
                    console.print(table)
                
                # Performance statistics
                if 'stats' in status:
                    stats = status['stats']
                    console.print(f"\n[bold]Performance Statistics:[/bold]")
                    perf_table = Table(show_header=True, header_style="bold magenta")
                    perf_table.add_column("Metric", style="cyan")
                    perf_table.add_column("Value", style="green")
                    
                    perf_table.add_row("Total Requests", str(stats.get('total_requests', 0)))
                    perf_table.add_row("Success Rate", f"{stats.get('success_rate', 0):.1f}%")
                    perf_table.add_row("Error Rate", f"{stats.get('error_rate', 0):.1f}%")
                    perf_table.add_row("Rate Limit Rate", f"{stats.get('rate_limit_rate', 0):.1f}%")
                    perf_table.add_row("Total Wait Time", f"{stats.get('total_wait_time', 0):.1f}s")
                    
                    console.print(perf_table)
                
                # Agent-specific stats
                if 'agent_stats' in status:
                    agent_stats = status['agent_stats']
                    console.print(f"\n[bold]Agent Statistics:[/bold]")
                    agent_table = Table(show_header=True, header_style="bold magenta")
                    agent_table.add_column("Metric", style="cyan")
                    agent_table.add_column("Value", style="green")
                    
                    agent_table.add_row("Total Calls", str(agent_stats.get('total_calls', 0)))
                    agent_table.add_row("Successful Calls", str(agent_stats.get('successful_calls', 0)))
                    agent_table.add_row("Success Rate", f"{agent_stats.get('success_rate', 0):.1f}%")
                    agent_table.add_row("Fallback Responses", str(agent_stats.get('fallback_responses', 0)))
                    agent_table.add_row("Fallback Rate", f"{agent_stats.get('fallback_rate', 0):.1f}%")
                    
                    console.print(agent_table)
            
            # Rate limit recommendations
            console.print(f"\n[bold]Recommendations:[/bold]")
            for agent_name, status in all_status.items():
                if 'stats' in status:
                    stats = status['stats']
                    error_rate = stats.get('error_rate', 0)
                    rate_limit_rate = stats.get('rate_limit_rate', 0)
                    
                    if error_rate > 20:
                        console.print("🔴 High error rate detected - consider reducing request frequency")
                    elif rate_limit_rate > 10:
                        console.print("🟡 Frequent rate limiting - consider upgrading API tier or reducing load")
                    elif stats.get('total_requests', 0) == 0:
                        console.print("ℹ️  No API usage yet - run an analysis to see performance metrics")
                    else:
                        console.print("🟢 API performance looks healthy")
        
        except Exception as e:
            console.print(f"[red]Error getting API status: {e}[/red]")
    
    async def _run_analysis(self, analysis_type: str = 'general'):
        """Run correlation analysis"""
        if self.unified_data is None:
            console.print("[yellow]No unified data available. Please load data first with `/load`[/yellow]")
            return
        
        # Get analysis query
        query = Prompt.ask(
            f"Enter your analysis question (type: {analysis_type})",
            default=f"Analyze correlations in this manufacturing data using {analysis_type} approach"
        )
        
        try:
            with console.status(f"[bold green]Running {analysis_type} correlation analysis..."):
                correlation_func = get_correlation_agent()
                if correlation_func is None:
                    return "Correlation analysis is not available due to import issues."
                
                results = await correlation_func(
                    data=self.unified_data,
                    query=query,
                    time_column='timestamp',
                    significance_threshold=self.settings['significance_threshold'],
                    min_correlation=self.settings['min_correlation'],
                    analysis_type=analysis_type
                )
            
            # Display results
            await self._display_analysis_results(results)
            
            # Add to conversation history
            self.conversation_history.append({
                'timestamp': datetime.now(),
                'query': query,
                'analysis_type': analysis_type,
                'results': results
            })
            
        except Exception as e:
            # Provide specific error messages for different types of failures
            error_str = str(e).lower()
            
            if 'rate limit' in error_str or 'too many requests' in error_str or '429' in error_str:
                console.print(f"[yellow]⏳ Rate limit reached. The analysis will automatically retry with exponential backoff.[/yellow]")
                console.print(f"[yellow]You can check API status with `/status` command.[/yellow]")
            elif 'connection' in error_str or 'network' in error_str:
                console.print(f"[yellow]🌐 Network connection issue. Please check your internet connection and try again.[/yellow]")
            elif 'api key' in error_str or 'authentication' in error_str:
                console.print(f"[red]🔑 API authentication failed. Please check your ANTHROPIC_API_KEY environment variable.[/red]")
            elif 'fallback_response' in error_str:
                console.print(f"[yellow]⚠️ Analysis completed with fallback response due to API limitations.[/yellow]")
                console.print(f"[yellow]Check `/status` for rate limit information or try again in a few minutes.[/yellow]")
            else:
                console.print(f"[red]❌ Analysis failed: {e}[/red]")
                console.print(f"[yellow]💡 Try `/status` to check API health or `/help` for assistance.[/yellow]")
    
    async def _display_analysis_results(self, results):
        """Display correlation analysis results"""
        console.print("\n[bold green]=== CORRELATION ANALYSIS RESULTS ===[/bold green]")
        
        # Check if this is a fallback response
        is_fallback = False
        if hasattr(results, 'analysis_metadata') and results.analysis_metadata:
            is_fallback = results.analysis_metadata.get('fallback_response', False)
        elif hasattr(results, 'dataset_summary') and results.dataset_summary:
            is_fallback = results.dataset_summary.get('fallback_response', False)
        
        if is_fallback:
            console.print("[yellow]⚠️ Note: This is a fallback response due to API limitations[/yellow]")
            console.print("[yellow]Results may be incomplete. Check `/status` for rate limit information.[/yellow]")
        
        # Data quality score
        quality_color = "green" if results.data_quality_score > 0.8 else "yellow" if results.data_quality_score > 0.6 else "red"
        console.print(f"Data Quality Score: [{quality_color}]{results.data_quality_score:.3f}[/{quality_color}]")
        
        # Significant correlations
        if results.significant_correlations:
            console.print(f"\n[bold]Significant Correlations Found: {len(results.significant_correlations)}[/bold]")
            
            # Create correlation table
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Variable 1", style="cyan")
            table.add_column("Variable 2", style="cyan")
            table.add_column("Correlation", justify="right", style="green")
            table.add_column("P-value", justify="right", style="yellow")
            table.add_column("Significance", style="blue")
            
            for corr in results.significant_correlations[:10]:  # Show top 10
                # Format correlation with higher precision
                corr_coef = corr.get('correlation_coefficient', 0)
                corr_str = f"{corr_coef:.6f}"
                
                # Get formatted p-value or format the numeric one
                p_val = corr.get('p_value', 1)
                
                # Ensure p_val is numeric for comparisons (defensive programming)
                if isinstance(p_val, str):
                    # If we accidentally got a formatted string, try to extract numeric value
                    try:
                        if p_val.startswith('<'):
                            p_val = 0.0  # Very small p-value
                        elif p_val == 'N/A' or p_val == 'unknown':
                            p_val = 1.0  # Invalid p-value
                        else:
                            p_val = float(p_val)
                    except (ValueError, AttributeError):
                        p_val = 1.0  # Default to non-significant
                
                p_val_str = corr.get('p_value_formatted')
                
                # Fallback to manual formatting if formatted version not available
                if p_val_str is None:
                    if p_val < 0.001:
                        p_val_str = f"{p_val:.2e}"
                    else:
                        p_val_str = f"{p_val:.6f}"
                
                # Get significance with robust fallback calculation
                significance = corr.get('significance_level', None)
                if significance is None or significance == 'unknown' or significance == '':
                    # Calculate significance based on p-value with proper handling
                    try:
                        if p_val is None or np.isnan(p_val):
                            significance = 'unknown'
                        elif p_val < 0.001:
                            significance = 'highly_significant'
                        elif p_val < 0.01:
                            significance = 'very_significant'
                        elif p_val < 0.05:
                            significance = 'significant'
                        else:
                            significance = 'not_significant'
                    except (TypeError, ValueError):
                        significance = 'unknown'
                
                table.add_row(
                    corr.get('variable_1', 'Unknown'),
                    corr.get('variable_2', 'Unknown'),
                    corr_str,
                    p_val_str,
                    significance
                )
            
            console.print(table)
            
            if len(results.significant_correlations) > 10:
                console.print(f"... and {len(results.significant_correlations) - 10} more correlations")
        
        # Key insights
        if results.insights:
            console.print("\n[bold]Key Insights:[/bold]")
            for insight in results.insights:
                confidence_color = "green" if insight.confidence_level == "high" else "yellow" if insight.confidence_level == "medium" else "red"
                console.print(f"• [{confidence_color}]{insight.confidence_level.upper()}[/{confidence_color}] {insight.description}")
                
                if insight.actionable_recommendation:
                    console.print(f"  → {insight.actionable_recommendation}")
        
        # Recommendations
        if results.recommendations:
            console.print("\n[bold]Recommendations:[/bold]")
            for i, rec in enumerate(results.recommendations, 1):
                console.print(f"{i}. {rec}")
    
    async def _export_results(self, filename: Optional[str] = None):
        """Export analysis results to file"""
        if not self.conversation_history:
            console.print("[yellow]No analysis results to export[/yellow]")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"correlation_analysis_{timestamp}.json"
        
        try:
            # Prepare export data
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'settings': self.settings,
                'data_summary': {
                    'datasets_loaded': list(self.loaded_data.keys()) if self.loaded_data else [],
                    'unified_data_shape': list(self.unified_data.shape) if self.unified_data is not None else None
                },
                'analysis_history': []
            }
            
            # Convert conversation history to exportable format
            for entry in self.conversation_history:
                export_entry = {
                    'timestamp': entry['timestamp'].isoformat(),
                    'query': entry['query'],
                    'analysis_type': entry['analysis_type'],
                    'results': entry['results'].dict() if hasattr(entry['results'], 'dict') else str(entry['results'])
                }
                export_data['analysis_history'].append(export_entry)
            
            # Write to file
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            console.print(f"[green]✓ Results exported to {filename}[/green]")
            
        except Exception as e:
            console.print(f"[red]Export failed: {e}[/red]")
    
    async def _process_with_agent(self, user_input: str):
        """Process user input with the correlation agent"""
        if self.unified_data is None:
            console.print("[yellow]Please load data first using `/load` command[/yellow]")
            return
        
        try:
            with console.status("[bold green]Analyzing with AI agent..."):
                correlation_func = get_correlation_agent()
                if correlation_func is None:
                    return "Correlation analysis is not available due to import issues."
                
                results = await correlation_func(
                    data=self.unified_data,
                    query=user_input,
                    time_column='timestamp',
                    significance_threshold=self.settings['significance_threshold'],
                    min_correlation=self.settings['min_correlation'],
                    analysis_type='general'
                )
            
            # Display results
            await self._display_analysis_results(results)
            
            # Add to conversation history
            self.conversation_history.append({
                'timestamp': datetime.now(),
                'query': user_input,
                'analysis_type': 'general',
                'results': results
            })
            
        except Exception as e:
            console.print(f"[red]Agent analysis failed: {e}[/red]")
            console.print("[dim]Try rephrasing your question or check if data is loaded properly.[/dim]")

@click.group()
def cli():
    """Manufacturing Correlation Analysis CLI"""
    pass

@cli.command()
@click.option('--data-dir', '-d', default='test-data', help='Data directory path')
def interactive(data_dir):
    """Start interactive correlation analysis session"""
    
    # Validate data directory
    data_path = Path(data_dir)
    if not data_path.exists():
        console.print(f"[red]Data directory not found: {data_dir}[/red]")
        console.print("Please specify a valid data directory with --data-dir")
        return
    
    # Initialize and start CLI
    cli_interface = ManufacturingCorrelationCLI(data_dir)
    
    # Run the interactive session
    try:
        asyncio.run(cli_interface.start())
    except KeyboardInterrupt:
        console.print("\n[yellow]Session interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error starting interactive session: {e}[/red]")

@cli.command()
@click.option('--data-dir', '-d', default='test-data', help='Data directory path')
@click.option('--query', '-q', required=True, help='Analysis query')
@click.option('--output', '-o', help='Output file for results')
@click.option('--analysis-type', '-t', default='general', help='Analysis type')
def analyze(data_dir, query, output, analysis_type):
    """Run single correlation analysis query"""
    
    async def run_analysis():
        try:
            # Load data
            loader = ManufacturingDataLoader(data_dir)
            data_dict = loader.load_all_data_sources()
            
            if not data_dict:
                console.print(f"[red]No data found in {data_dir}[/red]")
                return
            
            # Create unified dataset
            unified_data = loader.create_unified_dataset()
            
            # Run analysis
            with console.status("[bold green]Running correlation analysis..."):
                correlation_func = get_correlation_agent()
                if correlation_func is None:
                    return "Correlation analysis is not available due to import issues."
                
                results = await correlation_func(
                    data=unified_data,
                    query=query,
                    analysis_type=analysis_type
                )
            
            # Display results
            console.print(f"\n[bold]Analysis Results for: {query}[/bold]")
            console.print(f"Data Quality Score: {results.data_quality_score:.3f}")
            console.print(f"Significant Correlations: {len(results.significant_correlations)}")
            
            for insight in results.insights:
                console.print(f"• {insight.description}")
            
            # Export if requested
            if output:
                export_data = {
                    'query': query,
                    'analysis_type': analysis_type,
                    'timestamp': datetime.now().isoformat(),
                    'results': results.dict()
                }
                
                with open(output, 'w') as f:
                    json.dump(export_data, f, indent=2, default=str)
                
                console.print(f"[green]Results saved to {output}[/green]")
            
        except Exception as e:
            console.print(f"[red]Analysis failed: {e}[/red]")
    
    asyncio.run(run_analysis())

if __name__ == '__main__':
    cli()