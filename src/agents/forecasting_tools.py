"""
PatchTST Forecasting Agent Tools for Manufacturing Analysis

PydanticAI tools that provide time series forecasting capabilities to AI agents
using PatchTST transformer models. Integrates with existing correlation analysis
infrastructure for comprehensive manufacturing intelligence.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from pydantic_ai import RunContext
import logging
from datetime import datetime
from pathlib import Path

# Import forecasting infrastructure with stability enhancements
from ..forecasting import (
    ManufacturingPatchTSTModel,
    ManufacturingForecastTrainer,
    ForecastConfig,
    PatchTSTTrainingConfig,
    get_stabilized_training_config,
    get_stabilized_model_config
)
from ..forecasting.stability import ManufacturingStabilityValidator

# Import data structure utilities

# Import existing agent dependencies
from .correlation_agent import ManufacturingDataDependencies

logger = logging.getLogger(__name__)

def forecast_manufacturing_parameter_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variable: str,
    forecast_horizon: int,
    lookback_window: int = 240,
    include_confidence_intervals: bool = True
) -> Dict[str, Any]:
    """
    Forecast future values of manufacturing parameters using PatchTST.
    
    Args:
        ctx: Runtime context with manufacturing data dependencies
        target_variable: Manufacturing parameter to forecast (e.g., 'thickness_avg', 'scrap_rate')
        forecast_horizon: Number of time steps to forecast (in minutes)
        lookback_window: Historical data window for prediction (in minutes)
        include_confidence_intervals: Whether to include 95% confidence intervals
        
    Returns:
        Dictionary containing forecast results, insights, and manufacturing recommendations
    """
    try:
        logger.info(f"Generating forecast for {target_variable} with horizon {forecast_horizon} minutes")
        
        # Access unified data from context (Phase 2.1 infrastructure)
        if hasattr(ctx.deps, 'data'):
            unified_data = ctx.deps.data
        else:
            # Handle case where deps is passed as dict
            unified_data = ctx.deps.get('data') if hasattr(ctx.deps, 'get') else None
            if unified_data is None:
                # Try accessing as attribute for backwards compatibility
                unified_data = getattr(ctx.deps, 'data', None)
        
        # Validate data availability
        if unified_data is None or unified_data.empty:
            raise ValueError("No unified manufacturing data available for forecasting")
        
        if target_variable not in unified_data.columns:
            available_vars = list(unified_data.columns)
            raise ValueError(f"Target variable '{target_variable}' not found. Available: {available_vars}")
        
        # Load forecasting configuration with stability enhancements
        config_path = 'config/forecasting_config.json'
        if not Path(config_path).exists():
            logger.warning(f"Config file {config_path} not found, using default configuration")
            forecast_config = ForecastConfig()
        else:
            forecast_config = ForecastConfig.from_json(config_path)
        
        # Use stabilized training configuration for enhanced stability
        training_config = get_stabilized_training_config()
        
        # Check if pre-trained model exists
        model_path = f"./models/patchtst_manufacturing_{target_variable}/"
        
        if Path(model_path).exists():
            logger.info(f"Loading pre-trained model for {target_variable}")
            try:
                model = ManufacturingPatchTSTModel.load_pretrained(model_path)
                
                # Validate loaded model stability
                stability_validator = ManufacturingStabilityValidator()
                # Note: Full validation requires predictions, done after forecast generation
                
            except Exception as e:
                logger.warning(f"Failed to load pre-trained model: {e}, training new model with stability")
                model = ManufacturingPatchTSTModel(forecast_config, training_config)
                model.train(unified_data, target_variable)
        else:
            logger.info(f"No pre-trained model found for {target_variable}, training new model with stability")
            
            # Train new model with stability enhancements
            model = ManufacturingPatchTSTModel(forecast_config, training_config)
            trained_model = model.train(unified_data, target_variable)
            
            # Validate training stability
            if hasattr(trained_model, 'best_model_metrics'):
                eval_loss = trained_model.best_model_metrics.get('eval_loss', float('inf'))
                if not np.isfinite(eval_loss):
                    logger.error(f"Training instability detected for {target_variable}: infinite eval_loss")
                    raise RuntimeError(f"Model training failed due to instability: eval_loss={eval_loss}")
                else:
                    logger.info(f"Model training completed successfully with eval_loss={eval_loss:.4f}")
        
        # Prepare historical data for forecasting
        lookback_data = unified_data.tail(lookback_window)
        
        if len(lookback_data) < forecast_config.lookback_window:
            logger.warning(f"Limited historical data: {len(lookback_data)} records available")
        
        # Ensure timestamp column exists for forecasting
        if 'timestamp' not in lookback_data.columns and 'unified_timestamp' in lookback_data.columns:
            lookback_data = lookback_data.rename(columns={'unified_timestamp': 'timestamp'})
        elif 'timestamp' not in lookback_data.columns:
            # Create a synthetic timestamp column if missing
            lookback_data = lookback_data.copy()
            lookback_data.reset_index(inplace=True)
            lookback_data['timestamp'] = pd.date_range(
                start='2025-01-01', periods=len(lookback_data), freq='1min'
            )
        
        # Generate forecast
        forecast_result = model.forecast(
            historical_data=lookback_data,
            target_variable=target_variable,
            forecast_horizon=forecast_horizon,
            include_confidence_intervals=include_confidence_intervals
        )
        
        # Validate forecast stability and quality
        stability_validator = ManufacturingStabilityValidator()
        
        try:
            validation_result = stability_validator.validate_model_stability(
                model=model,
                test_predictions=forecast_result.forecast_values,
                target_variable=target_variable
            )
            
            # Log stability metrics
            logger.info(f"Forecast stability validation for {target_variable}:")
            logger.info(f"  - Stability score: {validation_result.stability_score:.3f}")
            logger.info(f"  - Prediction variance: {validation_result.prediction_variance:.3f}")
            logger.info(f"  - Manufacturing compliance: {validation_result.manufacturing_compliance}")
            
            # Check critical stability thresholds
            if validation_result.stability_score < 0.5:
                logger.warning(f"Low stability score for {target_variable}: {validation_result.stability_score:.3f}")
            
            if validation_result.prediction_variance < 0.01:
                logger.warning(f"Very low prediction variance detected for {target_variable}: {validation_result.prediction_variance:.6f}")
                logger.warning("This may indicate model is producing constant predictions")
            
            # Add stability metrics to forecast result
            forecast_result.stability_metrics = {
                'stability_score': float(validation_result.stability_score),
                'prediction_variance': float(validation_result.prediction_variance),
                'manufacturing_compliance': validation_result.manufacturing_compliance,
                'gradient_health': validation_result.gradient_health if hasattr(validation_result, 'gradient_health') else {}
            }
            
        except Exception as stability_error:
            logger.warning(f"Stability validation failed for {target_variable}: {stability_error}")
            # Continue with forecast but add warning
            forecast_result.stability_metrics = {
                'stability_validation_error': str(stability_error),
                'validation_status': 'failed'
            }
        
        # Convert to dictionary for agent response
        result_dict = {
            'target_variable': forecast_result.target_variable,
            'forecast_horizon': forecast_result.forecast_horizon,
            'forecast_values': forecast_result.forecast_values,
            'forecast_timestamps': forecast_result.forecast_timestamps,
            'model_performance': forecast_result.model_performance,
            'manufacturing_insights': forecast_result.manufacturing_insights,
            'confidence_intervals': forecast_result.confidence_intervals,
            'attention_insights': forecast_result.attention_insights,
            'stability_metrics': getattr(forecast_result, 'stability_metrics', {}),
            'forecast_summary': {
                'min_value': float(min(forecast_result.forecast_values)),
                'max_value': float(max(forecast_result.forecast_values)),
                'mean_value': float(np.mean(forecast_result.forecast_values)),
                'trend': 'increasing' if forecast_result.forecast_values[-1] > forecast_result.forecast_values[0] else 'decreasing',
                'volatility': float(np.std(forecast_result.forecast_values))
            }
        }
        
        logger.info(f"Successfully generated forecast for {target_variable}")
        return result_dict
        
    except Exception as e:
        logger.error(f"Error in forecast_manufacturing_parameter_tool: {str(e)}")
        return {
            'error': str(e),
            'target_variable': target_variable,
            'forecast_horizon': forecast_horizon,
            'status': 'failed'
        }

def multi_horizon_forecast_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variable: str,
    forecast_horizons: List[int] = [15, 60, 240],
    include_comparison: bool = True
) -> Dict[str, Any]:
    """
    Generate multi-horizon forecasts for manufacturing parameter analysis.
    
    Args:
        ctx: Runtime context with manufacturing data dependencies
        target_variable: Manufacturing parameter to forecast
        forecast_horizons: List of forecast horizons in minutes
        include_comparison: Whether to include horizon comparison analysis
        
    Returns:
        Dictionary containing multi-horizon forecast results and comparative analysis
    """
    try:
        logger.info(f"Generating multi-horizon forecast for {target_variable}")
        
        # Access unified data from context
        if hasattr(ctx.deps, 'data'):
            unified_data = ctx.deps.data
        else:
            # Handle case where deps is passed as dict
            unified_data = ctx.deps.get('data') if hasattr(ctx.deps, 'get') else None
            if unified_data is None:
                # Try accessing as attribute for backwards compatibility
                unified_data = getattr(ctx.deps, 'data', None)
        
        if unified_data is None or unified_data.empty:
            raise ValueError("No unified manufacturing data available")
        
        if target_variable not in unified_data.columns:
            raise ValueError(f"Target variable '{target_variable}' not found in data")
        
        # Load configuration
        forecast_config = ForecastConfig.from_json('config/forecasting_config.json')
        
        # Load or train model
        model_path = f"./models/patchtst_manufacturing_{target_variable}/"
        
        if Path(model_path).exists():
            model = ManufacturingPatchTSTModel.load_pretrained(model_path)
        else:
            model = ManufacturingPatchTSTModel(forecast_config)
            model.train(unified_data, target_variable)
        
        # Generate forecasts for each horizon
        horizon_results = {}
        
        for horizon in forecast_horizons:
            try:
                forecast_result = model.forecast(
                    historical_data=unified_data.tail(forecast_config.lookback_window),
                    target_variable=target_variable,
                    forecast_horizon=horizon,
                    include_confidence_intervals=True
                )
                
                horizon_results[f'horizon_{horizon}min'] = {
                    'forecast_values': forecast_result.forecast_values,
                    'forecast_timestamps': forecast_result.forecast_timestamps,
                    'confidence_intervals': forecast_result.confidence_intervals,
                    'manufacturing_insights': forecast_result.manufacturing_insights,
                    'summary': {
                        'mean_value': float(np.mean(forecast_result.forecast_values)),
                        'trend': 'increasing' if forecast_result.forecast_values[-1] > forecast_result.forecast_values[0] else 'decreasing',
                        'volatility': float(np.std(forecast_result.forecast_values))
                    }
                }
                
            except Exception as e:
                logger.error(f"Error generating forecast for horizon {horizon}: {str(e)}")
                horizon_results[f'horizon_{horizon}min'] = {'error': str(e)}
        
        # Generate comparative analysis
        comparative_analysis = {}
        if include_comparison and len(horizon_results) > 1:
            comparative_analysis = _generate_horizon_comparison(horizon_results, target_variable)
        
        result = {
            'target_variable': target_variable,
            'forecast_horizons': forecast_horizons,
            'horizon_results': horizon_results,
            'comparative_analysis': comparative_analysis,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"Successfully generated multi-horizon forecast for {target_variable}")
        return result
        
    except Exception as e:
        logger.error(f"Error in multi_horizon_forecast_tool: {str(e)}")
        return {
            'error': str(e),
            'target_variable': target_variable,
            'status': 'failed'
        }

def compare_forecast_scenarios_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variables: List[str],
    forecast_horizon: int = 60,
    scenario_analysis: bool = True
) -> Dict[str, Any]:
    """
    Compare forecasts across multiple manufacturing parameters for scenario analysis.
    
    Args:
        ctx: Runtime context with manufacturing data dependencies
        target_variables: List of manufacturing parameters to forecast
        forecast_horizon: Forecast horizon in minutes
        scenario_analysis: Whether to include scenario-based insights
        
    Returns:
        Dictionary containing comparative forecasts and scenario analysis
    """
    try:
        logger.info(f"Comparing forecasts for variables: {target_variables}")
        
        # Access unified data from context
        if hasattr(ctx.deps, 'data'):
            unified_data = ctx.deps.data
        else:
            # Handle case where deps is passed as dict
            unified_data = ctx.deps.get('data') if hasattr(ctx.deps, 'get') else None
            if unified_data is None:
                # Try accessing as attribute for backwards compatibility
                unified_data = getattr(ctx.deps, 'data', None)
        
        if unified_data is None or unified_data.empty:
            raise ValueError("No unified manufacturing data available")
        
        # Load configuration
        forecast_config = ForecastConfig.from_json('config/forecasting_config.json')
        
        # Generate forecasts for each variable
        variable_forecasts = {}
        
        for target_var in target_variables:
            if target_var not in unified_data.columns:
                logger.warning(f"Target variable '{target_var}' not found in data")
                continue
            
            try:
                # Load or train model
                model_path = f"./models/patchtst_manufacturing_{target_var}/"
                
                if Path(model_path).exists():
                    model = ManufacturingPatchTSTModel.load_pretrained(model_path)
                else:
                    model = ManufacturingPatchTSTModel(forecast_config)
                    model.train(unified_data, target_var)
                
                # Generate forecast
                forecast_result = model.forecast(
                    historical_data=unified_data.tail(forecast_config.lookback_window),
                    target_variable=target_var,
                    forecast_horizon=forecast_horizon,
                    include_confidence_intervals=True
                )
                
                variable_forecasts[target_var] = {
                    'forecast_values': forecast_result.forecast_values,
                    'forecast_timestamps': forecast_result.forecast_timestamps,
                    'confidence_intervals': forecast_result.confidence_intervals,
                    'manufacturing_insights': forecast_result.manufacturing_insights,
                    'model_performance': forecast_result.model_performance
                }
                
            except Exception as e:
                logger.error(f"Error generating forecast for {target_var}: {str(e)}")
                variable_forecasts[target_var] = {'error': str(e)}
        
        # Generate scenario analysis
        scenario_insights = {}
        if scenario_analysis and len(variable_forecasts) > 1:
            scenario_insights = _generate_scenario_analysis(variable_forecasts, forecast_horizon)
        
        result = {
            'target_variables': target_variables,
            'forecast_horizon': forecast_horizon,
            'variable_forecasts': variable_forecasts,
            'scenario_insights': scenario_insights,
            'cross_variable_analysis': _generate_cross_variable_analysis(variable_forecasts),
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"Successfully generated comparative forecasts for {len(variable_forecasts)} variables")
        return result
        
    except Exception as e:
        logger.error(f"Error in compare_forecast_scenarios_tool: {str(e)}")
        return {
            'error': str(e),
            'target_variables': target_variables,
            'status': 'failed'
        }

def train_forecasting_models_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variables: Optional[List[str]] = None,
    retrain_existing: bool = False
) -> Dict[str, Any]:
    """
    Train PatchTST models for manufacturing forecasting.
    
    Args:
        ctx: Runtime context with manufacturing data dependencies
        target_variables: Variables to train models for (defaults to config)
        retrain_existing: Whether to retrain existing models
        
    Returns:
        Dictionary containing training results and model performance
    """
    try:
        logger.info("Starting forecasting model training")
        
        # Access unified data from context
        if hasattr(ctx.deps, 'data'):
            unified_data = ctx.deps.data
        else:
            # Handle case where deps is passed as dict
            unified_data = ctx.deps.get('data') if hasattr(ctx.deps, 'get') else None
            if unified_data is None:
                # Try accessing as attribute for backwards compatibility
                unified_data = getattr(ctx.deps, 'data', None)
        
        if unified_data is None or unified_data.empty:
            raise ValueError("No unified manufacturing data available for training")
        
        # Load configuration with stability enhancements
        forecast_config = ForecastConfig.from_json('config/forecasting_config.json')
        training_config = get_stabilized_training_config()
        
        # Use provided target variables or default from config
        if target_variables is None:
            target_variables = forecast_config.target_variables
        
        # Initialize trainer with stability features
        trainer = ManufacturingForecastTrainer(forecast_config, training_config)
        
        logger.info(f"Training with stability configuration:")
        logger.info(f"  - Gradient clipping: {training_config.stability_config.gradient_clipping['enabled']}")
        logger.info(f"  - Max gradient norm: {training_config.stability_config.gradient_clipping['max_norm']}")
        logger.info(f"  - Early stopping patience: {training_config.stability_config.early_stopping['patience']}")
        
        # Train models
        training_results = {}
        
        for target_var in target_variables:
            if target_var not in unified_data.columns:
                logger.warning(f"Target variable '{target_var}' not found in data")
                continue
            
            # Check if model already exists
            model_path = f"./models/patchtst_manufacturing_{target_var}/"
            if Path(model_path).exists() and not retrain_existing:
                logger.info(f"Model for {target_var} already exists, skipping training")
                training_results[target_var] = {'status': 'skipped', 'reason': 'model_exists'}
                continue
            
            try:
                # Create and train model with stability enhancements
                model = ManufacturingPatchTSTModel(forecast_config, training_config)
                trained_model = model.train(unified_data, target_var)
                
                # Critical stability check - verify finite loss
                if hasattr(trained_model, 'best_model_metrics'):
                    eval_loss = trained_model.best_model_metrics.get('eval_loss', float('inf'))
                    if not np.isfinite(eval_loss):
                        raise RuntimeError(f"Training instability: infinite eval_loss={eval_loss}")
                    
                    logger.info(f"Training completed for {target_var} with eval_loss={eval_loss:.4f}")
                
                # Validate model performance
                validation_results = trainer._validate_model_performance(
                    trained_model, unified_data, target_var
                )
                
                # Compare with baselines
                baseline_comparison = trainer._compare_with_baselines(
                    trained_model, unified_data, target_var
                )
                
                # Generate test predictions for stability validation
                test_data = unified_data.tail(min(200, len(unified_data) // 4))
                test_forecast = model.forecast(test_data, target_var, horizon=15)
                
                # Perform stability validation
                stability_validator = ManufacturingStabilityValidator()
                stability_result = stability_validator.validate_model_stability(
                    model=trained_model,
                    test_predictions=test_forecast.forecast_values,
                    target_variable=target_var
                )
                
                training_results[target_var] = {
                    'status': 'completed',
                    'model_metrics': trained_model.best_model_metrics,
                    'validation_results': validation_results,
                    'baseline_comparison': baseline_comparison,
                    'stability_validation': {
                        'stability_score': float(stability_result.stability_score),
                        'prediction_variance': float(stability_result.prediction_variance),
                        'manufacturing_compliance': stability_result.manufacturing_compliance
                    },
                    'model_path': model_path
                }
                
                logger.info(f"Successfully trained model for {target_var}")
                logger.info(f"  - Stability score: {stability_result.stability_score:.3f}")
                logger.info(f"  - Prediction variance: {stability_result.prediction_variance:.3f}")
                
            except Exception as e:
                logger.error(f"Error training model for {target_var}: {str(e)}")
                # Check if error is stability-related
                if any(keyword in str(e).lower() for keyword in ['infinite', 'nan', 'stability', 'gradient']):
                    training_results[target_var] = {
                        'status': 'failed', 
                        'error': str(e),
                        'error_type': 'stability_failure'
                    }
                else:
                    training_results[target_var] = {'status': 'failed', 'error': str(e)}
        
        # Generate summary
        successful_models = sum(1 for r in training_results.values() if r.get('status') == 'completed')
        
        result = {
            'training_summary': {
                'total_variables': len(target_variables),
                'successful_models': successful_models,
                'failed_models': len(target_variables) - successful_models,
                'training_timestamp': datetime.now().isoformat()
            },
            'target_variables': target_variables,
            'training_results': training_results,
            'model_directory': training_config.model_save_path
        }
        
        logger.info(f"Training completed: {successful_models}/{len(target_variables)} models successful")
        return result
        
    except Exception as e:
        logger.error(f"Error in train_forecasting_models_tool: {str(e)}")
        return {
            'error': str(e),
            'status': 'failed'
        }

def _generate_horizon_comparison(horizon_results: Dict[str, Any], target_variable: str) -> Dict[str, Any]:
    """Generate comparative analysis across forecast horizons"""
    try:
        comparison = {
            'trend_consistency': [],
            'volatility_comparison': {},
            'insights': []
        }
        
        # Extract trends and volatilities
        trends = []
        volatilities = []
        
        for horizon_key, results in horizon_results.items():
            if 'error' not in results:
                trends.append(results['summary']['trend'])
                volatilities.append(results['summary']['volatility'])
                comparison['volatility_comparison'][horizon_key] = results['summary']['volatility']
        
        # Analyze trend consistency
        if trends:
            trend_consistency = len(set(trends)) == 1
            comparison['trend_consistency'] = trend_consistency
            
            if trend_consistency:
                comparison['insights'].append(f"Consistent {trends[0]} trend across all horizons")
            else:
                comparison['insights'].append("Inconsistent trends across horizons - review model stability")
        
        # Analyze volatility patterns
        if volatilities:
            if len(volatilities) > 1:
                volatility_trend = 'increasing' if volatilities[-1] > volatilities[0] else 'decreasing'
                comparison['insights'].append(f"Volatility is {volatility_trend} with longer horizons")
        
        return comparison
        
    except Exception as e:
        logger.error(f"Error generating horizon comparison: {str(e)}")
        return {'error': str(e)}

def _generate_scenario_analysis(variable_forecasts: Dict[str, Any], forecast_horizon: int) -> Dict[str, Any]:
    """Generate scenario-based insights from multi-variable forecasts"""
    try:
        scenarios = {
            'quality_scenario': [],
            'efficiency_scenario': [],
            'risk_scenario': [],
            'optimization_opportunities': []
        }
        
        # Analyze quality scenarios
        if 'thickness_avg' in variable_forecasts and 'scrap_rate' in variable_forecasts:
            thickness_trend = variable_forecasts['thickness_avg'].get('summary', {}).get('trend', 'stable')
            scrap_trend = variable_forecasts['scrap_rate'].get('summary', {}).get('trend', 'stable')
            
            if thickness_trend == 'increasing' and scrap_trend == 'decreasing':
                scenarios['quality_scenario'].append("Positive quality outlook: improving thickness with reducing scrap")
            elif thickness_trend == 'decreasing' and scrap_trend == 'increasing':
                scenarios['quality_scenario'].append("Quality risk: declining thickness with increasing scrap rates")
        
        # Analyze efficiency scenarios
        if 'speed' in variable_forecasts:
            speed_data = variable_forecasts['speed']
            if 'error' not in speed_data:
                speed_volatility = speed_data.get('summary', {}).get('volatility', 0)
                if speed_volatility > 5:  # High volatility threshold
                    scenarios['efficiency_scenario'].append("Production efficiency risk: high speed variability predicted")
                else:
                    scenarios['efficiency_scenario'].append("Stable production efficiency expected")
        
        # Generate optimization opportunities
        for var_name, var_data in variable_forecasts.items():
            if 'error' not in var_data:
                insights = var_data.get('manufacturing_insights', [])
                for insight in insights:
                    if 'optimization' in insight.lower() or 'improve' in insight.lower():
                        scenarios['optimization_opportunities'].append(f"{var_name}: {insight}")
        
        return scenarios
        
    except Exception as e:
        logger.error(f"Error generating scenario analysis: {str(e)}")
        return {'error': str(e)}

def _generate_cross_variable_analysis(variable_forecasts: Dict[str, Any]) -> Dict[str, Any]:
    """Generate cross-variable correlation analysis for forecasts"""
    try:
        analysis = {
            'correlation_opportunities': [],
            'process_interactions': [],
            'risk_factors': []
        }
        
        # Identify process interactions
        if 'thickness_avg' in variable_forecasts and 'speed' in variable_forecasts:
            thickness_trend = variable_forecasts['thickness_avg'].get('summary', {}).get('trend', 'stable')
            speed_trend = variable_forecasts['speed'].get('summary', {}).get('trend', 'stable')
            
            if thickness_trend != speed_trend:
                analysis['process_interactions'].append(
                    f"Process imbalance detected: thickness {thickness_trend} while speed {speed_trend}"
                )
        
        # Identify risk factors
        for var_name, var_data in variable_forecasts.items():
            if 'error' not in var_data:
                volatility = var_data.get('summary', {}).get('volatility', 0)
                if volatility > 2:  # High volatility threshold
                    analysis['risk_factors'].append(f"High volatility risk in {var_name}")
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error generating cross-variable analysis: {str(e)}")
        return {'error': str(e)}