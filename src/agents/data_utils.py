"""
Data Structure Utilities for Multi-Method Correlation Analysis

Provides validation, transformation, and safety utilities for handling
correlation analysis data structures across different formats and contexts.
"""

import numpy as np
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)


def safe_get_dict_value(data: Any, key: str, default: Any = None) -> Any:
    """
    Safely get a value from a dictionary-like object or result object.
    
    Args:
        data: Data structure to access (dict, object, list, or other)
        key: Key to access
        default: Default value if key not found or data is not accessible
        
    Returns:
        Value from data or default
    """
    # Try object attribute access first (for MultiMethodCorrelationResult objects)
    if hasattr(data, key):
        try:
            value = getattr(data, key)
            return value
        except AttributeError:
            pass
    
    # Try dictionary access
    if isinstance(data, dict):
        return data.get(key, default)
    
    # Try list/tuple access with numeric keys
    elif isinstance(data, (list, tuple)) and key.isdigit():
        try:
            index = int(key)
            return data[index] if 0 <= index < len(data) else default
        except (ValueError, IndexError):
            return default
    else:
        # Only log if we couldn't access the data at all
        if not hasattr(data, key):
            logger.debug(f"Could not access key '{key}' from {type(data)}")
        return default


def standardize_p_value(p_value: Any) -> float:
    """
    Standardize p-value format handling.
    
    Args:
        p_value: P-value in various formats (float, str, scientific notation)
        
    Returns:
        Standardized float p-value
    """
    if p_value is None:
        logger.warning("P-value is None, returning 1.0")
        return 1.0
    
    if isinstance(p_value, (int, float)):
        return float(p_value)
    
    if isinstance(p_value, str):
        try:
            return float(p_value)
        except ValueError:
            logger.warning(f"Could not convert p-value string '{p_value}' to float, returning 1.0")
            return 1.0
    
    logger.warning(f"Unexpected p-value type {type(p_value)}: {p_value}, returning 1.0")
    return 1.0


def format_p_value_for_display(p_value: float) -> str:
    """
    Format p-value for display with enhanced handling of extremely small values.
    
    Args:
        p_value: P-value as float
        
    Returns:
        Formatted p-value string with appropriate precision
    """
    if p_value is None or np.isnan(p_value):
        return "N/A"
    
    # Handle negative values (shouldn't happen but be safe)
    if p_value < 0:
        return "< 0"
    
    # Handle exact zero or extremely small values near machine precision
    if p_value == 0.0 or p_value < 1e-15:
        return "< 1e-15"
    
    # Standard decimal format for larger p-values
    if p_value >= 1e-3:
        return f"{p_value:.6f}"
    
    # Scientific notation for small but representable values
    elif p_value >= 1e-15:
        # Use higher precision for very small values to avoid 0.00e+00
        formatted = f"{p_value:.3e}"
        
        # Fix common formatting issues
        if formatted == "0.000e+00" or formatted.startswith("0.00"):
            # Fall back to higher precision
            formatted = f"{p_value:.6e}"
            if formatted.startswith("0.00"):
                return "< 1e-15"
        
        return formatted
    
    else:
        return "< 1e-15"


def validate_correlation_result_dict(result_data: Any) -> bool:
    """
    Validate that a correlation result has the expected structure (dict or MultiMethodCorrelationResult).
    
    Args:
        result_data: Data to validate
        
    Returns:
        True if valid, False otherwise
    """
    # Check if it's a MultiMethodCorrelationResult object
    if hasattr(result_data, 'variable_1') and hasattr(result_data, 'pearson_correlation'):
        return True
    
    # Check if it's a dictionary
    if not isinstance(result_data, dict):
        logger.warning(f"Expected dict or MultiMethodCorrelationResult, got {type(result_data)}")
        return False
    
    required_keys = [
        "variable_1", "variable_2",
        "pearson_correlation", "spearman_correlation", "kendall_correlation",
        "pearson_p_value", "spearman_p_value", "kendall_p_value",
        "sample_size", "method_convergence_score", "recommended_method"
    ]
    
    missing_keys = [key for key in required_keys if key not in result_data]
    if missing_keys:
        logger.warning(f"Missing required keys: {missing_keys}")
        return False
    
    return True


def safe_extract_correlation_data(
    correlation_results: Any,
    pair_key: str
) -> Optional[Dict[str, Any]]:
    """
    Safely extract correlation data for a specific variable pair.
    
    Args:
        correlation_results: Results data structure
        pair_key: Key for the variable pair
        
    Returns:
        Correlation data dict or None if extraction fails
    """
    if not isinstance(correlation_results, dict):
        logger.warning(f"Expected dict for correlation_results, got {type(correlation_results)}")
        return None
    
    result_data = safe_get_dict_value(correlation_results, pair_key)
    if result_data is None:
        logger.warning(f"No data found for pair key: {pair_key}")
        return None
    
    if not validate_correlation_result_dict(result_data):
        logger.warning(f"Invalid correlation result structure for {pair_key}")
        return None
    
    # Standardize p-values
    try:
        standardized_data = result_data.copy()
        standardized_data["pearson_p_value"] = standardize_p_value(result_data["pearson_p_value"])
        standardized_data["spearman_p_value"] = standardize_p_value(result_data["spearman_p_value"])
        standardized_data["kendall_p_value"] = standardize_p_value(result_data["kendall_p_value"])
        
        return standardized_data
    except Exception as e:
        logger.warning(f"Error standardizing correlation data for {pair_key}: {e}")
        return None


def validate_correlation_results_only(
    correlation_results: Any
) -> Dict[str, Dict[str, Any]]:
    """
    Validate and convert ONLY correlation results to standard format.
    Use this for actual correlation result objects, not response metadata.
    
    Args:
        correlation_results: Raw correlation results containing only correlation data
        
    Returns:
        Standardized correlation results dictionary
    """
    if not isinstance(correlation_results, dict):
        logger.debug(f"Expected dict for correlation_results, got {type(correlation_results)}")
        return {}
    
    standardized_results = {}
    
    for pair_key, result_data in correlation_results.items():
        # Only process if this looks like actual correlation data
        if not _is_correlation_data(result_data):
            logger.debug(f"Skipping non-correlation data: {pair_key}")
            continue
            
        # Handle MultiMethodCorrelationResult objects by converting to dict
        if hasattr(result_data, 'variable_1') and hasattr(result_data, 'pearson_correlation'):
            # Convert MultiMethodCorrelationResult to dict
            standardized_data = {
                "variable_1": safe_get_dict_value(result_data, "variable_1"),
                "variable_2": safe_get_dict_value(result_data, "variable_2"),
                "pearson_correlation": safe_get_dict_value(result_data, "pearson_correlation", 0.0),
                "spearman_correlation": safe_get_dict_value(result_data, "spearman_correlation", 0.0),
                "kendall_correlation": safe_get_dict_value(result_data, "kendall_correlation", 0.0),
                "pearson_p_value": standardize_p_value(safe_get_dict_value(result_data, "pearson_p_value")),
                "spearman_p_value": standardize_p_value(safe_get_dict_value(result_data, "spearman_p_value")),
                "kendall_p_value": standardize_p_value(safe_get_dict_value(result_data, "kendall_p_value")),
                "sample_size": safe_get_dict_value(result_data, "sample_size", 0),
                "method_convergence_score": safe_get_dict_value(result_data, "method_convergence_score", 0.0),
                "recommended_method": safe_get_dict_value(result_data, "recommended_method", "pearson"),
                "data_distribution_assessment": safe_get_dict_value(result_data, "data_distribution_assessment", {}),
                "pearson_confidence_interval": safe_get_dict_value(result_data, "pearson_confidence_interval", [0.0, 0.0]),
                "spearman_confidence_interval": safe_get_dict_value(result_data, "spearman_confidence_interval", [0.0, 0.0]),
                "kendall_confidence_interval": safe_get_dict_value(result_data, "kendall_confidence_interval", [0.0, 0.0]),
                "interpretation": safe_get_dict_value(result_data, "interpretation", "")
            }
            standardized_results[pair_key] = standardized_data
        else:
            # Handle regular dict format
            standardized_data = safe_extract_correlation_data(correlation_results, pair_key)
            if standardized_data is not None:
                standardized_results[pair_key] = standardized_data
            else:
                logger.debug(f"Skipping invalid correlation result for {pair_key}")
    
    return standardized_results


def validate_and_convert_correlation_results(
    correlation_results: Any
) -> Dict[str, Dict[str, Any]]:
    """
    DEPRECATED: Use validate_correlation_results_only() for correlation data.
    This function is kept for backward compatibility but logs a warning.
    """
    logger.warning("validate_and_convert_correlation_results is deprecated. Use validate_correlation_results_only() for correlation data.")
    return validate_correlation_results_only(correlation_results)


def _is_correlation_data(data: Any) -> bool:
    """
    Check if data represents actual correlation results vs response metadata.
    
    Args:
        data: Data to check
        
    Returns:
        True if this is correlation data, False if it's metadata
    """
    # MultiMethodCorrelationResult objects
    if hasattr(data, 'variable_1') and hasattr(data, 'pearson_correlation'):
        return True
    
    # Dictionary with correlation structure
    if isinstance(data, dict):
        correlation_keys = ['variable_1', 'variable_2', 'pearson_correlation']
        return all(key in data for key in correlation_keys)
    
    # Skip non-correlation data types
    if isinstance(data, (list, int, str)):
        return False
    
    return False


def safe_list_to_dict_conversion(data: Any, expected_keys: List[str]) -> Dict[str, Any]:
    """
    Safely convert list data to dictionary format when possible.
    
    Args:
        data: Data that might be a list or dict
        expected_keys: Keys to map list elements to
        
    Returns:
        Dictionary representation
    """
    if isinstance(data, dict):
        return data
    
    if isinstance(data, (list, tuple)):
        if len(data) == len(expected_keys):
            return dict(zip(expected_keys, data))
        else:
            logger.warning(f"List length {len(data)} doesn't match expected keys {len(expected_keys)}")
            return {}
    
    logger.warning(f"Cannot convert {type(data)} to dict")
    return {}


def create_safe_correlation_summary(correlation_results: Any) -> Dict[str, Any]:
    """
    Create a safe summary of correlation results with error handling.
    
    Args:
        correlation_results: Raw correlation results
        
    Returns:
        Safe summary dictionary
    """
    summary = {
        "total_correlations_analyzed": 0,
        "significant_correlations": [],
        "methods_analyzed": ["pearson", "spearman", "kendall"],
        "analysis_parameters": {},
        "data_summary": {},
        "robustness_metrics": {}
    }
    
    try:
        # Handle different input formats
        if isinstance(correlation_results, dict):
            # Check if this is a response structure with 'multi_method_results' key
            if 'multi_method_results' in correlation_results:
                actual_results = correlation_results['multi_method_results']
            else:
                actual_results = correlation_results
            
            # Only validate if we have correlation-like data
            if actual_results and isinstance(actual_results, dict):
                standardized_results = validate_correlation_results_only(actual_results)
                summary["total_correlations_analyzed"] = len(standardized_results)
                
                # Extract significant correlations
                for pair_key, result_data in standardized_results.items():
                    if not isinstance(result_data, dict):
                        continue
                        
                    for method in ["pearson", "spearman", "kendall"]:
                        correlation = safe_get_dict_value(result_data, f"{method}_correlation", 0.0)
                        p_value = safe_get_dict_value(result_data, f"{method}_p_value", 1.0)
                        
                        # Ensure p_value is numeric
                        p_value = standardize_p_value(p_value)
                        
                        # Additional defensive check to prevent string comparison
                        if isinstance(p_value, str):
                            logger.warning(f"Found string p_value '{p_value}' in correlation summary, converting to numeric")
                            p_value = standardize_p_value(p_value)
                        
                        if p_value < 0.05:
                            summary["significant_correlations"].append({
                                "variable_1": safe_get_dict_value(result_data, "variable_1", "unknown"),
                                "variable_2": safe_get_dict_value(result_data, "variable_2", "unknown"),
                                "method": method,
                                "correlation": correlation if correlation is not None else 0.0,
                                "p_value": p_value
                            })
        
    except Exception as e:
        logger.debug(f"Non-critical error in correlation summary: {e}")
        # Continue with default summary values
    
    return summary