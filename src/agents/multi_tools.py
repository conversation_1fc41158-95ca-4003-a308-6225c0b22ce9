"""
Multi-Method Agent Tools for Manufacturing Correlation Analysis

PydanticAI tools that provide multi-method correlation analysis capabilities 
to AI agents, supporting <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlation methods
with comparative analysis and intelligent method selection.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from pydantic_ai import RunContext
import logging
from datetime import datetime

# Import our multi-method correlation analysis engine
from ..data.multi_correlations import (
    MultiMethodCorrelationAnalyzer,
    MultiMethodCorrelationResult
)

# Import data structure utilities
from .data_utils import (
    validate_correlation_results_only,
    standardize_p_value,
    format_p_value_for_display,
    create_safe_correlation_summary,
    safe_get_dict_value
)

# Set up logging
logger = logging.getLogger(__name__)


def calculate_multi_method_correlations_tool(
    ctx: RunContext,
    variables: Optional[List[str]] = None,
    min_periods: int = 30,
    include_robustness: bool = False
) -> Dict[str, Any]:
    """
    Calculate correlations using all three methods (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>) 
    with comprehensive comparative analysis.
    
    Args:
        ctx: Run context with data dependencies
        variables: Specific variables to analyze (None for all numeric)
        min_periods: Minimum number of observations required
        include_robustness: Whether to include robustness analysis (computationally intensive)
        
    Returns:
        Dictionary containing multi-method correlation results and metadata
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for multi-method correlation analysis"}
        
        # Filter to specific variables if requested
        if variables:
            available_vars = [var for var in variables if var in data.columns]
            if not available_vars:
                return {"error": f"None of the requested variables {variables} found in data"}
            logger.info(f"Analyzing multi-method correlations for variables: {available_vars}")
        else:
            # Use all numeric columns
            numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_cols:
                return {"error": "No numeric variables found for correlation analysis"}
            logger.info(f"Analyzing multi-method correlations for all numeric variables: {numeric_cols}")
        
        # Initialize analyzer
        analyzer = MultiMethodCorrelationAnalyzer(
            significance_level=getattr(ctx.deps, 'significance_threshold', 0.05)
        )
        
        # Calculate multi-method correlations
        correlation_results = analyzer.calculate_multi_method_correlations(
            data, variables=variables, min_periods=min_periods
        )
        
        if not correlation_results:
            return {
                "error": "No valid correlations found",
                "details": "Insufficient data or no correlations meet minimum requirements"
            }
        
        # Convert results to serializable format
        serialized_results = {}
        for pair_key, result in correlation_results.items():
            serialized_results[pair_key] = {
                "variable_1": result.variable_1,
                "variable_2": result.variable_2,
                "pearson_correlation": round(result.pearson_correlation, 8),
                "spearman_correlation": round(result.spearman_correlation, 8),
                "kendall_correlation": round(result.kendall_correlation, 8),
                "pearson_p_value": standardize_p_value(result.pearson_p_value),
                "spearman_p_value": standardize_p_value(result.spearman_p_value),
                "kendall_p_value": standardize_p_value(result.kendall_p_value),
                "sample_size": result.sample_size,
                "method_convergence_score": round(result.method_convergence_score, 6),
                "recommended_method": result.recommended_method,
                "data_distribution_assessment": result.data_distribution_assessment,
                "pearson_confidence_interval": [round(ci, 6) for ci in result.pearson_confidence_interval],
                "spearman_confidence_interval": [round(ci, 6) for ci in result.spearman_confidence_interval],
                "kendall_confidence_interval": [round(ci, 6) for ci in result.kendall_confidence_interval],
                "interpretation": result.interpretation
            }
        
        # Helper function for significance calculation
        def calculate_significance_level(p_value):
            """Calculate significance level based on p-value."""
            try:
                if p_value is None or pd.isna(p_value) or np.isnan(p_value):
                    return 'unknown'
                elif p_value < 0.001:
                    return 'highly_significant'
                elif p_value < 0.01:
                    return 'very_significant'
                elif p_value < 0.05:
                    return 'significant'
                else:
                    return 'not_significant'
            except (TypeError, ValueError):
                return 'unknown'
        
        # Convert to expected format for display compatibility using safe data access
        display_correlations = []
        
        # Use serialized_results directly since they're already in dictionary format
        for pair_key, result_data in serialized_results.items():
            try:
                # Create display format for each method
                for method in ['pearson', 'spearman', 'kendall']:
                    try:
                        correlation = safe_get_dict_value(result_data, f'{method}_correlation', None)
                        p_value = safe_get_dict_value(result_data, f'{method}_p_value', None)
                        
                        # Validate correlation and p_value
                        if correlation is None or pd.isna(correlation):
                            logger.warning(f"Correlation is None/NaN for {method} in {pair_key}, setting to 0.0")
                            correlation = 0.0
                        
                        if p_value is not None and pd.isna(p_value):
                            p_value = None
                        
                        # Convert p_value to float for significance calculation
                        p_value_float = float(p_value) if p_value is not None else None
                        
                        # Extract variable names with better error handling
                        var1 = safe_get_dict_value(result_data, "variable_1", "NOT_FOUND")
                        var2 = safe_get_dict_value(result_data, "variable_2", "NOT_FOUND")
                        
                        # Only add to display if we have valid variable names
                        if var1 != "NOT_FOUND" and var2 != "NOT_FOUND":
                            display_correlations.append({
                                "variable_1": var1,
                                "variable_2": var2,
                                "correlation_coefficient": round(float(correlation), 8),
                                "p_value": p_value_float,  # Keep numeric p-value for CLI processing
                                "p_value_formatted": format_p_value_for_display(p_value_float) if p_value_float is not None else "N/A",
                                "method": method,
                                "sample_size": safe_get_dict_value(result_data, "sample_size", 0),
                                "significance_level": calculate_significance_level(p_value_float),
                                "interpretation": safe_get_dict_value(result_data, "interpretation", f"{method.title()} correlation")
                            })
                        else:
                            logger.warning(f"Skipping {method} correlation for {pair_key} due to missing variable names. Data structure: {result_data}")
                    except (AttributeError, TypeError, ValueError) as e:
                        logger.warning(f"Failed to process {method} correlation for {pair_key}: {e}")
                        continue
            except Exception as e:
                logger.warning(f"Failed to process result for {pair_key}: {e}")
                continue
        
        # Calculate convergence analysis
        convergence_analysis = analyzer.analyze_method_convergence(correlation_results)
        
        # Optionally include robustness metrics
        robustness_metrics = None
        if include_robustness:
            try:
                robustness_metrics = analyzer.calculate_robustness_metrics(
                    data, correlation_results, bootstrap_samples=50  # Reduced for performance
                )
                logger.info("Robustness analysis completed")
            except Exception as e:
                logger.warning(f"Robustness analysis failed: {e}")
                robustness_metrics = {"error": f"Robustness analysis failed: {str(e)}"}
        
        # Create safe summary to avoid data structure issues
        safe_summary = create_safe_correlation_summary(serialized_results)
        
        # Prepare response
        response = {
            "multi_method_results": serialized_results,
            "significant_correlations": display_correlations,  # Add display-compatible format
            "convergence_analysis": convergence_analysis,
            "total_correlations_analyzed": safe_summary["total_correlations_analyzed"],
            "methods_analyzed": safe_summary["methods_analyzed"],
            "analysis_parameters": {
                "variables_requested": variables,
                "min_periods": min_periods,
                "significance_threshold": getattr(ctx.deps, 'significance_threshold', 0.05),
                "include_robustness": include_robustness
            },
            "data_summary": {
                "total_observations": len(data),
                "variables_analyzed": variables or data.select_dtypes(include=[np.number]).columns.tolist(),
                "analysis_timestamp": datetime.now().isoformat()
            }
        }
        
        if robustness_metrics:
            response["robustness_metrics"] = robustness_metrics
        
        logger.info(f"Multi-method correlation analysis completed: {len(correlation_results)} correlations analyzed")
        return response
        
    except Exception as e:
        logger.error(f"Error in multi-method correlation analysis: {e}")
        return {"error": f"Multi-method correlation analysis failed: {str(e)}"}


def analyze_method_convergence_tool(
    ctx: RunContext,
    correlation_results: Optional[Dict[str, Any]] = None,
    convergence_threshold: float = 0.8
) -> Dict[str, Any]:
    """
    Analyze convergence and stability across correlation methods.
    
    Args:
        ctx: Run context with data dependencies
        correlation_results: Pre-calculated correlation results (None to calculate fresh)
        convergence_threshold: Threshold for high convergence classification
        
    Returns:
        Dictionary with method convergence analysis results
    """
    try:
        # Get correlation results if not provided
        if correlation_results is None:
            # Calculate fresh multi-method correlations
            multi_tool_result = calculate_multi_method_correlations_tool(ctx)
            if "error" in multi_tool_result:
                return multi_tool_result
            correlation_results = multi_tool_result["multi_method_results"]
        
        if not correlation_results:
            return {"error": "No correlation results available for convergence analysis"}
        
        # Initialize analyzer
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Convert serialized results back to MultiMethodCorrelationResult objects
        result_objects = {}
        
        # Validate and standardize input data first
        standardized_results = validate_correlation_results_only(correlation_results)
        
        for pair_key, result_data in standardized_results.items():
            try:
                # P-values are now guaranteed to be standardized floats
                pearson_p = result_data["pearson_p_value"]
                spearman_p = result_data["spearman_p_value"]
                kendall_p = result_data["kendall_p_value"]
                
                result_objects[pair_key] = MultiMethodCorrelationResult(
                    variable_1=result_data["variable_1"],
                    variable_2=result_data["variable_2"],
                    pearson_correlation=result_data["pearson_correlation"],
                    spearman_correlation=result_data["spearman_correlation"],
                    kendall_correlation=result_data["kendall_correlation"],
                    pearson_p_value=pearson_p,
                    spearman_p_value=spearman_p,
                    kendall_p_value=kendall_p,
                    sample_size=result_data["sample_size"],
                    method_convergence_score=result_data["method_convergence_score"],
                    recommended_method=result_data["recommended_method"],
                    data_distribution_assessment=result_data["data_distribution_assessment"],
                    pearson_confidence_interval=tuple(result_data["pearson_confidence_interval"]),
                    spearman_confidence_interval=tuple(result_data["spearman_confidence_interval"]),
                    kendall_confidence_interval=tuple(result_data["kendall_confidence_interval"]),
                    interpretation=result_data["interpretation"]
                )
            except Exception as e:
                logger.warning(f"Failed to process correlation result for {pair_key}: {type(e).__name__}: {e}")
                logger.debug(f"Result data structure: {type(result_data)}, keys: {list(result_data.keys()) if isinstance(result_data, dict) else 'not a dict'}")
                continue
        
        # Perform convergence analysis
        convergence_analysis = analyzer.analyze_method_convergence(result_objects)
        
        # Check if convergence analysis was successful
        if "error" in convergence_analysis:
            return {"error": f"Convergence analysis failed: {convergence_analysis['error']}"}
        
        # Add insights based on convergence patterns
        insights = []
        overall_convergence = convergence_analysis.get("overall_convergence_score", 0.0)
        
        if overall_convergence > convergence_threshold:
            insights.append("High method convergence indicates consistent relationships across all correlation methods")
        elif overall_convergence > 0.5:
            insights.append("Moderate method convergence suggests some differences between correlation methods")
        else:
            insights.append("Low method convergence indicates significant differences between correlation methods - careful method selection is important")
        
        # Method-specific insights
        cross_correlations = convergence_analysis.get("cross_method_correlations", {})
        if cross_correlations.get("pearson_spearman", 0) > 0.9:
            insights.append("Pearson and Spearman methods show very high agreement")
        if cross_correlations.get("spearman_kendall", 0) > 0.9:
            insights.append("Spearman and Kendall methods show very high agreement")
        if cross_correlations.get("pearson_kendall", 1) < 0.7:
            insights.append("Pearson and Kendall methods show notable disagreement - data may have non-linear patterns")
        
        # Distribution insights
        convergence_dist = convergence_analysis.get("convergence_distribution", {})
        convergence_rate = convergence_dist.get("convergence_rate", 0)
        
        if convergence_rate > 0.8:
            insights.append(f"High convergence rate ({convergence_rate:.1%}) indicates robust correlation patterns")
        elif convergence_rate < 0.3:
            insights.append(f"Low convergence rate ({convergence_rate:.1%}) suggests diverse data characteristics requiring method-specific analysis")
        
        # Prepare enhanced response
        response = {
            **convergence_analysis,
            "convergence_threshold": convergence_threshold,
            "insights": insights,
            "method_agreement_summary": {
                "pearson_spearman_agreement": "high" if cross_correlations.get("pearson_spearman", 0) > 0.8 else "moderate" if cross_correlations.get("pearson_spearman", 0) > 0.6 else "low",
                "pearson_kendall_agreement": "high" if cross_correlations.get("pearson_kendall", 0) > 0.8 else "moderate" if cross_correlations.get("pearson_kendall", 0) > 0.6 else "low",
                "spearman_kendall_agreement": "high" if cross_correlations.get("spearman_kendall", 0) > 0.8 else "moderate" if cross_correlations.get("spearman_kendall", 0) > 0.6 else "low"
            },
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"Method convergence analysis completed: {overall_convergence:.3f} overall convergence")
        return response
        
    except Exception as e:
        logger.error(f"Error in method convergence analysis: {e}")
        return {"error": f"Method convergence analysis failed: {str(e)}"}


def recommend_correlation_method_tool(
    ctx: RunContext,
    variable_1: str,
    variable_2: str,
    return_assessment: bool = True
) -> Dict[str, Any]:
    """
    Recommend the most appropriate correlation method for a specific variable pair
    based on data distribution characteristics.
    
    Args:
        ctx: Run context with data dependencies
        variable_1: First variable name
        variable_2: Second variable name
        return_assessment: Whether to include detailed distribution assessment
        
    Returns:
        Dictionary with method recommendation and reasoning
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for method recommendation"}
        
        if variable_1 not in data.columns:
            return {"error": f"Variable '{variable_1}' not found in data"}
        
        if variable_2 not in data.columns:
            return {"error": f"Variable '{variable_2}' not found in data"}
        
        # Get clean data for the variable pair
        clean_data = data[[variable_1, variable_2]].dropna()
        
        if len(clean_data) < 30:
            return {
                "error": f"Insufficient data for analysis: {len(clean_data)} observations (minimum 30 required)"
            }
        
        # Initialize analyzer
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Assess data distribution characteristics
        distribution_assessment = analyzer._assess_data_distribution(
            clean_data[variable_1], clean_data[variable_2]
        )
        
        # Get method recommendation
        recommended_method = analyzer._recommend_correlation_method(
            clean_data[variable_1], clean_data[variable_2], distribution_assessment
        )
        
        # Generate detailed reasoning
        reasoning = []
        
        # Check normality
        normality = distribution_assessment.get('normality', {})
        if 'error' not in normality:
            x_normal = normality.get('x_variable', {}).get('is_normal', False)
            y_normal = normality.get('y_variable', {}).get('is_normal', False)
            
            if x_normal and y_normal:
                reasoning.append("Both variables follow normal distributions")
            elif not x_normal and not y_normal:
                reasoning.append("Neither variable follows a normal distribution")
            else:
                reasoning.append("Variables have different distribution characteristics")
        
        # Check outliers
        outliers = distribution_assessment.get('outliers', {})
        if 'error' not in outliers:
            x_outliers = outliers.get('x_variable', {}).get('has_significant_outliers', False)
            y_outliers = outliers.get('y_variable', {}).get('has_significant_outliers', False)
            
            if x_outliers or y_outliers:
                reasoning.append("Significant outliers detected - robust methods recommended")
            else:
                reasoning.append("No significant outliers detected")
        
        # Check linearity
        linearity = distribution_assessment.get('linearity', {})
        if 'error' not in linearity:
            is_linear = linearity.get('is_linear', False)
            r_squared = linearity.get('r_squared', 0)
            
            if is_linear:
                reasoning.append(f"Strong linear relationship detected (R² = {r_squared:.3f})")
            else:
                reasoning.append(f"Non-linear relationship detected (R² = {r_squared:.3f})")
        
        # Check monotonicity
        monotonicity = distribution_assessment.get('monotonicity', {})
        if 'error' not in monotonicity:
            is_monotonic = monotonicity.get('is_monotonic', False)
            direction = monotonicity.get('direction', 'unknown')
            
            if is_monotonic:
                reasoning.append(f"Monotonic relationship detected ({direction})")
            else:
                reasoning.append("Non-monotonic relationship")
        
        # Method-specific guidance
        method_guidance = {
            'pearson': "Best for linear relationships between normally distributed variables",
            'spearman': "Best for monotonic relationships, robust to outliers and non-normal distributions",
            'kendall': "Best for ordinal data and highly robust to outliers, suitable for non-linear associations"
        }
        
        # Calculate all three correlations for comparison
        try:
            from scipy.stats import pearsonr, spearmanr, kendalltau
            
            pearson_r, pearson_p = pearsonr(clean_data[variable_1], clean_data[variable_2])
            spearman_r, spearman_p = spearmanr(clean_data[variable_1], clean_data[variable_2])
            kendall_r, kendall_p = kendalltau(clean_data[variable_1], clean_data[variable_2])
            
            correlation_comparison = {
                'pearson': {'correlation': round(pearson_r, 6), 'p_value': format_p_value_for_display(pearson_p)},
                'spearman': {'correlation': round(spearman_r, 6), 'p_value': format_p_value_for_display(spearman_p)},
                'kendall': {'correlation': round(kendall_r, 6), 'p_value': format_p_value_for_display(kendall_p)}
            }
        except Exception as e:
            logger.warning(f"Failed to calculate correlation comparison: {e}")
            correlation_comparison = {"error": "Failed to calculate correlations for comparison"}
        
        # Prepare response
        response = {
            "variable_pair": f"{variable_1} - {variable_2}",
            "recommended_method": recommended_method,
            "recommendation_confidence": "high" if len(reasoning) >= 3 else "medium" if len(reasoning) >= 2 else "low",
            "reasoning": reasoning,
            "method_guidance": method_guidance[recommended_method],
            "all_methods_guidance": method_guidance,
            "correlation_comparison": correlation_comparison,
            "sample_size": len(clean_data),
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        if return_assessment:
            response["data_distribution_assessment"] = distribution_assessment
        
        logger.info(f"Method recommendation completed for {variable_1}-{variable_2}: {recommended_method}")
        return response
        
    except Exception as e:
        logger.error(f"Error in correlation method recommendation: {e}")
        return {"error": f"Method recommendation failed: {str(e)}"}


def calculate_robustness_metrics_tool(
    ctx: RunContext,
    correlation_results: Optional[Dict[str, Any]] = None,
    bootstrap_samples: int = 100,
    variable_pairs: Optional[List[Tuple[str, str]]] = None
) -> Dict[str, Any]:
    """
    Calculate robustness metrics for correlation analysis through bootstrap sampling.
    
    Args:
        ctx: Run context with data dependencies
        correlation_results: Pre-calculated correlation results (None to calculate fresh)
        bootstrap_samples: Number of bootstrap samples for analysis
        variable_pairs: Specific variable pairs to analyze (None for all)
        
    Returns:
        Dictionary with robustness analysis results
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for robustness analysis"}
        
        # Get correlation results if not provided
        if correlation_results is None:
            multi_tool_result = calculate_multi_method_correlations_tool(ctx, include_robustness=False)
            if "error" in multi_tool_result:
                return multi_tool_result
            correlation_results = multi_tool_result["multi_method_results"]
        
        if not correlation_results:
            return {"error": "No correlation results available for robustness analysis"}
        
        # Initialize analyzer
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Convert to MultiMethodCorrelationResult objects
        result_objects = {}
        
        # Validate and standardize input data first
        standardized_results = validate_correlation_results_only(correlation_results)
        
        for pair_key, result_data in standardized_results.items():
            if variable_pairs:
                # Check if this pair is in the requested pairs
                var1, var2 = result_data["variable_1"], result_data["variable_2"]
                if not any((v1 == var1 and v2 == var2) or (v1 == var2 and v2 == var1) for v1, v2 in variable_pairs):
                    continue
            
            try:
                # P-values are now guaranteed to be standardized floats
                pearson_p = result_data["pearson_p_value"]
                spearman_p = result_data["spearman_p_value"]
                kendall_p = result_data["kendall_p_value"]
                
                result_objects[pair_key] = MultiMethodCorrelationResult(
                    variable_1=result_data["variable_1"],
                    variable_2=result_data["variable_2"],
                    pearson_correlation=result_data["pearson_correlation"],
                    spearman_correlation=result_data["spearman_correlation"],
                    kendall_correlation=result_data["kendall_correlation"],
                    pearson_p_value=pearson_p,
                    spearman_p_value=spearman_p,
                    kendall_p_value=kendall_p,
                    sample_size=result_data["sample_size"],
                    method_convergence_score=result_data["method_convergence_score"],
                    recommended_method=result_data["recommended_method"],
                    data_distribution_assessment=result_data["data_distribution_assessment"],
                    pearson_confidence_interval=tuple(result_data["pearson_confidence_interval"]),
                    spearman_confidence_interval=tuple(result_data["spearman_confidence_interval"]),
                    kendall_confidence_interval=tuple(result_data["kendall_confidence_interval"]),
                    interpretation=result_data["interpretation"]
                )
            except Exception as e:
                logger.warning(f"Failed to process correlation result for {pair_key}: {type(e).__name__}: {e}")
                logger.debug(f"Result data structure: {type(result_data)}, keys: {list(result_data.keys()) if isinstance(result_data, dict) else 'not a dict'}")
                continue
        
        if not result_objects:
            return {"error": "No valid correlation results for robustness analysis"}
        
        # Calculate robustness metrics
        logger.info(f"Starting robustness analysis with {bootstrap_samples} bootstrap samples for {len(result_objects)} variable pairs")
        
        robustness_metrics = analyzer.calculate_robustness_metrics(
            data, result_objects, bootstrap_samples=bootstrap_samples
        )
        
        # Generate robustness insights
        insights = []
        stable_methods = []
        unstable_methods = []
        
        for pair_key, pair_metrics in robustness_metrics.items():
            for method in ['pearson', 'spearman', 'kendall']:
                if method in pair_metrics:
                    stability_score = pair_metrics[method].get('stability_score', 0)
                    if stability_score > 0.8:
                        stable_methods.append(f"{method} for {pair_key}")
                    elif stability_score < 0.5:
                        unstable_methods.append(f"{method} for {pair_key}")
        
        if stable_methods:
            insights.append(f"High stability detected in {len(stable_methods)} method-pair combinations")
        if unstable_methods:
            insights.append(f"Low stability detected in {len(unstable_methods)} method-pair combinations - results may be sensitive to data variations")
        
        # Calculate overall robustness summary
        all_stability_scores = []
        method_robustness = {'pearson': [], 'spearman': [], 'kendall': []}
        
        for pair_metrics in robustness_metrics.values():
            for method in ['pearson', 'spearman', 'kendall']:
                if method in pair_metrics:
                    stability = pair_metrics[method].get('stability_score', 0)
                    all_stability_scores.append(stability)
                    method_robustness[method].append(stability)
        
        overall_robustness = {
            'overall_stability_score': round(np.mean(all_stability_scores), 6) if all_stability_scores else 0,
            'stability_standard_deviation': round(np.std(all_stability_scores), 6) if all_stability_scores else 0,
            'method_robustness_summary': {
                method: {
                    'mean_stability': round(np.mean(scores), 6) if scores else 0,
                    'min_stability': round(np.min(scores), 6) if scores else 0,
                    'max_stability': round(np.max(scores), 6) if scores else 0
                }
                for method, scores in method_robustness.items()
            }
        }
        
        # Prepare response
        response = {
            "robustness_metrics": robustness_metrics,
            "overall_robustness": overall_robustness,
            "insights": insights,
            "analysis_parameters": {
                "bootstrap_samples": bootstrap_samples,
                "variable_pairs_analyzed": len(result_objects),
                "requested_variable_pairs": variable_pairs
            },
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"Robustness analysis completed: {overall_robustness['overall_stability_score']:.3f} overall stability")
        return response
        
    except Exception as e:
        logger.error(f"Error in robustness analysis: {e}")
        return {"error": f"Robustness analysis failed: {str(e)}"}


# Tool registration - these will be used by the agent
MULTI_METHOD_TOOLS = [
    calculate_multi_method_correlations_tool,
    analyze_method_convergence_tool,
    recommend_correlation_method_tool,
    calculate_robustness_metrics_tool
]