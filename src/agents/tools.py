"""
Agent Tools for Manufacturing Correlation Analysis

PydanticAI tools that provide correlation analysis capabilities to AI agents,
integrating with the manufacturing data processing and correlation analysis engines.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from pydantic_ai import RunContext
import logging
from datetime import datetime

# Import our correlation analysis engine
from ..data.correlations import (
    ManufacturingCorrelationAnalyzer
)

# Import data utilities for enhanced p-value formatting
from .data_utils import format_p_value_for_display

# Set up logging
logger = logging.getLogger(__name__)

def calculate_correlation_matrix(
    ctx: RunContext, 
    variables: Optional[List[str]] = None,
    method: str = 'pearson',
    min_periods: int = 30
) -> Dict[str, Any]:
    """
    Calculate correlation matrix for manufacturing variables.
    
    Args:
        ctx: Run context with data dependencies
        variables: Specific variables to analyze (None for all numeric)
        method: Correlation method ('pearson', 'spearman', 'kendall')
        min_periods: Minimum number of observations required
        
    Returns:
        Dictionary containing correlation matrix and metadata
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for correlation analysis"}
        
        # Filter to specific variables if requested
        if variables:
            available_vars = [var for var in variables if var in data.columns]
            if not available_vars:
                return {"error": f"None of the requested variables {variables} found in data"}
            data_subset = data[available_vars]
        else:
            # Use all numeric columns
            data_subset = data.select_dtypes(include=[np.number])
        
        if data_subset.empty:
            return {"error": "No numeric variables found for correlation analysis"}
        
        # Initialize analyzer
        analyzer = ManufacturingCorrelationAnalyzer(
            significance_level=getattr(ctx.deps, 'significance_threshold', 0.05)
        )
        
        # Calculate correlation matrix
        corr_result = analyzer.calculate_correlation_matrix(
            data_subset, method=method, min_periods=min_periods
        )
        
        # Prepare response
        response = {
            "correlation_matrix": corr_result.matrix,
            "method": corr_result.method,
            "sample_sizes": corr_result.sample_sizes,
            "p_values": corr_result.p_values,
            "variables_analyzed": list(data_subset.columns),
            "total_observations": len(data_subset),
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error calculating correlation matrix: {e}")
        return {"error": f"Correlation matrix calculation failed: {str(e)}"}

def find_significant_correlations(
    ctx: RunContext,
    min_correlation: float = 0.01,
    method: str = 'pearson',
    max_results: int = 20
) -> List[Dict[str, Any]]:
    """
    Find statistically significant correlations above threshold.
    
    Args:
        ctx: Run context with data dependencies
        min_correlation: Minimum correlation coefficient threshold
        method: Correlation method
        max_results: Maximum number of results to return
        
    Returns:
        List of significant correlation results
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return [{"error": "No data available for correlation analysis"}]
        
        # Initialize analyzer
        analyzer = ManufacturingCorrelationAnalyzer(
            significance_level=getattr(ctx.deps, 'significance_threshold', 0.05)
        )
        
        # Find significant correlations
        correlations = analyzer.find_significant_correlations(
            data, 
            method=method,
            min_correlation=min_correlation,
            return_all=False
        )
        
        # Convert to dictionaries and limit results
        results = []
        for corr in correlations[:max_results]:
            # Calculate both numeric and formatted p-values
            p_value_numeric = corr.p_value if corr.p_value is not None else None
            p_value_formatted = format_p_value_for_display(p_value_numeric) if p_value_numeric is not None else None
            
            result_dict = {
                "variable_1": corr.variable_1,
                "variable_2": corr.variable_2,
                "correlation_coefficient": round(corr.correlation_coefficient, 8),  # Higher precision
                "p_value": p_value_numeric,  # Numeric value for calculations
                "p_value_formatted": p_value_formatted,  # Formatted string for display
                "confidence_interval": [round(ci, 6) for ci in corr.confidence_interval],
                "sample_size": corr.sample_size,
                "method": corr.method,
                "significance_level": corr.significance_level,
                "interpretation": corr.interpretation
            }
            results.append(result_dict)
        
        return results
        
    except Exception as e:
        logger.error(f"Error finding significant correlations: {e}")
        return [{"error": f"Significant correlation analysis failed: {str(e)}"}]

def analyze_lag_correlations(
    ctx: RunContext,
    variable_1: str,
    variable_2: str,
    max_lag: int = 60
) -> Dict[str, Any]:
    """
    Analyze time-lagged correlations between two variables.
    
    Args:
        ctx: Run context with data dependencies
        variable_1: First variable (leading indicator)
        variable_2: Second variable (response variable)
        max_lag: Maximum lag to test in time periods
        
    Returns:
        Dictionary with lag correlation analysis results
    """
    try:
        # Get data from context
        data = ctx.deps.data
        time_column = getattr(ctx.deps, 'time_column', 'timestamp')
        
        if data.empty:
            return {"error": "No data available for lag correlation analysis"}
        
        if variable_1 not in data.columns:
            return {"error": f"Variable '{variable_1}' not found in data"}
        
        if variable_2 not in data.columns:
            return {"error": f"Variable '{variable_2}' not found in data"}
        
        # Initialize analyzer
        analyzer = ManufacturingCorrelationAnalyzer()
        
        # Analyze lag correlations
        lag_result = analyzer.analyze_lag_correlations(
            data, variable_1, variable_2, max_lag=max_lag, time_column=time_column
        )
        
        # Calculate both numeric and formatted p-values
        p_value_numeric = lag_result.p_value if lag_result.p_value is not None else None
        p_value_formatted = format_p_value_for_display(p_value_numeric) if p_value_numeric is not None else None
        
        # Convert to dictionary
        result_dict = {
            "variable_1": lag_result.variable_1,
            "variable_2": lag_result.variable_2,
            "optimal_lag": lag_result.optimal_lag,
            "max_correlation": round(lag_result.max_correlation, 8),
            "p_value": p_value_numeric,  # Numeric value for calculations
            "p_value_formatted": p_value_formatted,  # Formatted string for display
            "lag_range": list(lag_result.lag_range),
            "correlation_by_lag": {str(k): round(v, 8) for k, v in lag_result.correlation_by_lag.items()},
            "interpretation": lag_result.interpretation,
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        return result_dict
        
    except Exception as e:
        logger.error(f"Error analyzing lag correlations: {e}")
        return {"error": f"Lag correlation analysis failed: {str(e)}"}

def analyze_process_correlations(
    ctx: RunContext,
    target_variable: str,
    process_variables: Optional[List[str]] = None
) -> Dict[str, Dict[str, Any]]:
    """
    Analyze correlations between a target variable and process variables.
    
    Args:
        ctx: Run context with data dependencies
        target_variable: Target variable for analysis
        process_variables: List of process variables (None for auto-detection)
        
    Returns:
        Dictionary of process correlation results
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for process correlation analysis"}
        
        if target_variable not in data.columns:
            return {"error": f"Target variable '{target_variable}' not found in data"}
        
        # Initialize analyzer
        analyzer = ManufacturingCorrelationAnalyzer()
        
        # Analyze process correlations
        process_results = analyzer.analyze_process_correlations(
            data, target_variable, process_variables
        )
        
        # Convert results to dictionaries
        results = {}
        for var, lag_result in process_results.items():
            # Calculate both numeric and formatted p-values
            p_value_numeric = lag_result.p_value if lag_result.p_value is not None else None
            p_value_formatted = format_p_value_for_display(p_value_numeric) if p_value_numeric is not None else None
            
            results[var] = {
                "optimal_lag": lag_result.optimal_lag,
                "max_correlation": round(lag_result.max_correlation, 8),
                "p_value": p_value_numeric,  # Numeric value for calculations
                "p_value_formatted": p_value_formatted,  # Formatted string for display
                "interpretation": lag_result.interpretation,
                "correlation_by_lag": {str(k): round(v, 8) for k, v in lag_result.correlation_by_lag.items()}
            }
        
        return results
        
    except Exception as e:
        logger.error(f"Error analyzing process correlations: {e}")
        return {"error": f"Process correlation analysis failed: {str(e)}"}

def calculate_data_quality_score(ctx: RunContext) -> Dict[str, Any]:
    """
    Calculate overall data quality score for the dataset.
    
    Args:
        ctx: Run context with data dependencies
        
    Returns:
        Dictionary with data quality assessment
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for quality assessment"}
        
        # Calculate completeness
        total_cells = data.shape[0] * data.shape[1]
        non_null_cells = data.count().sum()
        completeness = non_null_cells / total_cells if total_cells > 0 else 0
        
        # Calculate consistency (duplicate detection)
        duplicate_rows = data.duplicated().sum()
        consistency = 1 - (duplicate_rows / len(data)) if len(data) > 0 else 0
        
        # Calculate validity for numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        validity_scores = []
        
        outlier_info = {}
        for col in numeric_cols:
            col_data = data[col].dropna()
            if len(col_data) > 0:
                # Outlier detection using IQR method
                q1 = col_data.quantile(0.25)
                q3 = col_data.quantile(0.75)
                iqr = q3 - q1
                
                if iqr > 0:
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    
                    outliers = ((col_data < lower_bound) | (col_data > upper_bound)).sum()
                    validity = 1 - (outliers / len(col_data))
                    validity_scores.append(validity)
                    
                    outlier_info[col] = {
                        "outlier_count": int(outliers),
                        "outlier_percentage": round((outliers / len(col_data)) * 100, 2),
                        "valid_range": [float(lower_bound), float(upper_bound)]
                    }
                else:
                    validity_scores.append(1.0)
        
        validity = np.mean(validity_scores) if validity_scores else 1.0
        
        # Overall quality score
        quality_score = (completeness * 0.4 + consistency * 0.3 + validity * 0.3)
        
        # Timestamp quality assessment
        timestamp_quality = None
        time_column = getattr(ctx.deps, 'time_column', 'timestamp')
        
        if time_column and time_column in data.columns:
            try:
                timestamps = pd.to_datetime(data[time_column], errors='coerce')
                valid_timestamps = timestamps.notna().sum()
                timestamp_quality = {
                    "valid_timestamps": int(valid_timestamps),
                    "total_timestamps": len(data),
                    "validity_percentage": round((valid_timestamps / len(data)) * 100, 2),
                    "time_range": {
                        "start": timestamps.min().isoformat() if timestamps.notna().any() else None,
                        "end": timestamps.max().isoformat() if timestamps.notna().any() else None
                    },
                    "duplicate_timestamps": int(timestamps.duplicated().sum())
                }
            except Exception as e:
                timestamp_quality = {"error": f"Failed to parse timestamp column: {str(e)}"}
        
        return {
            "overall_quality_score": round(quality_score, 3),
            "completeness_score": round(completeness, 3),
            "consistency_score": round(consistency, 3),
            "validity_score": round(validity, 3),
            "data_shape": list(data.shape),
            "missing_data": {
                "total_missing_values": int(data.isnull().sum().sum()),
                "missing_percentage": round((data.isnull().sum().sum() / total_cells) * 100, 2),
                "columns_with_missing": data.columns[data.isnull().any()].tolist()
            },
            "duplicate_rows": int(duplicate_rows),
            "numeric_columns": len(numeric_cols),
            "outlier_analysis": outlier_info,
            "timestamp_quality": timestamp_quality,
            "assessment_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error calculating data quality score: {e}")
        return {"error": f"Data quality assessment failed: {str(e)}"}

def filter_data_by_time_range(
    ctx: RunContext,
    start_time: str,
    end_time: str,
    time_column: Optional[str] = None
) -> Dict[str, Any]:
    """
    Filter data by time range and update context.
    
    Args:
        ctx: Run context with data dependencies
        start_time: Start time (ISO format string)
        end_time: End time (ISO format string)
        time_column: Time column name (uses default if None)
        
    Returns:
        Dictionary with filtering results
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for time filtering"}
        
        # Use provided time column or default
        if time_column is None:
            time_column = getattr(ctx.deps, 'time_column', 'timestamp')
        
        if time_column not in data.columns:
            return {"error": f"Time column '{time_column}' not found in data"}
        
        # Parse time range
        try:
            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)
        except Exception as e:
            return {"error": f"Invalid time format. Use ISO format (YYYY-MM-DD HH:MM:SS): {str(e)}"}
        
        # Convert time column to datetime
        data_time = pd.to_datetime(data[time_column], errors='coerce')
        
        # Filter data
        time_mask = (data_time >= start_dt) & (data_time <= end_dt)
        filtered_data = data[time_mask]
        
        # Update context data (this modifies the original data in context)
        ctx.deps.data = filtered_data
        
        return {
            "filtering_successful": True,
            "original_rows": len(data),
            "filtered_rows": len(filtered_data),
            "rows_removed": len(data) - len(filtered_data),
            "time_range": {
                "start": start_time,
                "end": end_time
            },
            "actual_data_range": {
                "start": data_time[time_mask].min().isoformat() if len(filtered_data) > 0 else None,
                "end": data_time[time_mask].max().isoformat() if len(filtered_data) > 0 else None
            }
        }
        
    except Exception as e:
        logger.error(f"Error filtering data by time range: {e}")
        return {"error": f"Time range filtering failed: {str(e)}"}

def calculate_thickness_metrics(ctx: RunContext) -> Dict[str, Any]:
    """
    Calculate thickness metrics from sensor data if available.
    
    Args:
        ctx: Run context with data dependencies
        
    Returns:
        Dictionary with thickness calculation results
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for thickness calculation"}
        
        # Look for thickness sensor columns
        sensor_cols = [f'Sensor {i:02d}' for i in range(1, 11)]
        available_sensors = [col for col in sensor_cols if col in data.columns]
        
        if not available_sensors:
            return {"error": "No thickness sensor columns found (Sensor 01-10)"}
        
        # Convert sensor readings to numeric
        thickness_data = {}
        for col in available_sensors:
            numeric_values = pd.to_numeric(data[col], errors='coerce')
            thickness_data[col] = numeric_values
        
        # Create DataFrame for calculations
        thickness_df = pd.DataFrame(thickness_data)
        
        # Calculate metrics
        thickness_avg = thickness_df.mean(axis=1)
        thickness_uniformity = thickness_df.std(axis=1)
        thickness_range = thickness_df.max(axis=1) - thickness_df.min(axis=1)
        
        # Overall statistics
        overall_stats = {
            "sensors_available": len(available_sensors),
            "total_measurements": len(thickness_avg),
            "valid_measurements": thickness_avg.notna().sum(),
            "average_thickness": {
                "mean": float(thickness_avg.mean()),
                "std": float(thickness_avg.std()),
                "min": float(thickness_avg.min()),
                "max": float(thickness_avg.max()),
                "q25": float(thickness_avg.quantile(0.25)),
                "q75": float(thickness_avg.quantile(0.75))
            },
            "thickness_uniformity": {
                "mean": float(thickness_uniformity.mean()),
                "std": float(thickness_uniformity.std()),
                "min": float(thickness_uniformity.min()),
                "max": float(thickness_uniformity.max())
            },
            "thickness_range": {
                "mean": float(thickness_range.mean()),
                "std": float(thickness_range.std()),
                "min": float(thickness_range.min()),
                "max": float(thickness_range.max())
            }
        }
        
        # Individual sensor statistics
        sensor_stats = {}
        for col in available_sensors:
            sensor_data = thickness_df[col].dropna()
            if len(sensor_data) > 0:
                sensor_stats[col] = {
                    "mean": float(sensor_data.mean()),
                    "std": float(sensor_data.std()),
                    "min": float(sensor_data.min()),
                    "max": float(sensor_data.max()),
                    "valid_count": int(len(sensor_data))
                }
        
        # Update context data with calculated metrics
        if len(available_sensors) > 0:
            ctx.deps.data = ctx.deps.data.copy()
            ctx.deps.data['thickness_avg'] = thickness_avg
            ctx.deps.data['thickness_uniformity'] = thickness_uniformity
            ctx.deps.data['thickness_range'] = thickness_range
        
        return {
            "calculation_successful": True,
            "overall_statistics": overall_stats,
            "sensor_statistics": sensor_stats,
            "calculated_variables": ["thickness_avg", "thickness_uniformity", "thickness_range"],
            "calculation_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error calculating thickness metrics: {e}")
        return {"error": f"Thickness calculation failed: {str(e)}"}

def get_variable_summary(
    ctx: RunContext,
    variable_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get summary statistics for a variable or all variables.
    
    Args:
        ctx: Run context with data dependencies
        variable_name: Specific variable name (None for all variables)
        
    Returns:
        Dictionary with variable summary statistics
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for variable summary"}
        
        if variable_name:
            if variable_name not in data.columns:
                return {"error": f"Variable '{variable_name}' not found in data"}
            
            # Summary for specific variable
            var_data = data[variable_name].dropna()
            
            if var_data.empty:
                return {"error": f"Variable '{variable_name}' has no valid data"}
            
            if pd.api.types.is_numeric_dtype(var_data):
                summary = {
                    "variable_name": variable_name,
                    "data_type": "numeric",
                    "count": int(len(var_data)),
                    "missing_count": int(data[variable_name].isnull().sum()),
                    "mean": float(var_data.mean()),
                    "median": float(var_data.median()),
                    "std": float(var_data.std()),
                    "min": float(var_data.min()),
                    "max": float(var_data.max()),
                    "q25": float(var_data.quantile(0.25)),
                    "q75": float(var_data.quantile(0.75))
                }
            else:
                # Categorical variable
                value_counts = var_data.value_counts()
                summary = {
                    "variable_name": variable_name,
                    "data_type": "categorical",
                    "count": int(len(var_data)),
                    "missing_count": int(data[variable_name].isnull().sum()),
                    "unique_values": int(var_data.nunique()),
                    "most_common": value_counts.head(5).to_dict(),
                    "top_value": str(value_counts.index[0]) if len(value_counts) > 0 else None,
                    "top_frequency": int(value_counts.iloc[0]) if len(value_counts) > 0 else 0
                }
            
            return summary
        
        else:
            # Summary for all variables
            summaries = {}
            
            for col in data.columns:
                var_data = data[col].dropna()
                
                if pd.api.types.is_numeric_dtype(var_data) and not var_data.empty:
                    summaries[col] = {
                        "data_type": "numeric",
                        "count": int(len(var_data)),
                        "missing_count": int(data[col].isnull().sum()),
                        "mean": float(var_data.mean()),
                        "std": float(var_data.std()),
                        "min": float(var_data.min()),
                        "max": float(var_data.max())
                    }
                elif not var_data.empty:
                    summaries[col] = {
                        "data_type": "categorical",
                        "count": int(len(var_data)),
                        "missing_count": int(data[col].isnull().sum()),
                        "unique_values": int(var_data.nunique())
                    }
            
            return {
                "total_variables": len(data.columns),
                "numeric_variables": len(data.select_dtypes(include=[np.number]).columns),
                "variable_summaries": summaries
            }
        
    except Exception as e:
        logger.error(f"Error getting variable summary: {e}")
        return {"error": f"Variable summary failed: {str(e)}"}

# Enhanced tools for unified table analysis
def analyze_basic_correlations(
    ctx: RunContext,
    filter_matched_flows: bool = True
) -> Dict[str, Any]:
    """
    Perform basic correlation analysis for the unified manufacturing table.
    
    Analyzes direct SM-FM correlations and operational feature correlations
    for the 83-column unified table structure.
    
    Args:
        ctx: Run context with data dependencies
        filter_matched_flows: Whether to filter to matched SM-FM flows only
        
    Returns:
        Dictionary containing basic correlation analysis results
    """
    try:
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for basic correlation analysis"}
        
        # Filter to matched flows if requested
        if filter_matched_flows and 'has_fm_match' in data.columns:
            matched_df = data[data['has_fm_match']].copy()
            logger.info(f"Filtered to {len(matched_df)} matched SM-FM flows from {len(data)} total records")
        else:
            matched_df = data.copy()
            logger.info(f"Analyzing all {len(matched_df)} records (no filtering applied)")
        
        if matched_df.empty:
            return {"error": "No matched flows available for analysis"}
        
        results = {
            "analysis_type": "basic_correlations",
            "total_records": len(data),
            "matched_records": len(matched_df),
            "match_rate": len(matched_df) / len(data) if len(data) > 0 else 0,
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        # Direct SM-FM correlation if both variables exist
        if 'sm_scrap_pct' in matched_df.columns and 'fm_reject_pct' in matched_df.columns:
            valid_pairs = matched_df[['sm_scrap_pct', 'fm_reject_pct']].dropna()
            if len(valid_pairs) > 10:  # Minimum sample size
                direct_corr = valid_pairs.corr().iloc[0, 1]
                results["direct_sm_fm_correlation"] = {
                    "correlation_coefficient": round(direct_corr, 6),
                    "sample_size": len(valid_pairs),
                    "description": "Direct correlation between SM scrap % and FM reject %"
                }
            else:
                results["direct_sm_fm_correlation"] = {"error": "Insufficient data for SM-FM correlation"}
        
        # Operational features correlation matrix
        operational_features = [
            'sm_scrap_pct', 'fm_reject_pct', 'speed_avg', 'speed_cv', 
            'speed_deviation_pct', 'stops_during_production', 'total_stop_duration_minutes',
            'production_efficiency_pct', 'sm_production_rate', 'time_since_last_stop'
        ]
        
        # Filter to available features
        available_features = [feat for feat in operational_features if feat in matched_df.columns]
        
        if len(available_features) >= 3:
            feature_data = matched_df[available_features].select_dtypes(include=[np.number])
            if not feature_data.empty:
                correlation_matrix = feature_data.corr()
                
                # Convert to serializable format
                corr_dict = {}
                for col in correlation_matrix.columns:
                    corr_dict[col] = {}
                    for idx in correlation_matrix.index:
                        value = correlation_matrix.loc[idx, col]
                        if pd.notna(value):
                            corr_dict[col][idx] = round(value, 6)
                        else:
                            corr_dict[col][idx] = None
                
                results["operational_correlation_matrix"] = {
                    "matrix": corr_dict,
                    "features_analyzed": list(feature_data.columns),
                    "sample_size": len(feature_data)
                }
        
        # Machine type analysis if available
        if 'fm_machine_type' in matched_df.columns:
            machine_dist = matched_df['fm_machine_type'].value_counts()
            results["machine_type_distribution"] = {
                machine: int(count) for machine, count in machine_dist.items()
            }
            
            # Quality by machine type
            if 'sm_scrap_pct' in matched_df.columns:
                quality_by_machine = matched_df.groupby('fm_machine_type')['sm_scrap_pct'].agg(['mean', 'std', 'count'])
                results["quality_by_machine_type"] = {
                    machine: {
                        "mean_scrap_pct": round(stats['mean'], 4),
                        "std_scrap_pct": round(stats['std'], 4),
                        "sample_size": int(stats['count'])
                    } for machine, stats in quality_by_machine.iterrows()
                }
        
        return results
        
    except Exception as e:
        logger.error(f"Error in basic correlation analysis: {e}")
        return {"error": f"Basic correlation analysis failed: {str(e)}"}

def calculate_unified_correlation_matrix(
    ctx: RunContext,
    feature_groups: Optional[List[str]] = None,
    method: str = 'pearson',
    min_periods: int = 30
) -> Dict[str, Any]:
    """
    Calculate correlation matrix optimized for the 83-column unified table.
    
    Groups features by manufacturing context for focused analysis.
    
    Args:
        ctx: Run context with data dependencies
        feature_groups: Specific feature groups to analyze
        method: Correlation method ('pearson', 'spearman', 'kendall')
        min_periods: Minimum number of observations required
        
    Returns:
        Dictionary containing grouped correlation analysis
    """
    try:
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for unified correlation analysis"}
        
        # Define feature groups for the unified table
        feature_group_definitions = {
            "quality_metrics": [
                'sm_scrap_pct', 'fm_reject_pct', 'production_efficiency_pct', 
                'sm_quality_efficiency', 'fm_quality_efficiency'
            ],
            "speed_analytics": [
                'Speed', 'speed_avg', 'speed_std', 'speed_cv', 'speed_change_rate',
                'speed_deviation_pct', 'speed_performance'
            ],
            "stoppage_analytics": [
                'stops_during_production', 'total_stop_duration_minutes', 
                'restart_impact_score', 'time_since_last_stop', 'stop_duration_previous'
            ],
            "temporal_features": [
                'hour_of_day', 'day_of_week', 'production_shift', 'shift_avg_efficiency',
                'hourly_avg_scrap', 'is_weekend'
            ],
            "production_context": [
                'sm_production_rate', 'capacity_utilization_pct', 'sheets_per_minute',
                'sm_duration_minutes', 'fm_duration_minutes'
            ],
            "sm_fm_integration": [
                'sm_to_fm_gap_minutes', 'match_quality_score', 'has_fm_match'
            ]
        }
        
        # Use specified groups or all groups
        groups_to_analyze = feature_groups if feature_groups else list(feature_group_definitions.keys())
        
        results = {
            "analysis_type": "unified_correlation_matrix",
            "method": method,
            "min_periods": min_periods,
            "feature_groups_analyzed": groups_to_analyze,
            "total_records": len(data),
            "analysis_timestamp": datetime.now().isoformat(),
            "group_correlations": {}
        }
        
        # Initialize analyzer
        analyzer = ManufacturingCorrelationAnalyzer(
            significance_level=getattr(ctx.deps, 'significance_threshold', 0.05)
        )
        
        for group_name in groups_to_analyze:
            if group_name not in feature_group_definitions:
                continue
                
            features = feature_group_definitions[group_name]
            available_features = [feat for feat in features if feat in data.columns]
            
            if len(available_features) < 2:
                results["group_correlations"][group_name] = {
                    "error": f"Insufficient features available for {group_name}"
                }
                continue
            
            # Get numeric data for this group
            group_data = data[available_features].select_dtypes(include=[np.number])
            
            if group_data.empty:
                results["group_correlations"][group_name] = {
                    "error": f"No numeric data available for {group_name}"
                }
                continue
            
            # Calculate correlation matrix for this group
            try:
                corr_result = analyzer.calculate_correlation_matrix(
                    group_data, method=method, min_periods=min_periods
                )
                
                results["group_correlations"][group_name] = {
                    "correlation_matrix": corr_result.matrix,
                    "sample_sizes": corr_result.sample_sizes,
                    "p_values": corr_result.p_values,
                    "features_analyzed": list(group_data.columns),
                    "observations": len(group_data)
                }
                
            except Exception as e:
                results["group_correlations"][group_name] = {
                    "error": f"Correlation calculation failed: {str(e)}"
                }
        
        return results
        
    except Exception as e:
        logger.error(f"Error in unified correlation matrix calculation: {e}")
        return {"error": f"Unified correlation analysis failed: {str(e)}"}

def calculate_scrap_rate_metrics(ctx: RunContext) -> Dict[str, Any]:
    """
    Calculate comprehensive scrap rate metrics from manufacturing data.
    
    Analyzes scrap rates from both forming machine (fm_scrap_rate) and 
    sheet machine (scrap_rate) data, including material-specific analysis,
    off roller factor impact assessment, and statistical distributions.
    
    Args:
        ctx: Run context with data dependencies
        
    Returns:
        Dictionary with scrap rate calculation results
    """
    try:
        # Get data from context
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for scrap rate calculation"}
        
        # Look for scrap rate columns
        scrap_rate_cols = []
        if 'fm_scrap_rate' in data.columns:
            scrap_rate_cols.append('fm_scrap_rate')
        if 'scrap_rate' in data.columns:
            scrap_rate_cols.append('scrap_rate')
        
        if not scrap_rate_cols:
            return {"error": "No scrap rate columns found (fm_scrap_rate, scrap_rate)"}
        
        # Convert scrap rate readings to numeric
        scrap_rate_data = {}
        for col in scrap_rate_cols:
            numeric_values = pd.to_numeric(data[col], errors='coerce')
            # Ensure scrap rates are between 0 and 1
            numeric_values = numeric_values.clip(0, 1)
            scrap_rate_data[col] = numeric_values
        
        # Create DataFrame for calculations
        scrap_rate_df = pd.DataFrame(scrap_rate_data)
        
        # Calculate overall scrap rate (average of available rates)
        if len(scrap_rate_cols) > 1:
            overall_scrap_rate = scrap_rate_df.mean(axis=1)
        else:
            overall_scrap_rate = scrap_rate_df[scrap_rate_cols[0]]
        
        # Calculate scrap rate variation (for quality consistency assessment)
        if len(scrap_rate_cols) > 1:
            scrap_rate_variation = scrap_rate_df.std(axis=1)
        else:
            scrap_rate_variation = pd.Series([0.0] * len(overall_scrap_rate), index=overall_scrap_rate.index)
        
        # Overall statistics
        overall_stats = {
            "scrap_sources_available": len(scrap_rate_cols),
            "total_records": len(overall_scrap_rate),
            "valid_records": overall_scrap_rate.notna().sum(),
            "overall_scrap_rate": {
                "mean": float(overall_scrap_rate.mean()),
                "std": float(overall_scrap_rate.std()),
                "min": float(overall_scrap_rate.min()),
                "max": float(overall_scrap_rate.max()),
                "q25": float(overall_scrap_rate.quantile(0.25)),
                "q75": float(overall_scrap_rate.quantile(0.75)),
                "median": float(overall_scrap_rate.median())
            },
            "scrap_rate_variation": {
                "mean": float(scrap_rate_variation.mean()),
                "std": float(scrap_rate_variation.std()),
                "min": float(scrap_rate_variation.min()),
                "max": float(scrap_rate_variation.max())
            }
        }
        
        # Quality assessment thresholds (manufacturing best practices)
        high_scrap_threshold = 0.10  # 10% scrap rate is concerning
        critical_scrap_threshold = 0.20  # 20% scrap rate is critical
        
        high_scrap_records = (overall_scrap_rate > high_scrap_threshold).sum()
        critical_scrap_records = (overall_scrap_rate > critical_scrap_threshold).sum()
        
        overall_stats["quality_assessment"] = {
            "high_scrap_records": int(high_scrap_records),
            "critical_scrap_records": int(critical_scrap_records),
            "high_scrap_percentage": float((high_scrap_records / len(overall_scrap_rate)) * 100),
            "critical_scrap_percentage": float((critical_scrap_records / len(overall_scrap_rate)) * 100)
        }
        
        # Individual scrap source statistics
        source_stats = {}
        for col in scrap_rate_cols:
            scrap_data = scrap_rate_df[col].dropna()
            if len(scrap_data) > 0:
                source_stats[col] = {
                    "mean": float(scrap_data.mean()),
                    "std": float(scrap_data.std()),
                    "min": float(scrap_data.min()),
                    "max": float(scrap_data.max()),
                    "median": float(scrap_data.median()),
                    "valid_count": int(len(scrap_data)),
                    "high_scrap_count": int((scrap_data > high_scrap_threshold).sum()),
                    "critical_scrap_count": int((scrap_data > critical_scrap_threshold).sum())
                }
        
        # Material-specific analysis (if Material column available)
        material_analysis = {}
        if 'Material' in data.columns:
            for col in scrap_rate_cols:
                if col in data.columns:
                    material_scrap = data.groupby('Material')[col].agg(['mean', 'std', 'count']).round(6)
                    material_analysis[col] = {
                        material: {
                            'mean_scrap_rate': float(row['mean']) if not pd.isna(row['mean']) else None,
                            'std_scrap_rate': float(row['std']) if not pd.isna(row['std']) else None,
                            'record_count': int(row['count'])
                        }
                        for material, row in material_scrap.iterrows()
                        if row['count'] > 0
                    }
        
        # Update context data with calculated metrics
        if len(scrap_rate_cols) > 0:
            ctx.deps.data = ctx.deps.data.copy()
            ctx.deps.data['overall_scrap_rate'] = overall_scrap_rate
            ctx.deps.data['scrap_rate_variation'] = scrap_rate_variation
        
        return {
            "calculation_successful": True,
            "overall_statistics": overall_stats,
            "source_statistics": source_stats,
            "material_analysis": material_analysis,
            "calculated_variables": ["overall_scrap_rate", "scrap_rate_variation"],
            "quality_thresholds": {
                "high_scrap_threshold": high_scrap_threshold,
                "critical_scrap_threshold": critical_scrap_threshold
            },
            "calculation_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error calculating scrap rate metrics: {e}")
        return {"error": f"Scrap rate calculation failed: {str(e)}"}

# Stratified analysis tools
def analyze_stratified_correlations(
    ctx: RunContext,
    target_variable_1: str = 'sm_scrap_pct',
    target_variable_2: str = 'fm_reject_pct',
    stratification_types: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Perform stratified correlation analysis across different operational conditions.
    
    Args:
        ctx: Run context with data dependencies
        target_variable_1: First variable for correlation (default: sm_scrap_pct)
        target_variable_2: Second variable for correlation (default: fm_reject_pct)
        stratification_types: Types of stratification to perform
        
    Returns:
        Dictionary containing stratified correlation results
    """
    try:
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for stratified correlation analysis"}
        
        # Filter to matched flows if available
        if 'has_fm_match' in data.columns:
            matched_df = data[data['has_fm_match']].copy()
        else:
            matched_df = data.copy()
        
        if matched_df.empty:
            return {"error": "No data available after filtering"}
        
        # Check if target variables exist
        if target_variable_1 not in matched_df.columns or target_variable_2 not in matched_df.columns:
            return {"error": f"Target variables {target_variable_1} or {target_variable_2} not found"}
        
        # Default stratification types
        if stratification_types is None:
            stratification_types = ['time_lag', 'production_shift', 'speed_deviation', 'efficiency', 'machine_type']
        
        results = {
            "analysis_type": "stratified_correlations",
            "target_variables": [target_variable_1, target_variable_2],
            "total_records": len(matched_df),
            "stratification_results": {},
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        # Time lag stratification
        if 'time_lag' in stratification_types and 'sm_to_fm_gap_minutes' in matched_df.columns:
            # Convert minutes to hours for binning
            matched_df['time_lag_hours'] = matched_df['sm_to_fm_gap_minutes'] / 60
            
            # Create time lag bins
            matched_df['lag_bin'] = pd.cut(
                matched_df['time_lag_hours'],
                bins=[0, 12, 24, 48, np.inf],
                labels=['<12h', '12-24h', '24-48h', '>48h']
            )
            
            lag_correlations = matched_df.groupby('lag_bin').apply(
                lambda x: x[[target_variable_1, target_variable_2]].corr().iloc[0, 1] 
                if len(x) > 10 else np.nan
            )
            
            lag_counts = matched_df.groupby('lag_bin').size()
            
            results["stratification_results"]["time_lag"] = {
                "correlations": {str(k): round(v, 6) if pd.notna(v) else None 
                               for k, v in lag_correlations.items()},
                "sample_sizes": {str(k): int(v) for k, v in lag_counts.items()},
                "description": "Correlation by SM-to-FM processing time lag"
            }
        
        # Production shift stratification
        if 'production_shift' in stratification_types and 'production_shift' in matched_df.columns:
            shift_correlations = matched_df.groupby('production_shift').apply(
                lambda x: x[[target_variable_1, target_variable_2]].corr().iloc[0, 1] 
                if len(x) > 10 else np.nan
            )
            
            shift_counts = matched_df.groupby('production_shift').size()
            
            results["stratification_results"]["production_shift"] = {
                "correlations": {str(k): round(v, 6) if pd.notna(v) else None 
                               for k, v in shift_correlations.items()},
                "sample_sizes": {str(k): int(v) for k, v in shift_counts.items()},
                "description": "Correlation by production shift"
            }
        
        # Speed deviation stratification
        if 'speed_deviation' in stratification_types and 'speed_deviation_pct' in matched_df.columns:
            matched_df['speed_deviation_bin'] = pd.cut(
                matched_df['speed_deviation_pct'],
                bins=[-np.inf, -5, 0, 5, np.inf],
                labels=['<-5%', '-5% to 0%', '0% to 5%', '>5%']
            )
            
            speed_dev_correlations = matched_df.groupby('speed_deviation_bin').apply(
                lambda x: x[[target_variable_1, target_variable_2]].corr().iloc[0, 1] 
                if len(x) > 10 else np.nan
            )
            
            speed_dev_counts = matched_df.groupby('speed_deviation_bin').size()
            
            results["stratification_results"]["speed_deviation"] = {
                "correlations": {str(k): round(v, 6) if pd.notna(v) else None 
                               for k, v in speed_dev_correlations.items()},
                "sample_sizes": {str(k): int(v) for k, v in speed_dev_counts.items()},
                "description": "Correlation by speed deviation from design"
            }
        
        # Production efficiency stratification
        if 'efficiency' in stratification_types and 'production_efficiency_pct' in matched_df.columns:
            matched_df['efficiency_bin'] = pd.cut(
                matched_df['production_efficiency_pct'],
                bins=[0, 85, 95, 100],
                labels=['Low (<85%)', 'Medium (85-95%)', 'High (>95%)']
            )
            
            efficiency_correlations = matched_df.groupby('efficiency_bin').apply(
                lambda x: x[[target_variable_1, target_variable_2]].corr().iloc[0, 1] 
                if len(x) > 10 else np.nan
            )
            
            efficiency_counts = matched_df.groupby('efficiency_bin').size()
            
            results["stratification_results"]["efficiency"] = {
                "correlations": {str(k): round(v, 6) if pd.notna(v) else None 
                               for k, v in efficiency_correlations.items()},
                "sample_sizes": {str(k): int(v) for k, v in efficiency_counts.items()},
                "description": "Correlation by production efficiency level"
            }
        
        # Machine type stratification
        if 'machine_type' in stratification_types and 'fm_machine_type' in matched_df.columns:
            machine_correlations = matched_df.groupby('fm_machine_type').apply(
                lambda x: x[[target_variable_1, target_variable_2]].corr().iloc[0, 1] 
                if len(x) > 10 else np.nan
            )
            
            machine_counts = matched_df.groupby('fm_machine_type').size()
            
            results["stratification_results"]["machine_type"] = {
                "correlations": {str(k): round(v, 6) if pd.notna(v) else None 
                               for k, v in machine_correlations.items()},
                "sample_sizes": {str(k): int(v) for k, v in machine_counts.items()},
                "description": "Correlation by finishing machine type"
            }
        
        return results
        
    except Exception as e:
        logger.error(f"Error in stratified correlation analysis: {e}")
        return {"error": f"Stratified correlation analysis failed: {str(e)}"}

def analyze_lag_stratified_correlations(
    ctx: RunContext,
    variable_1: str,
    variable_2: str,
    lag_column: str = 'sm_to_fm_gap_minutes',
    max_lag_hours: int = 72
) -> Dict[str, Any]:
    """
    Analyze time lag stratified correlations with temporal validation.
    
    Args:
        ctx: Run context with data dependencies
        variable_1: First variable name
        variable_2: Second variable name  
        lag_column: Column containing lag time in minutes
        max_lag_hours: Maximum lag time to consider (hours)
        
    Returns:
        Dictionary containing lag stratification analysis
    """
    try:
        data = ctx.deps.data
        
        if data.empty:
            return {"error": "No data available for lag stratified analysis"}
        
        # Check if required columns exist
        required_cols = [variable_1, variable_2, lag_column]
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            return {"error": f"Missing required columns: {missing_cols}"}
        
        # Filter data
        max_lag_minutes = max_lag_hours * 60
        filtered_data = data[
            (data[lag_column] >= 60) &  # Minimum 1 hour lag
            (data[lag_column] <= max_lag_minutes) &  # Maximum lag limit
            (data[variable_1].notna()) &
            (data[variable_2].notna())
        ].copy()
        
        if len(filtered_data) < 50:
            return {"error": f"Insufficient data for lag analysis (only {len(filtered_data)} valid records)"}
        
        # Convert to hours
        filtered_data['lag_hours'] = filtered_data[lag_column] / 60
        
        # Create detailed lag bins
        lag_bins = [0, 6, 12, 24, 48, 72]
        lag_labels = ['1-6h', '6-12h', '12-24h', '24-48h', '48-72h']
        
        filtered_data['detailed_lag_bin'] = pd.cut(
            filtered_data['lag_hours'],
            bins=lag_bins,
            labels=lag_labels
        )
        
        results = {
            "analysis_type": "lag_stratified_correlations",
            "variables": [variable_1, variable_2],
            "lag_column": lag_column,
            "total_valid_records": len(filtered_data),
            "lag_range_hours": [1, max_lag_hours],
            "analysis_timestamp": datetime.now().isoformat(),
            "lag_correlations": {}
        }
        
        # Calculate correlations for each lag bin
        for lag_bin in lag_labels:
            bin_data = filtered_data[filtered_data['detailed_lag_bin'] == lag_bin]
            
            if len(bin_data) >= 30:  # Minimum sample size for meaningful correlation
                correlation = bin_data[[variable_1, variable_2]].corr().iloc[0, 1]
                
                # Calculate additional statistics
                mean_lag = bin_data['lag_hours'].mean()
                std_lag = bin_data['lag_hours'].std()
                
                results["lag_correlations"][lag_bin] = {
                    "correlation_coefficient": round(correlation, 6),
                    "sample_size": len(bin_data),
                    "mean_lag_hours": round(mean_lag, 2),
                    "std_lag_hours": round(std_lag, 2),
                    "lag_range": [bin_data['lag_hours'].min(), bin_data['lag_hours'].max()]
                }
            else:
                results["lag_correlations"][lag_bin] = {
                    "error": f"Insufficient data (n={len(bin_data)})",
                    "sample_size": len(bin_data)
                }
        
        # Find optimal lag range
        valid_correlations = {k: v for k, v in results["lag_correlations"].items() 
                            if "error" not in v}
        
        if valid_correlations:
            best_lag_bin = max(valid_correlations.keys(), 
                             key=lambda x: abs(valid_correlations[x]["correlation_coefficient"]))
            
            results["optimal_lag_analysis"] = {
                "strongest_correlation_bin": best_lag_bin,
                "correlation_coefficient": valid_correlations[best_lag_bin]["correlation_coefficient"],
                "manufacturing_implication": f"Strongest correlation observed at {best_lag_bin} processing lag"
            }
        
        return results
        
    except Exception as e:
        logger.error(f"Error in lag stratified correlation analysis: {e}")
        return {"error": f"Lag stratified analysis failed: {str(e)}"}

# Tool registration - these will be used by the agent
CORRELATION_TOOLS = [
    calculate_correlation_matrix,
    find_significant_correlations, 
    analyze_lag_correlations,
    analyze_process_correlations,
    calculate_data_quality_score,
    filter_data_by_time_range,
    calculate_thickness_metrics,
    calculate_scrap_rate_metrics,
    get_variable_summary,
    # Enhanced unified table tools
    analyze_basic_correlations,
    calculate_unified_correlation_matrix,
    # Stratified analysis tools
    analyze_stratified_correlations,
    analyze_lag_stratified_correlations
]