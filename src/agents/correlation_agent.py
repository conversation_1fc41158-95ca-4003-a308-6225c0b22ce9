"""
Manufacturing Multi-Method Correlation Analysis Agent (Phase 2.0)

Advanced PydanticAI agent for comprehensive multi-method correlation analysis of fiber cement 
manufacturing data. Supports <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlation methods with intelligent
method selection, convergence analysis, bootstrap robustness testing, and complete visualization suite.

Features:
    - Multi-method correlation analysis (<PERSON>, <PERSON><PERSON>, <PERSON>)
    - Intelligent method selection based on data distribution characteristics
    - Method convergence analysis for robustness validation
    - Bootstrap stability testing for critical manufacturing decisions
    - Manufacturing domain expertise with thickness sensor processing
    - Multiple LLM provider support (Anthrop<PERSON> Claude, Google Vertex AI)
    - High-precision statistical analysis (8-decimal internal, 6-decimal display)
    - Complete visualization suite with 5 plot types (heatmaps, dashboards, comparisons)
    - Interactive and static plot generation (HTML/PNG formats)

Manufacturing-Specific Capabilities:
    - Automatic thickness calculation from 10-sensor arrays
    - Comprehensive scrap rate calculation with off roller factor integration  
    - Lag correlation analysis for process delays and propagation
    - Quality metrics correlation with acceptance/rejection patterns
    - Material-specific scrap rate analysis and process optimization
    - Stoppage impact analysis on subsequent production quality
    - Shift pattern analysis and temporal correlation assessment
    - Manufacturing-focused visualization styling and interpretation
    - Process optimization dashboards and stakeholder communication plots

Usage:
    ```python
    from src.agents.correlation_agent import create_correlation_agent
    
    agent = await create_correlation_agent()
    result = await agent.run(
        "Comp<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlations for speed vs thickness",
        deps={'unified_data': manufacturing_data, 'datasets': datasets}
    )
    ```

Dependencies:
    - Requires ANTHROPIC_API_KEY or Vertex AI credentials
    - Manufacturing data in CSV format with timestamp alignment
    - Environment variables configured per CLAUDE.md guidelines
"""

import os
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Any, Tuple
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from datetime import datetime
import logging
from dotenv import load_dotenv

# Import our modules
from .prompts import CORRELATION_SYSTEM_PROMPT, get_correlation_prompt
from ..utils.api_wrapper import get_rate_limited_agent
from .tools import (
    calculate_correlation_matrix,
    find_significant_correlations,
    analyze_lag_correlations,
    analyze_process_correlations,
    calculate_data_quality_score,
    filter_data_by_time_range,
    calculate_thickness_metrics,
    calculate_scrap_rate_metrics,
    get_variable_summary,
    # Enhanced unified table tools
    analyze_basic_correlations,
    calculate_unified_correlation_matrix,
    # Stratified analysis tools
    analyze_stratified_correlations,
    analyze_lag_stratified_correlations
)
from .multi_tools import (
    calculate_multi_method_correlations_tool,
    analyze_method_convergence_tool,
    recommend_correlation_method_tool,
    calculate_robustness_metrics_tool
)
from .visualization_tools import (
    create_multi_method_heatmaps_tool,
    plot_method_convergence_tool,
    create_multi_method_dashboard_tool,
    plot_method_comparison_matrix_tool,
    generate_all_visualizations_tool
)

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Data models for structured outputs
class CorrelationInsight(BaseModel):
    """Individual correlation insight"""
    insight_type: str = Field(description="Type of insight (statistical, process, quality, etc.)")
    description: str = Field(description="Human-readable insight description")
    supporting_evidence: Dict[str, Any] = Field(description="Statistical evidence supporting the insight")
    confidence_level: str = Field(description="Confidence level (high, medium, low)")
    actionable_recommendation: Optional[str] = Field(description="Specific recommendation for action")

class CorrelationAnalysis(BaseModel):
    """Complete correlation analysis results"""
    dataset_summary: Dict[str, Any] = Field(description="Summary of dataset characteristics")
    significant_correlations: List[Dict[str, Any]] = Field(description="List of significant correlations found")
    correlation_matrix: Optional[Dict[str, Dict[str, float]]] = Field(description="Full correlation matrix")
    insights: List[CorrelationInsight] = Field(description="Key insights from correlation analysis")
    recommendations: List[str] = Field(description="Actionable recommendations for process improvement")
    data_quality_score: float = Field(description="Overall data quality score (0-1)")
    analysis_metadata: Dict[str, Any] = Field(description="Analysis parameters and timestamps")

class ManufacturingDataDependencies(BaseModel):
    """Dependencies for the manufacturing correlation agent"""
    model_config = {"arbitrary_types_allowed": True}
    
    data: pd.DataFrame = Field(description="Manufacturing data for analysis")
    time_column: Optional[str] = Field(default='timestamp', description="Name of the time column")
    target_variables: Optional[List[str]] = Field(default=None, description="Target variables for focused analysis")
    significance_threshold: float = Field(default=0.05, description="Statistical significance threshold")
    min_correlation: float = Field(default=0.01, description="Minimum correlation threshold for reporting")
    analysis_type: str = Field(default='general', description="Type of analysis to perform")

# Model selection based on environment
def get_model_config():
    """Get model configuration based on environment variables"""
    provider = os.getenv('LLM_PROVIDER', 'ANTHROPIC').upper()
    
    if provider == 'ANTHROPIC':
        api_key = os.getenv('ANTHROPIC_API_KEY')
        if not api_key:
            raise ValueError("ANTHROPIC_API_KEY not found in environment. Please set this variable to use Anthropic models.")
        
        model = os.getenv('ANTHROPIC_MODEL')
        if not model:
            raise ValueError("ANTHROPIC_MODEL not found in environment. Please set this variable (e.g., 'claude-3-5-sonnet-20241022').")
        
        return model
    
    elif provider == 'VERTEX_AI':
        # Vertex AI configuration
        try:
            from pydantic_ai.models.vertexai import VertexAIModel
            project = os.getenv('VERTEX_AI_PROJECT')
            location = os.getenv('VERTEX_AI_LOCATION', 'us-central1')
            
            if not project:
                raise ValueError("VERTEX_AI_PROJECT not found in environment. Please set this variable to use Vertex AI models.")
            
            model = os.getenv('GEMINI_MODEL')
            if not model:
                raise ValueError("GEMINI_MODEL not found in environment. Please set this variable (e.g., 'gemini-1.5-pro').")
            
            return VertexAIModel(model, project=project, location=location)
        except ImportError:
            raise ValueError("Vertex AI dependencies not available. Please install required packages: pip install pydantic-ai[vertexai]")
    
    else:
        raise ValueError(f"Unknown LLM_PROVIDER '{provider}'. Supported providers are: ANTHROPIC, VERTEX_AI")

# Create the correlation analysis agent
correlation_agent = Agent(
    model=get_model_config(),
    system_prompt=CORRELATION_SYSTEM_PROMPT,
    output_type=CorrelationAnalysis,
    deps_type=ManufacturingDataDependencies,
)

# Register tools with the agent
@correlation_agent.tool
def calculate_correlation_matrix_tool(
    ctx: RunContext[ManufacturingDataDependencies], 
    variables: Optional[List[str]] = None,
    method: str = 'pearson'
) -> Dict[str, Any]:
    """Calculate correlation matrix for manufacturing variables."""
    return calculate_correlation_matrix(ctx, variables, method)

@correlation_agent.tool
def find_significant_correlations_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    min_correlation: Optional[float] = None,
    method: str = 'pearson'
) -> List[Dict[str, Any]]:
    """Find statistically significant correlations above threshold."""
    threshold = min_correlation or ctx.deps.min_correlation
    return find_significant_correlations(ctx, threshold, method)

@correlation_agent.tool
def analyze_lag_correlations_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    variable_1: str,
    variable_2: str,
    max_lag: int = 60
) -> Dict[str, Any]:
    """Analyze time-lagged correlations between two variables."""
    return analyze_lag_correlations(ctx, variable_1, variable_2, max_lag)

@correlation_agent.tool
def analyze_process_correlations_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variable: str,
    process_variables: Optional[List[str]] = None
) -> Dict[str, Dict[str, Any]]:
    """Analyze correlations between target and process variables."""
    return analyze_process_correlations(ctx, target_variable, process_variables)

@correlation_agent.tool
def calculate_data_quality_score_tool(
    ctx: RunContext[ManufacturingDataDependencies]
) -> Dict[str, Any]:
    """Calculate overall data quality score."""
    return calculate_data_quality_score(ctx)

@correlation_agent.tool
def filter_data_by_time_range_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    start_time: str,
    end_time: str
) -> Dict[str, Any]:
    """Filter data by time range."""
    return filter_data_by_time_range(ctx, start_time, end_time, ctx.deps.time_column)

@correlation_agent.tool
def calculate_thickness_metrics_tool(
    ctx: RunContext[ManufacturingDataDependencies]
) -> Dict[str, Any]:
    """Calculate thickness metrics from sensor data."""
    return calculate_thickness_metrics(ctx)

@correlation_agent.tool
def calculate_scrap_rate_metrics_tool(
    ctx: RunContext[ManufacturingDataDependencies]
) -> Dict[str, Any]:
    """Calculate comprehensive scrap rate metrics from manufacturing data with off roller factor integration."""
    return calculate_scrap_rate_metrics(ctx)

@correlation_agent.tool
def get_variable_summary_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    variable_name: Optional[str] = None
) -> Dict[str, Any]:
    """Get summary statistics for variables."""
    return get_variable_summary(ctx, variable_name)

# Enhanced unified table analysis tools
@correlation_agent.tool
def analyze_basic_correlations_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    filter_matched_flows: bool = True
) -> Dict[str, Any]:
    """Perform basic correlation analysis for the unified manufacturing table."""
    return analyze_basic_correlations(ctx, filter_matched_flows)

@correlation_agent.tool
def calculate_unified_correlation_matrix_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    feature_groups: Optional[List[str]] = None,
    method: str = 'pearson',
    min_periods: int = 30
) -> Dict[str, Any]:
    """Calculate correlation matrix optimized for the 83-column unified table."""
    return calculate_unified_correlation_matrix(ctx, feature_groups, method, min_periods)

# Stratified analysis tools
@correlation_agent.tool
def analyze_stratified_correlations_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variable_1: str = 'sm_scrap_pct',
    target_variable_2: str = 'fm_reject_pct',
    stratification_types: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Perform stratified correlation analysis across different operational conditions."""
    return analyze_stratified_correlations(ctx, target_variable_1, target_variable_2, stratification_types)

@correlation_agent.tool
def analyze_lag_stratified_correlations_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    variable_1: str,
    variable_2: str,
    lag_column: str = 'sm_to_fm_gap_minutes',
    max_lag_hours: int = 72
) -> Dict[str, Any]:
    """Analyze time lag stratified correlations with temporal validation."""
    return analyze_lag_stratified_correlations(ctx, variable_1, variable_2, lag_column, max_lag_hours)

# Multi-method correlation analysis tools
@correlation_agent.tool
def calculate_multi_method_correlations_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    variables: Optional[List[str]] = None,
    min_periods: int = 30,
    include_robustness: bool = False
) -> Dict[str, Any]:
    """Calculate correlations using all three methods (Pearson, Spearman, Kendall) with comparative analysis."""
    return calculate_multi_method_correlations_tool(ctx, variables, min_periods, include_robustness)

@correlation_agent.tool
def analyze_method_convergence_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    correlation_results: Optional[Dict[str, Any]] = None,
    convergence_threshold: float = 0.8
) -> Dict[str, Any]:
    """Analyze convergence and stability across correlation methods."""
    return analyze_method_convergence_tool(ctx, correlation_results, convergence_threshold)

@correlation_agent.tool
def recommend_correlation_method_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    variable_1: str,
    variable_2: str,
    return_assessment: bool = True
) -> Dict[str, Any]:
    """Recommend the most appropriate correlation method for a specific variable pair."""
    return recommend_correlation_method_tool(ctx, variable_1, variable_2, return_assessment)

@correlation_agent.tool
def calculate_robustness_metrics_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    correlation_results: Optional[Dict[str, Any]] = None,
    bootstrap_samples: int = 100,
    variable_pairs: Optional[List[Tuple[str, str]]] = None
) -> Dict[str, Any]:
    """Calculate robustness metrics for correlation analysis through bootstrap sampling."""
    return calculate_robustness_metrics_tool(ctx, correlation_results, bootstrap_samples, variable_pairs)

# Visualization tools
@correlation_agent.tool
def create_multi_method_heatmaps_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    correlation_results: Dict[str, Any],
    title: str = "Multi-Method Correlation Analysis",
    save_path: Optional[str] = None,
    figsize: Optional[Tuple[int, int]] = None,
    show_convergence: bool = True
) -> Dict[str, Any]:
    """Create side-by-side correlation heatmaps for all three methods (Pearson, Spearman, Kendall)."""
    return create_multi_method_heatmaps_tool(ctx, correlation_results, title, save_path, figsize, show_convergence)

@correlation_agent.tool
def plot_method_convergence_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    correlation_results: Dict[str, Any],
    title: str = "Method Convergence Analysis",
    save_path: Optional[str] = None
) -> Dict[str, Any]:
    """Create interactive visualization of method convergence patterns."""
    return plot_method_convergence_tool(ctx, correlation_results, title, save_path)

@correlation_agent.tool
def create_multi_method_dashboard_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    correlation_results: Dict[str, Any],
    title: str = "Multi-Method Correlation Dashboard",
    save_path: Optional[str] = None
) -> Dict[str, Any]:
    """Create comprehensive multi-method correlation dashboard with 9 analysis panels."""
    return create_multi_method_dashboard_tool(ctx, correlation_results, title, save_path)

@correlation_agent.tool
def plot_method_comparison_matrix_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    correlation_results: Dict[str, Any],
    method_pair: Tuple[str, str] = ('pearson', 'spearman'),
    title: Optional[str] = None,
    save_path: Optional[str] = None
) -> Dict[str, Any]:
    """Create detailed comparison between two specific correlation methods."""
    return plot_method_comparison_matrix_tool(ctx, correlation_results, method_pair, title, save_path)

@correlation_agent.tool
def generate_all_visualizations_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    correlation_results: Dict[str, Any],
    output_dir: str = "correlation_visualizations",
    prefix: str = ""
) -> Dict[str, Any]:
    """Generate complete visualization suite for multi-method correlation analysis."""
    return generate_all_visualizations_tool(ctx, correlation_results, output_dir, prefix)

# PatchTST Forecasting tools
@correlation_agent.tool
def forecast_manufacturing_parameter_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variable: str,
    forecast_horizon: int,
    lookback_window: int = 240,
    include_confidence_intervals: bool = True
) -> Dict[str, Any]:
    """Forecast future values of manufacturing parameters using PatchTST."""
    from .forecasting_tools import forecast_manufacturing_parameter_tool
    return forecast_manufacturing_parameter_tool(ctx, target_variable, forecast_horizon, lookback_window, include_confidence_intervals)

@correlation_agent.tool
def multi_horizon_forecast_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variable: str,
    forecast_horizons: List[int] = [15, 60, 240],
    include_comparison: bool = True
) -> Dict[str, Any]:
    """Generate multi-horizon forecasts for manufacturing parameter analysis."""
    from .forecasting_tools import multi_horizon_forecast_tool
    return multi_horizon_forecast_tool(ctx, target_variable, forecast_horizons, include_comparison)

@correlation_agent.tool
def compare_forecast_scenarios_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variables: List[str],
    forecast_horizon: int = 60,
    scenario_analysis: bool = True
) -> Dict[str, Any]:
    """Compare forecasts across multiple manufacturing parameters for scenario analysis."""
    from .forecasting_tools import compare_forecast_scenarios_tool
    return compare_forecast_scenarios_tool(ctx, target_variables, forecast_horizon, scenario_analysis)

@correlation_agent.tool
def train_forecasting_models_tool_wrapper(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variables: Optional[List[str]] = None,
    retrain_existing: bool = False
) -> Dict[str, Any]:
    """Train PatchTST models for manufacturing forecasting."""
    from .forecasting_tools import train_forecasting_models_tool
    return train_forecasting_models_tool(ctx, target_variables, retrain_existing)

# Convenience function for running correlation analysis
async def analyze_manufacturing_correlations(
    data: pd.DataFrame,
    query: str = "Analyze the correlations in this manufacturing data",
    time_column: str = 'timestamp',
    target_variables: Optional[List[str]] = None,
    significance_threshold: float = 0.05,
    min_correlation: float = 0.01,  # Lowered to capture small correlations
    analysis_type: str = 'general'
) -> CorrelationAnalysis:
    """
    Perform comprehensive correlation analysis on manufacturing data.
    
    Args:
        data: Manufacturing data DataFrame
        query: Analysis query or question
        time_column: Name of timestamp column
        target_variables: Specific variables to focus on
        significance_threshold: Statistical significance threshold
        min_correlation: Minimum correlation threshold
        analysis_type: Type of analysis (general, lag, optimization, quality, etc.)
        
    Returns:
        CorrelationAnalysis with complete results and insights
    """
    
    # Validate inputs
    if data.empty:
        raise ValueError("Data cannot be empty")
    
    # Create dependencies
    deps = ManufacturingDataDependencies(
        data=data,
        time_column=time_column,
        target_variables=target_variables,
        significance_threshold=significance_threshold,
        min_correlation=min_correlation,
        analysis_type=analysis_type
    )
    
    # Auto-detect analysis type from query content
    if analysis_type == 'general':
        query_lower = query.lower()
        
        # Multi-method analysis detection
        multi_method_keywords = ['all three methods', 'three methods', 'pearson spearman kendall', 'compare methods', 'method comparison', 'multi-method', 'all correlation methods']
        if any(keyword in query_lower for keyword in multi_method_keywords):
            analysis_type = 'multi_method'
            logger.info(f"Auto-detected multi-method analysis request from query: {query[:100]}...")
        
        # Unified table analysis detection
        elif any(keyword in query_lower for keyword in ['unified table', '83 column', 'complete flows', 'sm-fm flows', 'matched flows']):
            analysis_type = 'unified_table'
            logger.info(f"Auto-detected unified table analysis request from query: {query[:100]}...")
        
        # Stratified analysis detection
        elif any(keyword in query_lower for keyword in ['stratified', 'by shift', 'by lag', 'by machine type', 'efficiency levels', 'time lag bins']):
            analysis_type = 'stratified'
            logger.info(f"Auto-detected stratified analysis request from query: {query[:100]}...")
        
        # Pattern identification detection
        elif any(keyword in query_lower for keyword in ['quality patterns', 'problem conditions', 'leading indicators', 'operational patterns']):
            analysis_type = 'pattern_identification'
            logger.info(f"Auto-detected pattern identification request from query: {query[:100]}...")
        
        # ML prediction detection
        elif any(keyword in query_lower for keyword in ['machine learning', 'predict', 'random forest', 'feature importance', 'shap']):
            analysis_type = 'ml_prediction'
            logger.info(f"Auto-detected ML prediction request from query: {query[:100]}...")
        
        # Time series analysis detection
        elif any(keyword in query_lower for keyword in ['time series', 'temporal patterns', 'rolling correlation', 'over time', 'hourly']):
            analysis_type = 'time_series'
            logger.info(f"Auto-detected time series analysis request from query: {query[:100]}...")
    
    # Get appropriate system prompt for analysis type
    if analysis_type != 'general':
        # Update agent's system prompt for specialized analysis
        specialized_prompt = get_correlation_prompt(analysis_type)
        # Note: In production, you might want to create specialized agents
        # For now, we'll use the general agent with contextual query
        
        contextual_query = f"""
        {specialized_prompt}
        
        User Query: {query}
        
        Dataset Information:
        - Shape: {data.shape}
        - Columns: {list(data.columns)}
        - Numeric columns: {list(data.select_dtypes(include=[np.number]).columns)}
        - Time column: {time_column}
        - Analysis type: {analysis_type}
        
        Please provide a comprehensive correlation analysis focused on the specified analysis type.
        """
    else:
        contextual_query = f"""
        Analyze the correlations in this manufacturing dataset.
        
        Dataset Information:
        - Shape: {data.shape}
        - Columns: {list(data.columns)}
        - Numeric columns: {list(data.select_dtypes(include=[np.number]).columns)}
        - Time column: {time_column}
        
        User Query: {query}
        
        Please provide a comprehensive correlation analysis with manufacturing insights.
        """
    
    # Run the agent with rate limiting
    try:
        # Get rate-limited agent wrapper
        rate_limited_agent = get_rate_limited_agent(
            agent=correlation_agent,
            agent_name="correlation_agent",
            requests_per_minute=int(os.getenv('ANTHROPIC_REQUESTS_PER_MINUTE', 50)),
            tokens_per_minute=int(os.getenv('ANTHROPIC_TOKENS_PER_MINUTE', 40000)),
            enable_fallbacks=True
        )
        
        # Run with rate limiting and error handling
        result = await rate_limited_agent.run(contextual_query, deps=deps)
        return result
        
    except Exception as e:
        logger.error(f"Error running correlation analysis: {e}")
        # Return a basic analysis with error information
        return CorrelationAnalysis(
            dataset_summary={
                "shape": list(data.shape),
                "columns": list(data.columns),
                "error": str(e)
            },
            significant_correlations=[],
            correlation_matrix=None,
            insights=[
                CorrelationInsight(
                    insight_type="error",
                    description=f"Analysis failed: {str(e)}",
                    supporting_evidence={"error_details": str(e)},
                    confidence_level="low",
                    actionable_recommendation="Check data format and try again"
                )
            ],
            recommendations=["Verify data quality and format", "Check for missing values"],
            data_quality_score=0.0,
            analysis_metadata={
                "analysis_timestamp": datetime.now().isoformat(),
                "analysis_type": analysis_type,
                "error": str(e)
            }
        )

# Factory function for creating the main correlation agent
def create_correlation_agent():
    """Create and return the main correlation agent instance"""
    return correlation_agent

# Function to get rate limit status
def get_correlation_agent_status():
    """Get rate limit and performance status for correlation agent"""
    from ..utils.api_wrapper import get_all_agent_status
    return get_all_agent_status().get("correlation_agent", {
        "status": "not_initialized",
        "message": "Agent has not been used yet"
    })

# Factory function for creating specialized agents
def create_specialized_correlation_agent(analysis_type: str) -> Agent:
    """
    Create a specialized correlation agent for specific analysis types.
    
    Args:
        analysis_type: Type of specialized analysis
        
    Returns:
        Configured Agent for the specific analysis type
    """
    specialized_prompt = get_correlation_prompt(analysis_type)
    
    return Agent(
        model=get_model_config(),
        system_prompt=specialized_prompt,
        output_type=CorrelationAnalysis,
        deps_type=ManufacturingDataDependencies,
        tools=[
            calculate_correlation_matrix_tool,
            find_significant_correlations_tool,
            analyze_lag_correlations_tool,
            analyze_process_correlations_tool,
            calculate_data_quality_score_tool,
            filter_data_by_time_range_tool,
            calculate_thickness_metrics_tool,
            get_variable_summary_tool,
            # Enhanced unified table tools
            analyze_basic_correlations_tool,
            calculate_unified_correlation_matrix_tool,
            # Stratified analysis tools
            analyze_stratified_correlations_tool,
            analyze_lag_stratified_correlations_tool,
            # Multi-method tools
            calculate_multi_method_correlations_tool_wrapper,
            analyze_method_convergence_tool_wrapper,
            recommend_correlation_method_tool_wrapper,
            calculate_robustness_metrics_tool_wrapper
        ]
    )

# Example usage function
async def example_usage():
    """Example usage of the correlation analysis agent"""
    
    # Create sample manufacturing data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate correlated manufacturing data
    timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='1min')
    
    # Base variables
    speed = np.random.normal(150, 10, n_samples)
    temperature = 80 + 0.1 * speed + np.random.normal(0, 2, n_samples)
    
    # Create correlated variables with manufacturing relationships
    thickness = 12.5 + 0.01 * speed + 0.005 * temperature + np.random.normal(0, 0.2, n_samples)
    pressure = 50 + 0.1 * speed + 0.05 * temperature + np.random.normal(0, 2, n_samples)
    
    # Quality score inversely related to process variation
    quality_score = 100 - 0.1 * np.abs(speed - 150) - 0.2 * np.abs(temperature - 80) + np.random.normal(0, 1, n_samples)
    
    # Create DataFrame
    data = pd.DataFrame({
        'timestamp': timestamps,
        'speed': speed,
        'temperature': temperature,
        'thickness': thickness,
        'pressure': pressure,
        'quality_score': quality_score
    })
    
    print("Running correlation analysis example...")
    print(f"Dataset shape: {data.shape}")
    
    # Run correlation analysis
    try:
        results = await analyze_manufacturing_correlations(
            data=data,
            query="What are the key correlations affecting quality in this manufacturing process?",
            time_column='timestamp',
            target_variables=['quality_score'],
            analysis_type='quality'
        )
        
        print("\n=== CORRELATION ANALYSIS RESULTS ===")
        print(f"Data Quality Score: {results.data_quality_score:.3f}")
        
        print(f"\nSignificant Correlations Found: {len(results.significant_correlations)}")
        for i, corr in enumerate(results.significant_correlations[:5], 1):
            print(f"{i}. {corr.get('variable_1', 'Unknown')} ↔ {corr.get('variable_2', 'Unknown')}: "
                  f"{corr.get('correlation_coefficient', 0):.3f}")
        
        print("\nKey Insights:")
        for insight in results.insights:
            print(f"  • [{insight.confidence_level}] {insight.description}")
        
        print("\nRecommendations:")
        for rec in results.recommendations:
            print(f"  • {rec}")
            
    except Exception as e:
        print(f"Example failed: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())