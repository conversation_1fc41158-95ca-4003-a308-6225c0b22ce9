"""
Visualization Tools for Manufacturing Multi-Method Correlation Analysis Agent

PydanticAI tools that provide visualization capabilities to AI agents, supporting
multi-method correlation plot generation including heatmaps, convergence analysis,
dashboards, and method comparison matrices for manufacturing data analysis.
"""

# Configure matplotlib to use non-GUI backend to prevent macOS NSWindow thread errors
import matplotlib
matplotlib.use('Agg')

import os
from pathlib import Path
from typing import Dict, Optional, Any, Tuple
from pydantic_ai import RunContext
import logging
from datetime import datetime

# Import visualization modules
from ..visualization.multi_plots import (
    MultiMethodCorrelationPlotter,
)

# Set up logging
logger = logging.getLogger(__name__)


def create_multi_method_heatmaps_tool(
    ctx: RunContext,
    correlation_results: Dict[str, Any],
    title: str = "Multi-Method Correlation Analysis",
    save_path: Optional[str] = None,
    figsize: Optional[Tuple[int, int]] = None,
    show_convergence: bool = True
) -> Dict[str, Any]:
    """
    Create side-by-side correlation heatmaps for all three methods (<PERSON>, <PERSON>, <PERSON>).
    
    Args:
        ctx: Run context with data dependencies
        correlation_results: Multi-method correlation results from calculate_multi_method_correlations_tool
        title: Plot title
        save_path: Path to save the figure (None for temp file)
        figsize: Figure size tuple (width, height)
        show_convergence: Whether to include convergence scores
        
    Returns:
        Dictionary with file path, visualization metadata, and success status
    """
    try:
        # Initialize plotter
        plotter = MultiMethodCorrelationPlotter()
        
        # Generate save path if not provided
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"multi_method_heatmaps_{timestamp}.png"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
        
        # Create heatmaps
        fig = plotter.create_multi_method_heatmaps(
            correlation_results=correlation_results,
            title=title,
            save_path=save_path,
            figsize=figsize,
            show_convergence=show_convergence
        )
        
        # Close figure to free memory
        import matplotlib.pyplot as plt
        plt.close(fig)
        
        # Get convergence info if available
        convergence_info = {}
        if show_convergence and 'convergence_analysis' in correlation_results:
            convergence_analysis = correlation_results['convergence_analysis']
            convergence_info = {
                'overall_convergence_score': convergence_analysis.get('overall_convergence_score', 0),
                'high_convergence_pairs': convergence_analysis.get('convergence_distribution', {}).get('high_convergence_pairs', 0),
                'convergence_rate': convergence_analysis.get('convergence_distribution', {}).get('convergence_rate', 0)
            }
        
        # Prepare response
        response = {
            "success": True,
            "file_path": save_path,
            "visualization_type": "multi_method_heatmaps",
            "title": title,
            "methods_displayed": ["pearson", "spearman", "kendall"],
            "convergence_displayed": show_convergence,
            "convergence_info": convergence_info,
            "figure_size": figsize or (18, 6),
            "file_format": "PNG",
            "timestamp": datetime.now().isoformat(),
            "pairs_analyzed": len(correlation_results.get('multi_method_results', {})),
            "description": f"Side-by-side correlation heatmaps showing Pearson, Spearman, and Kendall correlations with {'convergence analysis' if show_convergence else 'standard formatting'}"
        }
        
        logger.info(f"Multi-method heatmaps created successfully: {save_path}")
        return response
        
    except Exception as e:
        logger.error(f"Error creating multi-method heatmaps: {e}")
        return {
            "success": False,
            "error": f"Failed to create multi-method heatmaps: {str(e)}",
            "visualization_type": "multi_method_heatmaps",
            "timestamp": datetime.now().isoformat()
        }


def plot_method_convergence_tool(
    ctx: RunContext,
    correlation_results: Dict[str, Any],
    title: str = "Method Convergence Analysis",
    save_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create interactive visualization of method convergence patterns.
    
    Args:
        ctx: Run context with data dependencies
        correlation_results: Multi-method correlation results
        title: Plot title
        save_path: Path to save the HTML file (None for temp file)
        
    Returns:
        Dictionary with file path, convergence insights, and metadata
    """
    try:
        # Initialize plotter
        plotter = MultiMethodCorrelationPlotter()
        
        # Generate save path if not provided
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"method_convergence_{timestamp}.html"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
        
        # Create convergence plot
        plotter.plot_method_convergence(
            correlation_results=correlation_results,
            title=title,
            save_path=save_path
        )
        
        # Extract convergence insights
        convergence_analysis = correlation_results.get('convergence_analysis', {})
        overall_convergence = convergence_analysis.get('overall_convergence_score', 0)
        cross_correlations = convergence_analysis.get('cross_method_correlations', {})
        convergence_dist = convergence_analysis.get('convergence_distribution', {})
        
        # Generate insights
        insights = []
        if overall_convergence > 0.8:
            insights.append("High method convergence indicates robust correlation patterns")
        elif overall_convergence > 0.5:
            insights.append("Moderate method convergence suggests some method-specific differences")
        else:
            insights.append("Low method convergence indicates significant method disagreement")
        
        # Method agreement insights
        if cross_correlations.get('pearson_spearman', 0) > 0.9:
            insights.append("Pearson and Spearman methods show very high agreement")
        if cross_correlations.get('spearman_kendall', 0) > 0.9:
            insights.append("Spearman and Kendall methods show very high agreement")
        if cross_correlations.get('pearson_kendall', 1) < 0.7:
            insights.append("Pearson and Kendall methods show notable disagreement")
        
        # Prepare response
        response = {
            "success": True,
            "file_path": save_path,
            "visualization_type": "method_convergence",
            "title": title,
            "file_format": "HTML",
            "interactive": True,
            "overall_convergence_score": overall_convergence,
            "convergence_classification": "high" if overall_convergence > 0.8 else "medium" if overall_convergence > 0.5 else "low",
            "convergence_insights": insights,
            "cross_method_correlations": cross_correlations,
            "convergence_distribution": convergence_dist,
            "panels_included": [
                "convergence_scores_by_pair",
                "method_correlation_comparison", 
                "convergence_distribution_histogram",
                "method_recommendation_pie_chart"
            ],
            "timestamp": datetime.now().isoformat(),
            "pairs_analyzed": len(correlation_results.get('multi_method_results', {})),
            "description": "Interactive 4-panel convergence analysis showing method agreement patterns and recommendation distributions"
        }
        
        logger.info(f"Method convergence plot created successfully: {save_path}")
        return response
        
    except Exception as e:
        logger.error(f"Error creating method convergence plot: {e}")
        return {
            "success": False,
            "error": f"Failed to create method convergence plot: {str(e)}",
            "visualization_type": "method_convergence", 
            "timestamp": datetime.now().isoformat()
        }


def create_multi_method_dashboard_tool(
    ctx: RunContext,
    correlation_results: Dict[str, Any],
    title: str = "Multi-Method Correlation Dashboard",
    save_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create comprehensive multi-method correlation dashboard with 9 analysis panels.
    
    Args:
        ctx: Run context with data dependencies
        correlation_results: Multi-method correlation results
        title: Dashboard title
        save_path: Path to save the HTML file (None for temp file)
        
    Returns:
        Dictionary with file path, dashboard summary, and metadata
    """
    try:
        # Initialize plotter
        plotter = MultiMethodCorrelationPlotter()
        
        # Generate save path if not provided
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"multi_method_dashboard_{timestamp}.html"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
        
        # Create dashboard
        plotter.create_multi_method_dashboard(
            correlation_results=correlation_results,
            title=title,
            save_path=save_path
        )
        
        # Extract summary statistics
        multi_results = correlation_results.get('multi_method_results', {})
        convergence_analysis = correlation_results.get('convergence_analysis', {})
        
        # Calculate summary metrics
        pearson_vals = [result['pearson_correlation'] for result in multi_results.values()]
        spearman_vals = [result['spearman_correlation'] for result in multi_results.values()]
        kendall_vals = [result['kendall_correlation'] for result in multi_results.values()]
        recommendations = [result['recommended_method'] for result in multi_results.values()]
        
        summary_stats = {
            "total_pairs": len(multi_results),
            "pearson_range": [round(min(pearson_vals), 3), round(max(pearson_vals), 3)] if pearson_vals else [0, 0],
            "spearman_range": [round(min(spearman_vals), 3), round(max(spearman_vals), 3)] if spearman_vals else [0, 0],
            "kendall_range": [round(min(kendall_vals), 3), round(max(kendall_vals), 3)] if kendall_vals else [0, 0],
            "method_recommendations": {
                method: recommendations.count(method) for method in set(recommendations)
            } if recommendations else {},
            "overall_convergence": convergence_analysis.get('overall_convergence_score', 0)
        }
        
        # Prepare response
        response = {
            "success": True,
            "file_path": save_path,
            "visualization_type": "multi_method_dashboard",
            "title": title,
            "file_format": "HTML",
            "interactive": True,
            "dashboard_panels": [
                "method_correlation_matrices",
                "convergence_vs_correlation_strength",
                "method_agreement_boxplots",
                "p_value_comparison_scatter",
                "method_stability_bar_chart",
                "recommendation_distribution_pie",
                "cross_method_correlation_heatmap"
            ],
            "summary_statistics": summary_stats,
            "timestamp": datetime.now().isoformat(),
            "description": "Comprehensive 9-panel interactive dashboard with complete multi-method correlation analysis including convergence, stability, and recommendation patterns"
        }
        
        logger.info(f"Multi-method dashboard created successfully: {save_path}")
        return response
        
    except Exception as e:
        logger.error(f"Error creating multi-method dashboard: {e}")
        return {
            "success": False,
            "error": f"Failed to create multi-method dashboard: {str(e)}",
            "visualization_type": "multi_method_dashboard",
            "timestamp": datetime.now().isoformat()
        }


def plot_method_comparison_matrix_tool(
    ctx: RunContext,
    correlation_results: Dict[str, Any],
    method_pair: Tuple[str, str] = ('pearson', 'spearman'),
    title: Optional[str] = None,
    save_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create detailed comparison between two specific correlation methods.
    
    Args:
        ctx: Run context with data dependencies
        correlation_results: Multi-method correlation results
        method_pair: Tuple of two methods to compare ('pearson', 'spearman', 'kendall')
        title: Plot title (auto-generated if None)
        save_path: Path to save the PNG file (None for temp file)
        
    Returns:
        Dictionary with file path, comparison analysis, and metadata
    """
    try:
        # Initialize plotter
        plotter = MultiMethodCorrelationPlotter()
        
        method1, method2 = method_pair
        
        # Generate title if not provided
        if title is None:
            title = f'{method1.title()} vs {method2.title()} Correlation Comparison'
        
        # Generate save path if not provided
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"{method1}_vs_{method2}_comparison_{timestamp}.png"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
        
        # Create comparison matrix
        fig = plotter.plot_method_comparison_matrix(
            correlation_results=correlation_results,
            method_pair=method_pair,
            title=title,
            save_path=save_path
        )
        
        # Close figure to free memory
        import matplotlib.pyplot as plt
        plt.close(fig)
        
        # Calculate comparison statistics
        multi_results = correlation_results.get('multi_method_results', {})
        method1_vals = [result[f'{method1}_correlation'] for result in multi_results.values()]
        method2_vals = [result[f'{method2}_correlation'] for result in multi_results.values()]
        
        import numpy as np
        differences = np.array(method1_vals) - np.array(method2_vals)
        
        comparison_stats = {
            "method_pair": method_pair,
            "correlation_agreement": round(np.corrcoef(method1_vals, method2_vals)[0, 1], 6) if len(method1_vals) > 1 else 0,
            "mean_difference": round(np.mean(differences), 6),
            "std_difference": round(np.std(differences), 6),
            "max_absolute_difference": round(np.max(np.abs(differences)), 6),
            "agreement_within_01": sum(np.abs(differences) < 0.1) / len(differences) if differences.size > 0 else 0,
            "agreement_within_005": sum(np.abs(differences) < 0.05) / len(differences) if differences.size > 0 else 0
        }
        
        # Generate comparison insights
        insights = []
        if comparison_stats["correlation_agreement"] > 0.9:
            insights.append(f"Very high agreement between {method1} and {method2} methods")
        elif comparison_stats["correlation_agreement"] > 0.7:
            insights.append(f"Good agreement between {method1} and {method2} methods")
        else:
            insights.append(f"Notable disagreement between {method1} and {method2} methods")
        
        if comparison_stats["agreement_within_01"] > 0.8:
            insights.append("Most correlations agree within 0.1 units")
        elif comparison_stats["agreement_within_005"] > 0.5:
            insights.append("Moderate agreement with most correlations within 0.05 units")
        else:
            insights.append("Significant method differences detected - careful method selection recommended")
        
        # Prepare response
        response = {
            "success": True,
            "file_path": save_path,
            "visualization_type": "method_comparison_matrix",
            "title": title,
            "methods_compared": method_pair,
            "file_format": "PNG",
            "comparison_statistics": comparison_stats,
            "comparison_insights": insights,
            "plot_components": [
                "method_correlation_scatter",
                "difference_distribution_histogram",
                "bland_altman_plot",
                "top_differences_table"
            ],
            "timestamp": datetime.now().isoformat(),
            "pairs_analyzed": len(multi_results),
            "description": f"4-panel detailed comparison between {method1} and {method2} correlation methods including scatter plot, Bland-Altman analysis, and difference statistics"
        }
        
        logger.info(f"Method comparison matrix created successfully: {save_path}")
        return response
        
    except Exception as e:
        logger.error(f"Error creating method comparison matrix: {e}")
        return {
            "success": False,
            "error": f"Failed to create method comparison matrix: {str(e)}",
            "visualization_type": "method_comparison_matrix",
            "methods_compared": method_pair,
            "timestamp": datetime.now().isoformat()
        }


def generate_all_visualizations_tool(
    ctx: RunContext,
    correlation_results: Dict[str, Any],
    output_dir: str = "correlation_visualizations",
    prefix: str = ""
) -> Dict[str, Any]:
    """
    Generate complete visualization suite for multi-method correlation analysis.
    
    Args:
        ctx: Run context with data dependencies
        correlation_results: Multi-method correlation results
        output_dir: Output directory for all visualizations
        prefix: Prefix for all generated file names
        
    Returns:
        Dictionary with all file paths, generation summary, and metadata
    """
    try:
        # Initialize plotter
        plotter = MultiMethodCorrelationPlotter()
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"Generating complete visualization suite in {output_dir}/...")
        
        # Use the existing batch generation method
        plotter.save_all_multi_method_plots(
            correlation_results=correlation_results,
            output_dir=output_dir,
            prefix=prefix
        )
        
        # Collect generated files
        output_path = Path(output_dir)
        generated_files = []
        
        # Look for expected file patterns
        file_patterns = [
            f"{prefix}multi_method_heatmaps.png",
            f"{prefix}method_convergence.html",
            f"{prefix}multi_method_dashboard.html",
            f"{prefix}pearson_vs_spearman_comparison.png",
            f"{prefix}pearson_vs_kendall_comparison.png",
            f"{prefix}spearman_vs_kendall_comparison.png"
        ]
        
        for pattern in file_patterns:
            file_path = output_path / pattern
            if file_path.exists():
                generated_files.append({
                    "filename": pattern,
                    "path": str(file_path),
                    "type": "PNG" if pattern.endswith('.png') else "HTML",
                    "size_mb": round(file_path.stat().st_size / (1024 * 1024), 2)
                })
        
        # Calculate summary statistics
        total_files = len(generated_files)
        total_size_mb = sum(f["size_mb"] for f in generated_files)
        png_files = len([f for f in generated_files if f["type"] == "PNG"])
        html_files = len([f for f in generated_files if f["type"] == "HTML"])
        
        # Extract analysis summary
        multi_results = correlation_results.get('multi_method_results', {})
        convergence_analysis = correlation_results.get('convergence_analysis', {})
        
        analysis_summary = {
            "correlation_pairs_analyzed": len(multi_results),
            "overall_convergence_score": convergence_analysis.get('overall_convergence_score', 0),
            "methods_analyzed": ["pearson", "spearman", "kendall"],
            "convergence_classification": "high" if convergence_analysis.get('overall_convergence_score', 0) > 0.8 else "medium" if convergence_analysis.get('overall_convergence_score', 0) > 0.5 else "low"
        }
        
        # Prepare response
        response = {
            "success": True,
            "output_directory": output_dir,
            "visualization_suite": "complete_multi_method_analysis",
            "generated_files": generated_files,
            "generation_summary": {
                "total_files": total_files,
                "total_size_mb": total_size_mb,
                "png_files": png_files,
                "html_files": html_files,
                "static_visualizations": png_files,
                "interactive_visualizations": html_files
            },
            "analysis_summary": analysis_summary,
            "visualization_types": [
                "multi_method_heatmaps",
                "method_convergence_analysis", 
                "comprehensive_dashboard",
                "pairwise_method_comparisons"
            ],
            "timestamp": datetime.now().isoformat(),
            "description": f"Complete visualization suite with {total_files} files including static plots and interactive dashboards for comprehensive multi-method correlation analysis"
        }
        
        logger.info(f"Complete visualization suite generated: {total_files} files, {total_size_mb:.2f} MB")
        return response
        
    except Exception as e:
        logger.error(f"Error generating visualization suite: {e}")
        return {
            "success": False,
            "error": f"Failed to generate visualization suite: {str(e)}",
            "output_directory": output_dir,
            "timestamp": datetime.now().isoformat()
        }


# Tool registration list for easy import
VISUALIZATION_TOOLS = [
    create_multi_method_heatmaps_tool,
    plot_method_convergence_tool,
    create_multi_method_dashboard_tool,
    plot_method_comparison_matrix_tool,
    generate_all_visualizations_tool
]