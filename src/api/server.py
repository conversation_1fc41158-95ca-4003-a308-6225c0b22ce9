"""
FastAPI server for Manufacturing Correlation Analysis Web UI

Provides REST API endpoints for data loading, correlation analysis,
and visualization generation with support for real-time updates.
"""

import base64
import io
import json
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import pandas as pd
import plotly
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt

# Import our modules
from ..data.loader import ManufacturingDataLoader
from ..agents.correlation_agent import analyze_manufacturing_correlations
from ..visualization.web_plots import WebManufacturingCorrelationPlotter

# Load environment variables
load_dotenv(override=True)

# Initialize FastAPI app
app = FastAPI(
    title="Manufacturing Correlation Analysis API",
    description="API for manufacturing data correlation analysis and visualization",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global state
class ServerState:
    def __init__(self):
        self.data_loader: Optional[ManufacturingDataLoader] = None
        self.loaded_data: Dict[str, pd.DataFrame] = {}
        self.unified_data: Optional[pd.DataFrame] = None
        self.correlation_matrix: Optional[pd.DataFrame] = None
        self.plotter = WebManufacturingCorrelationPlotter()
        self.active_connections: List[WebSocket] = []
        self.plot_cache: Dict[str, Any] = {}
        self.analysis_results: Dict[str, Any] = {}

state = ServerState()

# Pydantic models
class DataLoadRequest(BaseModel):
    data_dir: str = Field(default="test-data", description="Directory containing manufacturing data files")

class AnalysisRequest(BaseModel):
    query: str = Field(..., description="Analysis query or question")
    analysis_type: str = Field(default="general", description="Type of analysis to perform")
    significance_threshold: float = Field(default=0.05, description="Statistical significance threshold")
    min_correlation: float = Field(default=0.3, description="Minimum correlation coefficient to report")

class PlotRequest(BaseModel):
    plot_type: str = Field(..., description="Type of plot to generate")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Plot-specific parameters")

class SettingsUpdate(BaseModel):
    significance_threshold: Optional[float] = None
    min_correlation: Optional[float] = None
    correlation_method: Optional[str] = None
    time_window: Optional[int] = None

class DataSummary(BaseModel):
    datasets: Dict[str, Dict[str, Any]]
    unified_data_shape: Optional[List[int]] = None
    total_records: int
    numeric_variables: List[str]
    time_range: Optional[Dict[str, str]] = None

class PlotResponse(BaseModel):
    plot_id: str
    plot_type: str
    format: str  # 'base64_png', 'plotly_json', 'html'
    data: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

# Utility functions
def matplotlib_to_base64(fig) -> str:
    """Convert matplotlib figure to base64 string"""
    buffer = io.BytesIO()
    fig.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    buffer.close()
    plt.close(fig)
    return image_base64

def plotly_to_json(fig) -> str:
    """Convert plotly figure to JSON string"""
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)

async def broadcast_message(message: Dict[str, Any]):
    """Broadcast message to all connected WebSocket clients"""
    if state.active_connections:
        disconnected = []
        for connection in state.active_connections:
            try:
                await connection.send_json(message)
            except WebSocketDisconnect:
                disconnected.append(connection)
        
        # Remove disconnected clients
        for connection in disconnected:
            state.active_connections.remove(connection)

# API Endpoints

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Manufacturing Correlation Analysis API",
        "version": "1.0.0",
        "endpoints": {
            "data": "/data",
            "analysis": "/analysis",
            "plots": "/plots",
            "websocket": "/ws"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "data_loaded": state.loaded_data is not None and len(state.loaded_data) > 0,
        "api_configured": bool(os.getenv('ANTHROPIC_API_KEY') or os.getenv('VERTEX_AI_PROJECT'))
    }

@app.post("/data/load")
async def load_data(request: DataLoadRequest):
    """Load manufacturing data from specified directory"""
    try:
        # Initialize data loader
        data_path = Path(request.data_dir)
        if not data_path.exists():
            raise HTTPException(status_code=400, detail=f"Data directory not found: {request.data_dir}")
        
        state.data_loader = ManufacturingDataLoader(str(data_path))
        
        # Load all manufacturing data
        state.loaded_data = state.data_loader.load_all_manufacturing_data()
        
        if not state.loaded_data:
            raise HTTPException(status_code=400, detail="No data files found in directory")
        
        # Create unified dataset
        try:
            state.unified_data = state.data_loader.create_unified_dataset()
        except Exception as e:
            state.unified_data = None
            print(f"Could not create unified dataset: {e}")
        
        # Calculate correlation matrix
        if state.unified_data is not None:
            numeric_cols = state.unified_data.select_dtypes(include=[pd.api.types.is_numeric_dtype]).columns
            if len(numeric_cols) > 1:
                state.correlation_matrix = state.unified_data[numeric_cols].corr()
        
        # Broadcast update to WebSocket clients
        await broadcast_message({
            "type": "data_loaded",
            "data": await get_data_summary()
        })
        
        return {
            "status": "success",
            "message": f"Loaded {len(state.loaded_data)} datasets",
            "datasets": list(state.loaded_data.keys()),
            "unified_data_shape": list(state.unified_data.shape) if state.unified_data is not None else None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading data: {str(e)}")

@app.get("/data/summary")
async def get_data_summary():
    """Get summary of loaded data"""
    if not state.loaded_data:
        raise HTTPException(status_code=400, detail="No data loaded")
    
    # Prepare dataset summaries
    datasets = {}
    total_records = 0
    
    for name, df in state.loaded_data.items():
        total_records += len(df)
        
        # Get time range
        time_range = None
        if 'timestamp' in df.columns:
            try:
                timestamps = pd.to_datetime(df['timestamp'], errors='coerce').dropna()
                if len(timestamps) > 0:
                    time_range = {
                        "start": timestamps.min().isoformat(),
                        "end": timestamps.max().isoformat()
                    }
            except Exception:
                pass
        
        datasets[name] = {
            "records": len(df),
            "variables": len(df.columns),
            "missing_data_pct": (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
            "time_range": time_range
        }
    
    # Get numeric variables
    numeric_variables = []
    if state.unified_data is not None:
        numeric_cols = state.unified_data.select_dtypes(include=[pd.api.types.is_numeric_dtype]).columns
        numeric_variables = numeric_cols.tolist()
    
    return DataSummary(
        datasets=datasets,
        unified_data_shape=list(state.unified_data.shape) if state.unified_data is not None else None,
        total_records=total_records,
        numeric_variables=numeric_variables
    )

@app.post("/analysis/correlations")
async def analyze_correlations(request: AnalysisRequest):
    """Perform correlation analysis using AI agent"""
    if state.unified_data is None:
        raise HTTPException(status_code=400, detail="No unified data available. Please load data first.")
    
    try:
        # Run correlation analysis
        results = await analyze_manufacturing_correlations(
            data=state.unified_data,
            query=request.query,
            time_column='timestamp',
            significance_threshold=request.significance_threshold,
            min_correlation=request.min_correlation,
            analysis_type=request.analysis_type
        )
        
        # Store results for later use
        analysis_id = str(uuid.uuid4())
        state.analysis_results[analysis_id] = {
            "timestamp": datetime.now().isoformat(),
            "request": request.dict(),
            "results": results
        }
        
        # Broadcast analysis update
        await broadcast_message({
            "type": "analysis_complete",
            "analysis_id": analysis_id,
            "query": request.query
        })
        
        return {
            "analysis_id": analysis_id,
            "data_quality_score": results.data_quality_score,
            "significant_correlations": results.significant_correlations,
            "insights": [insight.dict() for insight in results.insights] if results.insights else [],
            "recommendations": results.recommendations
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/plots/generate")
async def generate_plot(request: PlotRequest):
    """Generate visualization plot"""
    if state.unified_data is None or state.correlation_matrix is None:
        raise HTTPException(status_code=400, detail="No data available for plotting")
    
    try:
        plot_id = str(uuid.uuid4())
        
        # Generate plot based on type using web-optimized plotter
        if request.plot_type == "correlation_matrix":
            plot_result = state.plotter.create_web_correlation_matrix(
                state.correlation_matrix,
                title=request.parameters.get("title", "Correlation Matrix"),
                significance_threshold=request.parameters.get("significance_threshold", 0.05),
                output_format=request.parameters.get("output_format", "plotly_json")
            )
            
            response = PlotResponse(
                plot_id=plot_id,
                plot_type=request.plot_type,
                format=plot_result["format"],
                data=plot_result["data"],
                metadata=plot_result["metadata"]
            )
            
        elif request.plot_type == "correlation_network":
            plot_result = state.plotter.create_web_correlation_network(
                state.correlation_matrix,
                threshold=request.parameters.get("threshold", 0.5),
                title=request.parameters.get("title", "Correlation Network")
            )
            
            response = PlotResponse(
                plot_id=plot_id,
                plot_type=request.plot_type,
                format=plot_result["format"],
                data=plot_result["data"],
                metadata=plot_result["metadata"]
            )
            
        elif request.plot_type == "scatter_plot":
            x_var = request.parameters.get("x_var")
            y_var = request.parameters.get("y_var")
            
            if not x_var or not y_var:
                raise HTTPException(status_code=400, detail="x_var and y_var required for scatter plot")
            
            if x_var not in state.unified_data.columns or y_var not in state.unified_data.columns:
                raise HTTPException(status_code=400, detail="Variables not found in data")
            
            plot_result = state.plotter.create_web_scatter_plot(
                state.unified_data,
                x_var=x_var,
                y_var=y_var,
                title=request.parameters.get("title"),
                output_format=request.parameters.get("output_format", "plotly_json")
            )
            
            response = PlotResponse(
                plot_id=plot_id,
                plot_type=request.plot_type,
                format=plot_result["format"],
                data=plot_result["data"],
                metadata=plot_result["metadata"]
            )
            
        elif request.plot_type == "dashboard":
            # Get significant correlations for dashboard
            significant_correlations = []
            if state.correlation_matrix is not None:
                # Extract significant correlations
                threshold = request.parameters.get("min_correlation", 0.3)
                for i, var1 in enumerate(state.correlation_matrix.columns):
                    for j, var2 in enumerate(state.correlation_matrix.columns):
                        if i < j:
                            corr_val = state.correlation_matrix.loc[var1, var2]
                            if abs(corr_val) >= threshold:
                                significant_correlations.append({
                                    "variable_1": var1,
                                    "variable_2": var2,
                                    "correlation_coefficient": corr_val
                                })
                
                # Sort by absolute correlation
                significant_correlations.sort(key=lambda x: abs(x["correlation_coefficient"]), reverse=True)
            
            plot_result = state.plotter.create_web_dashboard(
                state.correlation_matrix,
                significant_correlations,
                state.unified_data,
                title=request.parameters.get("title", "Manufacturing Correlation Dashboard")
            )
            
            response = PlotResponse(
                plot_id=plot_id,
                plot_type=request.plot_type,
                format=plot_result["format"],
                data=plot_result["data"],
                metadata=plot_result["metadata"]
            )
            
        else:
            raise HTTPException(status_code=400, detail=f"Unknown plot type: {request.plot_type}")
        
        # Cache the plot
        state.plot_cache[plot_id] = response
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Plot generation failed: {str(e)}")

@app.get("/plots/available")
async def get_available_plots():
    """Get list of available plot types"""
    return {
        "plot_types": [
            {
                "name": "correlation_matrix",
                "description": "Correlation matrix heatmap",
                "format": "base64_png",
                "parameters": ["title", "significance_threshold"]
            },
            {
                "name": "correlation_network",
                "description": "Interactive correlation network",
                "format": "plotly_json",
                "parameters": ["threshold", "title"]
            },
            {
                "name": "scatter_plot",
                "description": "Scatter plot with correlation",
                "format": "base64_png",
                "parameters": ["x_var", "y_var", "title", "show_trendline"]
            },
            {
                "name": "dashboard",
                "description": "Comprehensive correlation dashboard",
                "format": "plotly_json",
                "parameters": ["min_correlation"]
            }
        ]
    }

@app.get("/plots/{plot_id}")
async def get_plot(plot_id: str):
    """Get specific plot by ID"""
    if plot_id not in state.plot_cache:
        raise HTTPException(status_code=404, detail="Plot not found")
    
    return state.plot_cache[plot_id]

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket.accept()
    state.active_connections.append(websocket)
    
    try:
        # Send initial status
        await websocket.send_json({
            "type": "connection_established",
            "timestamp": datetime.now().isoformat(),
            "data_loaded": len(state.loaded_data) > 0
        })
        
        # Keep connection alive and handle messages
        while True:
            try:
                message = await websocket.receive_json()
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_json({"type": "pong"})
                elif message.get("type") == "request_status":
                    await websocket.send_json({
                        "type": "status_update",
                        "data_loaded": len(state.loaded_data) > 0,
                        "datasets": list(state.loaded_data.keys()),
                        "unified_data_shape": list(state.unified_data.shape) if state.unified_data is not None else None
                    })
                    
            except WebSocketDisconnect:
                break
            except Exception as e:
                await websocket.send_json({
                    "type": "error",
                    "message": f"Error processing message: {str(e)}"
                })
                
    except WebSocketDisconnect:
        pass
    finally:
        if websocket in state.active_connections:
            state.active_connections.remove(websocket)

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "timestamp": datetime.now().isoformat()}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "timestamp": datetime.now().isoformat()}
    )

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup"""
    print("Starting Manufacturing Correlation Analysis API...")
    
    # Check for environment variables
    if not os.getenv('ANTHROPIC_API_KEY') and not os.getenv('VERTEX_AI_PROJECT'):
        print("Warning: No API keys configured. Analysis features may not work.")
    
    # Try to auto-load data if directory exists
    if Path("test-data").exists():
        try:
            state.data_loader = ManufacturingDataLoader("test-data")
            state.loaded_data = state.data_loader.load_all_manufacturing_data()
            if state.loaded_data:
                state.unified_data = state.data_loader.create_unified_dataset()
                if state.unified_data is not None:
                    numeric_cols = state.unified_data.select_dtypes(include=[pd.api.types.is_numeric_dtype]).columns
                    if len(numeric_cols) > 1:
                        state.correlation_matrix = state.unified_data[numeric_cols].corr()
                print(f"Auto-loaded {len(state.loaded_data)} datasets")
        except Exception as e:
            print(f"Could not auto-load data: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)