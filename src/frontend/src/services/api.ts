/**
 * API service for Manufacturing Correlation Analysis
 * 
 * Provides methods to interact with the FastAPI backend
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  ApiResponse,
  DataSummary,
  AnalysisRequest,
  AnalysisResults,
  PlotRequest,
  PlotResponse,
  AvailablePlotType
} from '@/types';

class ApiService {
  private client: AxiosInstance;
  private baseURL: string;

  constructor(baseURL: string = 'http://localhost:8000') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.error || error.response.data?.detail || 'Server error';
      return new Error(`${error.response.status}: ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error: No response from server');
    } else {
      // Something else happened
      return new Error(`Request error: ${error.message}`);
    }
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.client.get('/health');
    return response.data;
  }

  // Data management
  async loadData(dataDir: string = 'test-data'): Promise<ApiResponse> {
    const response = await this.client.post('/data/load', { data_dir: dataDir });
    return response.data;
  }

  async getDataSummary(): Promise<DataSummary> {
    const response = await this.client.get('/data/summary');
    return response.data;
  }

  // Analysis
  async analyzeCorrelations(request: AnalysisRequest): Promise<AnalysisResults> {
    const response = await this.client.post('/analysis/correlations', request);
    return response.data;
  }

  // Plotting
  async generatePlot(request: PlotRequest): Promise<PlotResponse> {
    const response = await this.client.post('/plots/generate', request);
    return response.data;
  }

  async getAvailablePlots(): Promise<{ plot_types: AvailablePlotType[] }> {
    const response = await this.client.get('/plots/available');
    return response.data;
  }

  async getPlot(plotId: string): Promise<PlotResponse> {
    const response = await this.client.get(`/plots/${plotId}`);
    return response.data;
  }

  // Convenience methods for specific plot types
  async generateCorrelationMatrix(
    title?: string,
    significanceThreshold?: number
  ): Promise<PlotResponse> {
    return this.generatePlot({
      plot_type: 'correlation_matrix',
      parameters: {
        title: title || 'Correlation Matrix',
        significance_threshold: significanceThreshold || 0.05
      }
    });
  }

  async generateScatterPlot(
    xVar: string,
    yVar: string,
    title?: string,
    showTrendline: boolean = true
  ): Promise<PlotResponse> {
    return this.generatePlot({
      plot_type: 'scatter_plot',
      parameters: {
        x_var: xVar,
        y_var: yVar,
        title,
        show_trendline: showTrendline
      }
    });
  }

  async generateCorrelationNetwork(
    threshold: number = 0.5,
    title?: string
  ): Promise<PlotResponse> {
    return this.generatePlot({
      plot_type: 'correlation_network',
      parameters: {
        threshold,
        title: title || 'Correlation Network'
      }
    });
  }

  async generateDashboard(
    minCorrelation: number = 0.3
  ): Promise<PlotResponse> {
    return this.generatePlot({
      plot_type: 'dashboard',
      parameters: {
        min_correlation: minCorrelation
      }
    });
  }

  // Utility methods
  getBaseURL(): string {
    return this.baseURL;
  }

  getWebSocketURL(): string {
    return this.baseURL.replace('http', 'ws') + '/ws';
  }
}

// Create singleton instance
const apiService = new ApiService(
  import.meta.env.VITE_API_URL || 'http://localhost:8000'
);

export default apiService;
export { ApiService };