/**
 * WebSocket service for real-time communication
 */

import { io, Socket } from 'socket.io-client';
import { WebSocketMessage } from '@/types';

class WebSocketService {
  private socket: Socket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, Function[]> = new Map();

  constructor(url: string = 'ws://localhost:8000/ws') {
    this.url = url.replace('ws://', 'http://').replace('wss://', 'https://');
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Use socket.io-client to connect to FastAPI WebSocket
        this.socket = io(this.url, {
          transports: ['websocket'],
          upgrade: false,
        });

        this.socket.on('connect', () => {
          console.log('WebSocket connected');
          this.reconnectAttempts = 0;
          this.emit('connection_established', { timestamp: new Date().toISOString() });
          resolve();
        });

        this.socket.on('disconnect', (reason) => {
          console.log('WebSocket disconnected:', reason);
          this.handleDisconnect();
        });

        this.socket.on('connect_error', (error) => {
          console.error('WebSocket connection error:', error);
          this.handleConnectionError();
          reject(error);
        });

        // Handle incoming messages
        this.socket.onAny((event, data) => {
          this.handleMessage({ type: event, ...data });
        });

      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.listeners.clear();
  }

  private handleMessage(message: WebSocketMessage): void {
    console.log('WebSocket message received:', message);
    
    // Emit to specific listeners
    const typeListeners = this.listeners.get(message.type) || [];
    typeListeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error('Error in WebSocket listener:', error);
      }
    });

    // Emit to global listeners
    const globalListeners = this.listeners.get('*') || [];
    globalListeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error('Error in global WebSocket listener:', error);
      }
    });
  }

  private handleDisconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
      this.emit('max_reconnects_reached', {});
    }
  }

  private handleConnectionError(): void {
    // Handle connection errors
    this.emit('connection_error', {});
  }

  // Public methods
  emit(type: string, data: any): void {
    if (this.socket && this.socket.connected) {
      this.socket.emit(type, data);
    } else {
      console.warn('WebSocket not connected, cannot emit message');
    }
  }

  on(type: string, listener: (message: WebSocketMessage) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    this.listeners.get(type)!.push(listener);
  }

  off(type: string, listener?: (message: WebSocketMessage) => void): void {
    if (listener) {
      const typeListeners = this.listeners.get(type) || [];
      const index = typeListeners.indexOf(listener);
      if (index > -1) {
        typeListeners.splice(index, 1);
      }
    } else {
      this.listeners.delete(type);
    }
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Convenience methods for common message types
  requestStatus(): void {
    this.emit('request_status', {});
  }

  ping(): void {
    this.emit('ping', {});
  }

  // Event handler shortcuts
  onDataLoaded(listener: (data: any) => void): void {
    this.on('data_loaded', (message) => listener(message.data));
  }

  onAnalysisComplete(listener: (analysisId: string, query: string) => void): void {
    this.on('analysis_complete', (message) => 
      listener(message.analysis_id!, message.query!)
    );
  }

  onConnectionEstablished(listener: (data: any) => void): void {
    this.on('connection_established', (message) => listener(message.data));
  }

  onStatusUpdate(listener: (data: any) => void): void {
    this.on('status_update', (message) => listener(message.data));
  }

  onError(listener: (error: string) => void): void {
    this.on('error', (message) => listener(message.message || 'Unknown error'));
  }
}

// Alternative WebSocket implementation for FastAPI WebSocket endpoint
class FastAPIWebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, Function[]> = new Map();

  constructor(url: string = 'ws://localhost:8000/ws') {
    this.url = url;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('FastAPI WebSocket connected');
          this.reconnectAttempts = 0;
          resolve();
        };

        this.ws.onclose = (event) => {
          console.log('FastAPI WebSocket closed:', event.code, event.reason);
          this.handleDisconnect();
        };

        this.ws.onerror = (error) => {
          console.error('FastAPI WebSocket error:', error);
          reject(error);
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

      } catch (error) {
        console.error('Failed to create FastAPI WebSocket connection:', error);
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
  }

  private handleMessage(message: WebSocketMessage): void {
    console.log('FastAPI WebSocket message received:', message);
    
    // Emit to specific listeners
    const typeListeners = this.listeners.get(message.type) || [];
    typeListeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error('Error in WebSocket listener:', error);
      }
    });

    // Emit to global listeners
    const globalListeners = this.listeners.get('*') || [];
    globalListeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error('Error in global WebSocket listener:', error);
      }
    });
  }

  private handleDisconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('FastAPI WebSocket not connected, cannot send message');
    }
  }

  on(type: string, listener: (message: WebSocketMessage) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    this.listeners.get(type)!.push(listener);
  }

  off(type: string, listener?: (message: WebSocketMessage) => void): void {
    if (listener) {
      const typeListeners = this.listeners.get(type) || [];
      const index = typeListeners.indexOf(listener);
      if (index > -1) {
        typeListeners.splice(index, 1);
      }
    } else {
      this.listeners.delete(type);
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  // Convenience methods
  requestStatus(): void {
    this.send({ type: 'request_status' });
  }

  ping(): void {
    this.send({ type: 'ping' });
  }
}

// Create singleton instance (using FastAPI WebSocket)
const wsService = new FastAPIWebSocketService(
  import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws'
);

export default wsService;
export { WebSocketService, FastAPIWebSocketService };