import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Box, CircularProgress, Alert } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from './store';
import { setConnected, setDataLoaded, setError } from './store/slices/uiSlice';
import { setDataSummary } from './store/slices/dataSlice';

// Components
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Analysis from './pages/Analysis';
import Plots from './pages/Plots';
import Settings from './pages/Settings';

// Services
import apiService from './services/api';
import wsService from './services/websocket';

function App() {
  const dispatch = useDispatch();
  const { loading, error, connected } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    // Initialize application
    const initializeApp = async () => {
      try {
        // Check API health
        await apiService.healthCheck();
        
        // Try to load existing data summary
        try {
          const dataSummary = await apiService.getDataSummary();
          dispatch(setDataSummary(dataSummary));
          dispatch(setDataLoaded(true));
        } catch (error) {
          // Data not loaded yet, that's okay
          console.log('No data loaded yet');
        }

        // Connect to WebSocket
        try {
          await wsService.connect();
          dispatch(setConnected(true));

          // Set up WebSocket listeners
          wsService.onDataLoaded((data) => {
            dispatch(setDataSummary(data));
            dispatch(setDataLoaded(true));
          });

          wsService.onError((error) => {
            dispatch(setError(`WebSocket error: ${error}`));
          });

          wsService.onConnectionEstablished(() => {
            dispatch(setConnected(true));
          });

        } catch (error) {
          console.warn('WebSocket connection failed:', error);
          // Continue without WebSocket
        }

      } catch (error) {
        console.error('Failed to initialize app:', error);
        dispatch(setError(`Failed to connect to API: ${error}`));
      }
    };

    initializeApp();

    // Cleanup on unmount
    return () => {
      wsService.disconnect();
    };
  }, [dispatch]);

  if (error) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        p={3}
      >
        <Alert severity="error" sx={{ maxWidth: 500 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/analysis" element={<Analysis />} />
          <Route path="/plots" element={<Plots />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;