/**
 * Correlation Matrix Heatmap Component
 * 
 * Displays correlation matrix data as an interactive heatmap using Plotly.js
 */

import React, { useMemo, useState } from 'react';
import Plot from 'react-plotly.js';
import {
  Box,
  Card,
  CardHeader,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Typography,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { PlotComponentProps, PlotResponse } from '@/types';

interface CorrelationMatrixProps extends PlotComponentProps {
  title?: string;
  showControls?: boolean;
  showMetadata?: boolean;
  height?: number;
  width?: number;
}

const CorrelationMatrix: React.FC<CorrelationMatrixProps> = ({
  plotData,
  onExport,
  onRefresh,
  className,
  title,
  showControls = true,
  showMetadata = true,
  height = 500,
  width,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Parse plot data based on format
  const plotlyData = useMemo(() => {
    if (!plotData) return null;

    try {
      if (plotData.format === 'plotly_json') {
        // Data is already in Plotly format
        const parsedData = typeof plotData.data === 'string' 
          ? JSON.parse(plotData.data) 
          : plotData.data;
        return parsedData;
      } else if (plotData.format === 'base64_png') {
        // For base64 images, we'll show them differently
        return null;
      }
    } catch (error) {
      console.error('Error parsing plot data:', error);
      return null;
    }

    return null;
  }, [plotData]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleExport = (format: 'png' | 'pdf' | 'html') => {
    onExport?.(format);
    handleMenuClose();
  };

  const handleRefresh = () => {
    onRefresh?.();
    handleMenuClose();
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    handleMenuClose();
  };

  if (!plotData) {
    return (
      <Card className={className}>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height={200}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  // Handle base64 image format
  if (plotData.format === 'base64_png') {
    return (
      <Card className={className}>
        <CardHeader
          title={title || plotData.metadata.title || 'Correlation Matrix'}
          action={
            showControls ? (
              <>
                <IconButton onClick={handleMenuOpen}>
                  <MoreVertIcon />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                >
                  <MenuItem onClick={() => handleExport('png')}>
                    <DownloadIcon sx={{ mr: 1 }} />
                    Export PNG
                  </MenuItem>
                  <MenuItem onClick={() => handleExport('pdf')}>
                    <DownloadIcon sx={{ mr: 1 }} />
                    Export PDF
                  </MenuItem>
                  <MenuItem onClick={handleRefresh}>
                    <RefreshIcon sx={{ mr: 1 }} />
                    Refresh
                  </MenuItem>
                  <MenuItem onClick={toggleFullscreen}>
                    <FullscreenIcon sx={{ mr: 1 }} />
                    Fullscreen
                  </MenuItem>
                </Menu>
              </>
            ) : null
          }
        />
        <CardContent>
          <Box textAlign="center">
            <img
              src={plotData.data as string}
              alt="Correlation Matrix"
              style={{
                maxWidth: '100%',
                height: 'auto',
                maxHeight: height,
              }}
            />
          </Box>
          {showMetadata && plotData.metadata && (
            <Box mt={2}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <InfoIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                Matrix Information
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                {plotData.metadata.shape && (
                  <Chip 
                    label={`${plotData.metadata.shape[0]}×${plotData.metadata.shape[1]}`} 
                    size="small" 
                    variant="outlined" 
                  />
                )}
                {plotData.metadata.variables && (
                  <Chip 
                    label={`${plotData.metadata.variables.length} variables`} 
                    size="small" 
                    variant="outlined" 
                  />
                )}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  }

  // Handle Plotly JSON format
  if (!plotlyData) {
    return (
      <Card className={className}>
        <CardContent>
          <Alert severity="error">
            Unable to display correlation matrix. Invalid data format.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader
        title={title || plotData.metadata.title || 'Correlation Matrix'}
        action={
          showControls ? (
            <>
              <IconButton onClick={handleMenuOpen}>
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                <MenuItem onClick={() => handleExport('png')}>
                  <DownloadIcon sx={{ mr: 1 }} />
                  Export PNG
                </MenuItem>
                <MenuItem onClick={() => handleExport('pdf')}>
                  <DownloadIcon sx={{ mr: 1 }} />
                  Export PDF
                </MenuItem>
                <MenuItem onClick={() => handleExport('html')}>
                  <DownloadIcon sx={{ mr: 1 }} />
                  Export HTML
                </MenuItem>
                <MenuItem onClick={handleRefresh}>
                  <RefreshIcon sx={{ mr: 1 }} />
                  Refresh
                </MenuItem>
                <MenuItem onClick={toggleFullscreen}>
                  <FullscreenIcon sx={{ mr: 1 }} />
                  {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
                </MenuItem>
              </Menu>
            </>
          ) : null
        }
      />
      <CardContent>
        <Box
          sx={{
            position: isFullscreen ? 'fixed' : 'relative',
            top: isFullscreen ? 0 : 'auto',
            left: isFullscreen ? 0 : 'auto',
            width: isFullscreen ? '100vw' : width || '100%',
            height: isFullscreen ? '100vh' : height,
            zIndex: isFullscreen ? 9999 : 'auto',
            backgroundColor: isFullscreen ? 'white' : 'transparent',
          }}
        >
          <Plot
            data={plotlyData.data || []}
            layout={{
              ...plotlyData.layout,
              width: isFullscreen ? window.innerWidth - 40 : width || undefined,
              height: isFullscreen ? window.innerHeight - 120 : height - 50,
              margin: { l: 100, r: 50, t: 50, b: 100 },
              font: { size: 10 },
              xaxis: {
                ...plotlyData.layout?.xaxis,
                tickangle: -45,
              },
              yaxis: {
                ...plotlyData.layout?.yaxis,
                automargin: true,
              },
            }}
            config={{
              responsive: true,
              displayModeBar: true,
              modeBarButtonsToAdd: [
                {
                  name: 'Export',
                  icon: {
                    width: 1000,
                    height: 1000,
                    path: 'M500 100L900 400L700 400L700 700L300 700L300 400L100 400Z',
                  },
                  click: () => handleExport('png'),
                },
              ],
              modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
            }}
            style={{
              width: '100%',
              height: '100%',
            }}
          />
          {isFullscreen && (
            <IconButton
              onClick={toggleFullscreen}
              sx={{
                position: 'absolute',
                top: 16,
                right: 16,
                backgroundColor: 'white',
                boxShadow: 2,
              }}
            >
              <FullscreenIcon />
            </IconButton>
          )}
        </Box>

        {showMetadata && plotData.metadata && (
          <Box mt={2}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              <InfoIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
              Matrix Information
            </Typography>
            <Box display="flex" gap={1} flexWrap="wrap">
              {plotData.metadata.shape && (
                <Chip 
                  label={`Dimensions: ${plotData.metadata.shape[0]}×${plotData.metadata.shape[1]}`} 
                  size="small" 
                  variant="outlined" 
                />
              )}
              {plotData.metadata.variables && (
                <Tooltip title={plotData.metadata.variables.join(', ')}>
                  <Chip 
                    label={`${plotData.metadata.variables.length} Variables`} 
                    size="small" 
                    variant="outlined" 
                  />
                </Tooltip>
              )}
              <Chip 
                label={`Plot ID: ${plotData.plot_id.slice(0, 8)}...`} 
                size="small" 
                variant="outlined" 
              />
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default CorrelationMatrix;