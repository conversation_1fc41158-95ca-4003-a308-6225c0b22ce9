/**
 * Main Layout Component
 * 
 * Provides the main application layout with navigation, header, and content area
 */

import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Chip,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  ShowChart as ChartsIcon,
  Settings as SettingsIcon,
  CloudOff as DisconnectedIcon,
  CheckCircle as ConnectedIcon,
  Storage as DataIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const drawerWidth = 280;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  const { connected, dataLoaded } = useSelector((state: RootState) => state.ui);
  const { summary } = useSelector((state: RootState) => state.data);
  const { results } = useSelector((state: RootState) => state.analysis);
  const { plots } = useSelector((state: RootState) => state.plot);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const navigationItems = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/dashboard',
      badge: dataLoaded ? 'active' : undefined,
    },
    {
      text: 'Analysis',
      icon: <AnalyticsIcon />,
      path: '/analysis',
      badge: results.length > 0 ? results.length : undefined,
    },
    {
      text: 'Visualizations',
      icon: <ChartsIcon />,
      path: '/plots',
      badge: Object.keys(plots).length > 0 ? Object.keys(plots).length : undefined,
    },
    {
      text: 'Settings',
      icon: <SettingsIcon />,
      path: '/settings',
    },
  ];

  const drawer = (
    <div>
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" noWrap component="div" color="primary">
          Manufacturing
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Correlation Analysis
        </Typography>
        
        {/* Connection Status */}
        <Box mt={2} display="flex" alignItems="center" gap={1}>
          <Tooltip title={connected ? 'Connected to server' : 'Disconnected from server'}>
            {connected ? (
              <ConnectedIcon color="success" fontSize="small" />
            ) : (
              <DisconnectedIcon color="error" fontSize="small" />
            )}
          </Tooltip>
          <Typography variant="caption" color="text.secondary">
            {connected ? 'Connected' : 'Disconnected'}
          </Typography>
        </Box>

        {/* Data Status */}
        {dataLoaded && summary && (
          <Box mt={1} display="flex" alignItems="center" gap={1}>
            <DataIcon color="primary" fontSize="small" />
            <Typography variant="caption" color="text.secondary">
              {summary.total_records.toLocaleString()} records loaded
            </Typography>
          </Box>
        )}
      </Box>
      
      <Divider />
      
      <List>
        {navigationItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => {
                navigate(item.path);
                setMobileOpen(false);
              }}
              sx={{
                '&.Mui-selected': {
                  backgroundColor: 'primary.main',
                  color: 'primary.contrastText',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                  },
                  '& .MuiListItemIcon-root': {
                    color: 'primary.contrastText',
                  },
                },
              }}
            >
              <ListItemIcon>
                {item.badge ? (
                  <Badge badgeContent={item.badge} color="secondary" max={99}>
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider sx={{ mt: 2 }} />
      
      {/* Status Indicators */}
      <Box sx={{ p: 2 }}>
        <Typography variant="caption" color="text.secondary" gutterBottom display="block">
          System Status
        </Typography>
        <Box display="flex" flexDirection="column" gap={1}>
          <Chip
            label={connected ? "API Connected" : "API Disconnected"}
            size="small"
            color={connected ? "success" : "error"}
            variant="outlined"
            icon={connected ? <ConnectedIcon /> : <DisconnectedIcon />}
          />
          <Chip
            label={dataLoaded ? "Data Loaded" : "No Data"}
            size="small"
            color={dataLoaded ? "success" : "default"}
            variant="outlined"
            icon={<DataIcon />}
          />
        </Box>
      </Box>
    </div>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {navigationItems.find(item => item.path === location.pathname)?.text || 'Dashboard'}
          </Typography>

          {/* Header Status Indicators */}
          <Box display="flex" alignItems="center" gap={2}>
            {dataLoaded && summary && (
              <Tooltip title={`${Object.keys(summary.datasets).length} datasets, ${summary.total_records.toLocaleString()} total records`}>
                <Chip
                  label={`${Object.keys(summary.datasets).length} datasets`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Tooltip>
            )}
            
            <Tooltip title={connected ? 'Connected to server' : 'Disconnected from server'}>
              {connected ? (
                <ConnectedIcon color="success" />
              ) : (
                <DisconnectedIcon color="error" />
              )}
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
      
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="navigation"
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better mobile performance
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        
        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: 'background.default',
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

export default Layout;