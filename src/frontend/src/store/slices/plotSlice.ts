/**
 * Plot state management slice
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PlotState, PlotResponse, AvailablePlotType } from '@/types';

const initialState: PlotState = {
  plots: {},
  availableTypes: [],
  currentPlot: undefined,
  gallery: [],
};

const plotSlice = createSlice({
  name: 'plot',
  initialState,
  reducers: {
    addPlot: (state, action: PayloadAction<PlotResponse>) => {
      const plot = action.payload;
      state.plots[plot.plot_id] = plot;
      
      // Add to gallery if not already present
      if (!state.gallery.includes(plot.plot_id)) {
        state.gallery.push(plot.plot_id);
      }
      
      // Keep only last 20 plots to manage memory
      if (state.gallery.length > 20) {
        const removedId = state.gallery.shift()!;
        delete state.plots[removedId];
      }
    },
    setCurrentPlot: (state, action: PayloadAction<string | undefined>) => {
      state.currentPlot = action.payload;
    },
    removePlot: (state, action: PayloadAction<string>) => {
      const plotId = action.payload;
      delete state.plots[plotId];
      state.gallery = state.gallery.filter(id => id !== plotId);
      
      if (state.currentPlot === plotId) {
        state.currentPlot = undefined;
      }
    },
    setAvailableTypes: (state, action: PayloadAction<AvailablePlotType[]>) => {
      state.availableTypes = action.payload;
    },
    clearPlots: (state) => {
      state.plots = {};
      state.gallery = [];
      state.currentPlot = undefined;
    },
    reorderGallery: (state, action: PayloadAction<string[]>) => {
      // Validate that all IDs exist in plots
      const validIds = action.payload.filter(id => id in state.plots);
      state.gallery = validIds;
    },
    updatePlotMetadata: (state, action: PayloadAction<{ plotId: string; metadata: any }>) => {
      const { plotId, metadata } = action.payload;
      if (state.plots[plotId]) {
        state.plots[plotId].metadata = { ...state.plots[plotId].metadata, ...metadata };
      }
    },
  },
});

export const {
  addPlot,
  setCurrentPlot,
  removePlot,
  setAvailableTypes,
  clearPlots,
  reorderGallery,
  updatePlotMetadata,
} = plotSlice.actions;

// Selectors
export const selectPlotById = (state: { plot: PlotState }, plotId: string) => 
  state.plot.plots[plotId];

export const selectPlotsByType = (state: { plot: PlotState }, plotType: string) => 
  Object.values(state.plot.plots).filter(plot => plot.plot_type === plotType);

export const selectCurrentPlot = (state: { plot: PlotState }) => 
  state.plot.currentPlot ? state.plot.plots[state.plot.currentPlot] : undefined;

export default plotSlice.reducer;