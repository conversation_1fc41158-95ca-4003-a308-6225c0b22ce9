/**
 * Analysis state management slice
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AnalysisState, AnalysisResults, AnalysisRequest } from '@/types';

const initialState: AnalysisState = {
  results: [],
  currentAnalysis: undefined,
  history: [],
};

const analysisSlice = createSlice({
  name: 'analysis',
  initialState,
  reducers: {
    addAnalysisResult: (state, action: PayloadAction<AnalysisResults>) => {
      state.results.push(action.payload);
      state.currentAnalysis = action.payload;
    },
    setCurrentAnalysis: (state, action: PayloadAction<AnalysisResults | undefined>) => {
      state.currentAnalysis = action.payload;
    },
    addToHistory: (state, action: PayloadAction<AnalysisRequest>) => {
      state.history.push(action.payload);
      // Keep only last 50 entries
      if (state.history.length > 50) {
        state.history = state.history.slice(-50);
      }
    },
    clearAnalysisResults: (state) => {
      state.results = [];
      state.currentAnalysis = undefined;
    },
    clearHistory: (state) => {
      state.history = [];
    },
    removeAnalysisResult: (state, action: PayloadAction<string>) => {
      state.results = state.results.filter(result => result.analysis_id !== action.payload);
      if (state.currentAnalysis?.analysis_id === action.payload) {
        state.currentAnalysis = undefined;
      }
    },
    updateAnalysisResult: (state, action: PayloadAction<AnalysisResults>) => {
      const index = state.results.findIndex(result => result.analysis_id === action.payload.analysis_id);
      if (index !== -1) {
        state.results[index] = action.payload;
        if (state.currentAnalysis?.analysis_id === action.payload.analysis_id) {
          state.currentAnalysis = action.payload;
        }
      }
    },
  },
});

export const {
  addAnalysisResult,
  setCurrentAnalysis,
  addToHistory,
  clearAnalysisResults,
  clearHistory,
  removeAnalysisResult,
  updateAnalysisResult,
} = analysisSlice.actions;

export default analysisSlice.reducer;