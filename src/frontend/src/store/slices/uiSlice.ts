/**
 * UI state management slice
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UIState } from '@/types';

const initialState: UIState = {
  loading: false,
  error: undefined,
  connected: false,
  dataLoaded: false,
  currentView: 'dashboard',
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | undefined>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = undefined;
    },
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.connected = action.payload;
    },
    setDataLoaded: (state, action: PayloadAction<boolean>) => {
      state.dataLoaded = action.payload;
    },
    setCurrentView: (state, action: PayloadAction<UIState['currentView']>) => {
      state.currentView = action.payload;
    },
    startLoading: (state) => {
      state.loading = true;
      state.error = undefined;
    },
    stopLoading: (state) => {
      state.loading = false;
    },
    showError: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  clearError,
  setConnected,
  setDataLoaded,
  setCurrentView,
  startLoading,
  stopLoading,
  showError,
} = uiSlice.actions;

export default uiSlice.reducer;