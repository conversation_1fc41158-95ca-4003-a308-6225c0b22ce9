/**
 * Data state management slice
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DataState, DataSummary } from '@/types';

const initialState: DataState = {
  summary: undefined,
  correlationMatrix: undefined,
  correlationVariables: undefined,
  lastUpdated: undefined,
};

const dataSlice = createSlice({
  name: 'data',
  initialState,
  reducers: {
    setDataSummary: (state, action: PayloadAction<DataSummary>) => {
      state.summary = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
    setCorrelationMatrix: (state, action: PayloadAction<{ matrix: number[][], variables: string[] }>) => {
      state.correlationMatrix = action.payload.matrix;
      state.correlationVariables = action.payload.variables;
      state.lastUpdated = new Date().toISOString();
    },
    clearData: (state) => {
      state.summary = undefined;
      state.correlationMatrix = undefined;
      state.correlationVariables = undefined;
      state.lastUpdated = undefined;
    },
    updateLastUpdated: (state) => {
      state.lastUpdated = new Date().toISOString();
    },
  },
});

export const {
  setDataSummary,
  setCorrelationMatrix,
  clearData,
  updateLastUpdated,
} = dataSlice.actions;

export default dataSlice.reducer;