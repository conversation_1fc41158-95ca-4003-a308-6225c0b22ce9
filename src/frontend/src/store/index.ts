/**
 * Redux store configuration
 */

import { configureStore } from '@reduxjs/toolkit';
import uiReducer from './slices/uiSlice';
import dataReducer from './slices/dataSlice';
import analysisReducer from './slices/analysisSlice';
import plotReducer from './slices/plotSlice';

export const store = configureStore({
  reducer: {
    ui: uiReducer,
    data: dataReducer,
    analysis: analysisReducer,
    plot: plotReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['plot/addPlot'],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ['payload.data'],
        // Ignore these paths in the state
        ignoredPaths: ['plot.plots'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;