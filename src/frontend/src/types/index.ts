/**
 * TypeScript type definitions for Manufacturing Correlation Analysis UI
 */

// API Response Types
export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  timestamp?: string;
}

// Data Types
export interface DatasetInfo {
  records: number;
  variables: number;
  missing_data_pct: number;
  time_range?: {
    start: string;
    end: string;
  };
}

export interface DataSummary {
  datasets: Record<string, DatasetInfo>;
  unified_data_shape?: number[];
  total_records: number;
  numeric_variables: string[];
}

// Correlation Analysis Types
export interface CorrelationResult {
  variable_1: string;
  variable_2: string;
  correlation_coefficient: number;
  p_value: number;
  significance_level: string;
}

export interface AnalysisInsight {
  description: string;
  confidence_level: 'high' | 'medium' | 'low';
  actionable_recommendation?: string;
}

export interface AnalysisResults {
  analysis_id: string;
  data_quality_score: number;
  significant_correlations: CorrelationResult[];
  insights: AnalysisInsight[];
  recommendations: string[];
}

// Plot Types
export interface PlotMetadata {
  shape?: number[];
  variables?: string[];
  title?: string;
  correlation?: number;
  x_var?: string;
  y_var?: string;
  color_var?: string;
  data_points?: number;
  threshold?: number;
  nodes_count?: number;
  edges_count?: number;
  layout?: string;
  correlations_count?: number;
  dashboard_type?: string;
  time_column?: string;
  events_count?: number;
}

export interface PlotResponse {
  plot_id: string;
  plot_type: string;
  format: 'base64_png' | 'plotly_json' | 'html';
  data: string | object;
  metadata: PlotMetadata;
}

export interface PlotRequest {
  plot_type: string;
  parameters: Record<string, any>;
}

export interface AvailablePlotType {
  name: string;
  description: string;
  format: string;
  parameters: string[];
}

// Analysis Request Types
export interface AnalysisRequest {
  query: string;
  analysis_type?: string;
  significance_threshold?: number;
  min_correlation?: number;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: string;
  data?: any;
  message?: string;
  timestamp?: string;
  analysis_id?: string;
  query?: string;
}

// Application State Types
export interface AppSettings {
  significance_threshold: number;
  min_correlation: number;
  correlation_method: string;
  time_window: number;
  api_base_url: string;
  websocket_url: string;
}

export interface UIState {
  loading: boolean;
  error?: string;
  connected: boolean;
  dataLoaded: boolean;
  currentView: 'dashboard' | 'analysis' | 'plots' | 'settings';
}

export interface DataState {
  summary?: DataSummary;
  correlationMatrix?: number[][];
  correlationVariables?: string[];
  lastUpdated?: string;
}

export interface AnalysisState {
  results: AnalysisResults[];
  currentAnalysis?: AnalysisResults;
  history: AnalysisRequest[];
}

export interface PlotState {
  plots: Record<string, PlotResponse>;
  availableTypes: AvailablePlotType[];
  currentPlot?: string;
  gallery: string[];
}

// Component Props Types
export interface PlotComponentProps {
  plotData: PlotResponse;
  onExport?: (format: 'png' | 'pdf' | 'html') => void;
  onRefresh?: () => void;
  className?: string;
  height?: number;
  width?: number;
}

export interface DashboardCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  loading?: boolean;
}

export interface AnalysisFormProps {
  onSubmit: (request: AnalysisRequest) => void;
  loading?: boolean;
  initialValues?: Partial<AnalysisRequest>;
}

// Utility Types
export type PlotFormat = 'base64_png' | 'plotly_json' | 'html';
export type AnalysisType = 'general' | 'lag' | 'quality' | 'optimization' | 'rca';
export type CorrelationMethod = 'pearson' | 'spearman' | 'kendall';
export type ConfidenceLevel = 'high' | 'medium' | 'low';

// Export/Import Types
export interface ExportOptions {
  format: 'json' | 'csv' | 'pdf';
  includeMetadata: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface ImportData {
  file: File;
  type: 'csv' | 'json';
  encoding?: string;
}