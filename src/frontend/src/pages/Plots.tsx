/**
 * Plots Page Component
 * 
 * Gallery and management interface for visualizations
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Alert,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Fab,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Fullscreen as FullscreenIcon,
  Delete as DeleteIcon,
  ShowChart as ChartIcon,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { addPlot, removePlot, setAvailableTypes } from '@/store/slices/plotSlice';
import { showError } from '@/store/slices/uiSlice';

// Components
import CorrelationMatrix from '@/components/Charts/CorrelationMatrix';

// Services
import apiService from '@/services/api';
import { PlotRequest, AvailablePlotType } from '@/types';

const Plots: React.FC = () => {
  const dispatch = useDispatch();
  const { dataLoaded } = useSelector((state: RootState) => state.ui);
  const { summary } = useSelector((state: RootState) => state.data);
  const { plots, availableTypes, gallery } = useSelector((state: RootState) => state.plot);
  
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedPlotType, setSelectedPlotType] = useState('');
  const [plotParameters, setPlotParameters] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Load available plot types on mount
    const loadAvailableTypes = async () => {
      try {
        const response = await apiService.getAvailablePlots();
        dispatch(setAvailableTypes(response.plot_types));
      } catch (error) {
        console.error('Failed to load available plot types:', error);
      }
    };

    loadAvailableTypes();
  }, [dispatch]);

  const handleCreatePlot = async () => {
    if (!selectedPlotType) {
      dispatch(showError('Please select a plot type'));
      return;
    }

    const plotType = availableTypes.find(type => type.name === selectedPlotType);
    if (!plotType) {
      dispatch(showError('Invalid plot type selected'));
      return;
    }

    // Validate required parameters
    const missingParams = plotType.parameters.filter(param => {
      if (param === 'x_var' || param === 'y_var') {
        return !plotParameters[param];
      }
      return false;
    });

    if (missingParams.length > 0) {
      dispatch(showError(`Missing required parameters: ${missingParams.join(', ')}`));
      return;
    }

    try {
      setLoading(true);
      
      const request: PlotRequest = {
        plot_type: selectedPlotType,
        parameters: plotParameters,
      };
      
      const plot = await apiService.generatePlot(request);
      dispatch(addPlot(plot));
      
      // Close dialog and reset
      setCreateDialogOpen(false);
      setSelectedPlotType('');
      setPlotParameters({});
      
    } catch (error) {
      dispatch(showError(`Failed to create plot: ${error}`));
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePlot = (plotId: string) => {
    dispatch(removePlot(plotId));
  };

  const handleExportPlot = (plotId: string, format: 'png' | 'pdf' | 'html') => {
    // Implementation for exporting plots
    console.log('Export plot:', plotId, format);
  };

  const getPlotTypeIcon = (plotType: string) => {
    switch (plotType) {
      case 'correlation_matrix':
        return '🔥';
      case 'scatter_plot':
        return '📊';
      case 'correlation_network':
        return '🕸️';
      case 'dashboard':
        return '📈';
      case 'time_series':
        return '📉';
      default:
        return '📊';
    }
  };

  const getPlotTypeDescription = (plotType: string) => {
    const type = availableTypes.find(t => t.name === plotType);
    return type?.description || plotType;
  };

  const renderParameterInput = (param: string, plotType: AvailablePlotType) => {
    if (param === 'x_var' || param === 'y_var') {
      return (
        <FormControl fullWidth key={param} sx={{ mb: 2 }}>
          <InputLabel>{param.replace('_', ' ').toUpperCase()}</InputLabel>
          <Select
            value={plotParameters[param] || ''}
            label={param.replace('_', ' ').toUpperCase()}
            onChange={(e) => setPlotParameters(prev => ({ ...prev, [param]: e.target.value }))}
          >
            {summary?.numeric_variables.map(variable => (
              <MenuItem key={variable} value={variable}>
                {variable}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      );
    }

    if (param === 'threshold' || param === 'min_correlation' || param === 'significance_threshold') {
      return (
        <TextField
          key={param}
          fullWidth
          type="number"
          label={param.replace('_', ' ').toUpperCase()}
          value={plotParameters[param] || ''}
          onChange={(e) => setPlotParameters(prev => ({ ...prev, [param]: parseFloat(e.target.value) || 0 }))}
          inputProps={{ min: 0, max: 1, step: 0.1 }}
          sx={{ mb: 2 }}
        />
      );
    }

    return (
      <TextField
        key={param}
        fullWidth
        label={param.replace('_', ' ').toUpperCase()}
        value={plotParameters[param] || ''}
        onChange={(e) => setPlotParameters(prev => ({ ...prev, [param]: e.target.value }))}
        sx={{ mb: 2 }}
      />
    );
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Visualizations
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create and manage correlation analysis visualizations
          </Typography>
        </Box>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
          disabled={!dataLoaded}
        >
          Create Plot
        </Button>
      </Box>

      {!dataLoaded && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Please load manufacturing data first to create visualizations.
        </Alert>
      )}

      {/* Plot Gallery */}
      {gallery.length > 0 ? (
        <Grid container spacing={3}>
          {gallery.map(plotId => {
            const plot = plots[plotId];
            if (!plot) return null;

            return (
              <Grid item xs={12} lg={6} key={plotId}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="h6">
                          {getPlotTypeIcon(plot.plot_type)} {plot.metadata.title || plot.plot_type}
                        </Typography>
                        <Chip 
                          label={plot.plot_type} 
                          size="small" 
                          variant="outlined" 
                        />
                      </Box>
                      
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => handleExportPlot(plotId, 'png')}
                          title="Export"
                        >
                          <DownloadIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeletePlot(plotId)}
                          title="Delete"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </Box>

                    <CorrelationMatrix
                      plotData={plot}
                      showControls={false}
                      showMetadata={false}
                      height={300}
                      onExport={(format) => handleExportPlot(plotId, format)}
                    />

                    <Box mt={2}>
                      <Typography variant="body2" color="text.secondary">
                        {getPlotTypeDescription(plot.plot_type)}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      ) : (
        <Card>
          <CardContent>
            <Box textAlign="center" py={8}>
              <ChartIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                No Visualizations Yet
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Create your first visualization to get started with correlation analysis
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setCreateDialogOpen(true)}
                disabled={!dataLoaded}
              >
                Create First Plot
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Floating Action Button for Quick Creation */}
      <Fab
        color="primary"
        aria-label="create plot"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setCreateDialogOpen(true)}
        disabled={!dataLoaded}
      >
        <AddIcon />
      </Fab>

      {/* Create Plot Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Visualization</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Plot Type</InputLabel>
              <Select
                value={selectedPlotType}
                label="Plot Type"
                onChange={(e) => {
                  setSelectedPlotType(e.target.value);
                  setPlotParameters({});
                }}
              >
                {availableTypes.map(type => (
                  <MenuItem key={type.name} value={type.name}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <span>{getPlotTypeIcon(type.name)}</span>
                      <Box>
                        <Typography variant="body1">{type.name.replace('_', ' ')}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.description}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {selectedPlotType && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Parameters
                </Typography>
                {availableTypes
                  .find(type => type.name === selectedPlotType)
                  ?.parameters.map(param => renderParameterInput(param, availableTypes.find(type => type.name === selectedPlotType)!))}
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleCreatePlot}
            disabled={!selectedPlotType || loading}
          >
            {loading ? 'Creating...' : 'Create Plot'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Plots;