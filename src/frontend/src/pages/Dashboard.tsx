/**
 * Dashboard Page Component
 * 
 * Main dashboard showing overview of manufacturing correlation analysis
 */

import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Assessment as AssessmentIcon,
  Storage as DataIcon,
  Timeline as TimelineIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { startLoading, stopLoading, showError, setDataLoaded } from '@/store/slices/uiSlice';
import { setDataSummary } from '@/store/slices/dataSlice';

// Components
import CorrelationMatrix from '@/components/Charts/CorrelationMatrix';

// Services
import apiService from '@/services/api';
import { DashboardCardProps } from '@/types';

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  loading = false,
}) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="flex-start" justifyContent="space-between">
        <Box>
          <Typography variant="h4" color={`${color}.main`} gutterBottom>
            {loading ? <CircularProgress size={32} /> : value}
          </Typography>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        {icon && (
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        )}
      </Box>
    </CardContent>
  </Card>
);

const Dashboard: React.FC = () => {
  const dispatch = useDispatch();
  const { loading, dataLoaded } = useSelector((state: RootState) => state.ui);
  const { summary } = useSelector((state: RootState) => state.data);
  const { results } = useSelector((state: RootState) => state.analysis);
  const [correlationMatrixPlot, setCorrelationMatrixPlot] = useState(null);
  const [dashboardPlot, setDashboardPlot] = useState(null);

  const loadData = async () => {
    try {
      dispatch(startLoading());
      
      // Load data from default directory
      await apiService.loadData('test-data');
      
      // Get data summary
      const dataSummary = await apiService.getDataSummary();
      dispatch(setDataSummary(dataSummary));
      dispatch(setDataLoaded(true));
      
      dispatch(stopLoading());
    } catch (error) {
      dispatch(showError(`Failed to load data: ${error}`));
    }
  };

  const generateCorrelationMatrix = async () => {
    try {
      const plot = await apiService.generateCorrelationMatrix();
      setCorrelationMatrixPlot(plot);
    } catch (error) {
      console.error('Failed to generate correlation matrix:', error);
    }
  };

  const generateDashboard = async () => {
    try {
      const plot = await apiService.generateDashboard();
      setDashboardPlot(plot);
    } catch (error) {
      console.error('Failed to generate dashboard:', error);
    }
  };

  useEffect(() => {
    // Try to load existing data on mount
    const initializeDashboard = async () => {
      try {
        const dataSummary = await apiService.getDataSummary();
        dispatch(setDataSummary(dataSummary));
        dispatch(setDataLoaded(true));
        
        // Generate initial visualizations
        generateCorrelationMatrix();
        generateDashboard();
      } catch (error) {
        // Data not loaded yet, that's okay
        console.log('No data loaded yet');
      }
    };

    initializeDashboard();
  }, [dispatch]);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toLocaleString();
  };

  const getDataQualityColor = (pct: number) => {
    if (pct < 5) return 'success';
    if (pct < 15) return 'warning';
    return 'error';
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Manufacturing Correlation Analysis Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Monitor and analyze correlations in your fiber cement manufacturing data
      </Typography>

      {!dataLoaded && (
        <Alert 
          severity="info" 
          action={
            <Button 
              color="inherit" 
              size="small" 
              onClick={loadData}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={16} /> : <UploadIcon />}
            >
              Load Data
            </Button>
          }
          sx={{ mb: 3 }}
        >
          No manufacturing data loaded. Click "Load Data" to get started with sample data.
        </Alert>
      )}

      {loading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
            Loading manufacturing data...
          </Typography>
        </Box>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Total Records"
            value={summary ? formatNumber(summary.total_records) : '0'}
            subtitle="Manufacturing data points"
            icon={<DataIcon fontSize="large" />}
            color="primary"
            loading={loading}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Datasets"
            value={summary ? Object.keys(summary.datasets).length : 0}
            subtitle="Active data sources"
            icon={<AssessmentIcon fontSize="large" />}
            color="secondary"
            loading={loading}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Variables"
            value={summary ? summary.numeric_variables.length : 0}
            subtitle="Correlation candidates"
            icon={<TimelineIcon fontSize="large" />}
            color="success"
            loading={loading}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Analyses"
            value={results.length}
            subtitle="Completed correlations"
            icon={<AssessmentIcon fontSize="large" />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Dataset Details */}
      {summary && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">Dataset Overview</Typography>
                  <Button
                    startIcon={<RefreshIcon />}
                    onClick={loadData}
                    disabled={loading}
                    size="small"
                  >
                    Refresh
                  </Button>
                </Box>
                
                <Grid container spacing={2}>
                  {Object.entries(summary.datasets).map(([name, info]) => (
                    <Grid item xs={12} sm={6} md={4} key={name}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle1" gutterBottom>
                            {name.replace('_', ' ').toUpperCase()}
                          </Typography>
                          <Typography variant="h6" color="primary.main">
                            {formatNumber(info.records)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            records
                          </Typography>
                          
                          <Box display="flex" gap={0.5} flexWrap="wrap" mt={1}>
                            <Chip 
                              label={`${info.variables} vars`} 
                              size="small" 
                              variant="outlined" 
                            />
                            <Chip 
                              label={`${info.missing_data_pct.toFixed(1)}% missing`} 
                              size="small" 
                              color={getDataQualityColor(info.missing_data_pct)}
                              variant="outlined" 
                            />
                          </Box>
                          
                          {info.time_range && (
                            <Typography variant="caption" color="text.secondary" display="block" mt={1}>
                              {new Date(info.time_range.start).toLocaleDateString()} - {new Date(info.time_range.end).toLocaleDateString()}
                            </Typography>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Visualizations */}
      {dataLoaded && (
        <Grid container spacing={3}>
          {correlationMatrixPlot && (
            <Grid item xs={12} lg={6}>
              <CorrelationMatrix
                plotData={correlationMatrixPlot}
                title="Correlation Matrix"
                onRefresh={generateCorrelationMatrix}
                height={400}
              />
            </Grid>
          )}
          
          {dashboardPlot && (
            <Grid item xs={12} lg={6}>
              <CorrelationMatrix
                plotData={dashboardPlot}
                title="Analysis Dashboard"
                onRefresh={generateDashboard}
                height={400}
              />
            </Grid>
          )}
          
          {!correlationMatrixPlot && !dashboardPlot && dataLoaded && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box textAlign="center" py={4}>
                    <Typography variant="h6" gutterBottom>
                      Generate Visualizations
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Create correlation matrix and dashboard visualizations from your loaded data
                    </Typography>
                    <Box display="flex" gap={2} justifyContent="center">
                      <Button
                        variant="contained"
                        onClick={generateCorrelationMatrix}
                        startIcon={<AssessmentIcon />}
                      >
                        Correlation Matrix
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={generateDashboard}
                        startIcon={<TimelineIcon />}
                      >
                        Dashboard
                      </Button>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      )}
    </Box>
  );
};

export default Dashboard;