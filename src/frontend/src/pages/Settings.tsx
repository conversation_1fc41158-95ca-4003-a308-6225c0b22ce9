/**
 * Settings Page Component
 * 
 * Configuration interface for analysis parameters and application settings
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  Switch,
  FormControlLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const Settings: React.FC = () => {
  const { connected, dataLoaded } = useSelector((state: RootState) => state.ui);
  const { summary } = useSelector((state: RootState) => state.data);
  
  // Analysis settings
  const [significanceThreshold, setSignificanceThreshold] = useState(0.05);
  const [minCorrelation, setMinCorrelation] = useState(0.3);
  const [correlationMethod, setCorrelationMethod] = useState('pearson');
  const [timeWindow, setTimeWindow] = useState(60);
  
  // Display settings
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [exportFormat, setExportFormat] = useState('json');
  
  // API settings
  const [apiUrl, setApiUrl] = useState('http://localhost:8000');
  const [websocketUrl, setWebsocketUrl] = useState('ws://localhost:8000/ws');
  
  const [savedMessage, setSavedMessage] = useState('');

  const handleSave = () => {
    // In a real implementation, these would be saved to localStorage or backend
    const settings = {
      analysis: {
        significanceThreshold,
        minCorrelation,
        correlationMethod,
        timeWindow,
      },
      display: {
        autoRefresh,
        showAdvancedOptions,
        exportFormat,
      },
      api: {
        apiUrl,
        websocketUrl,
      },
    };
    
    localStorage.setItem('manufacturing-analysis-settings', JSON.stringify(settings));
    setSavedMessage('Settings saved successfully!');
    
    setTimeout(() => setSavedMessage(''), 3000);
  };

  const handleReset = () => {
    setSignificanceThreshold(0.05);
    setMinCorrelation(0.3);
    setCorrelationMethod('pearson');
    setTimeWindow(60);
    setAutoRefresh(false);
    setShowAdvancedOptions(false);
    setExportFormat('json');
    setApiUrl('http://localhost:8000');
    setWebsocketUrl('ws://localhost:8000/ws');
  };

  const exportSettings = () => {
    const settings = {
      analysis: {
        significanceThreshold,
        minCorrelation,
        correlationMethod,
        timeWindow,
      },
      display: {
        autoRefresh,
        showAdvancedOptions,
        exportFormat,
      },
      api: {
        apiUrl,
        websocketUrl,
      },
      metadata: {
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
      },
    };
    
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'manufacturing-analysis-settings.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Configure analysis parameters and application preferences
      </Typography>

      {savedMessage && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {savedMessage}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Analysis Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Analysis Parameters
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure default parameters for correlation analysis
              </Typography>

              <TextField
                fullWidth
                type="number"
                label="Significance Threshold"
                value={significanceThreshold}
                onChange={(e) => setSignificanceThreshold(parseFloat(e.target.value))}
                inputProps={{ min: 0, max: 1, step: 0.01 }}
                helperText="P-value threshold for statistical significance (0.01 - 0.10)"
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                type="number"
                label="Minimum Correlation"
                value={minCorrelation}
                onChange={(e) => setMinCorrelation(parseFloat(e.target.value))}
                inputProps={{ min: 0, max: 1, step: 0.1 }}
                helperText="Minimum correlation coefficient to report (0.1 - 0.9)"
                sx={{ mb: 2 }}
              />

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Correlation Method</InputLabel>
                <Select
                  value={correlationMethod}
                  label="Correlation Method"
                  onChange={(e) => setCorrelationMethod(e.target.value)}
                >
                  <MenuItem value="pearson">Pearson (Linear relationships)</MenuItem>
                  <MenuItem value="spearman">Spearman (Rank-based)</MenuItem>
                  <MenuItem value="kendall">Kendall (Robust rank-based)</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                type="number"
                label="Default Time Window (minutes)"
                value={timeWindow}
                onChange={(e) => setTimeWindow(parseInt(e.target.value))}
                inputProps={{ min: 1, max: 1440 }}
                helperText="Default time window for time-based analysis"
                sx={{ mb: 2 }}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Display Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Display & Interface
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Customize the user interface and display options
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                  />
                }
                label="Auto-refresh data"
                sx={{ mb: 2, display: 'block' }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={showAdvancedOptions}
                    onChange={(e) => setShowAdvancedOptions(e.target.checked)}
                  />
                }
                label="Show advanced options"
                sx={{ mb: 2, display: 'block' }}
              />

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Default Export Format</InputLabel>
                <Select
                  value={exportFormat}
                  label="Default Export Format"
                  onChange={(e) => setExportFormat(e.target.value)}
                >
                  <MenuItem value="json">JSON</MenuItem>
                  <MenuItem value="csv">CSV</MenuItem>
                  <MenuItem value="pdf">PDF</MenuItem>
                  <MenuItem value="html">HTML</MenuItem>
                </Select>
              </FormControl>
            </CardContent>
          </Card>
        </Grid>

        {/* API Configuration */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                API Configuration
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure connection settings for the analysis backend
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="API Base URL"
                    value={apiUrl}
                    onChange={(e) => setApiUrl(e.target.value)}
                    helperText="Backend API server URL"
                    sx={{ mb: 2 }}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="WebSocket URL"
                    value={websocketUrl}
                    onChange={(e) => setWebsocketUrl(e.target.value)}
                    helperText="Real-time communication endpoint"
                    sx={{ mb: 2 }}
                  />
                </Grid>
              </Grid>

              <Box display="flex" alignItems="center" gap={2} mt={2}>
                <Chip
                  label={connected ? "Connected" : "Disconnected"}
                  color={connected ? "success" : "error"}
                  variant="outlined"
                />
                <Chip
                  label={dataLoaded ? "Data Loaded" : "No Data"}
                  color={dataLoaded ? "success" : "default"}
                  variant="outlined"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Information
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Application Version" 
                    secondary="1.0.0" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Connection Status" 
                    secondary={connected ? "Connected" : "Disconnected"} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Data Status" 
                    secondary={dataLoaded ? `${summary?.total_records || 0} records loaded` : "No data loaded"} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Browser" 
                    secondary={navigator.userAgent.split(' ').slice(-2).join(' ')} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Last Updated" 
                    secondary={new Date().toLocaleString()} 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Data Information */}
        {dataLoaded && summary && (
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Data Information
                </Typography>
                
                <List dense>
                  <ListItem>
                    <ListItemText 
                      primary="Total Records" 
                      secondary={summary.total_records.toLocaleString()} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Datasets" 
                      secondary={Object.keys(summary.datasets).length} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Numeric Variables" 
                      secondary={summary.numeric_variables.length} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Unified Data Shape" 
                      secondary={summary.unified_data_shape ? `${summary.unified_data_shape[0]} × ${summary.unified_data_shape[1]}` : 'N/A'} 
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle2" gutterBottom>
                  Available Variables
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={0.5}>
                  {summary.numeric_variables.map(variable => (
                    <Chip
                      key={variable}
                      label={variable}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Actions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Actions
              </Typography>
              
              <Box display="flex" gap={2} flexWrap="wrap">
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                >
                  Save Settings
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={handleReset}
                >
                  Reset to Defaults
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={exportSettings}
                >
                  Export Settings
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  component="label"
                >
                  Import Settings
                  <input
                    type="file"
                    hidden
                    accept=".json"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                          try {
                            const settings = JSON.parse(event.target?.result as string);
                            // Apply imported settings
                            if (settings.analysis) {
                              setSignificanceThreshold(settings.analysis.significanceThreshold || 0.05);
                              setMinCorrelation(settings.analysis.minCorrelation || 0.3);
                              setCorrelationMethod(settings.analysis.correlationMethod || 'pearson');
                              setTimeWindow(settings.analysis.timeWindow || 60);
                            }
                            if (settings.display) {
                              setAutoRefresh(settings.display.autoRefresh || false);
                              setShowAdvancedOptions(settings.display.showAdvancedOptions || false);
                              setExportFormat(settings.display.exportFormat || 'json');
                            }
                            if (settings.api) {
                              setApiUrl(settings.api.apiUrl || 'http://localhost:8000');
                              setWebsocketUrl(settings.api.websocketUrl || 'ws://localhost:8000/ws');
                            }
                            setSavedMessage('Settings imported successfully!');
                            setTimeout(() => setSavedMessage(''), 3000);
                          } catch (error) {
                            console.error('Error importing settings:', error);
                          }
                        };
                        reader.readAsText(file);
                      }
                    }}
                  />
                </Button>
              </Box>

              <Alert severity="info" sx={{ mt: 2 }} icon={<InfoIcon />}>
                Settings are automatically saved to your browser's local storage. 
                Use export/import to share settings between devices or browsers.
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;