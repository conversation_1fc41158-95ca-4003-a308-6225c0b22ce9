/**
 * Analysis Page Component
 * 
 * Interface for running correlation analysis queries
 */

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Send as SendIcon,
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  Lightbulb as LightbulbIcon,
  Recommend as RecommendIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { addAnalysisResult, addToHistory } from '@/store/slices/analysisSlice';
import { showError } from '@/store/slices/uiSlice';

// Services
import apiService from '@/services/api';
import { AnalysisRequest, AnalysisResults } from '@/types';

const Analysis: React.FC = () => {
  const dispatch = useDispatch();
  const { dataLoaded } = useSelector((state: RootState) => state.ui);
  const { summary } = useSelector((state: RootState) => state.data);
  const { results, history } = useSelector((state: RootState) => state.analysis);
  
  const [query, setQuery] = useState('');
  const [analysisType, setAnalysisType] = useState<string>('general');
  const [significanceThreshold, setSignificanceThreshold] = useState(0.05);
  const [minCorrelation, setMinCorrelation] = useState(0.3);
  const [loading, setLoading] = useState(false);
  const [currentResults, setCurrentResults] = useState<AnalysisResults | null>(null);

  const analysisTypes = [
    { value: 'general', label: 'General Correlation Analysis' },
    { value: 'lag', label: 'Lag-based Correlation Analysis' },
    { value: 'quality', label: 'Quality-focused Analysis' },
    { value: 'optimization', label: 'Process Optimization Analysis' },
    { value: 'rca', label: 'Root Cause Analysis' },
  ];

  const suggestedQueries = [
    "What correlations exist between speed and thickness?",
    "Analyze the impact of stoppages on scrap rates",
    "Show me correlations above 0.5 with high significance",
    "Find relationships between temperature and quality measures",
    "Identify process variables that predict equipment failures",
    "What variables correlate with production efficiency?",
  ];

  const handleSubmit = async () => {
    if (!query.trim()) {
      dispatch(showError('Please enter a query'));
      return;
    }

    if (!dataLoaded) {
      dispatch(showError('Please load data first'));
      return;
    }

    const request: AnalysisRequest = {
      query: query.trim(),
      analysis_type: analysisType,
      significance_threshold: significanceThreshold,
      min_correlation: minCorrelation,
    };

    try {
      setLoading(true);
      const results = await apiService.analyzeCorrelations(request);
      
      // Add to store
      dispatch(addAnalysisResult(results));
      dispatch(addToHistory(request));
      
      // Set current results
      setCurrentResults(results);
      
    } catch (error) {
      dispatch(showError(`Analysis failed: ${error}`));
    } finally {
      setLoading(false);
    }
  };

  const getSignificanceColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'success';
      case 'medium':
        return 'warning';
      case 'low':
        return 'error';
      default:
        return 'default';
    }
  };

  const getConfidenceColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'success';
      case 'medium':
        return 'warning';
      case 'low':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Correlation Analysis
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Ask natural language questions about correlations in your manufacturing data
      </Typography>

      {!dataLoaded && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Please load manufacturing data first from the Dashboard to run correlation analysis.
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Analysis Form */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Analysis Query
              </Typography>

              <Box component="form" noValidate sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Enter your analysis question"
                  placeholder="e.g., What correlations exist between speed and thickness?"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  disabled={!dataLoaded || loading}
                  sx={{ mb: 2 }}
                />

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Analysis Type</InputLabel>
                      <Select
                        value={analysisType}
                        label="Analysis Type"
                        onChange={(e) => setAnalysisType(e.target.value)}
                        disabled={!dataLoaded || loading}
                      >
                        {analysisTypes.map((type) => (
                          <MenuItem key={type.value} value={type.value}>
                            {type.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      type="number"
                      label="Min Correlation"
                      value={minCorrelation}
                      onChange={(e) => setMinCorrelation(parseFloat(e.target.value))}
                      inputProps={{ min: 0, max: 1, step: 0.1 }}
                      disabled={!dataLoaded || loading}
                    />
                  </Grid>
                </Grid>

                <TextField
                  fullWidth
                  type="number"
                  label="Significance Threshold"
                  value={significanceThreshold}
                  onChange={(e) => setSignificanceThreshold(parseFloat(e.target.value))}
                  inputProps={{ min: 0, max: 1, step: 0.01 }}
                  disabled={!dataLoaded || loading}
                  sx={{ mb: 3 }}
                />

                <Button
                  fullWidth
                  variant="contained"
                  size="large"
                  onClick={handleSubmit}
                  disabled={!dataLoaded || loading || !query.trim()}
                  startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                >
                  {loading ? 'Analyzing...' : 'Run Analysis'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Suggested Queries */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Suggested Queries
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {suggestedQueries.map((suggestion, index) => (
                  <Chip
                    key={index}
                    label={suggestion}
                    variant="outlined"
                    onClick={() => setQuery(suggestion)}
                    disabled={!dataLoaded || loading}
                    sx={{ mb: 1 }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Results Display */}
        <Grid item xs={12} lg={6}>
          {currentResults && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Analysis Results
                </Typography>

                {/* Data Quality Score */}
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Data Quality Score
                  </Typography>
                  <Typography variant="h4" color="primary.main">
                    {(currentResults.data_quality_score * 100).toFixed(1)}%
                  </Typography>
                </Box>

                {/* Significant Correlations */}
                <Accordion defaultExpanded>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">
                      <TrendingUpIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Significant Correlations ({currentResults.significant_correlations.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {currentResults.significant_correlations.length > 0 ? (
                      <TableContainer component={Paper} variant="outlined">
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Variable 1</TableCell>
                              <TableCell>Variable 2</TableCell>
                              <TableCell align="right">Correlation</TableCell>
                              <TableCell align="right">P-value</TableCell>
                              <TableCell>Significance</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {currentResults.significant_correlations.slice(0, 10).map((corr, index) => (
                              <TableRow key={index}>
                                <TableCell>{corr.variable_1}</TableCell>
                                <TableCell>{corr.variable_2}</TableCell>
                                <TableCell align="right">
                                  <Chip
                                    label={corr.correlation_coefficient.toFixed(3)}
                                    size="small"
                                    color={Math.abs(corr.correlation_coefficient) > 0.5 ? 'success' : 'default'}
                                  />
                                </TableCell>
                                <TableCell align="right">{corr.p_value.toFixed(4)}</TableCell>
                                <TableCell>
                                  <Chip
                                    label={corr.significance_level}
                                    size="small"
                                    color={getSignificanceColor(corr.significance_level)}
                                  />
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    ) : (
                      <Alert severity="info">
                        No significant correlations found with the current criteria.
                      </Alert>
                    )}
                  </AccordionDetails>
                </Accordion>

                {/* Insights */}
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">
                      <LightbulbIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Key Insights ({currentResults.insights.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {currentResults.insights.length > 0 ? (
                      <List>
                        {currentResults.insights.map((insight, index) => (
                          <ListItem key={index} divider>
                            <ListItemIcon>
                              <Chip
                                label={insight.confidence_level}
                                size="small"
                                color={getConfidenceColor(insight.confidence_level)}
                              />
                            </ListItemIcon>
                            <ListItemText
                              primary={insight.description}
                              secondary={insight.actionable_recommendation}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Alert severity="info">
                        No insights generated for this analysis.
                      </Alert>
                    )}
                  </AccordionDetails>
                </Accordion>

                {/* Recommendations */}
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">
                      <RecommendIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Recommendations ({currentResults.recommendations.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {currentResults.recommendations.length > 0 ? (
                      <List>
                        {currentResults.recommendations.map((recommendation, index) => (
                          <ListItem key={index} divider>
                            <ListItemText primary={`${index + 1}. ${recommendation}`} />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Alert severity="info">
                        No recommendations available for this analysis.
                      </Alert>
                    )}
                  </AccordionDetails>
                </Accordion>
              </CardContent>
            </Card>
          )}

          {!currentResults && !loading && (
            <Card>
              <CardContent>
                <Box textAlign="center" py={4}>
                  <AnalyticsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Ready for Analysis
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Enter a query and run analysis to see results here
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Analysis History */}
        {results.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Analyses ({results.length})
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Query</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell align="right">Correlations Found</TableCell>
                        <TableCell align="right">Data Quality</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {results.slice(-10).reverse().map((result, index) => (
                        <TableRow key={result.analysis_id} hover>
                          <TableCell>
                            <Typography variant="body2">
                              {history[history.length - 1 - index]?.query || 'Unknown query'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip 
                              label={history[history.length - 1 - index]?.analysis_type || 'general'} 
                              size="small" 
                              variant="outlined" 
                            />
                          </TableCell>
                          <TableCell align="right">
                            {result.significant_correlations.length}
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={`${(result.data_quality_score * 100).toFixed(1)}%`}
                              size="small"
                              color={result.data_quality_score > 0.8 ? 'success' : result.data_quality_score > 0.6 ? 'warning' : 'error'}
                            />
                          </TableCell>
                          <TableCell>
                            <Button
                              size="small"
                              onClick={() => setCurrentResults(result)}
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default Analysis;