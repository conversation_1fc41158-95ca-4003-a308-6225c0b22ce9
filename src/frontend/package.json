{"name": "manufacturing-correlation-ui", "version": "1.0.0", "description": "React frontend for Manufacturing Correlation Analysis", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.0", "@mui/lab": "^5.0.0-alpha.157", "@mui/material": "^5.15.0", "@reduxjs/toolkit": "^2.0.1", "@types/node": "^24.0.10", "axios": "^1.6.2", "date-fns": "^2.30.0", "file-saver": "^2.0.5", "lodash": "^4.17.21", "plotly.js": "^2.27.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-plotly.js": "^2.6.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/plotly.js": "^2.12.29", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-plotly.js": "^2.6.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.3.3", "vite": "^5.0.8"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}