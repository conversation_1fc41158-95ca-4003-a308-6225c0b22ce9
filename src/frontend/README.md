# Manufacturing Correlation Analysis - Web UI

Modern React-based web interface for manufacturing correlation analysis with interactive visualizations.

## Features

- 📊 **Interactive Dashboards** - Real-time correlation matrices, scatter plots, and network visualizations
- 🤖 **AI-Powered Analysis** - Natural language queries for correlation discovery
- 📈 **Advanced Visualizations** - Plotly.js interactive charts with export capabilities
- ⚡ **Real-time Updates** - WebSocket connection for live data updates
- 🎨 **Material Design** - Professional manufacturing dashboard aesthetics
- 📱 **Responsive Design** - Works on desktop and tablet devices

## Technology Stack

- **Frontend**: React 18 + TypeScript + Material-UI
- **State Management**: Redux Toolkit
- **Charts**: Plotly.js + React-Plotly.js
- **Build Tool**: Vite
- **Backend**: FastAPI + WebSocket

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- Python 3.9+ with the backend server running

### Installation

1. Navigate to the frontend directory:
```bash
cd src/frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open http://localhost:3000 in your browser

### Backend Connection

The frontend expects the FastAPI backend to be running on `http://localhost:8000`. 

Start the backend server:
```bash
# From project root
python run_web_server.py
```

## Project Structure

```
src/frontend/
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── Charts/        # Visualization components
│   │   └── Layout/        # Layout components
│   ├── pages/             # Main application pages
│   ├── services/          # API and WebSocket services
│   ├── store/             # Redux store and slices
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
├── public/                # Static assets
└── package.json           # Dependencies and scripts
```

## Key Components

### Charts
- **CorrelationMatrix** - Interactive heatmap with significance masking
- **ScatterPlot** - Correlation scatter plots with trend lines
- **NetworkGraph** - Correlation network visualization
- **Dashboard** - Multi-plot comprehensive view

### Pages
- **Dashboard** - Overview with data summary and key visualizations
- **Analysis** - Natural language query interface for correlation analysis
- **Plots** - Visualization gallery and plot management
- **Settings** - Configuration for analysis parameters and UI preferences

### Services
- **API Service** - REST API communication with FastAPI backend
- **WebSocket Service** - Real-time updates and notifications

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

### Environment Variables

Create a `.env.local` file in the frontend directory:

```bash
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws
```

### Adding New Visualizations

1. Create a new chart component in `src/components/Charts/`
2. Add the plot type to the backend API
3. Update the plot request types in `src/types/`
4. Add the component to the plots gallery

Example:
```typescript
// src/components/Charts/MyNewChart.tsx
import React from 'react';
import { PlotComponentProps } from '@/types';

const MyNewChart: React.FC<PlotComponentProps> = ({ plotData }) => {
  // Implementation
};

export default MyNewChart;
```

## API Integration

The frontend communicates with the FastAPI backend through:

1. **REST API** - Data loading, analysis requests, plot generation
2. **WebSocket** - Real-time updates, status notifications

### API Endpoints Used

- `GET /health` - Health check
- `POST /data/load` - Load manufacturing data
- `GET /data/summary` - Get data summary
- `POST /analysis/correlations` - Run correlation analysis
- `POST /plots/generate` - Generate visualizations
- `GET /plots/available` - Get available plot types
- `WS /ws` - WebSocket connection

## Deployment

### Production Build

```bash
npm run build
```

The build output will be in the `dist/` directory.

### Serve with Backend

The FastAPI server can serve the React build:

```python
from fastapi.staticfiles import StaticFiles

app.mount("/", StaticFiles(directory="src/frontend/dist", html=True), name="frontend")
```

## Troubleshooting

### Common Issues

1. **Backend Connection Failed**
   - Ensure FastAPI server is running on port 8000
   - Check CORS configuration in backend

2. **WebSocket Connection Failed**
   - Verify WebSocket URL configuration
   - Check firewall settings

3. **Plots Not Loading**
   - Ensure data is loaded in the backend
   - Check browser console for errors

4. **Build Errors**
   - Run `npm install` to update dependencies
   - Check TypeScript errors with `npm run type-check`

### Debug Mode

Enable debug logging by setting:
```typescript
localStorage.setItem('debug', 'true');
```

## Contributing

1. Follow the existing code style and structure
2. Add TypeScript types for new features
3. Include proper error handling
4. Test with the backend API
5. Update documentation as needed

## Performance

The UI is optimized for:
- Large datasets (295K+ records tested)
- Interactive visualizations with 1000+ data points
- Real-time updates without blocking the UI
- Memory management for plot caching

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+