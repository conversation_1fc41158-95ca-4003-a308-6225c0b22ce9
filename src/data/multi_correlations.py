"""
Multi-Method Correlation Analysis Engine for Manufacturing Data

Comprehensive multi-method correlation analysis supporting <PERSON>, <PERSON><PERSON><PERSON>, 
and <PERSON> correlation methods with comparative reporting, data distribution 
assessment, and method convergence analysis for manufacturing time series data.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from scipy.stats import pearsonr, spearmanr, kendalltau, jarque_bera
import logging
from pydantic import BaseModel, field_validator
import warnings

# Import the base analyzer to extend it
from .correlations import ManufacturingCorrelationAnalyzer

warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MultiMethodCorrelationResult(BaseModel):
    """Data model for multi-method correlation analysis results"""
    variable_1: str
    variable_2: str
    pearson_correlation: float
    spearman_correlation: float
    kendall_correlation: float
    pearson_p_value: float
    spearman_p_value: float
    kendall_p_value: float
    sample_size: int
    method_convergence_score: float  # How similar are the three methods (0-1)
    recommended_method: str          # Based on data characteristics
    data_distribution_assessment: Dict[str, Any]
    pearson_confidence_interval: Tuple[float, float]
    spearman_confidence_interval: Tuple[float, float]
    kendall_confidence_interval: Tuple[float, float]
    interpretation: Dict[str, str]   # Interpretation for each method
    
    @field_validator('pearson_correlation', 'spearman_correlation', 'kendall_correlation')
    @classmethod
    def validate_correlation_range(cls, v):
        """Validate correlation coefficients are in valid range [-1, 1]"""
        if not (-1 <= v <= 1):
            raise ValueError(f"Correlation coefficient must be between -1 and 1, got {v}")
        return v
    
    @field_validator('pearson_p_value', 'spearman_p_value', 'kendall_p_value')
    @classmethod
    def validate_p_value_range(cls, v):
        """Validate p-values are in valid range [0, 1]"""
        if not (0 <= v <= 1):
            raise ValueError(f"P-value must be between 0 and 1, got {v}")
        return v
    
    @field_validator('method_convergence_score')
    @classmethod
    def validate_convergence_score(cls, v):
        """Validate convergence score is in valid range [0, 1]"""
        if not (0 <= v <= 1):
            raise ValueError(f"Convergence score must be between 0 and 1, got {v}")
        return v


class MethodComparisonAnalysis(BaseModel):
    """Data model for comprehensive method comparison analysis"""
    correlation_results: List[MultiMethodCorrelationResult]
    method_stability_analysis: Dict[str, float]  # Variance across methods
    data_quality_assessment: Dict[str, Any]
    overall_convergence_score: float  # Overall agreement between methods
    method_recommendations: Dict[str, str]  # Recommendations by variable pair
    robustness_metrics: Dict[str, Any]  # Robustness analysis results
    analysis_metadata: Dict[str, Any]


class MultiMethodCorrelationAnalyzer(ManufacturingCorrelationAnalyzer):
    """
    Extended correlation analyzer supporting multiple correlation methods
    with comparative analysis and intelligent method selection.
    """
    
    def __init__(self, significance_level: float = 0.05):
        """
        Initialize the multi-method correlation analyzer.
        
        Args:
            significance_level: Statistical significance threshold
        """
        super().__init__(significance_level)
        
        # Additional configuration for multi-method analysis
        self.method_weights = {
            'pearson': 1.0,
            'spearman': 1.0, 
            'kendall': 0.8  # Slightly lower weight due to computational complexity
        }
        
        # Thresholds for method selection
        self.normality_threshold = 0.05  # p-value threshold for normality tests
        self.outlier_threshold = 0.1     # Proportion of outliers to consider data contaminated
        self.linearity_threshold = 0.8   # R² threshold for linearity assessment
    
    def calculate_multi_method_correlations(self, 
                                          df: pd.DataFrame,
                                          variables: Optional[List[str]] = None,
                                          min_periods: int = 30) -> Dict[str, MultiMethodCorrelationResult]:
        """
        Calculate correlations using all three methods with comprehensive analysis.
        
        Args:
            df: DataFrame with numeric data
            variables: Specific variables to analyze (None for all numeric)
            min_periods: Minimum number of observations required
            
        Returns:
            Dictionary of MultiMethodCorrelationResult objects
        """
        # Select variables
        if variables:
            available_vars = [var for var in variables if var in df.columns]
            if not available_vars:
                logger.warning(f"None of the requested variables {variables} found in data")
                return {}
            numeric_df = df[available_vars].select_dtypes(include=[np.number])
        else:
            numeric_df = df.select_dtypes(include=[np.number])
        
        if numeric_df.empty:
            logger.warning("No numeric columns found for multi-method correlation analysis")
            return {}
        
        results = {}
        columns = numeric_df.columns.tolist()
        
        for i, col1 in enumerate(columns):
            for col2 in columns[i+1:]:
                # Clean data (remove NaN values)
                clean_data = numeric_df[[col1, col2]].dropna()
                
                if len(clean_data) < min_periods:
                    logger.debug(f"Insufficient data for {col1}-{col2}: {len(clean_data)} < {min_periods}")
                    continue
                
                try:
                    # Calculate all three correlation methods
                    pearson_r, pearson_p = pearsonr(clean_data[col1], clean_data[col2])
                    spearman_r, spearman_p = spearmanr(clean_data[col1], clean_data[col2])
                    kendall_r, kendall_p = kendalltau(clean_data[col1], clean_data[col2])
                    
                    # Skip if any correlation is NaN
                    if any(pd.isna(val) for val in [pearson_r, spearman_r, kendall_r]):
                        continue
                    
                    # Calculate confidence intervals for all methods
                    pearson_ci = self._calculate_confidence_interval(pearson_r, len(clean_data))
                    spearman_ci = self._calculate_confidence_interval(spearman_r, len(clean_data))
                    kendall_ci = self._calculate_confidence_interval(kendall_r, len(clean_data))
                    
                    # Assess data distribution characteristics
                    distribution_assessment = self._assess_data_distribution(
                        clean_data[col1], clean_data[col2]
                    )
                    
                    # Calculate method convergence score
                    correlations = [pearson_r, spearman_r, kendall_r]
                    method_variance = np.var(correlations)
                    convergence_score = max(0, 1 - min(method_variance * 4, 1))  # Scale to [0,1]
                    
                    # Recommend optimal method based on data characteristics
                    recommended_method = self._recommend_correlation_method(
                        clean_data[col1], clean_data[col2], distribution_assessment
                    )
                    
                    # Generate interpretations for each method
                    interpretations = {
                        'pearson': self._generate_interpretation(pearson_r, pearson_p, 'pearson'),
                        'spearman': self._generate_interpretation(spearman_r, spearman_p, 'spearman'),
                        'kendall': self._generate_interpretation(kendall_r, kendall_p, 'kendall')
                    }
                    
                    # Create result object
                    result = MultiMethodCorrelationResult(
                        variable_1=col1,
                        variable_2=col2,
                        pearson_correlation=round(pearson_r, 8),
                        spearman_correlation=round(spearman_r, 8),
                        kendall_correlation=round(kendall_r, 8),
                        pearson_p_value=round(pearson_p, 10) if not pd.isna(pearson_p) else 0.0,
                        spearman_p_value=round(spearman_p, 10) if not pd.isna(spearman_p) else 0.0,
                        kendall_p_value=round(kendall_p, 10) if not pd.isna(kendall_p) else 0.0,
                        sample_size=len(clean_data),
                        method_convergence_score=round(convergence_score, 6),
                        recommended_method=recommended_method,
                        data_distribution_assessment=distribution_assessment,
                        pearson_confidence_interval=pearson_ci,
                        spearman_confidence_interval=spearman_ci,
                        kendall_confidence_interval=kendall_ci,
                        interpretation=interpretations
                    )
                    
                    results[f"{col1}_{col2}"] = result
                    
                except Exception as e:
                    logger.warning(f"Failed to analyze correlation between {col1} and {col2}: {e}")
                    continue
        
        return results
    
    def analyze_method_convergence(self, 
                                 correlation_results: Dict[str, MultiMethodCorrelationResult]) -> Dict[str, Any]:
        """
        Analyze convergence and stability across correlation methods.
        
        Args:
            correlation_results: Dictionary of multi-method correlation results
            
        Returns:
            Dictionary with convergence analysis results
        """
        if not correlation_results:
            return {"error": "No correlation results provided for convergence analysis"}
        
        # Extract correlation values for each method
        pearson_values = []
        spearman_values = []
        kendall_values = []
        convergence_scores = []
        
        for result in correlation_results.values():
            pearson_values.append(result.pearson_correlation)
            spearman_values.append(result.spearman_correlation)
            kendall_values.append(result.kendall_correlation)
            convergence_scores.append(result.method_convergence_score)
        
        # Calculate method stability metrics
        pearson_std = np.std(pearson_values)
        spearman_std = np.std(spearman_values)
        kendall_std = np.std(kendall_values)
        
        # Cross-method correlations
        pearson_spearman_corr = np.corrcoef(pearson_values, spearman_values)[0, 1]
        pearson_kendall_corr = np.corrcoef(pearson_values, kendall_values)[0, 1]
        spearman_kendall_corr = np.corrcoef(spearman_values, kendall_values)[0, 1]
        
        # Overall convergence metrics
        overall_convergence = np.mean(convergence_scores)
        convergence_std = np.std(convergence_scores)
        
        # Method agreement analysis
        high_convergence_pairs = sum(1 for score in convergence_scores if score > 0.8)
        low_convergence_pairs = sum(1 for score in convergence_scores if score < 0.3)
        
        return {
            "overall_convergence_score": round(overall_convergence, 6),
            "convergence_standard_deviation": round(convergence_std, 6),
            "method_stability": {
                "pearson_stability": round(1 - min(pearson_std, 1), 6),
                "spearman_stability": round(1 - min(spearman_std, 1), 6),
                "kendall_stability": round(1 - min(kendall_std, 1), 6)
            },
            "cross_method_correlations": {
                "pearson_spearman": round(pearson_spearman_corr, 6),
                "pearson_kendall": round(pearson_kendall_corr, 6),
                "spearman_kendall": round(spearman_kendall_corr, 6)
            },
            "convergence_distribution": {
                "high_convergence_pairs": high_convergence_pairs,
                "medium_convergence_pairs": len(correlation_results) - high_convergence_pairs - low_convergence_pairs,
                "low_convergence_pairs": low_convergence_pairs,
                "convergence_rate": round(high_convergence_pairs / len(correlation_results), 3)
            },
            "method_statistics": {
                "pearson": {
                    "mean": round(np.mean(pearson_values), 6),
                    "std": round(pearson_std, 6),
                    "min": round(np.min(pearson_values), 6),
                    "max": round(np.max(pearson_values), 6)
                },
                "spearman": {
                    "mean": round(np.mean(spearman_values), 6),
                    "std": round(spearman_std, 6),
                    "min": round(np.min(spearman_values), 6),
                    "max": round(np.max(spearman_values), 6)
                },
                "kendall": {
                    "mean": round(np.mean(kendall_values), 6),
                    "std": round(kendall_std, 6),
                    "min": round(np.min(kendall_values), 6),
                    "max": round(np.max(kendall_values), 6)
                }
            }
        }
    
    def _assess_data_distribution(self, x: pd.Series, y: pd.Series) -> Dict[str, Any]:
        """
        Assess data distribution characteristics for method recommendation.
        
        Args:
            x: First variable data
            y: Second variable data
            
        Returns:
            Dictionary with distribution assessment results
        """
        assessment = {}
        
        # Normality tests
        try:
            # Jarque-Bera test for normality
            x_jb_stat, x_jb_p = jarque_bera(x)
            y_jb_stat, y_jb_p = jarque_bera(y)
            
            assessment['normality'] = {
                'x_variable': {
                    'jarque_bera_statistic': round(x_jb_stat, 6),
                    'jarque_bera_p_value': round(x_jb_p, 8),
                    'is_normal': bool(x_jb_p > self.normality_threshold)
                },
                'y_variable': {
                    'jarque_bera_statistic': round(y_jb_stat, 6),
                    'jarque_bera_p_value': round(y_jb_p, 8),
                    'is_normal': bool(y_jb_p > self.normality_threshold)
                }
            }
        except Exception:
            assessment['normality'] = {'error': 'Failed to perform normality tests'}
        
        # Outlier detection using IQR method
        try:
            x_outliers = self._detect_outliers_iqr(x)
            y_outliers = self._detect_outliers_iqr(y)
            
            assessment['outliers'] = {
                'x_variable': {
                    'outlier_count': len(x_outliers),
                    'outlier_percentage': round(len(x_outliers) / len(x) * 100, 2),
                    'has_significant_outliers': bool(len(x_outliers) / len(x) > self.outlier_threshold)
                },
                'y_variable': {
                    'outlier_count': len(y_outliers),
                    'outlier_percentage': round(len(y_outliers) / len(y) * 100, 2),
                    'has_significant_outliers': bool(len(y_outliers) / len(y) > self.outlier_threshold)
                }
            }
        except Exception:
            assessment['outliers'] = {'error': 'Failed to detect outliers'}
        
        # Linearity assessment
        try:
            # Simple linear regression R²
            correlation = np.corrcoef(x, y)[0, 1]
            r_squared = correlation ** 2
            
            assessment['linearity'] = {
                'r_squared': round(r_squared, 6),
                'is_linear': bool(r_squared > self.linearity_threshold),
                'linearity_strength': 'strong' if r_squared > 0.8 else 'moderate' if r_squared > 0.5 else 'weak'
            }
        except Exception:
            assessment['linearity'] = {'error': 'Failed to assess linearity'}
        
        # Monotonicity assessment (for Spearman recommendation)
        try:
            # Check if relationship is monotonic by comparing signs of consecutive differences
            x_sorted = x.sort_values()
            y_reordered = y.reindex(x_sorted.index)
            
            y_diffs = np.diff(y_reordered.values)
            monotonic_increasing = bool(np.all(y_diffs >= 0))
            monotonic_decreasing = bool(np.all(y_diffs <= 0))
            
            assessment['monotonicity'] = {
                'is_monotonic': bool(monotonic_increasing or monotonic_decreasing),
                'direction': 'increasing' if monotonic_increasing else 'decreasing' if monotonic_decreasing else 'non_monotonic'
            }
        except Exception:
            assessment['monotonicity'] = {'error': 'Failed to assess monotonicity'}
        
        return assessment
    
    def _detect_outliers_iqr(self, data: pd.Series) -> List[int]:
        """Detect outliers using the IQR method."""
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outlier_indices = data[(data < lower_bound) | (data > upper_bound)].index.tolist()
        return outlier_indices
    
    def _recommend_correlation_method(self, 
                                    x: pd.Series, 
                                    y: pd.Series, 
                                    distribution_assessment: Dict[str, Any]) -> str:
        """
        Recommend the most appropriate correlation method based on data characteristics.
        
        Args:
            x: First variable data
            y: Second variable data
            distribution_assessment: Results from distribution assessment
            
        Returns:
            Recommended correlation method ('pearson', 'spearman', or 'kendall')
        """
        try:
            # Check normality
            x_normal = distribution_assessment.get('normality', {}).get('x_variable', {}).get('is_normal', False)
            y_normal = distribution_assessment.get('normality', {}).get('y_variable', {}).get('is_normal', False)
            
            # Check outliers
            x_outliers = distribution_assessment.get('outliers', {}).get('x_variable', {}).get('has_significant_outliers', False)
            y_outliers = distribution_assessment.get('outliers', {}).get('y_variable', {}).get('has_significant_outliers', False)
            
            # Check linearity
            is_linear = distribution_assessment.get('linearity', {}).get('is_linear', False)
            
            # Check monotonicity
            is_monotonic = distribution_assessment.get('monotonicity', {}).get('is_monotonic', False)
            
            # Decision logic
            if x_outliers or y_outliers:
                # Significant outliers present - prefer robust methods
                if is_monotonic:
                    return 'spearman'  # Robust and handles monotonic relationships
                else:
                    return 'kendall'   # Most robust to outliers
            
            elif x_normal and y_normal and is_linear:
                # Normal distributions and linear relationship - Pearson is optimal
                return 'pearson'
            
            elif is_monotonic:
                # Monotonic but not necessarily linear - Spearman is appropriate
                return 'spearman'
            
            else:
                # Non-linear, non-monotonic - Kendall for general association
                return 'kendall'
                
        except Exception as e:
            logger.warning(f"Failed to recommend correlation method: {e}")
            return 'pearson'  # Default fallback
    
    def _generate_interpretation(self, correlation: float, p_value: float, method: str) -> str:
        """Generate human-readable interpretation for correlation result."""
        # Determine significance
        if p_value < 0.001:
            significance = 'highly significant'
        elif p_value < 0.01:
            significance = 'very significant'
        elif p_value < self.significance_level:
            significance = 'significant'
        else:
            significance = 'not significant'
        
        # Determine strength
        abs_corr = abs(correlation)
        if abs_corr > 0.8:
            strength = 'very strong'
        elif abs_corr > 0.6:
            strength = 'strong'
        elif abs_corr > 0.4:
            strength = 'moderate'
        elif abs_corr > 0.2:
            strength = 'weak'
        else:
            strength = 'very weak'
        
        # Determine direction
        direction = 'positive' if correlation > 0 else 'negative'
        
        # Method-specific context
        method_context = {
            'pearson': 'linear',
            'spearman': 'monotonic',
            'kendall': 'ordinal'
        }
        
        relationship_type = method_context.get(method, 'association')
        
        return f"{strength.title()} {direction} {relationship_type} relationship ({significance})"
    
    def calculate_robustness_metrics(self, 
                                   df: pd.DataFrame,
                                   correlation_results: Dict[str, MultiMethodCorrelationResult],
                                   bootstrap_samples: int = 100) -> Dict[str, Any]:
        """
        Calculate robustness metrics through bootstrap analysis.
        
        Args:
            df: Original DataFrame
            correlation_results: Multi-method correlation results
            bootstrap_samples: Number of bootstrap samples
            
        Returns:
            Dictionary with robustness analysis results
        """
        robustness_metrics = {}
        
        for pair_key, result in correlation_results.items():
            var1, var2 = result.variable_1, result.variable_2
            
            try:
                # Get clean data
                clean_data = df[[var1, var2]].dropna()
                if len(clean_data) < 30:  # Need sufficient data for bootstrap
                    continue
                
                # Bootstrap analysis
                bootstrap_results = {
                    'pearson': [],
                    'spearman': [],
                    'kendall': []
                }
                
                np.random.seed(42)  # For reproducibility
                for _ in range(bootstrap_samples):
                    # Bootstrap sample
                    bootstrap_indices = np.random.choice(len(clean_data), len(clean_data), replace=True)
                    bootstrap_sample = clean_data.iloc[bootstrap_indices]
                    
                    # Calculate correlations for bootstrap sample
                    try:
                        pearson_r, _ = pearsonr(bootstrap_sample[var1], bootstrap_sample[var2])
                        spearman_r, _ = spearmanr(bootstrap_sample[var1], bootstrap_sample[var2])
                        kendall_r, _ = kendalltau(bootstrap_sample[var1], bootstrap_sample[var2])
                        
                        if not pd.isna(pearson_r):
                            bootstrap_results['pearson'].append(pearson_r)
                        if not pd.isna(spearman_r):
                            bootstrap_results['spearman'].append(spearman_r)
                        if not pd.isna(kendall_r):
                            bootstrap_results['kendall'].append(kendall_r)
                    except Exception:
                        continue
                
                # Calculate robustness metrics
                pair_metrics = {}
                for method in ['pearson', 'spearman', 'kendall']:
                    if bootstrap_results[method]:
                        values = bootstrap_results[method]
                        pair_metrics[method] = {
                            'bootstrap_mean': round(np.mean(values), 6),
                            'bootstrap_std': round(np.std(values), 6),
                            'bootstrap_ci_lower': round(np.percentile(values, 2.5), 6),
                            'bootstrap_ci_upper': round(np.percentile(values, 97.5), 6),
                            'stability_score': round(1 - min(np.std(values), 1), 6)
                        }
                
                robustness_metrics[pair_key] = pair_metrics
                
            except Exception as e:
                logger.warning(f"Failed to calculate robustness metrics for {pair_key}: {e}")
                continue
        
        return robustness_metrics


def create_sample_multi_method_analysis() -> Dict[str, Any]:
    """
    Create sample data for testing multi-method correlation analysis.
    
    Returns:
        Dictionary with sample data and analysis results
    """
    # Create sample manufacturing data with different characteristics
    np.random.seed(42)
    n_samples = 1000
    
    # Linear relationship (good for Pearson)
    speed = np.random.normal(150, 10, n_samples)
    temperature = 80 + 0.2 * speed + np.random.normal(0, 2, n_samples)
    
    # Non-linear but monotonic (good for Spearman)
    pressure_base = np.random.normal(50, 5, n_samples)
    thickness = 12 + 0.1 * np.sqrt(np.abs(pressure_base)) + np.random.normal(0, 0.3, n_samples)
    
    # Ordinal-like relationship with outliers (good for Kendall)
    quality_base = np.random.normal(85, 8, n_samples)
    # Add some outliers
    outlier_indices = np.random.choice(n_samples, size=50, replace=False)
    quality_base[outlier_indices] += np.random.normal(0, 20, 50)
    scrap_rate = np.where(quality_base < 70, 1, np.where(quality_base < 80, 0.5, 0.1))
    scrap_rate += np.random.normal(0, 0.1, n_samples)
    
    # Create DataFrame
    df = pd.DataFrame({
        'speed': speed,
        'temperature': temperature,
        'pressure': pressure_base,
        'thickness': thickness,
        'quality_score': quality_base,
        'scrap_rate': np.clip(scrap_rate, 0, 1)
    })
    
    # Initialize analyzer and run analysis
    analyzer = MultiMethodCorrelationAnalyzer()
    
    # Calculate multi-method correlations
    correlation_results = analyzer.calculate_multi_method_correlations(df)
    
    # Analyze method convergence
    convergence_analysis = analyzer.analyze_method_convergence(correlation_results)
    
    # Calculate robustness metrics
    robustness_metrics = analyzer.calculate_robustness_metrics(df, correlation_results)
    
    return {
        'sample_data': df,
        'correlation_results': correlation_results,
        'convergence_analysis': convergence_analysis,
        'robustness_metrics': robustness_metrics,
        'analyzer': analyzer
    }


if __name__ == "__main__":
    # Example usage and testing
    print("Testing Multi-Method Correlation Analysis...")
    
    results = create_sample_multi_method_analysis()
    
    print(f"\nDataset shape: {results['sample_data'].shape}")
    print(f"Variables: {list(results['sample_data'].columns)}")
    
    print(f"\nMulti-method correlations found: {len(results['correlation_results'])}")
    
    # Show example results
    for i, (pair, result) in enumerate(list(results['correlation_results'].items())[:3]):
        print(f"\n{i+1}. {result.variable_1} ↔ {result.variable_2}:")
        print(f"   Pearson:  r={result.pearson_correlation:.3f}, p={result.pearson_p_value:.4f}")
        print(f"   Spearman: r={result.spearman_correlation:.3f}, p={result.spearman_p_value:.4f}")
        print(f"   Kendall:  r={result.kendall_correlation:.3f}, p={result.kendall_p_value:.4f}")
        print(f"   Convergence: {result.method_convergence_score:.3f}")
        print(f"   Recommended: {result.recommended_method}")
    
    print(f"\nOverall convergence score: {results['convergence_analysis']['overall_convergence_score']:.3f}")
    print("Multi-method correlation analysis test completed!")