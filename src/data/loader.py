"""
Manufacturing Data Loader - Rebuilt for Unified Table Creation

Advanced CSV data loading and validation for fiber cement manufacturing data with
specialized handling for mixed timestamp formats, thickness sensor arrays, and
comprehensive data integration across 5 manufacturing data sources.

Features:
    - Configuration-driven data loading from data.config.json
    - Unified table creation algorithm with temporal alignment
    - Multi-source data integration (VM Capacity, FM Stack, SM Stack, Speed, Stop)
    - Advanced timestamp parsing with auto-format detection
    - Thickness sensor array processing with derived metrics
    - Manufacturing domain validation and quality scoring
    - Memory-efficient loading of 500K+ records across sources

Data Sources:
    - VM Capacity Report.csv: Product specifications and capacity data
    - fm_stack.csv: Forming machine stack data with quality metrics
    - sm_stack.csv: Sheet machine production and quality data
    - speed.csv: Production line speed measurements (continuous monitoring)
    - stop.csv: Machine stoppage events with duration and reason codes

Unified Algorithm:
    1. Use existing thickness data as baseline (from speed.csv processing)
    2. Integrate speed measurements with forward-fill strategy
    3. Calculate stoppage features (time since stop, restart periods)
    4. Map to stack machine data for quality metrics
    5. Align with forming machine data for production tracking
    6. Add product specifications from VM Capacity Report
    7. Generate temporal and sequence features for analysis

Usage:
    ```python
    from src.data.loader import ManufacturingDataLoader
    
    loader = ManufacturingDataLoader()
    unified_data = loader.create_unified_table()
    
    print(f"Unified table created with {len(unified_data)} rows and {len(unified_data.columns)} columns")
    ```
"""

import pandas as pd
import numpy as np
import json
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path
import logging
from datetime import datetime, timedelta
from pydantic import BaseModel

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataValidationResult(BaseModel):
    """Results of data validation"""
    is_valid: bool = True
    errors: List[str] = []
    warnings: List[str] = []
    quality_score: float = 0.0
    recommendations: List[str] = []

def parse_datetime(date_str, time_str):
    """
    Convert date and time strings to datetime with robust format handling.
    
    Supports multiple date formats:
    - YYYY.MM.DD (current format)
    - YYYY-MM-DD (legacy format)
    - DD-MMM-YYYY (alternative format)
    
    Args:
        date_str: Date string in various formats
        time_str: Time string in H:M:S format
        
    Returns:
        pandas Timestamp or NaT if parsing fails
    """
    if pd.isna(date_str) or pd.isna(time_str):
        return pd.NaT
        
    if not isinstance(date_str, str) or not isinstance(time_str, str):
        return pd.NaT
    
    try:
        # Handle different date formats
        if '.' in date_str:
            # Format: "2025.03.01"
            return pd.to_datetime(f"{date_str} {time_str}", format="%Y.%m.%d %H:%M:%S")
        elif '-' in date_str and len(date_str.split('-')[0]) == 4:
            # Format: "2025-03-01"
            return pd.to_datetime(f"{date_str} {time_str}", format="%Y-%m-%d %H:%M:%S")
        elif '-' in date_str:
            # Format: "1-Mar-2025"
            return pd.to_datetime(f"{date_str} {time_str}", format="%d-%b-%Y %H:%M:%S")
        else:
            # Fallback to pandas auto-detection
            return pd.to_datetime(f"{date_str} {time_str}")
    except (ValueError, TypeError):
        logger.warning(f"Failed to parse datetime: {date_str} {time_str}")
        return pd.NaT

def parse_single_datetime(datetime_str):
    """
    Parse single datetime string with multiple format support.
    
    Handles common datetime formats found in manufacturing data:
    - YYYY.MM.DD H:M:S
    - YYYY-MM-DD H:M:S  
    - DD-MMM-YYYY H:M:S
    - ISO format strings
    
    Args:
        datetime_str: Combined datetime string
        
    Returns:
        pandas Timestamp or NaT if parsing fails
    """
    if pd.isna(datetime_str) or not isinstance(datetime_str, str):
        return pd.NaT
    
    datetime_str = datetime_str.strip()
    
    # List of format patterns to try in order
    formats = [
        "%Y.%m.%d %H:%M:%S",     # 2025.03.01 14:30:15
        "%Y-%m-%d %H:%M:%S",     # 2025-03-01 14:30:15
        "%d-%b-%Y %H:%M:%S",     # 01-Mar-2025 14:30:15
        "%Y.%m.%d %H:%M",        # 2025.03.01 14:30
        "%Y-%m-%d %H:%M",        # 2025-03-01 14:30
        "%Y.%m.%d",              # 2025.03.01
        "%Y-%m-%d",              # 2025-03-01
    ]
    
    for fmt in formats:
        try:
            return pd.to_datetime(datetime_str, format=fmt)
        except (ValueError, TypeError):
            continue
    
    # Final fallback to pandas inference (may issue warnings)
    try:
        return pd.to_datetime(datetime_str)
    except (ValueError, TypeError):
        logger.warning(f"Failed to parse datetime string: {datetime_str}")
        return pd.NaT

def calculate_thickness_metrics(row):
    """
    Calculate derived thickness metrics from 10-sensor array.
    
    Computes standard manufacturing quality indicators:
    - Basic statistics (mean, range, std deviation)
    - Manufacturing-specific indices (wedge, crown-bow)
    
    Args:
        row: DataFrame row containing sensor_01 through sensor_10 columns
        
    Returns:
        pandas Series with calculated metrics
    """
    try:
        # Extract sensor values (sensor_01 through sensor_10)
        sensors = []
        for i in range(1, 11):
            sensor_col = f'sensor_{i:02d}'
            if sensor_col in row.index:
                value = row[sensor_col]
                if pd.notna(value) and isinstance(value, (int, float)):
                    sensors.append(float(value))
        
        if len(sensors) < 5:  # Require at least half the sensors
            return pd.Series({
                'thickness_avg': np.nan,
                'thickness_range': np.nan,
                'thickness_std': np.nan,
                'wedge_index': np.nan,
                'crown_bow_index': np.nan
            })
        
        sensors_array = np.array(sensors)
        
        # Basic statistics
        thickness_avg = np.mean(sensors_array)
        thickness_range = np.max(sensors_array) - np.min(sensors_array)
        thickness_std = np.std(sensors_array)
        
        # Manufacturing-specific indices
        if len(sensors) >= 10:
            # Wedge index: left 3 sensors vs right 3 sensors
            wedge_index = np.mean(sensors_array[:3]) - np.mean(sensors_array[-3:])
            # Crown-bow index: center 4 vs edge 4 sensors
            center_4 = sensors_array[3:7]
            edge_4 = np.concatenate([sensors_array[0:2], sensors_array[-2:]])
            crown_bow_index = np.mean(center_4) - np.mean(edge_4)
        else:
            # Fallback for incomplete sensor arrays
            wedge_index = sensors_array[0] - sensors_array[-1] if len(sensors) >= 2 else 0
            crown_bow_index = np.mean(sensors_array[1:-1]) - np.mean([sensors_array[0], sensors_array[-1]]) if len(sensors) >= 3 else 0
        
        return pd.Series({
            'thickness_avg': thickness_avg,
            'thickness_range': thickness_range,
            'thickness_std': thickness_std,
            'wedge_index': wedge_index,
            'crown_bow_index': crown_bow_index
        })
        
    except Exception as e:
        logger.warning(f"Error calculating thickness metrics: {e}")
        return pd.Series({
            'thickness_avg': np.nan,
            'thickness_range': np.nan,
            'thickness_std': np.nan,
            'wedge_index': np.nan,
            'crown_bow_index': np.nan
        })

class ManufacturingDataLoader:
    """
    Rebuilt manufacturing data loader with unified table creation algorithm.
    
    Loads and integrates data from 5 sources based on data.config.json configuration:
    - VM Capacity Report (product specifications)
    - FM Stack (forming machine data)
    - SM Stack (sheet machine data)
    - Speed (production speeds)
    - Stop (stoppage events)
    """
    
    def __init__(self, config_path_or_data_dir: str = "data.config.json"):
        """
        Initialize the data loader with configuration.
        
        Args:
            config_path_or_data_dir: Path to JSON configuration file or data directory
        """
        path = Path(config_path_or_data_dir)
        
        # Determine if this is a config file or data directory
        if path.is_dir():
            # Legacy mode: data directory provided, use default config
            self.data_dir = path
            self.config_path = Path("data.config.json")
            if not self.config_path.exists():
                # Create default config for legacy data directory
                self._create_legacy_config(path)
        elif path.suffix == '.json':
            # New mode: config file provided
            self.config_path = path
            self.data_dir = None
        else:
            # Try as config file path
            self.config_path = path
            self.data_dir = None
        
        self.config = self._load_config()
        self.loaded_data: Dict[str, pd.DataFrame] = {}
        self.validation_results: Dict[str, DataValidationResult] = {}
        
        # Read thickness configuration flag (default: False for clean data)
        self.add_thickness = self.config.get('add_thickness', False)
        
        logger.info(f"Initialized ManufacturingDataLoader with {len(self.config['data_sources'])} data sources")
        logger.info(f"Thickness simulation: {'enabled' if self.add_thickness else 'disabled'}")
    
    def _create_legacy_config(self, data_dir: Path):
        """Create a default config for legacy data directory structure."""
        legacy_config = {
            "description": "Auto-generated legacy configuration",
            "version": "1.0-legacy",
            "add_thickness": False,
            "data_sources": {
                "speed": {
                    "file_path": str(data_dir / "speed.csv"),
                    "description": "Production line speed measurements",
                    "key_columns": ["Work Center/Resource", "Log Date", "Log Time", "Speed"],
                    "timestamp_columns": {"log_date": "Log Date", "log_time": "Log Time"},
                    "data_type": "continuous"
                },
                "stop": {
                    "file_path": str(data_dir / "stop.csv"),
                    "description": "Machine stoppage events",
                    "key_columns": ["Work Center/Resource", "Stop Date", "Stop Time", "Restart Date", "Restart Time"],
                    "timestamp_columns": {"stop_date": "Stop Date", "stop_time": "Stop Time", "restart_date": "Restart Date", "restart_time": "Restart Time"},
                    "data_type": "event"
                },
                "thickness": {
                    "file_path": str(data_dir / "thickness.csv"),
                    "description": "Product thickness measurements",
                    "key_columns": ["Work Center/Resource", "Sensor Date", "Sensor Time"],
                    "timestamp_columns": {"sensor_date": "Sensor Date", "sensor_time": "Sensor Time"},
                    "data_type": "continuous"
                },
                "fm_stack": {
                    "file_path": str(data_dir / "fm_stack.csv"),
                    "description": "Forming machine stack data",
                    "key_columns": ["Production Order", "Scrap Rate"],
                    "timestamp_columns": {},
                    "data_type": "batch"
                },
                "sm_stack": {
                    "file_path": str(data_dir / "sm_stack.csv"),
                    "description": "Sheet machine stack data",
                    "key_columns": ["Production Order", "Scrap%"],
                    "timestamp_columns": {},
                    "data_type": "batch"
                }
            },
            "unified_schema": {
                "base_table": "speed",
                "timestamp_column": "timestamp",
                "work_center_column": "work_center"
            }
        }
        
        # Write the legacy config
        with open(self.config_path, 'w') as f:
            json.dump(legacy_config, f, indent=2)
        
        logger.info(f"Created legacy config at {self.config_path}")

    def _load_config(self) -> Dict:
        """Load and validate configuration file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            
            required_keys = ['data_sources']
            for key in required_keys:
                if key not in config:
                    raise ValueError(f"Missing required config key: {key}")
            
            return config
        except Exception as e:
            raise ValueError(f"Failed to load config from {self.config_path}: {e}")
    
    def load_all_data_sources(self) -> Dict[str, pd.DataFrame]:
        """
        Load all data sources defined in configuration.
        
        Returns:
            Dictionary mapping data source names to DataFrames
        """
        logger.info("Loading all manufacturing data sources...")
        
        for source_name, source_config in self.config['data_sources'].items():
            try:
                file_path = Path(source_config['file_path'])
                logger.info(f"Loading {source_name} from {file_path}")
                
                # Load CSV with basic error handling
                skip_rows = source_config.get('skip_rows', 0)
                df = pd.read_csv(file_path, skiprows=skip_rows)
                
                # Process timestamps if defined
                df = self._process_timestamps(df, source_config)
                
                # Store loaded data
                self.loaded_data[source_name] = df
                
                logger.info(f"Loaded {source_name}: {len(df):,} rows × {len(df.columns)} columns")
                
            except Exception as e:
                logger.error(f"Failed to load {source_name}: {e}")
                self.validation_results[source_name] = DataValidationResult(
                    is_valid=False,
                    errors=[f"Loading error: {e}"]
                )
        
        logger.info(f"Successfully loaded {len(self.loaded_data)} data sources")
        return self.loaded_data
    
    def _process_timestamps(self, df: pd.DataFrame, source_config: Dict) -> pd.DataFrame:
        """Process timestamp columns based on configuration."""
        timestamp_cols = source_config.get('timestamp_columns', {})
        
        if not timestamp_cols:
            return df
        
        df = df.copy()
        
        # Handle different timestamp patterns
        if isinstance(timestamp_cols, dict):
            for ts_name, col_name in timestamp_cols.items():
                if col_name in df.columns:
                    # Single datetime column - use our robust parser
                    df[f'{ts_name}_timestamp'] = df[col_name].apply(parse_single_datetime)
                    
        # Handle separate date/time columns (like speed and stop data)
        if 'log_date' in timestamp_cols and 'log_time' in timestamp_cols:
            date_col = timestamp_cols['log_date']
            time_col = timestamp_cols['log_time']
            if date_col in df.columns and time_col in df.columns:
                df['timestamp'] = df.apply(
                    lambda x: parse_datetime(x[date_col], x[time_col]), axis=1
                )
        
        if 'stop_date' in timestamp_cols and 'stop_time' in timestamp_cols:
            # Stop data has separate stop and restart timestamps
            stop_date_col = timestamp_cols['stop_date']
            stop_time_col = timestamp_cols['stop_time']
            restart_date_col = timestamp_cols.get('restart_date')
            restart_time_col = timestamp_cols.get('restart_time')
            
            if all(col in df.columns for col in [stop_date_col, stop_time_col]):
                df['stop_timestamp'] = df.apply(
                    lambda x: parse_datetime(x[stop_date_col], x[stop_time_col]), axis=1
                )
            
            if restart_date_col and restart_time_col and all(col in df.columns for col in [restart_date_col, restart_time_col]):
                df['restart_timestamp'] = df.apply(
                    lambda x: parse_datetime(x[restart_date_col], x[restart_time_col]), axis=1
                )
        
        # Handle TM480 finishing machine timestamps (separate date/time columns)
        if 'finish_start_date' in timestamp_cols and 'finish_start_time' in timestamp_cols:
            start_date_col = timestamp_cols['finish_start_date']
            start_time_col = timestamp_cols['finish_start_time']
            if start_date_col in df.columns and start_time_col in df.columns:
                df['finish_start_timestamp'] = df.apply(
                    lambda x: parse_datetime(x[start_date_col], x[start_time_col]), axis=1
                )
        
        if 'finish_end_date' in timestamp_cols and 'finish_end_time' in timestamp_cols:
            end_date_col = timestamp_cols['finish_end_date']
            end_time_col = timestamp_cols['finish_end_time']
            if end_date_col in df.columns and end_time_col in df.columns:
                df['finish_end_timestamp'] = df.apply(
                    lambda x: parse_datetime(x[end_date_col], x[end_time_col]), axis=1
                )
        
        return df
    
    def create_unified_table(self) -> pd.DataFrame:
        """
        Enhanced unified table creation with stack-level intelligence.
        
        Implements the complete 10-step enhanced algorithm following PRP specifications:
        1. Load all data sources
        2. Use thickness data as base (derived from speed.csv)
        3. NEW - SM stack data preparation and enhancement
        4. NEW - FM stack data preparation and correlation 
        5. NEW - Speed aggregation per manufacturing stack
        6. NEW - Stoppage analysis with production impact
        7. NEW - SM-FM stack matching with temporal validation
        8. Enhanced product specification integration
        9. NEW - Comprehensive temporal and sequence features
        10. NEW - Data validation and quality reporting
        
        Returns:
            Enhanced unified DataFrame with stack-level manufacturing intelligence
        """
        logger.info("Creating enhanced unified manufacturing data table with stack-level intelligence...")
        
        # Step 1: Load all data sources
        if not self.loaded_data:
            self.load_all_data_sources()
        
        # Extract individual datasets
        speed_df = self.loaded_data.get('speed')
        stop_df = self.loaded_data.get('stop')
        sm_stack_df = self.loaded_data.get('sm_stack')
        fm_stack_df = self.loaded_data.get('fm_stack')
        vm_capacity_df = self.loaded_data.get('vm_capacity')
        
        if speed_df is None:
            raise ValueError("Speed data is required as the base for unified table creation")
        
        # Step 2: Create baseline data from speed measurements (conditional thickness)
        if self.add_thickness:
            logger.info("Step 2: Processing speed data as thickness baseline (with simulated sensors)...")
            unified_df = self._create_thickness_baseline(speed_df)
        else:
            logger.info("Step 2: Processing speed data as clean baseline (no thickness simulation)...")
            unified_df = self._create_speed_baseline(speed_df)
        
        # Step 3: NEW - SM stack data preparation and enhancement
        prepared_sm_df = None
        if sm_stack_df is not None:
            logger.info("Step 3: Preparing SM stack data with manufacturing calculations...")
            prepared_sm_df = self.prepare_sm_stack_data(sm_stack_df)
        
        # Step 4: NEW - FM stack data preparation and correlation
        prepared_fm_df = None
        if fm_stack_df is not None:
            logger.info("Step 4a: Preparing FM stack data with reject calculations...")
            prepared_fm_df = self.prepare_fm_stack_data(fm_stack_df)
        
        # Step 4b: NEW - TM480 stack data preparation
        prepared_tm480_df = None
        tm480_stack_df = self.loaded_data.get('tm480_stack')
        if tm480_stack_df is not None:
            logger.info("Step 4b: Preparing TM480 stack data with quality calculations...")
            prepared_tm480_df = self.prepare_tm480_stack_data(tm480_stack_df)
        
        # Step 5: NEW - Speed aggregation per manufacturing stack
        if prepared_sm_df is not None and speed_df is not None:
            logger.info("Step 5: Aggregating speed data per manufacturing stack...")
            prepared_sm_df = self.aggregate_speed_data(prepared_sm_df, speed_df)
        
        # Step 6: NEW - Stoppage analysis with production impact
        if prepared_sm_df is not None and stop_df is not None:
            logger.info("Step 6: Analyzing stoppage impact per manufacturing stack...")
            prepared_sm_df = self.aggregate_stoppage_data(prepared_sm_df, stop_df)
        
        # Step 7: NEW - SM-FM stack matching with temporal validation (multi-FM support)
        if prepared_sm_df is not None and (prepared_fm_df is not None or prepared_tm480_df is not None):
            logger.info("Step 7: Matching SM stacks to multiple FM sources with temporal validation...")
            prepared_sm_df = self.match_sm_multi_fm_stacks(prepared_sm_df, prepared_fm_df, prepared_tm480_df)
        
        # Step 8: Integrate speed data (enhanced from original)
        logger.info("Step 8: Integrating speed measurements with enhanced patterns...")
        unified_df = self._integrate_speed_data(unified_df, speed_df)
        
        # Step 9: Add enhanced stoppage features to unified table
        if stop_df is not None:
            logger.info("Step 9: Adding enhanced stoppage features to unified table...")
            unified_df = self._add_stoppage_features(unified_df, stop_df)
        
        # Step 10: Map enhanced stack data to unified table
        if prepared_sm_df is not None:
            logger.info("Step 10a: Mapping enhanced SM stack data to unified table...")
            unified_df = self._map_enhanced_sm_stack_data(unified_df, prepared_sm_df)
        
        if prepared_fm_df is not None:
            logger.info("Step 10b: Mapping enhanced FM stack data to unified table...")
            unified_df = self._map_enhanced_fm_stack_data(unified_df, prepared_fm_df)
        
        # Step 11: Enhanced product specification integration (upgraded from original)
        if vm_capacity_df is not None:
            logger.info("Step 11: Adding enhanced product specifications...")
            unified_df = self._add_enhanced_product_specifications(unified_df, vm_capacity_df)
        
        # Step 12: Generate comprehensive temporal and sequence features
        logger.info("Step 12: Generating comprehensive temporal and sequence features...")
        unified_df = self._add_enhanced_temporal_features(unified_df)
        unified_df = self._add_enhanced_sequence_features(unified_df)
        
        logger.info(f"Enhanced unified table created with {len(unified_df):,} rows and {len(unified_df.columns)} columns")
        logger.info("Enhanced features include: stack-level aggregations, SM-FM matching, production efficiency metrics")
        
        # Add product encoding features
        unified_df = self._add_product_encoding_features(unified_df)
        
        return unified_df
    
    def _create_thickness_baseline(self, speed_df: pd.DataFrame) -> pd.DataFrame:
        """
        Create baseline thickness data from speed measurements.
        
        Since we don't have actual thickness sensor data in the current datasets,
        we'll create a baseline using speed data and simulate thickness sensors.
        """
        if 'timestamp' not in speed_df.columns:
            raise ValueError("Speed data must have timestamp column")
        
        # Create base DataFrame with timestamps and work center info
        base_df = speed_df[['timestamp', 'Work Center/Resource', 'Speed']].copy()
        base_df.rename(columns={'Work Center/Resource': 'work_center'}, inplace=True)
        
        # Remove rows with invalid timestamps
        base_df = base_df.dropna(subset=['timestamp'])
        
        # Convert Speed to numeric, handling any string values
        base_df['Speed'] = pd.to_numeric(base_df['Speed'], errors='coerce')
        base_df = base_df.dropna(subset=['Speed'])
        
        # Simulate thickness sensor data based on speed (for demonstration)
        # In production, this would load actual thickness.csv data
        np.random.seed(42)  # For reproducible results
        
        # Create 10 simulated thickness sensors
        base_thickness = 7.5  # Base thickness in mm
        for i in range(1, 11):
            sensor_col = f'sensor_{i:02d}'
            # Add realistic variation based on speed
            speed_factor = base_df['Speed'] / 90.0  # Normalize around 90 speed
            thickness_variation = np.random.normal(0, 0.2, len(base_df))  # ±0.2mm variation
            base_df[sensor_col] = base_thickness + (speed_factor - 1) * 0.5 + thickness_variation
        
        # Calculate thickness metrics using our function
        thickness_metrics = base_df.apply(calculate_thickness_metrics, axis=1)
        base_df = pd.concat([base_df, thickness_metrics], axis=1)
        
        logger.info(f"Created thickness baseline with {len(base_df):,} records")
        return base_df
    
    def _create_speed_baseline(self, speed_df: pd.DataFrame) -> pd.DataFrame:
        """
        Create clean baseline from speed measurements without thickness simulation.
        
        This method creates a manufacturing baseline using only real speed data,
        avoiding any simulated thickness sensors for clean, honest data exports.
        """
        if 'timestamp' not in speed_df.columns:
            raise ValueError("Speed data must have timestamp column")
        
        # Create base DataFrame with timestamps and work center info
        base_df = speed_df[['timestamp', 'Work Center/Resource', 'Speed']].copy()
        base_df.rename(columns={'Work Center/Resource': 'work_center'}, inplace=True)
        
        # Remove rows with invalid timestamps
        base_df = base_df.dropna(subset=['timestamp'])
        
        # Convert Speed to numeric, handling any string values
        base_df['Speed'] = pd.to_numeric(base_df['Speed'], errors='coerce')
        base_df = base_df.dropna(subset=['Speed'])
        
        logger.info(f"Created clean speed baseline with {len(base_df):,} records")
        return base_df
    
    def _integrate_speed_data(self, unified_df: pd.DataFrame, speed_df: pd.DataFrame) -> pd.DataFrame:
        """Integrate speed data using forward-fill strategy."""
        if 'timestamp' not in speed_df.columns:
            return unified_df
        
        # Prepare speed data
        speed_clean = speed_df[['timestamp', 'Work Center/Resource', 'Speed']].copy()
        speed_clean.rename(columns={'Work Center/Resource': 'work_center'}, inplace=True)
        speed_clean = speed_clean.dropna(subset=['timestamp', 'Speed'])
        
        # Merge using asof (forward-fill) strategy
        unified_df = unified_df.sort_values('timestamp')
        speed_clean = speed_clean.sort_values('timestamp')
        
        merged_df = pd.merge_asof(
            unified_df,
            speed_clean[['timestamp', 'work_center', 'Speed']],
            on='timestamp',
            by='work_center',
            direction='backward',
            suffixes=('', '_speed')
        )
        
        # Update speed column if merge was successful
        if 'Speed_speed' in merged_df.columns:
            merged_df['Speed'] = merged_df['Speed_speed']
            merged_df.drop('Speed_speed', axis=1, inplace=True)
        
        # Ensure Speed column is numeric
        if 'Speed' in merged_df.columns:
            merged_df['Speed'] = pd.to_numeric(merged_df['Speed'], errors='coerce')
        
        # Add rolling speed statistics
        merged_df = merged_df.sort_values(['work_center', 'timestamp'])
        merged_df['speed_avg_5min'] = merged_df.groupby('work_center')['Speed'].transform(
            lambda x: x.rolling(window=5, min_periods=1).mean()
        )
        merged_df['speed_change_rate'] = merged_df.groupby('work_center')['Speed'].diff()
        
        return merged_df
    
    def _add_stoppage_features(self, unified_df: pd.DataFrame, stop_df: pd.DataFrame) -> pd.DataFrame:
        """Add stoppage-related features with optimized vectorized operations."""
        if 'stop_timestamp' not in stop_df.columns or 'restart_timestamp' not in stop_df.columns:
            logger.warning("Stop data missing required timestamp columns")
            return unified_df
        
        # Initialize stoppage feature columns
        unified_df['time_since_last_stop'] = np.nan
        unified_df['is_restart_period'] = False
        unified_df['stop_duration_previous'] = 0.0
        unified_df['stops_last_hour'] = 0
        
        # Process each work center separately for efficiency
        work_centers = unified_df['work_center'].unique()
        total_centers = len(work_centers)
        
        for i, work_center in enumerate(work_centers):
            if i % 10 == 0:  # Progress indicator
                logger.info(f"Processing stoppage features for work center {i+1}/{total_centers}")
            
            # Filter data for this work center
            wc_mask = unified_df['work_center'] == work_center
            wc_data = unified_df[wc_mask].copy()
            
            # Filter stops for this work center (with string normalization)
            wc_stops = stop_df[stop_df['Work Center/Resource'].str.strip() == work_center.strip()].copy()
            
            if wc_stops.empty:
                continue
            
            # Sort by timestamp for efficient processing
            wc_data = wc_data.sort_values('timestamp')
            wc_stops = wc_stops.sort_values('restart_timestamp')
            
            # Vectorized operations using merge_asof for time-based lookups
            # Find most recent restart for each timestamp
            restart_lookup = pd.merge_asof(
                wc_data[['timestamp']],
                wc_stops[['restart_timestamp', 'MPS Stop Duration']].dropna(),
                left_on='timestamp',
                right_on='restart_timestamp',
                direction='backward'
            )
            
            # Calculate time since last stop (vectorized)
            time_diff = (wc_data['timestamp'] - restart_lookup['restart_timestamp']).dt.total_seconds() / 60.0
            time_since_stop = pd.Series(
                np.where(pd.notna(restart_lookup['restart_timestamp']), time_diff, np.nan),
                index=wc_data.index
            )
            
            # Restart period detection (first 10 minutes after restart)
            is_restart = time_since_stop <= 10
            
            # Stop duration (from previous stop) - ensure numeric conversion
            stop_duration = pd.to_numeric(restart_lookup['MPS Stop Duration'], errors='coerce').fillna(0.0)
            
            # Count stops in last hour using vectorized rolling window approach
            if not wc_stops.empty and 'stop_timestamp' in wc_stops.columns:
                # Create time-based bins for efficient counting
                stops_last_hour = pd.Series(0, index=wc_data.index)
                
                # Vectorized approach: for small datasets, use searchsorted for efficiency
                wc_stop_times = wc_stops['stop_timestamp'].dropna().sort_values()
                
                for idx, timestamp in wc_data['timestamp'].items():
                    hour_ago = timestamp - timedelta(hours=1)
                    # Use binary search to count stops in window
                    left_idx = wc_stop_times.searchsorted(hour_ago, side='left')
                    right_idx = wc_stop_times.searchsorted(timestamp, side='right')
                    stops_last_hour.loc[idx] = max(0, right_idx - left_idx)
            else:
                stops_last_hour = pd.Series(0, index=wc_data.index)
            
            # Update unified dataframe with calculated features
            unified_df.loc[wc_mask, 'time_since_last_stop'] = time_since_stop
            unified_df.loc[wc_mask, 'is_restart_period'] = is_restart
            unified_df.loc[wc_mask, 'stop_duration_previous'] = stop_duration
            unified_df.loc[wc_mask, 'stops_last_hour'] = stops_last_hour
        
        logger.info(f"Completed stoppage features for {total_centers} work centers")
        return unified_df
    
    def _map_sm_stack_data(self, unified_df: pd.DataFrame, sm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """Map to sheet machine stack data."""
        if 'first_sheet_timestamp' not in sm_stack_df.columns:
            logger.warning("SM stack data missing required timestamp columns")
            return unified_df
        
        def find_sm_stack(timestamp, sm_data):
            """Find which stack a sheet belongs to based on timestamp."""
            mask = (sm_data['first_sheet_timestamp'] <= timestamp) & \
                   (sm_data['last_sheet_timestamp'] >= timestamp)
            matched = sm_data[mask]
            
            if not matched.empty:
                stack = matched.iloc[0]
                
                # Parse scrap percentage
                scrap_pct = stack.get('Scrap%', '0%')
                if isinstance(scrap_pct, str) and '%' in scrap_pct:
                    try:
                        scrap_pct = float(scrap_pct.rstrip('%'))
                    except ValueError:
                        scrap_pct = 0.0
                elif pd.notna(scrap_pct):
                    scrap_pct = float(scrap_pct)
                else:
                    scrap_pct = 0.0
                
                return pd.Series({
                    'sm_stack_number': stack.get('Stack Number'),
                    'sm_production_order': stack.get('Production Order'),
                    'sm_product': stack.get('Product'),
                    'sm_good_sheets': stack.get('Good Sheets'),
                    'sm_scrap_pct': scrap_pct
                })
            
            return pd.Series({
                'sm_stack_number': None,
                'sm_production_order': None,
                'sm_product': None,
                'sm_good_sheets': None,
                'sm_scrap_pct': None
            })
        
        sm_stack_info = unified_df.apply(
            lambda x: find_sm_stack(x['timestamp'], sm_stack_df), axis=1
        )
        
        return pd.concat([unified_df, sm_stack_info], axis=1)
    
    def _map_fm_stack_data(self, unified_df: pd.DataFrame, fm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """Map to forming machine stack data."""
        if 'on_load_timestamp' not in fm_stack_df.columns:
            logger.warning("FM stack data missing required timestamp columns")
            return unified_df
        
        def find_fm_stack(timestamp, fm_data):
            """Find which forming machine stack based on timestamp."""
            mask = (fm_data['on_load_timestamp'] <= timestamp) & \
                   (fm_data['off_load_timestamp'] >= timestamp)
            matched = fm_data[mask]
            
            if not matched.empty:
                stack = matched.iloc[0]
                
                # Calculate position percentage within stack processing
                on_load = stack['on_load_timestamp']
                off_load = stack['off_load_timestamp']
                if pd.notna(on_load) and pd.notna(off_load):
                    stack_duration = (off_load - on_load).total_seconds()
                    position_pct = ((timestamp - on_load).total_seconds() / stack_duration * 100) if stack_duration > 0 else 0
                else:
                    position_pct = 0
                
                return pd.Series({
                    'fm_mps_id': stack.get('MPS ID'),
                    'fm_production_order': stack.get('Production Order'),
                    'fm_product_description': stack.get('Product Description'),
                    'fm_ok_sheets': stack.get('Ok'),
                    'fm_reject_sheets': stack.get('Rej.'),
                    'fm_position_pct': position_pct
                })
            
            return pd.Series({
                'fm_mps_id': None,
                'fm_production_order': None,
                'fm_product_description': None,
                'fm_ok_sheets': None,
                'fm_reject_sheets': None,
                'fm_position_pct': None
            })
        
        fm_stack_info = unified_df.apply(
            lambda x: find_fm_stack(x['timestamp'], fm_stack_df), axis=1
        )
        
        return pd.concat([unified_df, fm_stack_info], axis=1)
    
    def _add_product_specifications(self, unified_df: pd.DataFrame, vm_capacity_df: pd.DataFrame) -> pd.DataFrame:
        """Add product specifications from VM Capacity Report."""
        # Extract product code from SM product description
        if 'sm_product' in unified_df.columns:
            unified_df['product_code'] = unified_df['sm_product'].str.extract(r'(\d{7})')
        
        # Clean up SAP Code column name (remove tab character)
        sap_col = 'SAP Code\t' if 'SAP Code\t' in vm_capacity_df.columns else 'SAP Code'
        
        if sap_col in vm_capacity_df.columns:
            # Merge with VM capacity data
            capacity_cols = [sap_col, 'Design Capacity', 'Design Felt Speed', 'Sheet Machine', 'Finishing Machine']
            available_cols = [col for col in capacity_cols if col in vm_capacity_df.columns]
            
            if available_cols:
                merged_df = unified_df.merge(
                    vm_capacity_df[available_cols],
                    left_on='product_code',
                    right_on=sap_col,
                    how='left'
                )
                return merged_df
        
        logger.warning("Could not merge VM capacity data - missing required columns")
        return unified_df
    
    def _add_temporal_features(self, unified_df: pd.DataFrame) -> pd.DataFrame:
        """Add temporal features for time-based analysis."""
        if 'timestamp' not in unified_df.columns:
            return unified_df
        
        df = unified_df.copy()
        
        # Basic temporal features
        df['hour_of_day'] = df['timestamp'].dt.hour
        df['minute_of_hour'] = df['timestamp'].dt.minute
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        
        # Shift classification
        df['shift'] = df['hour_of_day'].apply(
            lambda h: 'night' if h < 6 or h >= 22 else 'morning' if h < 14 else 'afternoon'
        )
        
        # Cyclical encoding for time
        df['hour_sin'] = np.sin(2 * np.pi * df['hour_of_day'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour_of_day'] / 24)
        
        return df
    
    def _add_sequence_features(self, unified_df: pd.DataFrame) -> pd.DataFrame:
        """Add sequence and quality features."""
        if 'timestamp' not in unified_df.columns or 'work_center' not in unified_df.columns:
            return unified_df
        
        df = unified_df.copy()
        df = df.sort_values(['work_center', 'timestamp'])
        
        # Sequence features
        df['sheet_sequence'] = df.groupby('work_center').cumcount()
        df['time_since_start'] = df.groupby('work_center')['timestamp'].transform(
            lambda x: (x - x.min()).dt.total_seconds() / 60
        )
        
        # Quality indicators
        if 'thickness_avg' in df.columns:
            df['thickness_deviation'] = df['thickness_avg'] - df['thickness_avg'].rolling(window=10).mean()
            
            # Define thickness specification based on quantiles
            thickness_lower = df['thickness_avg'].quantile(0.05)
            thickness_upper = df['thickness_avg'].quantile(0.95)
            df['is_within_spec'] = (
                (df['thickness_avg'] >= thickness_lower) &
                (df['thickness_avg'] <= thickness_upper)
            )
        
        return df
    
    # =====================================================================
    # ENHANCED MAPPING AND FEATURE ENGINEERING FUNCTIONS
    # Supporting the new 10-step enhanced unified table creation algorithm
    # =====================================================================
    
    def _map_enhanced_sm_stack_data(self, unified_df: pd.DataFrame, prepared_sm_df: pd.DataFrame) -> pd.DataFrame:
        """
        Map enhanced SM stack data with all calculated metrics to unified table.
        
        Enhanced mapping includes:
        - All original SM stack fields
        - Speed aggregation statistics
        - Stoppage analysis metrics
        - FM matching results
        - Production efficiency calculations
        """
        try:
            logger.info("Mapping enhanced SM stack data to unified table...")
            
            def find_enhanced_sm_stack(timestamp, sm_data):
                """Find enhanced SM stack with comprehensive metrics."""
                mask = (sm_data['first_sheet_timestamp'] <= timestamp) & \
                       (sm_data['last_sheet_timestamp'] >= timestamp)
                matched = sm_data[mask]
                
                if not matched.empty:
                    stack = matched.iloc[0]
                    
                    # Enhanced SM stack information with all calculated fields
                    result = {
                        # Original fields
                        'sm_stack_number': stack.get('Stack Number'),
                        'sm_production_order': stack.get('Production Order'),
                        'sm_product': stack.get('Product'),
                        'sm_good_sheets': stack.get('Good Sheets'),
                        'sm_scrap_pct': stack.get('sm_scrap_pct', 0.0),
                        
                        # Enhanced calculated fields
                        'sm_duration_minutes': stack.get('sm_duration_minutes'),
                        'sm_production_rate': stack.get('sm_production_rate', 0.0),
                        'sm_quality_efficiency': stack.get('sm_quality_efficiency', 1.0),
                        
                        # Speed aggregation metrics
                        'speed_avg': stack.get('speed_avg', 90.0),
                        'speed_std': stack.get('speed_std', 0.0),
                        'speed_cv': stack.get('speed_cv', 0.0),
                        'speed_min': stack.get('speed_min', 90.0),
                        'speed_max': stack.get('speed_max', 90.0),
                        'speed_readings_count': stack.get('speed_readings_count', 0),
                        
                        # Stoppage analysis metrics
                        'stops_during_production': stack.get('stops_during_production', 0),
                        'total_stop_duration_minutes': stack.get('total_stop_duration_minutes', 0.0),
                        'production_efficiency_pct': stack.get('production_efficiency_pct', 100.0),
                        'primary_stoppage_reason': stack.get('primary_stoppage_reason', 'No Stops'),
                        'restart_impact_score': stack.get('restart_impact_score', 0.0),
                        
                        # FM matching results
                        'has_fm_match': stack.get('has_fm_match', False),
                        'fm_machine_type': stack.get('fm_machine_type'),
                        'fm_source_system': stack.get('fm_source_system'),
                        'fm_reject_pct': stack.get('fm_reject_pct'),
                        'sm_to_fm_gap_minutes': stack.get('sm_to_fm_gap_minutes'),
                        'match_quality_score': stack.get('match_quality_score', 0.0)
                    }
                    
                    return pd.Series(result)
                
                # Return empty result for no match
                return pd.Series({
                    'sm_stack_number': None, 'sm_production_order': None, 'sm_product': None,
                    'sm_good_sheets': None, 'sm_scrap_pct': None, 'sm_duration_minutes': None,
                    'sm_production_rate': 0.0, 'sm_quality_efficiency': 1.0,
                    'speed_avg': 90.0, 'speed_std': 0.0, 'speed_cv': 0.0, 'speed_min': 90.0,
                    'speed_max': 90.0, 'speed_readings_count': 0, 'stops_during_production': 0,
                    'total_stop_duration_minutes': 0.0, 'production_efficiency_pct': 100.0,
                    'primary_stoppage_reason': 'No Stops', 'restart_impact_score': 0.0,
                    'has_fm_match': False, 'fm_machine_type': None, 'fm_source_system': None,
                    'fm_reject_pct': None, 'sm_to_fm_gap_minutes': None, 'match_quality_score': 0.0
                })
            
            enhanced_sm_info = unified_df.apply(
                lambda x: find_enhanced_sm_stack(x['timestamp'], prepared_sm_df), axis=1
            )
            
            return pd.concat([unified_df, enhanced_sm_info], axis=1)
            
        except Exception as e:
            logger.error(f"Enhanced SM stack mapping failed: {e}")
            return unified_df
    
    def _map_enhanced_fm_stack_data(self, unified_df: pd.DataFrame, prepared_fm_df: pd.DataFrame) -> pd.DataFrame:
        """
        Map enhanced FM stack data with calculated metrics to unified table.
        
        Enhanced FM mapping includes:
        - Original FM stack information 
        - Processing duration and rates
        - Quality efficiency metrics
        - Temporal position within FM processing
        """
        try:
            logger.info("Mapping enhanced FM stack data to unified table...")
            
            def find_enhanced_fm_stack(timestamp, fm_data):
                """Find enhanced FM stack with comprehensive metrics."""
                mask = (fm_data['fm_on_load_timestamp'] <= timestamp) & \
                       (fm_data['fm_off_load_timestamp'] >= timestamp)
                matched = fm_data[mask]
                
                if not matched.empty:
                    stack = matched.iloc[0]
                    
                    # Calculate position percentage within FM processing
                    on_load = stack['fm_on_load_timestamp']
                    off_load = stack['fm_off_load_timestamp']
                    if pd.notna(on_load) and pd.notna(off_load):
                        stack_duration = (off_load - on_load).total_seconds()
                        position_pct = ((timestamp - on_load).total_seconds() / stack_duration * 100) if stack_duration > 0 else 0
                    else:
                        position_pct = 0
                    
                    return pd.Series({
                        # Original FM fields
                        'fm_mps_id': stack.get('MPS ID'),
                        'fm_production_order': stack.get('Production Order'),
                        'fm_product_description': stack.get('Product Description'),
                        
                        # Enhanced calculated fields
                        'fm_duration_minutes': stack.get('fm_duration_minutes'),
                        'fm_processing_rate': stack.get('fm_processing_rate', 0.0),
                        'fm_quality_efficiency': stack.get('fm_quality_efficiency', 1.0),
                        'fm_ok_sheets': stack.get('fm_ok_sheets', 0),
                        'fm_reject_sheets': stack.get('fm_reject_sheets', 0),
                        'fm_reject_pct_calculated': stack.get('fm_reject_pct', 0.0),
                        'fm_position_pct': position_pct
                    })
                
                return pd.Series({
                    'fm_mps_id': None, 'fm_production_order': None, 'fm_product_description': None,
                    'fm_duration_minutes': None, 'fm_processing_rate': 0.0, 'fm_quality_efficiency': 1.0,
                    'fm_ok_sheets': None, 'fm_reject_sheets': None, 'fm_reject_pct_calculated': None,
                    'fm_position_pct': None
                })
            
            enhanced_fm_info = unified_df.apply(
                lambda x: find_enhanced_fm_stack(x['timestamp'], prepared_fm_df), axis=1
            )
            
            return pd.concat([unified_df, enhanced_fm_info], axis=1)
            
        except Exception as e:
            logger.error(f"Enhanced FM stack mapping failed: {e}")
            return unified_df
    
    def _add_enhanced_product_specifications(self, unified_df: pd.DataFrame, vm_capacity_df: pd.DataFrame) -> pd.DataFrame:
        """
        Enhanced product specification integration with performance analysis.
        
        Includes:
        - Design vs. actual performance analysis
        - Speed deviation calculations
        - Capacity utilization metrics
        """
        try:
            logger.info("Adding enhanced product specifications...")
            
            # Start with original product specification logic
            enhanced_df = self._add_product_specifications(unified_df, vm_capacity_df)
            
            # Add enhanced performance analysis
            if 'Design Felt Speed' in enhanced_df.columns and 'Speed' in enhanced_df.columns:
                # Speed deviation calculation: (actual - design) / design * 100
                enhanced_df['speed_deviation_pct'] = (
                    (enhanced_df['Speed'] - enhanced_df['Design Felt Speed']) / 
                    enhanced_df['Design Felt Speed'] * 100
                ).fillna(0.0)
                
                # Speed performance category
                enhanced_df['speed_performance'] = enhanced_df['speed_deviation_pct'].apply(
                    lambda x: 'Above Design' if x > 5 else 'Below Design' if x < -5 else 'On Target'
                )
            
            if 'Design Capacity' in enhanced_df.columns and 'sm_production_rate' in enhanced_df.columns:
                # Capacity utilization: actual_rate / design_capacity * 100
                enhanced_df['capacity_utilization_pct'] = (
                    enhanced_df['sm_production_rate'] / enhanced_df['Design Capacity'] * 100
                ).fillna(0.0)
                
                # Capacity performance category
                enhanced_df['capacity_performance'] = enhanced_df['capacity_utilization_pct'].apply(
                    lambda x: 'High Utilization' if x > 90 else 'Low Utilization' if x < 70 else 'Normal Utilization'
                )
            
            return enhanced_df
            
        except Exception as e:
            logger.error(f"Enhanced product specification integration failed: {e}")
            return unified_df
    
    def _add_enhanced_temporal_features(self, unified_df: pd.DataFrame) -> pd.DataFrame:
        """
        Enhanced temporal features with manufacturing context.
        
        Includes all original temporal features plus:
        - Production shift classification with manufacturing context
        - Weekend/weekday analysis
        - Time-based quality patterns
        """
        try:
            logger.info("Adding enhanced temporal features...")
            
            # Start with original temporal features
            enhanced_df = self._add_temporal_features(unified_df)
            
            if 'timestamp' not in enhanced_df.columns:
                return enhanced_df
            
            # Enhanced shift classification with manufacturing context
            enhanced_df['production_shift'] = enhanced_df['hour_of_day'].apply(
                lambda h: 'Night Shift (22:00-06:00)' if h < 6 or h >= 22 
                else 'Morning Shift (06:00-14:00)' if h < 14 
                else 'Afternoon Shift (14:00-22:00)'
            )
            
            # Weekend/weekday analysis
            enhanced_df['is_weekend'] = enhanced_df['day_of_week'].isin([5, 6])  # Saturday, Sunday
            enhanced_df['day_type'] = enhanced_df['is_weekend'].map({True: 'Weekend', False: 'Weekday'})
            
            # Time-based quality indicators
            if 'sm_scrap_pct' in enhanced_df.columns:
                # Rolling average scrap rate by time of day
                enhanced_df['hourly_avg_scrap'] = enhanced_df.groupby('hour_of_day')['sm_scrap_pct'].transform('mean')
                
                # Quality performance vs. time patterns
                enhanced_df['scrap_vs_hourly_avg'] = enhanced_df['sm_scrap_pct'] - enhanced_df['hourly_avg_scrap']
            
            if 'production_efficiency_pct' in enhanced_df.columns:
                # Shift-based efficiency analysis
                enhanced_df['shift_avg_efficiency'] = enhanced_df.groupby('production_shift')['production_efficiency_pct'].transform('mean')
                enhanced_df['efficiency_vs_shift_avg'] = enhanced_df['production_efficiency_pct'] - enhanced_df['shift_avg_efficiency']
            
            # Manufacturing calendar features
            enhanced_df['month'] = enhanced_df['timestamp'].dt.month
            enhanced_df['quarter'] = enhanced_df['timestamp'].dt.quarter
            enhanced_df['week_of_year'] = enhanced_df['timestamp'].dt.isocalendar().week
            
            return enhanced_df
            
        except Exception as e:
            logger.error(f"Enhanced temporal features failed: {e}")
            return unified_df
    
    def _add_enhanced_sequence_features(self, unified_df: pd.DataFrame) -> pd.DataFrame:
        """
        Enhanced sequence features with manufacturing intelligence.
        
        Includes all original sequence features plus:
        - Stack-level sequence tracking
        - Quality trend analysis
        - Production momentum indicators
        """
        try:
            logger.info("Adding enhanced sequence features...")
            
            # Start with original sequence features
            enhanced_df = self._add_sequence_features(unified_df)
            
            if 'timestamp' not in enhanced_df.columns or 'work_center' not in enhanced_df.columns:
                return enhanced_df
            
            # Sort for sequence analysis
            enhanced_df = enhanced_df.sort_values(['work_center', 'timestamp'])
            
            # Stack-level sequence tracking
            if 'sm_stack_number' in enhanced_df.columns:
                # Stack change indicator
                enhanced_df['stack_change'] = enhanced_df.groupby('work_center')['sm_stack_number'].transform(
                    lambda x: x != x.shift(1)
                )
                
                # Position within current stack
                enhanced_df['position_in_stack'] = enhanced_df.groupby(['work_center', 'sm_stack_number']).cumcount()
                
                # Stack progression percentage
                if 'sm_duration_minutes' in enhanced_df.columns:
                    enhanced_df['stack_progress_pct'] = (
                        enhanced_df['position_in_stack'] / 
                        enhanced_df.groupby(['work_center', 'sm_stack_number'])['position_in_stack'].transform('max') * 100
                    ).fillna(0)
            
            # Quality trend analysis
            if 'sm_scrap_pct' in enhanced_df.columns:
                # Rolling quality metrics
                enhanced_df['scrap_trend_5min'] = enhanced_df.groupby('work_center')['sm_scrap_pct'].transform(
                    lambda x: x.rolling(window=5, min_periods=1).mean()
                )
                enhanced_df['scrap_trend_direction'] = enhanced_df.groupby('work_center')['scrap_trend_5min'].diff()
                
                # Quality improvement indicator
                enhanced_df['quality_improving'] = enhanced_df['scrap_trend_direction'] < -0.1
            
            # Production momentum indicators
            if 'Speed' in enhanced_df.columns:
                # Speed momentum
                enhanced_df['speed_momentum'] = enhanced_df.groupby('work_center')['Speed'].diff()
                enhanced_df['speed_acceleration'] = enhanced_df.groupby('work_center')['speed_momentum'].diff()
                
                # Production stability indicator
                enhanced_df['speed_stability'] = enhanced_df.groupby('work_center')['Speed'].transform(
                    lambda x: x.rolling(window=10, min_periods=1).std()
                )
                enhanced_df['production_stable'] = enhanced_df['speed_stability'] < 5.0  # Low variability threshold
            
            # Manufacturing state classification
            conditions = []
            choices = []
            
            if 'production_stable' in enhanced_df.columns and 'quality_improving' in enhanced_df.columns:
                conditions = [
                    enhanced_df['production_stable'] & enhanced_df['quality_improving'],
                    enhanced_df['production_stable'] & ~enhanced_df['quality_improving'],
                    ~enhanced_df['production_stable'] & enhanced_df['quality_improving'],
                    ~enhanced_df['production_stable'] & ~enhanced_df['quality_improving']
                ]
                choices = ['Optimal Production', 'Stable Production', 'Quality Focus', 'Adjustment Needed']
                
                enhanced_df['manufacturing_state'] = np.select(conditions, choices, default='Normal Production')
            
            return enhanced_df
            
        except Exception as e:
            logger.error(f"Enhanced sequence features failed: {e}")
            return unified_df
    
    def _add_product_encoding_features(self, unified_df: pd.DataFrame) -> pd.DataFrame:
        """
        Add product encoding features to convert string product names into numeric features.
        
        Args:
            unified_df: DataFrame with sm_product column
            
        Returns:
            DataFrame with added product features
        """
        try:
            if 'sm_product' not in unified_df.columns:
                logger.warning("sm_product column not found - skipping product encoding")
                return unified_df
            
            logger.info("Adding product encoding features...")
            
            # Import the product encoder
            from .product_encoder import ProductEncoder
            
            # Create and fit encoder
            encoder = ProductEncoder()
            product_features = encoder.fit_transform(unified_df['sm_product'])
            
            # Add features to unified DataFrame
            for feature_name in encoder.get_feature_names():
                unified_df[feature_name] = product_features[feature_name]
            
            logger.info(f"Added {len(encoder.get_feature_names())} product encoding features")
            
            return unified_df
            
        except Exception as e:
            logger.error(f"Product encoding failed: {e}")
            return unified_df
    
    # =====================================================================
    # ENHANCED STACK-LEVEL INTELLIGENCE FUNCTIONS
    # Following PRP: enhanced-unified-table-creation.md implementation
    # =====================================================================
    
    def prepare_sm_stack_data(self, sm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """
        Transform SM stack data with manufacturing-specific calculations.
        
        Enhanced preparation following DATA_WITHOUT_THICKNESS.md methodology:
        - Parse timestamps using existing parse_single_datetime
        - Calculate duration in minutes with validation
        - Convert scrap percentage: "2.33%" -> 2.33
        - Extract base production order for FM matching
        - Add manufacturing domain validation
        
        Args:
            sm_stack_df: Raw SM stack DataFrame
            
        Returns:
            Enhanced SM stack DataFrame with calculated fields
        """
        try:
            logger.info(f"Preparing SM stack data: {len(sm_stack_df)} stacks")
            
            # Create a copy to avoid modifying original data
            df = sm_stack_df.copy()
            
            # Parse timestamps using existing function
            df['first_sheet_timestamp'] = df['First Sheet Date Time'].apply(parse_single_datetime)
            df['last_sheet_timestamp'] = df['Last Sheet Date Time'].apply(parse_single_datetime)
            
            # Calculate duration in minutes with validation
            df['sm_duration_minutes'] = (
                df['last_sheet_timestamp'] - df['first_sheet_timestamp']
            ).dt.total_seconds() / 60.0
            
            # Validate duration is positive
            invalid_duration = df['sm_duration_minutes'] <= 0
            if invalid_duration.any():
                logger.warning(f"Found {invalid_duration.sum()} stacks with invalid durations")
                df.loc[invalid_duration, 'sm_duration_minutes'] = np.nan
            
            # Convert scrap percentage from string to float
            # "2.33%" -> 2.33
            if 'Scrap%' in df.columns:
                df['sm_scrap_pct'] = df['Scrap%'].astype(str).str.rstrip('%').replace('', '0')
                df['sm_scrap_pct'] = pd.to_numeric(df['sm_scrap_pct'], errors='coerce').fillna(0.0)
                
                # Validate scrap percentage bounds (0-100%)
                invalid_scrap = (df['sm_scrap_pct'] < 0) | (df['sm_scrap_pct'] > 100)
                if invalid_scrap.any():
                    logger.warning(f"Found {invalid_scrap.sum()} stacks with invalid scrap percentages")
                    df.loc[invalid_scrap, 'sm_scrap_pct'] = np.nan
            else:
                df['sm_scrap_pct'] = 0.0
            
            # Extract base production order for FM matching
            # "1508615/1" -> "1508615"
            if 'Production Order' in df.columns:
                df['base_production_order'] = df['Production Order'].astype(str).str.split('/').str[0]
                df['base_production_order'] = df['base_production_order'].replace('nan', '')
            else:
                df['base_production_order'] = ''
            
            # Add production rate calculation (sheets per minute)
            if 'Good Sheets' in df.columns:
                df['sm_production_rate'] = df['Good Sheets'] / df['sm_duration_minutes'].replace(0, np.nan)
                df['sm_production_rate'] = df['sm_production_rate'].fillna(0.0)
            else:
                df['sm_production_rate'] = 0.0
            
            # Add quality efficiency metric
            df['sm_quality_efficiency'] = (100 - df['sm_scrap_pct']) / 100.0
            
            logger.info(f"SM stack preparation completed: {len(df)} stacks processed")
            return df
            
        except Exception as e:
            logger.error(f"SM stack preparation failed: {e}")
            # Return original data with minimal processing to ensure pipeline continues
            df = sm_stack_df.copy()
            df['sm_duration_minutes'] = np.nan
            df['sm_scrap_pct'] = 0.0
            df['base_production_order'] = ''
            df['sm_production_rate'] = 0.0
            df['sm_quality_efficiency'] = 1.0
            return df
    
    def prepare_fm_stack_data(self, fm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """
        Process FM stack data with reject calculations and temporal validation.
        
        Key transformations following PRP specifications:
        - Parse on-load and off-load timestamps
        - Calculate FM processing duration with validation
        - Calculate reject percentage with zero-division handling
        - Validate temporal consistency (off-load > on-load)
        - Extract production order for matching
        
        Args:
            fm_stack_df: Raw FM stack DataFrame
            
        Returns:
            Enhanced FM stack DataFrame with calculated fields
        """
        try:
            logger.info(f"Preparing FM stack data: {len(fm_stack_df)} stacks")
            
            # Create a copy to avoid modifying original data
            df = fm_stack_df.copy()
            
            # Filter to only include MPS IDs starting with "7" to match SM stack pattern
            original_count = len(df)
            if 'MPS ID' in df.columns:
                mps_id_filter = df['MPS ID'].astype(str).str.startswith('7')
                df = df[mps_id_filter].copy()
                filtered_count = len(df)
                logger.info(f"Filtered FM data from {original_count} to {filtered_count} records (MPS IDs starting with '7')")
                
                if filtered_count == 0:
                    logger.warning("No FM records found with MPS IDs starting with '7' - this will result in 0% matching rate")
                elif filtered_count < original_count * 0.1:
                    logger.warning(f"Only {filtered_count/original_count:.1%} of FM records start with '7' - check if this is expected")
            else:
                logger.warning("No 'MPS ID' column found in FM data - cannot apply MPS ID filtering")
            
            # Parse timestamps - handle different possible column names
            on_load_col = None
            off_load_col = None
            
            # Find the correct column names (case-insensitive)
            for col in df.columns:
                if 'on-load' in col.lower() or 'onload' in col.lower():
                    on_load_col = col
                elif 'off-load' in col.lower() or 'offload' in col.lower():
                    off_load_col = col
            
            # Fallback to positional columns if not found
            if on_load_col is None and len(df.columns) >= 3:
                on_load_col = df.columns[2]  # 3rd column typically On-Load
            if off_load_col is None and len(df.columns) >= 4:
                off_load_col = df.columns[3]  # 4th column typically Off-Load
            
            if on_load_col and off_load_col:
                df['fm_on_load_timestamp'] = df[on_load_col].apply(parse_single_datetime)
                df['fm_off_load_timestamp'] = df[off_load_col].apply(parse_single_datetime)
            else:
                logger.warning("Could not identify on-load and off-load columns")
                df['fm_on_load_timestamp'] = pd.NaT
                df['fm_off_load_timestamp'] = pd.NaT
            
            # Calculate processing duration in minutes
            df['fm_duration_minutes'] = (
                df['fm_off_load_timestamp'] - df['fm_on_load_timestamp']
            ).dt.total_seconds() / 60.0
            
            # Validate temporal consistency and duration
            invalid_temporal = (df['fm_off_load_timestamp'] <= df['fm_on_load_timestamp']) | (df['fm_duration_minutes'] <= 0)
            if invalid_temporal.any():
                logger.warning(f"Found {invalid_temporal.sum()} FM stacks with invalid temporal consistency")
                df.loc[invalid_temporal, 'fm_duration_minutes'] = np.nan
            
            # Calculate reject percentage with zero-division handling
            if 'Ok' in df.columns and 'Rej.' in df.columns:
                df['fm_ok_sheets'] = pd.to_numeric(df['Ok'], errors='coerce').fillna(0)
                df['fm_reject_sheets'] = pd.to_numeric(df['Rej.'], errors='coerce').fillna(0)
                
                total_sheets = df['fm_ok_sheets'] + df['fm_reject_sheets']
                df['fm_reject_pct'] = np.where(
                    total_sheets > 0,
                    df['fm_reject_sheets'] / total_sheets * 100,
                    0.0
                )
                
                # Validate reject percentage bounds
                invalid_reject = (df['fm_reject_pct'] < 0) | (df['fm_reject_pct'] > 100)
                if invalid_reject.any():
                    logger.warning(f"Found {invalid_reject.sum()} FM stacks with invalid reject percentages")
                    df.loc[invalid_reject, 'fm_reject_pct'] = np.nan
            else:
                df['fm_ok_sheets'] = 0
                df['fm_reject_sheets'] = 0
                df['fm_reject_pct'] = 0.0
            
            # Extract base production order for matching - handle CSV column mapping issues
            production_order_col = None
            
            # Try multiple possible column names for production order
            possible_po_cols = ['Production Order', 'Unnamed: 4']  # Column 4 contains production orders
            for col in possible_po_cols:
                if col in df.columns and not df[col].isna().all():
                    production_order_col = col
                    logger.info(f"Found production orders in column: {col}")
                    break
            
            if production_order_col:
                # Handle numeric production orders and remove .0 suffix
                df['base_production_order'] = df[production_order_col].astype(str).str.replace('.0', '', regex=False).str.split('/').str[0]
                df['base_production_order'] = df['base_production_order'].replace('nan', '')
                logger.info(f"Extracted {df['base_production_order'].nunique()} unique production order bases")
            else:
                logger.warning("No production order column found in FM data")
                df['base_production_order'] = ''
            
            # Also map product description from the correct column
            product_desc_col = None
            possible_desc_cols = ['Product Description', 'Unnamed: 9']  # Column 9 contains product descriptions
            for col in possible_desc_cols:
                if col in df.columns and not df[col].isna().all():
                    product_desc_col = col
                    logger.info(f"Found product descriptions in column: {col}")
                    break
                    
            if product_desc_col:
                df['fm_product_description'] = df[product_desc_col]
            else:
                df['fm_product_description'] = ''
            
            # Calculate processing rate (sheets per minute)
            total_sheets = df['fm_ok_sheets'] + df['fm_reject_sheets']
            df['fm_processing_rate'] = total_sheets / df['fm_duration_minutes'].replace(0, np.nan)
            df['fm_processing_rate'] = df['fm_processing_rate'].fillna(0.0)
            
            # Calculate quality efficiency
            df['fm_quality_efficiency'] = (100 - df['fm_reject_pct']) / 100.0
            
            logger.info(f"FM stack preparation completed: {len(df)} stacks processed")
            return df
            
        except Exception as e:
            logger.error(f"FM stack preparation failed: {e}")
            # Return original data with minimal processing
            df = fm_stack_df.copy()
            df['fm_duration_minutes'] = np.nan
            df['fm_reject_pct'] = 0.0
            df['base_production_order'] = ''
            df['fm_processing_rate'] = 0.0
            df['fm_quality_efficiency'] = 1.0
            return df
    
    def prepare_tm480_stack_data(self, tm480_stack_df: pd.DataFrame) -> pd.DataFrame:
        """
        Process TM480 finishing machine stack data with quality calculations.
        
        Key transformations for TM480 data:
        - Parse start and end timestamps from separate date/time columns
        - Calculate TM480 processing duration with validation
        - Calculate reject percentage from accepted/rejected sheets
        - Filter for stack numbers starting with "7" for SM270 matching
        - Extract production order and normalize format
        
        Args:
            tm480_stack_df: Raw TM480 stack DataFrame
            
        Returns:
            Enhanced TM480 stack DataFrame with calculated fields
        """
        try:
            logger.info(f"Preparing TM480 stack data: {len(tm480_stack_df)} stacks")
            
            # Create a copy to avoid modifying original data
            df = tm480_stack_df.copy()
            
            # Filter to only include Actual Stack Numbers starting with "7" to match SM stack pattern
            original_count = len(df)
            if 'Actual Stack Number' in df.columns:
                stack_id_filter = df['Actual Stack Number'].astype(str).str.startswith('7')
                df = df[stack_id_filter].copy()
                filtered_count = len(df)
                logger.info(f"Filtered TM480 data from {original_count} to {filtered_count} records (Actual Stack Numbers starting with '7')")
                
                if filtered_count == 0:
                    logger.warning("No TM480 records found with Actual Stack Numbers starting with '7'")
                elif filtered_count < original_count * 0.1:
                    logger.warning(f"Only {filtered_count/original_count:.1%} of TM480 records start with '7' - check if this is expected")
            else:
                logger.warning("No 'Actual Stack Number' column found in TM480 data - cannot apply stack filtering")
            
            # Parse timestamps using existing timestamps from config (finish_start_timestamp, finish_end_timestamp)
            start_timestamp_col = 'finish_start_timestamp'
            end_timestamp_col = 'finish_end_timestamp'
            
            if start_timestamp_col in df.columns and end_timestamp_col in df.columns:
                df['tm480_start_timestamp'] = df[start_timestamp_col]
                df['tm480_end_timestamp'] = df[end_timestamp_col]
            else:
                logger.warning("TM480 timestamp columns not found - attempting manual parsing")
                # Fallback to manual parsing if config processing failed
                if 'Finish Start Date' in df.columns and 'Finish Start Time' in df.columns:
                    df['tm480_start_timestamp'] = df.apply(
                        lambda x: parse_datetime(x['Finish Start Date'], x['Finish Start Time']), axis=1
                    )
                if 'Finish End Date' in df.columns and 'Finish End Time' in df.columns:
                    df['tm480_end_timestamp'] = df.apply(
                        lambda x: parse_datetime(x['Finish End Date'], x['Finish End Time']), axis=1
                    )
            
            # Calculate processing duration in minutes
            df['tm480_duration_minutes'] = (
                df['tm480_end_timestamp'] - df['tm480_start_timestamp']
            ).dt.total_seconds() / 60.0
            
            # Validate temporal consistency and duration
            invalid_temporal = (df['tm480_end_timestamp'] <= df['tm480_start_timestamp']) | (df['tm480_duration_minutes'] <= 0)
            if invalid_temporal.any():
                logger.warning(f"Found {invalid_temporal.sum()} TM480 stacks with invalid temporal consistency")
                df.loc[invalid_temporal, 'tm480_duration_minutes'] = np.nan
            
            # Calculate reject percentage with zero-division handling
            if 'Sheet Accepted' in df.columns and 'Total Sheet Rejected' in df.columns:
                df['tm480_accepted_sheets'] = pd.to_numeric(df['Sheet Accepted'], errors='coerce').fillna(0)
                df['tm480_rejected_sheets'] = pd.to_numeric(df['Total Sheet Rejected'], errors='coerce').fillna(0)
                
                total_sheets = df['tm480_accepted_sheets'] + df['tm480_rejected_sheets']
                df['tm480_reject_pct'] = np.where(
                    total_sheets > 0,
                    df['tm480_rejected_sheets'] / total_sheets * 100,
                    0.0
                )
                
                # Validate reject percentage bounds
                invalid_reject = (df['tm480_reject_pct'] < 0) | (df['tm480_reject_pct'] > 100)
                if invalid_reject.any():
                    logger.warning(f"Found {invalid_reject.sum()} TM480 stacks with invalid reject percentages")
                    df.loc[invalid_reject, 'tm480_reject_pct'] = np.nan
            else:
                df['tm480_accepted_sheets'] = 0
                df['tm480_rejected_sheets'] = 0
                df['tm480_reject_pct'] = 0.0
            
            # Extract production order for matching
            if 'Production Order' in df.columns:
                # Handle numeric production orders and remove .0 suffix
                df['base_production_order'] = df['Production Order'].astype(str).str.replace('.0', '', regex=False).str.split('/').str[0]
                df['base_production_order'] = df['base_production_order'].replace('nan', '')
                logger.info(f"Extracted {df['base_production_order'].nunique()} unique production order bases from TM480")
            else:
                logger.warning("No production order column found in TM480 data")
                df['base_production_order'] = ''
            
            # Map Actual Stack Number to standard matching field
            if 'Actual Stack Number' in df.columns:
                df['tm480_stack_number'] = df['Actual Stack Number'].astype(str)
            else:
                df['tm480_stack_number'] = ''
            
            # Calculate processing rate (sheets per minute)
            total_sheets = df['tm480_accepted_sheets'] + df['tm480_rejected_sheets']
            df['tm480_processing_rate'] = total_sheets / df['tm480_duration_minutes'].replace(0, np.nan)
            df['tm480_processing_rate'] = df['tm480_processing_rate'].fillna(0.0)
            
            # Calculate quality efficiency
            df['tm480_quality_efficiency'] = (100 - df['tm480_reject_pct']) / 100.0
            
            # Add machine type identifier
            df['fm_machine_type'] = 'TM480'
            df['fm_source_system'] = 'TM480'
            
            logger.info(f"TM480 stack preparation completed: {len(df)} stacks processed")
            return df
            
        except Exception as e:
            logger.error(f"TM480 stack preparation failed: {e}")
            # Return original data with minimal processing
            df = tm480_stack_df.copy()
            df['tm480_duration_minutes'] = np.nan
            df['tm480_reject_pct'] = 0.0
            df['base_production_order'] = ''
            df['tm480_processing_rate'] = 0.0
            df['tm480_quality_efficiency'] = 1.0
            df['fm_machine_type'] = 'TM480'
            df['fm_source_system'] = 'TM480'
            df['tm480_stack_number'] = ''
            return df
    
    def aggregate_speed_data(self, sm_stack_df: pd.DataFrame, speed_df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate comprehensive speed metrics for each stack's production period.
        
        Per-stack speed analytics with vectorized operations following PRP specifications:
        - Filter speed data to stack production timeline
        - Calculate: avg, std, min, max, CV, reading counts
        - Handle missing data with manufacturing-appropriate defaults
        - Memory-optimized processing with progress indicators
        
        Args:
            sm_stack_df: Prepared SM stack DataFrame with timestamps
            speed_df: Raw speed DataFrame with timestamp column
            
        Returns:
            DataFrame with aggregated speed statistics per stack
        """
        try:
            logger.info(f"Aggregating speed data for {len(sm_stack_df)} stacks...")
            
            # Ensure speed data has timestamps
            if 'timestamp' not in speed_df.columns:
                logger.warning("Speed data missing timestamp column - skipping aggregation")
                return self._create_empty_speed_stats(sm_stack_df)
            
            # Prepare speed data for efficient processing
            speed_clean = speed_df[['timestamp', 'Work Center/Resource', 'Speed']].copy()
            speed_clean = speed_clean.dropna(subset=['timestamp'])
            speed_clean['Speed'] = pd.to_numeric(speed_clean['Speed'], errors='coerce')
            speed_clean = speed_clean.dropna(subset=['Speed'])
            
            # Validate speed range (0-300 m/min with outlier detection)
            speed_outliers = (speed_clean['Speed'] < 0) | (speed_clean['Speed'] > 300)
            if speed_outliers.any():
                logger.warning(f"Found {speed_outliers.sum()} speed outliers (outside 0-300 range)")
                speed_clean = speed_clean[~speed_outliers]
            
            # Sort for efficient temporal filtering
            speed_clean = speed_clean.sort_values('timestamp')
            
            speed_stats_list = []
            total_stacks = len(sm_stack_df)
            
            for idx, (_, stack) in enumerate(sm_stack_df.iterrows()):
                if idx % 1000 == 0:  # Progress indicator for large datasets
                    logger.info(f"Processing speed aggregation: {idx+1}/{total_stacks} stacks")
                
                # Extract stack temporal boundaries
                first_sheet = stack.get('first_sheet_timestamp')
                last_sheet = stack.get('last_sheet_timestamp')
                work_center = stack.get('Work Center/Resource', 'SM270')  # Default work center
                
                # Skip if invalid timestamps
                if pd.isna(first_sheet) or pd.isna(last_sheet):
                    stats = self._create_default_speed_stats(stack)
                    speed_stats_list.append(stats)
                    continue
                
                # Filter speed data to stack production period and work center
                mask = (
                    (speed_clean['timestamp'] >= first_sheet) & 
                    (speed_clean['timestamp'] <= last_sheet) &
                    (speed_clean['Work Center/Resource'] == work_center)
                )
                
                relevant_speeds = speed_clean[mask]['Speed']
                
                if not relevant_speeds.empty and len(relevant_speeds) >= 1:
                    # Calculate comprehensive statistics
                    speed_mean = relevant_speeds.mean()
                    speed_std = relevant_speeds.std()
                    speed_cv = (speed_std / speed_mean * 100) if speed_mean > 0 else 0
                    
                    stats = {
                        'stack_id': stack.get('Stack Number'),
                        'production_order': stack.get('Production Order'),
                        'speed_avg': round(speed_mean, 2),
                        'speed_std': round(speed_std, 2),
                        'speed_min': round(relevant_speeds.min(), 2),
                        'speed_max': round(relevant_speeds.max(), 2),
                        'speed_cv': round(speed_cv, 2),
                        'speed_readings_count': len(relevant_speeds),
                        'speed_range': round(relevant_speeds.max() - relevant_speeds.min(), 2),
                        'speed_p10': round(relevant_speeds.quantile(0.10), 2),
                        'speed_p90': round(relevant_speeds.quantile(0.90), 2)
                    }
                else:
                    # No speed data found - use manufacturing-appropriate defaults
                    stats = self._create_default_speed_stats(stack)
                
                speed_stats_list.append(stats)
            
            # Convert to DataFrame and merge with stack data
            speed_stats_df = pd.DataFrame(speed_stats_list)
            
            # Merge back with original stack data
            enhanced_sm_df = sm_stack_df.merge(
                speed_stats_df, 
                left_on='Stack Number', 
                right_on='stack_id', 
                how='left'
            )
            
            logger.info(f"Speed aggregation completed for {len(enhanced_sm_df)} stacks")
            return enhanced_sm_df
            
        except Exception as e:
            logger.error(f"Speed aggregation failed: {e}")
            return self._create_empty_speed_stats(sm_stack_df)
    
    def _create_default_speed_stats(self, stack: pd.Series) -> Dict:
        """Create default speed statistics for stacks with no speed data."""
        return {
            'stack_id': stack.get('Stack Number'),
            'production_order': stack.get('Production Order'),
            'speed_avg': 90.0,  # Manufacturing default baseline
            'speed_std': 0.0,
            'speed_min': 90.0,
            'speed_max': 90.0,
            'speed_cv': 0.0,
            'speed_readings_count': 0,
            'speed_range': 0.0,
            'speed_p10': 90.0,
            'speed_p90': 90.0
        }
    
    def _create_empty_speed_stats(self, sm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """Create DataFrame with empty speed statistics for error cases."""
        speed_cols = ['speed_avg', 'speed_std', 'speed_min', 'speed_max', 'speed_cv',
                     'speed_readings_count', 'speed_range', 'speed_p10', 'speed_p90']
        
        df = sm_stack_df.copy()
        for col in speed_cols:
            df[col] = 0.0
        return df
    
    def aggregate_stoppage_data(self, sm_stack_df: pd.DataFrame, stop_df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate detailed stoppage metrics for production optimization.
        
        Production impact analysis following PRP specifications:
        - Stops during production with duration summation
        - Pre-production stops (1-hour window) for restart impact analysis
        - Production efficiency calculation with downtime deduction
        - Primary stoppage reason categorization for root cause analysis
        
        Args:
            sm_stack_df: Prepared SM stack DataFrame with timestamps
            stop_df: Raw stoppage DataFrame with timestamp columns
            
        Returns:
            DataFrame with aggregated stoppage statistics per stack
        """
        try:
            logger.info(f"Aggregating stoppage data for {len(sm_stack_df)} stacks...")
            
            # Ensure stop data has required timestamps
            if 'stop_timestamp' not in stop_df.columns or 'restart_timestamp' not in stop_df.columns:
                logger.warning("Stop data missing required timestamp columns - skipping aggregation")
                return self._create_empty_stoppage_stats(sm_stack_df)
            
            # Prepare stop data for efficient processing
            stop_clean = stop_df[['stop_timestamp', 'restart_timestamp', 'Work Center/Resource', 
                                'MPS Stop Duration', 'Stoppage Reason']].copy()
            stop_clean = stop_clean.dropna(subset=['stop_timestamp', 'restart_timestamp'])
            
            # Convert duration to numeric (minutes)
            stop_clean['stop_duration_minutes'] = pd.to_numeric(
                stop_clean['MPS Stop Duration'], errors='coerce'
            ).fillna(0.0)
            
            # Validate stop duration bounds (reasonable manufacturing limits)
            duration_outliers = (stop_clean['stop_duration_minutes'] < 0) | (stop_clean['stop_duration_minutes'] > 1440)  # > 24 hours
            if duration_outliers.any():
                logger.warning(f"Found {duration_outliers.sum()} stoppage duration outliers")
                stop_clean.loc[duration_outliers, 'stop_duration_minutes'] = 0.0
            
            # Sort for efficient temporal processing
            stop_clean = stop_clean.sort_values('stop_timestamp')
            
            stoppage_stats_list = []
            total_stacks = len(sm_stack_df)
            
            for idx, (_, stack) in enumerate(sm_stack_df.iterrows()):
                if idx % 1000 == 0:  # Progress indicator
                    logger.info(f"Processing stoppage aggregation: {idx+1}/{total_stacks} stacks")
                
                # Extract stack temporal boundaries
                first_sheet = stack.get('first_sheet_timestamp')
                last_sheet = stack.get('last_sheet_timestamp')
                work_center = stack.get('Work Center/Resource', 'SM270')
                stack_duration = stack.get('sm_duration_minutes', 0)
                
                # Skip if invalid timestamps
                if pd.isna(first_sheet) or pd.isna(last_sheet):
                    stats = self._create_default_stoppage_stats(stack)
                    stoppage_stats_list.append(stats)
                    continue
                
                # Filter stops for this work center
                wc_stops = stop_clean[stop_clean['Work Center/Resource'].str.strip() == str(work_center).strip()]
                
                if wc_stops.empty:
                    stats = self._create_default_stoppage_stats(stack)
                    stoppage_stats_list.append(stats)
                    continue
                
                # 1. Stops during production period
                production_stops = wc_stops[
                    (wc_stops['stop_timestamp'] >= first_sheet) & 
                    (wc_stops['stop_timestamp'] <= last_sheet)
                ]
                
                # 2. Pre-production stops (1-hour window before start)
                hour_before = first_sheet - timedelta(hours=1)
                pre_production_stops = wc_stops[
                    (wc_stops['stop_timestamp'] >= hour_before) & 
                    (wc_stops['stop_timestamp'] < first_sheet)
                ]
                
                # Calculate production stoppage metrics
                total_stop_duration = production_stops['stop_duration_minutes'].sum()
                stop_count_production = len(production_stops)
                avg_stop_duration = production_stops['stop_duration_minutes'].mean() if stop_count_production > 0 else 0
                
                # Calculate production efficiency
                # Efficiency = (scheduled_time - stop_duration) / scheduled_time * 100
                production_efficiency = (
                    (stack_duration - total_stop_duration) / stack_duration * 100
                ) if stack_duration > 0 else 100.0
                production_efficiency = max(0, min(100, production_efficiency))  # Bound between 0-100%
                
                # Pre-production stop impact
                pre_production_stop_duration = pre_production_stops['stop_duration_minutes'].sum()
                restart_impact_score = min(pre_production_stop_duration / 60.0, 5.0)  # Normalized 0-5 scale
                
                # Primary stoppage reason categorization
                primary_reason = 'No Stops'
                if not production_stops.empty:
                    reason_counts = production_stops['Stoppage Reason'].value_counts()
                    primary_reason = reason_counts.index[0] if len(reason_counts) > 0 else 'Unknown'
                
                stats = {
                    'stack_id': stack.get('Stack Number'),
                    'production_order': stack.get('Production Order'),
                    'stops_during_production': stop_count_production,
                    'total_stop_duration_minutes': round(total_stop_duration, 1),
                    'avg_stop_duration_minutes': round(avg_stop_duration, 1),
                    'production_efficiency_pct': round(production_efficiency, 1),
                    'pre_production_stops': len(pre_production_stops),
                    'pre_production_stop_duration': round(pre_production_stop_duration, 1),
                    'restart_impact_score': round(restart_impact_score, 2),
                    'primary_stoppage_reason': primary_reason,
                    'max_single_stop_duration': round(production_stops['stop_duration_minutes'].max(), 1) if stop_count_production > 0 else 0.0,
                    'stop_frequency_per_hour': round(stop_count_production / (stack_duration / 60.0), 2) if stack_duration > 60 else 0.0
                }
                
                stoppage_stats_list.append(stats)
            
            # Convert to DataFrame and merge with stack data
            stoppage_stats_df = pd.DataFrame(stoppage_stats_list)
            
            # Merge back with original stack data
            enhanced_sm_df = sm_stack_df.merge(
                stoppage_stats_df, 
                left_on='Stack Number', 
                right_on='stack_id', 
                how='left'
            )
            
            logger.info(f"Stoppage aggregation completed for {len(enhanced_sm_df)} stacks")
            return enhanced_sm_df
            
        except Exception as e:
            logger.error(f"Stoppage aggregation failed: {e}")
            return self._create_empty_stoppage_stats(sm_stack_df)
    
    def _create_default_stoppage_stats(self, stack: pd.Series) -> Dict:
        """Create default stoppage statistics for stacks with no stoppage data."""
        return {
            'stack_id': stack.get('Stack Number'),
            'production_order': stack.get('Production Order'),
            'stops_during_production': 0,
            'total_stop_duration_minutes': 0.0,
            'avg_stop_duration_minutes': 0.0,
            'production_efficiency_pct': 100.0,
            'pre_production_stops': 0,
            'pre_production_stop_duration': 0.0,
            'restart_impact_score': 0.0,
            'primary_stoppage_reason': 'No Stops',
            'max_single_stop_duration': 0.0,
            'stop_frequency_per_hour': 0.0
        }
    
    def _create_empty_stoppage_stats(self, sm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """Create DataFrame with empty stoppage statistics for error cases."""
        stoppage_cols = ['stops_during_production', 'total_stop_duration_minutes', 'avg_stop_duration_minutes',
                        'production_efficiency_pct', 'pre_production_stops', 'pre_production_stop_duration',
                        'restart_impact_score', 'primary_stoppage_reason', 'max_single_stop_duration',
                        'stop_frequency_per_hour']
        
        df = sm_stack_df.copy()
        for col in stoppage_cols:
            if col == 'primary_stoppage_reason':
                df[col] = 'No Stops'
            elif col == 'production_efficiency_pct':
                df[col] = 100.0
            else:
                df[col] = 0.0
        return df
    
    def match_sm_fm_stacks(self, sm_stack_df: pd.DataFrame, fm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """
        Match SM stacks to FM processing using Stack Number correlation.
        
        Stack number correlation with temporal validation:
        - Direct stack number matching: SM "Stack Number" to FM "MPS ID"
        - Time gap calculation: FM on-load must be after SM completion
        - Best match selection: exact stack number match with valid timing
        - Comprehensive mismatch reporting for quality analysis
        
        Args:
            sm_stack_df: Prepared SM stack DataFrame with timestamps and Stack Number
            fm_stack_df: Prepared FM stack DataFrame with timestamps and MPS ID
            
        Returns:
            Enhanced SM stack DataFrame with FM matching information
        """
        try:
            logger.info(f"Matching {len(sm_stack_df)} SM stacks to {len(fm_stack_df)} FM stacks...")
            
            # Ensure both DataFrames have required columns for stack number matching
            required_sm_cols = ['last_sheet_timestamp', 'Stack Number']
            required_fm_cols = ['fm_on_load_timestamp', 'MPS ID']
            
            missing_sm_cols = [col for col in required_sm_cols if col not in sm_stack_df.columns]
            missing_fm_cols = [col for col in required_fm_cols if col not in fm_stack_df.columns]
            
            if missing_sm_cols or missing_fm_cols:
                logger.warning(f"Missing required columns for matching. SM: {missing_sm_cols}, FM: {missing_fm_cols}")
                return self._create_empty_fm_matches(sm_stack_df)
            
            # Enhanced debugging: Analyze stack number distributions before matching
            logger.info("=== STACK NUMBER DISTRIBUTION ANALYSIS ===")
            
            # SM stack number analysis
            sm_stacks = sm_stack_df['Stack Number'].dropna().astype(str)
            if len(sm_stacks) > 0:
                logger.info(f"SM Stack Numbers: {len(sm_stacks)} stacks")
                logger.info(f"SM Range: {sm_stacks.min()} to {sm_stacks.max()}")
                logger.info(f"SM Sample stacks: {list(sm_stacks.head(5))}")
            else:
                logger.warning("No valid SM stack numbers found!")
            
            # FM stack number analysis  
            fm_stacks = fm_stack_df['MPS ID'].dropna().astype(str)
            if len(fm_stacks) > 0:
                logger.info(f"FM MPS IDs: {len(fm_stacks)} stacks")
                logger.info(f"FM Range: {fm_stacks.min()} to {fm_stacks.max()}")
                logger.info(f"FM Sample IDs: {list(fm_stacks.head(5))}")
            else:
                logger.warning("No valid FM MPS IDs found!")
            
            # Check for potential matches
            if len(sm_stacks) > 0 and len(fm_stacks) > 0:
                overlap = set(sm_stacks).intersection(set(fm_stacks))
                logger.info(f"Exact stack matches: {len(overlap)} common stack numbers")
                match_rate = len(overlap) / len(sm_stacks) * 100
                logger.info(f"Expected match rate: {match_rate:.1f}%")
                if len(overlap) > 0:
                    logger.info(f"Sample matching stacks: {sorted(list(overlap))[:5]}")
                else:
                    logger.warning("❌ NO OVERLAPPING STACK NUMBERS - 0% match rate expected!")
                    logger.info("This indicates SM and FM data are from different production periods")
            
            logger.info("=" * 55)
            
            # Prepare FM stack data for efficient matching using MPS ID
            available_fm_cols = ['fm_on_load_timestamp', 'fm_off_load_timestamp', 'MPS ID',
                                 'fm_duration_minutes', 'fm_reject_pct', 'fm_ok_sheets', 'fm_reject_sheets',
                                 'fm_product_description']
            
            # Select only available columns
            fm_cols_to_use = [col for col in available_fm_cols if col in fm_stack_df.columns]
            fm_match_df = fm_stack_df[fm_cols_to_use].copy()
            
            # Remove FM stacks with invalid data
            fm_match_df = fm_match_df.dropna(subset=['fm_on_load_timestamp', 'MPS ID'])
            fm_match_df['MPS ID'] = fm_match_df['MPS ID'].astype(str)
            
            # Sort FM stacks by on-load timestamp for efficient searching
            fm_match_df = fm_match_df.sort_values('fm_on_load_timestamp')
            
            # Initialize matching results
            match_results = []
            match_statistics = {
                'total_sm_stacks': len(sm_stack_df),
                'successful_matches': 0,
                'temporal_failures': 0,
                'production_order_failures': 0,
                'no_fm_candidates': 0,
                'multiple_candidates': 0
            }
            
            for idx, (_, sm_stack) in enumerate(sm_stack_df.iterrows()):
                if idx % 1000 == 0:  # Progress indicator
                    logger.info(f"Processing SM-FM matching: {idx+1}/{len(sm_stack_df)} stacks")
                
                # Extract SM stack information
                sm_completion_time = sm_stack.get('last_sheet_timestamp')
                sm_stack_number = str(sm_stack.get('Stack Number', ''))  # Ensure string type for matching
                
                # Initialize match result
                match_result = {
                    'sm_stack_number': sm_stack_number,
                    'sm_production_order': sm_stack.get('Production Order'),
                    'has_fm_match': False,
                    'fm_mps_id': None,
                    'fm_production_order': None,
                    'fm_on_load_timestamp': pd.NaT,
                    'fm_off_load_timestamp': pd.NaT,
                    'fm_duration_minutes': np.nan,
                    'fm_reject_pct': np.nan,
                    'fm_ok_sheets': np.nan,
                    'fm_reject_sheets': np.nan,
                    'sm_to_fm_gap_minutes': np.nan,
                    'match_quality_score': 0.0,
                    'match_failure_reason': 'Unknown'
                }
                
                # Skip if SM stack has invalid data
                if pd.isna(sm_completion_time) or sm_stack_number == '':
                    match_result['match_failure_reason'] = 'Invalid SM data'
                    match_results.append(match_result)
                    match_statistics['production_order_failures'] += 1
                    continue
                
                # Find FM candidates with matching stack number (MPS ID)
                fm_candidates = fm_match_df[fm_match_df['MPS ID'] == sm_stack_number].copy()
                
                if fm_candidates.empty:
                    match_result['match_failure_reason'] = 'No FM with matching stack number'
                    match_results.append(match_result)
                    match_statistics['no_fm_candidates'] += 1
                    continue
                
                # Apply temporal validation: FM on-load must be after SM completion
                # Note: Manufacturing reality allows gaps from 1 hour to 30+ days due to:
                # - Inventory storage, quality control, batch scheduling, maintenance windows
                valid_fm_candidates = fm_candidates[fm_candidates['fm_on_load_timestamp'] > sm_completion_time].copy()
                
                if valid_fm_candidates.empty:
                    match_result['match_failure_reason'] = 'No temporally valid FM candidates'
                    match_results.append(match_result)
                    match_statistics['temporal_failures'] += 1
                    continue
                
                # Select best match: earliest valid FM processing
                valid_fm_candidates['sm_to_fm_gap'] = (
                    valid_fm_candidates['fm_on_load_timestamp'] - sm_completion_time
                ).dt.total_seconds() / 60.0  # Convert to minutes
                
                # Additional quality scoring based on gap time and production order exactness
                valid_fm_candidates['match_quality'] = 100.0  # Base score
                
                # Manufacturing-realistic gap time scoring
                # Acceptable range: 1 hour to 7 days (168 hours)
                gap_hours = valid_fm_candidates['sm_to_fm_gap'] / 60.0
                
                # Optimal range: 4-48 hours (typical manufacturing flow)
                optimal_gap_mask = (gap_hours >= 4) & (gap_hours <= 48)
                valid_fm_candidates.loc[optimal_gap_mask, 'match_quality'] += 10
                
                # Acceptable range: 1-168 hours (1 hour to 7 days)  
                acceptable_gap_mask = (gap_hours >= 1) & (gap_hours <= 168)
                # No penalty for acceptable gaps
                
                # Long but reasonable: 7-30 days (extended storage/scheduling)
                extended_gap_mask = (gap_hours > 168) & (gap_hours <= (30 * 24))
                valid_fm_candidates.loc[extended_gap_mask, 'match_quality'] -= 20
                
                # Very long gaps: > 30 days (questionable but possible)
                very_long_gap_mask = gap_hours > (30 * 24)
                valid_fm_candidates.loc[very_long_gap_mask, 'match_quality'] -= 40
                
                # Bonus for quick turnaround (4-12 hours - efficient flow)
                quick_gap_mask = (gap_hours >= 4) & (gap_hours <= 12)
                valid_fm_candidates.loc[quick_gap_mask, 'match_quality'] += 5
                
                # Select best match (highest quality score, then earliest gap)
                best_match = valid_fm_candidates.loc[
                    valid_fm_candidates.groupby('match_quality')['sm_to_fm_gap'].idxmin()
                ].iloc[0]
                
                # Populate match result
                match_result.update({
                    'has_fm_match': True,
                    'fm_mps_id': best_match['MPS ID'],
                    'fm_production_order': best_match.get('base_production_order'),
                    'fm_product_description': best_match.get('fm_product_description'),
                    'fm_on_load_timestamp': best_match['fm_on_load_timestamp'],
                    'fm_off_load_timestamp': best_match.get('fm_off_load_timestamp'),
                    'fm_duration_minutes': best_match.get('fm_duration_minutes'),
                    'fm_reject_pct': best_match.get('fm_reject_pct'),
                    'fm_ok_sheets': best_match.get('fm_ok_sheets'),
                    'fm_reject_sheets': best_match.get('fm_reject_sheets'),
                    'sm_to_fm_gap_minutes': best_match['sm_to_fm_gap'],
                    'match_quality_score': best_match['match_quality'],
                    'match_failure_reason': f'Success - {len(valid_fm_candidates)} candidates'
                })
                
                match_results.append(match_result)
                match_statistics['successful_matches'] += 1
                
                if len(valid_fm_candidates) > 1:
                    match_statistics['multiple_candidates'] += 1
            
            # Convert to DataFrame and merge with SM stack data
            match_results_df = pd.DataFrame(match_results)
            
            # Ensure data types match for merge - convert both to string
            sm_stack_df_copy = sm_stack_df.copy()
            sm_stack_df_copy['Stack Number'] = sm_stack_df_copy['Stack Number'].astype(str)
            
            # Merge back with original SM stack data
            enhanced_sm_df = sm_stack_df_copy.merge(
                match_results_df,
                left_on='Stack Number',
                right_on='sm_stack_number',
                how='left'
            )
            
            # Calculate and log matching statistics
            match_rate = match_statistics['successful_matches'] / match_statistics['total_sm_stacks'] * 100
            logger.info(f"=== SM-FM STACK MATCHING RESULTS ===")
            logger.info(f"Total SM stacks: {match_statistics['total_sm_stacks']}")
            logger.info(f"Successful matches: {match_statistics['successful_matches']} ({match_rate:.1f}%)")
            logger.info(f"Temporal failures: {match_statistics['temporal_failures']}")
            logger.info(f"Stack number failures: {match_statistics['production_order_failures']}")
            logger.info(f"No FM candidates: {match_statistics['no_fm_candidates']}")
            logger.info(f"Multiple candidates: {match_statistics['multiple_candidates']}")
            
            # Enhanced matching quality assessment with recommendations
            if match_rate < 85:
                logger.warning(f"⚠️  Stack matching rate {match_rate:.1f}% below 85% target")
                
                if match_statistics['no_fm_candidates'] > match_statistics['total_sm_stacks'] * 0.8:
                    logger.warning("📊 ROOT CAUSE: Most failures due to 'No FM candidates'")
                    logger.info("💡 RECOMMENDATION: Check if SM and FM data are from the same time period")
                    logger.info("💡 RECOMMENDATION: Verify production order formats match between SM and FM systems")
                    logger.info("💡 RECOMMENDATION: Consider collecting data for overlapping production batches")
                    logger.info("ℹ️  NOTE: SM-FM gaps can be 1 hour to 30+ days due to storage/scheduling")
                
                if match_statistics['temporal_failures'] > match_statistics['total_sm_stacks'] * 0.2:
                    logger.warning("⏰ HIGH TEMPORAL FAILURES: FM processing times may be before SM completion")
                    logger.info("💡 RECOMMENDATION: Review production flow timing and data collection timestamps")
                    logger.info("ℹ️  NOTE: Valid gaps range from 1 hour to several weeks depending on manufacturing flow")
            else:
                logger.info(f"✅ Good matching rate: {match_rate:.1f}% meets 85% target")
            
            logger.info("=" * 40)
            
            return enhanced_sm_df
            
        except Exception as e:
            logger.error(f"SM-FM stack matching failed: {e}")
            return self._create_empty_fm_matches(sm_stack_df)
    
    def match_sm_multi_fm_stacks(self, sm_stack_df: pd.DataFrame, 
                                 prepared_fm_df: Optional[pd.DataFrame] = None,
                                 prepared_tm480_df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Match SM stacks to multiple FM sources (original FM + TM480) using Stack Number correlation.
        
        Multi-source FM matching strategy:
        - Combine FM and TM480 data into unified FM dataset
        - Apply stack number-based matching to combined dataset
        - Use temporal proximity as tie-breaker when multiple matches exist
        - Track which finishing machine processed each stack
        - Provide comprehensive matching statistics across all sources
        
        Args:
            sm_stack_df: Prepared SM stack DataFrame
            prepared_fm_df: Prepared original FM stack DataFrame (optional)
            prepared_tm480_df: Prepared TM480 stack DataFrame (optional)
            
        Returns:
            Enhanced SM stack DataFrame with multi-FM matching information
        """
        try:
            logger.info("=== MULTI-SOURCE FM MATCHING ===")
            
            # Collect all available FM sources
            fm_sources = []
            if prepared_fm_df is not None and len(prepared_fm_df) > 0:
                fm_sources.append(('FM', prepared_fm_df))
                logger.info(f"Available FM source: Original FM with {len(prepared_fm_df)} stacks")
            
            if prepared_tm480_df is not None and len(prepared_tm480_df) > 0:
                fm_sources.append(('TM480', prepared_tm480_df))
                logger.info(f"Available FM source: TM480 with {len(prepared_tm480_df)} stacks")
            
            if not fm_sources:
                logger.warning("No FM sources available for matching")
                return self._create_empty_fm_matches(sm_stack_df)
            
            # Create unified FM dataset
            logger.info("Creating unified FM dataset from multiple sources...")
            unified_fm_list = []
            
            for source_name, source_df in fm_sources:
                df_copy = source_df.copy()
                
                # Standardize column names for unified processing
                if source_name == 'FM':
                    # Original FM structure
                    df_copy['unified_stack_id'] = df_copy['MPS ID'].astype(str)
                    df_copy['unified_start_timestamp'] = df_copy['fm_on_load_timestamp']
                    df_copy['unified_end_timestamp'] = df_copy['fm_off_load_timestamp']
                    df_copy['unified_duration_minutes'] = df_copy['fm_duration_minutes']
                    df_copy['unified_reject_pct'] = df_copy['fm_reject_pct']
                    df_copy['unified_ok_sheets'] = df_copy['fm_ok_sheets']
                    df_copy['unified_reject_sheets'] = df_copy['fm_reject_sheets']
                    df_copy['unified_processing_rate'] = df_copy['fm_processing_rate']
                    df_copy['unified_quality_efficiency'] = df_copy['fm_quality_efficiency']
                    df_copy['unified_production_order'] = df_copy.get('base_production_order', '')
                    df_copy['fm_machine_type'] = 'FM'
                    df_copy['fm_source_system'] = 'FM'
                    
                elif source_name == 'TM480':
                    # TM480 structure
                    df_copy['unified_stack_id'] = df_copy['tm480_stack_number'].astype(str)
                    df_copy['unified_start_timestamp'] = df_copy['tm480_start_timestamp']
                    df_copy['unified_end_timestamp'] = df_copy['tm480_end_timestamp']
                    df_copy['unified_duration_minutes'] = df_copy['tm480_duration_minutes']
                    df_copy['unified_reject_pct'] = df_copy['tm480_reject_pct']
                    df_copy['unified_ok_sheets'] = df_copy['tm480_accepted_sheets']
                    df_copy['unified_reject_sheets'] = df_copy['tm480_rejected_sheets']
                    df_copy['unified_processing_rate'] = df_copy['tm480_processing_rate']
                    df_copy['unified_quality_efficiency'] = df_copy['tm480_quality_efficiency']
                    df_copy['unified_production_order'] = df_copy.get('base_production_order', '')
                    # fm_machine_type and fm_source_system already set in prepare method
                
                # Filter for stack numbers starting with "7"
                valid_stack_filter = df_copy['unified_stack_id'].str.startswith('7')
                df_copy = df_copy[valid_stack_filter].copy()
                
                if len(df_copy) > 0:
                    unified_fm_list.append(df_copy)
                    logger.info(f"Added {len(df_copy)} {source_name} stacks to unified FM dataset")
                else:
                    logger.warning(f"No valid stacks found in {source_name} after filtering")
            
            if not unified_fm_list:
                logger.warning("No valid FM stacks found across all sources")
                return self._create_empty_fm_matches(sm_stack_df)
            
            # Combine all FM sources
            unified_fm_df = pd.concat(unified_fm_list, ignore_index=True)
            logger.info(f"Created unified FM dataset with {len(unified_fm_df)} stacks from {len(fm_sources)} sources")
            
            # Apply stack number matching using the proven algorithm
            logger.info("Applying stack number-based matching to unified FM dataset...")
            
            # Use the existing matching logic adapted for unified FM data
            enhanced_sm_df = sm_stack_df.copy()
            match_results = []
            
            for _, sm_stack in sm_stack_df.iterrows():
                sm_completion_time = sm_stack.get('last_sheet_timestamp')
                sm_stack_number = str(sm_stack.get('Stack Number', ''))
                
                # Find FM candidates with matching stack number
                fm_candidates = unified_fm_df[unified_fm_df['unified_stack_id'] == sm_stack_number].copy()
                
                if not fm_candidates.empty:
                    # Calculate time gaps (FM must start after SM completion)
                    fm_candidates['time_gap_hours'] = (
                        fm_candidates['unified_start_timestamp'] - sm_completion_time
                    ).dt.total_seconds() / 3600
                    
                    # Filter for valid temporal matches (positive time gap)
                    valid_candidates = fm_candidates[fm_candidates['time_gap_hours'] > 0]
                    
                    if not valid_candidates.empty:
                        # If multiple valid matches, use temporal proximity as tie-breaker
                        best_match = valid_candidates.loc[valid_candidates['time_gap_hours'].idxmin()]
                        
                        match_result = {
                            'has_fm_match': True,
                            'fm_machine_type': best_match['fm_machine_type'],
                            'fm_source_system': best_match['fm_source_system'],
                            'fm_production_order': best_match['unified_production_order'],
                            'fm_on_load_timestamp': best_match['unified_start_timestamp'],
                            'fm_off_load_timestamp': best_match['unified_end_timestamp'],
                            'fm_duration_minutes': best_match['unified_duration_minutes'],
                            'fm_reject_pct': best_match['unified_reject_pct'],
                            'fm_ok_sheets': best_match['unified_ok_sheets'],
                            'fm_reject_sheets': best_match['unified_reject_sheets'],
                            'sm_to_fm_gap_minutes': best_match['time_gap_hours'] * 60,
                            'match_quality_score': 100.0,  # Perfect stack number match
                            'match_failure_reason': None
                        }
                    else:
                        # Stack number match but invalid timing
                        match_result = {
                            'has_fm_match': False,
                            'fm_machine_type': None,
                            'fm_source_system': None,
                            'fm_production_order': None,
                            'fm_on_load_timestamp': None,
                            'fm_off_load_timestamp': None,
                            'fm_duration_minutes': None,
                            'fm_reject_pct': None,
                            'fm_ok_sheets': None,
                            'fm_reject_sheets': None,
                            'sm_to_fm_gap_minutes': None,
                            'match_quality_score': 0.0,
                            'match_failure_reason': 'Temporal validation failed'
                        }
                else:
                    # No stack number match found
                    match_result = {
                        'has_fm_match': False,
                        'fm_machine_type': None,
                        'fm_source_system': None,
                        'fm_production_order': None,
                        'fm_on_load_timestamp': None,
                        'fm_off_load_timestamp': None,
                        'fm_duration_minutes': None,
                        'fm_reject_pct': None,
                        'fm_ok_sheets': None,
                        'fm_reject_sheets': None,
                        'sm_to_fm_gap_minutes': None,
                        'match_quality_score': 0.0,
                        'match_failure_reason': 'No stack number match'
                    }
                
                match_results.append(match_result)
            
            # Add match results to SM DataFrame
            match_df = pd.DataFrame(match_results)
            enhanced_sm_df = pd.concat([enhanced_sm_df.reset_index(drop=True), match_df], axis=1)
            
            # Generate comprehensive matching statistics
            total_sm_stacks = len(enhanced_sm_df)
            successful_matches = enhanced_sm_df['has_fm_match'].sum()
            match_rate = (successful_matches / total_sm_stacks * 100) if total_sm_stacks > 0 else 0
            
            # Machine type breakdown
            machine_breakdown = enhanced_sm_df[enhanced_sm_df['has_fm_match']]['fm_machine_type'].value_counts()
            
            logger.info("=== MULTI-FM MATCHING RESULTS ===")
            logger.info(f"📊 OVERALL STATISTICS:")
            logger.info(f"   • Total SM stacks: {total_sm_stacks:,}")
            logger.info(f"   • Successful matches: {successful_matches:,}")
            logger.info(f"   • Match rate: {match_rate:.1f}%")
            logger.info(f"   • Available FM sources: {len(fm_sources)}")
            logger.info(f"   • Total FM stacks: {len(unified_fm_df):,}")
            
            logger.info(f"📈 MACHINE TYPE BREAKDOWN:")
            for machine_type, count in machine_breakdown.items():
                percentage = (count / successful_matches * 100) if successful_matches > 0 else 0
                logger.info(f"   • {machine_type}: {count:,} matches ({percentage:.1f}%)")
            
            # Gap time analysis
            if successful_matches > 0:
                valid_gaps = enhanced_sm_df[enhanced_sm_df['has_fm_match']]['sm_to_fm_gap_minutes'].dropna()
                if len(valid_gaps) > 0:
                    logger.info(f"⏱️  TIME GAP ANALYSIS:")
                    logger.info(f"   • Average gap: {valid_gaps.mean():.0f} minutes ({valid_gaps.mean()/60:.1f} hours)")
                    logger.info(f"   • Gap range: {valid_gaps.min():.0f} - {valid_gaps.max():.0f} minutes")
                    logger.info(f"   • Median gap: {valid_gaps.median():.0f} minutes")
            
            logger.info("=" * 40)
            
            return enhanced_sm_df
            
        except Exception as e:
            logger.error(f"Multi-FM stack matching failed: {e}")
            return self._create_empty_fm_matches(sm_stack_df)
    
    def _create_empty_fm_matches(self, sm_stack_df: pd.DataFrame) -> pd.DataFrame:
        """Create DataFrame with empty FM matching information for error cases."""
        fm_match_cols = ['has_fm_match', 'fm_machine_type', 'fm_source_system', 'fm_production_order', 
                        'fm_on_load_timestamp', 'fm_off_load_timestamp', 'fm_duration_minutes', 
                        'fm_reject_pct', 'fm_ok_sheets', 'fm_reject_sheets', 'sm_to_fm_gap_minutes',
                        'match_quality_score', 'match_failure_reason']
        
        df = sm_stack_df.copy()
        for col in fm_match_cols:
            if col == 'has_fm_match':
                df[col] = False
            elif col == 'match_failure_reason':
                df[col] = 'Matching system error'
            elif col in ['fm_production_order', 'fm_machine_type', 'fm_source_system']:
                df[col] = None
            elif col in ['fm_on_load_timestamp', 'fm_off_load_timestamp']:
                df[col] = pd.NaT
            else:
                df[col] = np.nan
        return df
    
    def create_matched_stacks_table(self) -> pd.DataFrame:
        """
        Create unified table containing only complete SM-FM matched stacks.
        
        This method filters the full unified table to include only time-series records
        that have complete manufacturing context with successful SM-FM stack matching.
        
        Filtering criteria:
        - Records must have SM stack context (not null sm_stack_number)
        - Records must have successful FM matching (has_fm_match = True)
        - Records must have valid manufacturing timing and quality data
        
        Returns:
            Filtered DataFrame with only complete SM-FM manufacturing flows
        """
        try:
            logger.info("🔄 Creating matched stacks table...")
            
            # Step 1: Create full unified table
            logger.info("Step 1: Creating complete unified table...")
            full_unified_df = self.create_unified_table()
            
            logger.info(f"Full unified table: {len(full_unified_df):,} time-series records")
            
            # Step 2: Filter for records with SM stack context
            logger.info("Step 2: Filtering for records with SM stack context...")
            
            # Records must have valid SM stack context
            sm_context_filter = (
                full_unified_df['sm_stack_number'].notna() & 
                (full_unified_df['sm_stack_number'] != '') &
                (full_unified_df['sm_stack_number'] != 'None')
            )
            
            sm_context_records = full_unified_df[sm_context_filter]
            logger.info(f"Records with SM context: {len(sm_context_records):,} ({len(sm_context_records)/len(full_unified_df)*100:.1f}%)")
            
            # Step 3: Filter for successful FM matching
            logger.info("Step 3: Filtering for successful FM matching...")
            
            if 'has_fm_match' not in sm_context_records.columns:
                logger.warning("No 'has_fm_match' column found - returning records with SM context only")
                matched_stacks_df = sm_context_records
            else:
                # Records must have successful FM matching
                fm_match_filter = sm_context_records['has_fm_match'] == True
                matched_stacks_df = sm_context_records[fm_match_filter]
                
                logger.info(f"Records with FM matching: {len(matched_stacks_df):,} ({len(matched_stacks_df)/len(sm_context_records)*100:.1f}% of SM context records)")
            
            # Step 4: Validate data completeness
            logger.info("Step 4: Validating manufacturing data completeness...")
            
            # Check for critical manufacturing fields
            critical_fields = ['sm_stack_number', 'sm_scrap_pct', 'timestamp', 'Speed']
            missing_critical = []
            
            for field in critical_fields:
                if field in matched_stacks_df.columns:
                    missing_count = matched_stacks_df[field].isna().sum()
                    if missing_count > 0:
                        missing_critical.append(f"{field}: {missing_count:,} missing values")
                else:
                    missing_critical.append(f"{field}: column not found")
            
            if missing_critical:
                logger.warning(f"Data completeness issues: {missing_critical}")
            else:
                logger.info("✅ All critical manufacturing fields complete")
            
            # Step 5: Generate machine type analysis
            if 'fm_machine_type' in matched_stacks_df.columns:
                logger.info("Step 5: Analyzing machine type distribution...")
                machine_breakdown = matched_stacks_df['fm_machine_type'].value_counts()
                
                logger.info("🏭 MACHINE TYPE BREAKDOWN:")
                for machine_type, count in machine_breakdown.items():
                    percentage = (count / len(matched_stacks_df) * 100) if len(matched_stacks_df) > 0 else 0
                    logger.info(f"   • {machine_type}: {count:,} records ({percentage:.1f}%)")
            
            # Step 6: Sort by timestamp for chronological analysis
            logger.info("Step 6: Sorting records chronologically...")
            matched_stacks_df = matched_stacks_df.sort_values(['work_center', 'timestamp'])
            
            # Step 7: Final statistics
            logger.info("=" * 50)
            logger.info("📊 MATCHED STACKS TABLE SUMMARY:")
            logger.info(f"   • Original unified records: {len(full_unified_df):,}")
            logger.info(f"   • Records with SM context: {len(sm_context_records):,}")
            logger.info(f"   • Final matched records: {len(matched_stacks_df):,}")
            logger.info(f"   • Data reduction: {(1 - len(matched_stacks_df)/len(full_unified_df))*100:.1f}%")
            logger.info(f"   • Columns: {len(matched_stacks_df.columns)}")
            logger.info(f"   • Manufacturing completeness: 100% (SM-FM matched flows only)")
            
            # Temporal analysis
            if len(matched_stacks_df) > 0:
                time_span = matched_stacks_df['timestamp'].max() - matched_stacks_df['timestamp'].min()
                logger.info(f"   • Time span: {time_span.days} days")
                
                work_centers = matched_stacks_df['work_center'].nunique()
                logger.info(f"   • Work centers: {work_centers}")
                
                if 'sm_stack_number' in matched_stacks_df.columns:
                    unique_stacks = matched_stacks_df['sm_stack_number'].nunique()
                    logger.info(f"   • Unique SM stacks represented: {unique_stacks:,}")
            
            logger.info("=" * 50)
            logger.info("✅ Matched stacks table created successfully!")
            
            return matched_stacks_df
            
        except Exception as e:
            logger.error(f"❌ Failed to create matched stacks table: {e}")
            raise
    
    def export_unified_table(self, unified_df: pd.DataFrame, output_dir: str, filename: str = None) -> str:
        """
        Export unified table to CSV with proper formatting and metadata.
        
        Args:
            unified_df: DataFrame to export
            output_dir: Directory path for export
            filename: Optional custom filename (auto-generated if None)
            
        Returns:
            Full path to exported CSV file
        """
        try:
            logger.info("📁 Exporting unified table to CSV...")
            
            # Step 1: Prepare output directory
            output_path = Path(output_dir)
            if not output_path.exists():
                logger.info(f"Creating output directory: {output_path}")
                output_path.mkdir(parents=True, exist_ok=True)
            
            # Step 2: Generate filename if not provided
            if filename is None:
                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M")
                filename = f"unified_matched_stacks_{timestamp}.csv"
            
            # Ensure .csv extension
            if not filename.endswith('.csv'):
                filename += '.csv'
            
            full_path = output_path / filename
            
            # Step 3: Prepare data for export
            logger.info("Step 1: Preparing data for export...")
            export_df = unified_df.copy()
            
            # Convert timestamps to string format for CSV compatibility
            timestamp_columns = [col for col in export_df.columns if 'timestamp' in col.lower() or col == 'timestamp']
            for col in timestamp_columns:
                if col in export_df.columns:
                    export_df[col] = export_df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Step 4: Generate metadata header
            logger.info("Step 2: Generating metadata header...")
            metadata_lines = [
                "# Manufacturing Unified Table Export",
                f"# Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"# Total Records: {len(export_df):,}",
                f"# Total Columns: {len(export_df.columns)}",
                f"# Data Type: Complete SM-FM Matched Manufacturing Flows",
                "#"
            ]
            
            # Add column summary
            if 'fm_machine_type' in export_df.columns:
                machine_breakdown = export_df['fm_machine_type'].value_counts()
                metadata_lines.append("# Machine Type Distribution:")
                for machine_type, count in machine_breakdown.items():
                    percentage = (count / len(export_df) * 100) if len(export_df) > 0 else 0
                    metadata_lines.append(f"#   {machine_type}: {count:,} records ({percentage:.1f}%)")
                metadata_lines.append("#")
            
            # Add temporal summary
            if 'timestamp' in export_df.columns:
                time_min = export_df['timestamp'].min()
                time_max = export_df['timestamp'].max()
                metadata_lines.extend([
                    "# Temporal Coverage:",
                    f"#   Start: {time_min}",
                    f"#   End: {time_max}",
                    "#"
                ])
            
            # Add work center summary
            if 'work_center' in export_df.columns:
                work_centers = export_df['work_center'].value_counts()
                metadata_lines.append("# Work Center Distribution:")
                for center, count in work_centers.items():
                    percentage = (count / len(export_df) * 100) if len(export_df) > 0 else 0
                    metadata_lines.append(f"#   {center}: {count:,} records ({percentage:.1f}%)")
                metadata_lines.append("#")
            
            metadata_lines.append("# Column Definitions:")
            metadata_lines.append("# - timestamp: Manufacturing time series timestamp")
            metadata_lines.append("# - work_center: Production line identifier (SM270, SM300, etc.)")
            metadata_lines.append("# - Speed: Production line speed (m/min)")
            metadata_lines.append("# - sm_*: Sheet machine production metrics")
            metadata_lines.append("# - fm_*: Finishing machine processing metrics")
            metadata_lines.append("# - has_fm_match: SM-FM correlation success indicator")
            metadata_lines.append("# - fm_machine_type: Finishing machine type (FM, TM480)")
            metadata_lines.append("#")
            
            # Step 5: Write CSV with metadata
            logger.info("Step 3: Writing CSV file...")
            
            with open(full_path, 'w', encoding='utf-8') as f:
                # Write metadata header
                for line in metadata_lines:
                    f.write(line + '\n')
                
                # Write CSV data
                export_df.to_csv(f, index=False, float_format='%.6f')
            
            # Step 6: Validate export
            logger.info("Step 4: Validating export...")
            
            # Check file size
            file_size = full_path.stat().st_size
            file_size_mb = file_size / (1024 * 1024)
            
            # Verify by reading back a sample
            verification_df = pd.read_csv(full_path, comment='#', nrows=5)
            
            logger.info("=" * 50)
            logger.info("📊 CSV EXPORT SUMMARY:")
            logger.info(f"   • File path: {full_path}")
            logger.info(f"   • File size: {file_size_mb:.2f} MB")
            logger.info(f"   • Records exported: {len(export_df):,}")
            logger.info(f"   • Columns exported: {len(export_df.columns)}")
            logger.info(f"   • Verification: {len(verification_df)} sample rows read successfully")
            
            # Manufacturing-specific validation
            if 'has_fm_match' in export_df.columns:
                match_rate = export_df['has_fm_match'].mean() * 100
                logger.info(f"   • FM match rate: {match_rate:.1f}% (should be 100% for matched table)")
            
            if 'sm_stack_number' in export_df.columns:
                unique_stacks = export_df['sm_stack_number'].nunique()
                logger.info(f"   • Unique SM stacks: {unique_stacks:,}")
            
            logger.info("=" * 50)
            logger.info(f"✅ CSV export completed successfully: {filename}")
            
            return str(full_path)
            
        except Exception as e:
            logger.error(f"❌ Failed to export unified table: {e}")
            raise
    
    def create_and_export_matched_stacks(self, output_dir: str, filename: str = None) -> str:
        """
        Convenience method: Create matched stacks table and export to CSV in one step.
        
        Args:
            output_dir: Directory path for export
            filename: Optional custom filename
            
        Returns:
            Full path to exported CSV file
        """
        try:
            logger.info("🚀 Creating and exporting matched stacks table...")
            
            # Step 1: Create matched stacks table
            matched_stacks_df = self.create_matched_stacks_table()
            
            # Step 2: Export to CSV
            export_path = self.export_unified_table(matched_stacks_df, output_dir, filename)
            
            logger.info("🎉 Matched stacks table creation and export completed successfully!")
            return export_path
            
        except Exception as e:
            logger.error(f"❌ Failed to create and export matched stacks: {e}")
            raise
    
    def validate_unified_table(self, unified_df: pd.DataFrame) -> DataValidationResult:
        """
        Comprehensive manufacturing data validation following PRP specifications.
        
        Validation includes:
        - Temporal consistency validation (following DATA_WITHOUT_THICKNESS.md)
        - Manufacturing domain bounds checking
        - Data completeness analysis with criticality scoring
        - Range validation with manufacturing-specific limits
        - Production flow logic validation (SM before FM)
        
        Args:
            unified_df: Enhanced unified DataFrame to validate
            
        Returns:
            DataValidationResult with validation status, errors, warnings, and quality score
        """
        try:
            logger.info(f"Validating unified table with {len(unified_df)} rows and {len(unified_df.columns)} columns...")
            
            validation_result = DataValidationResult()
            validation_errors = []
            validation_warnings = []
            validation_recommendations = []
            
            # =================================================================
            # 1. BASIC DATA STRUCTURE VALIDATION
            # =================================================================
            
            # Check for minimum required columns
            required_columns = ['timestamp', 'work_center', 'Speed']
            missing_required = [col for col in required_columns if col not in unified_df.columns]
            if missing_required:
                validation_errors.append(f"Missing required columns: {missing_required}")
            
            # Check for empty DataFrame
            if len(unified_df) == 0:
                validation_errors.append("Unified table is empty - no data to validate")
                validation_result.is_valid = False
                validation_result.errors = validation_errors
                validation_result.quality_score = 0.0
                return validation_result
            
            # =================================================================
            # 2. TEMPORAL CONSISTENCY VALIDATION
            # =================================================================
            
            if 'timestamp' in unified_df.columns:
                # Check for valid timestamps
                null_timestamps = unified_df['timestamp'].isna().sum()
                if null_timestamps > 0:
                    null_pct = null_timestamps / len(unified_df) * 100
                    if null_pct > 10:
                        validation_errors.append(f"High percentage of null timestamps: {null_pct:.1f}%")
                    else:
                        validation_warnings.append(f"Some null timestamps detected: {null_timestamps} records ({null_pct:.1f}%)")
                
                # Check temporal ordering by work center
                for work_center in unified_df['work_center'].unique():
                    wc_data = unified_df[unified_df['work_center'] == work_center]['timestamp'].dropna()
                    if len(wc_data) > 1:
                        unsorted_count = (wc_data.diff() < pd.Timedelta(0)).sum()
                        if unsorted_count > 0:
                            validation_warnings.append(f"Temporal ordering issues in {work_center}: {unsorted_count} records")
                
                # Check for reasonable timestamp range (manufacturing context)
                if not unified_df['timestamp'].dropna().empty:
                    date_range = unified_df['timestamp'].max() - unified_df['timestamp'].min()
                    if date_range.days > 365:
                        validation_warnings.append(f"Large date range detected: {date_range.days} days - ensure this is expected")
            
            # =================================================================
            # 3. MANUFACTURING DOMAIN BOUNDS CHECKING
            # =================================================================
            
            domain_checks = 0
            passed_checks = 0
            
            # Speed validation (0-300 m/min)
            if 'Speed' in unified_df.columns:
                domain_checks += 1
                speed_outliers = ((unified_df['Speed'] < 0) | (unified_df['Speed'] > 300)).sum()
                if speed_outliers == 0:
                    passed_checks += 1
                elif speed_outliers < len(unified_df) * 0.01:  # Less than 1%
                    validation_warnings.append(f"Minor speed outliers detected: {speed_outliers} records outside 0-300 m/min range")
                    passed_checks += 0.8  # Partial credit
                else:
                    validation_errors.append(f"Significant speed outliers: {speed_outliers} records outside 0-300 m/min range")
            
            # Scrap percentage validation (0-100%)
            if 'sm_scrap_pct' in unified_df.columns:
                domain_checks += 1
                scrap_outliers = ((unified_df['sm_scrap_pct'] < 0) | (unified_df['sm_scrap_pct'] > 100)).sum()
                if scrap_outliers == 0:
                    passed_checks += 1
                else:
                    validation_errors.append(f"Invalid scrap percentages: {scrap_outliers} records outside 0-100% range")
            
            # Production efficiency validation (0-100%)
            if 'production_efficiency_pct' in unified_df.columns:
                domain_checks += 1
                efficiency_outliers = ((unified_df['production_efficiency_pct'] < 0) | (unified_df['production_efficiency_pct'] > 100)).sum()
                if efficiency_outliers == 0:
                    passed_checks += 1
                else:
                    validation_warnings.append(f"Production efficiency outliers: {efficiency_outliers} records outside 0-100% range")
                    passed_checks += 0.8
            
            # Duration validation (positive values)
            duration_cols = ['sm_duration_minutes', 'fm_duration_minutes', 'total_stop_duration_minutes']
            for col in duration_cols:
                if col in unified_df.columns:
                    domain_checks += 1
                    negative_durations = (unified_df[col] < 0).sum()
                    if negative_durations == 0:
                        passed_checks += 1
                    else:
                        validation_warnings.append(f"Negative durations in {col}: {negative_durations} records")
                        passed_checks += 0.8
            
            # =================================================================
            # 4. PRODUCTION FLOW LOGIC VALIDATION
            # =================================================================
            
            # SM before FM temporal logic
            if all(col in unified_df.columns for col in ['last_sheet_timestamp', 'fm_on_load_timestamp', 'has_fm_match']):
                matched_stacks = unified_df[unified_df['has_fm_match'] == True]
                if len(matched_stacks) > 0:
                    temporal_violations = (matched_stacks['fm_on_load_timestamp'] <= matched_stacks['last_sheet_timestamp']).sum()
                    if temporal_violations > 0:
                        validation_errors.append(f"Production flow violations: {temporal_violations} FM loads before SM completion")
                    else:
                        passed_checks += 1
                        domain_checks += 1
            
            # Stack matching rate validation
            if 'has_fm_match' in unified_df.columns:
                domain_checks += 1
                match_rate = unified_df['has_fm_match'].mean() * 100
                if match_rate >= 85:
                    passed_checks += 1
                elif match_rate >= 70:
                    validation_warnings.append(f"Stack matching rate {match_rate:.1f}% below 85% target but above 70%")
                    passed_checks += 0.7
                else:
                    validation_errors.append(f"Low stack matching rate: {match_rate:.1f}% below 70% minimum")
            
            # =================================================================
            # 5. DATA COMPLETENESS ANALYSIS
            # =================================================================
            
            # Critical columns completeness
            critical_columns = ['timestamp', 'work_center', 'Speed']
            completeness_scores = []
            
            for col in critical_columns:
                if col in unified_df.columns:
                    completeness = (1 - unified_df[col].isna().mean()) * 100
                    completeness_scores.append(completeness)
                    if completeness < 95:
                        validation_warnings.append(f"Critical column {col} only {completeness:.1f}% complete")
            
            # Enhanced columns completeness
            enhanced_columns = ['sm_scrap_pct', 'production_efficiency_pct', 'speed_avg', 'has_fm_match']
            for col in enhanced_columns:
                if col in unified_df.columns:
                    completeness = (1 - unified_df[col].isna().mean()) * 100
                    completeness_scores.append(completeness)
                    if completeness < 80:
                        validation_warnings.append(f"Enhanced column {col} only {completeness:.1f}% complete")
            
            # =================================================================
            # 6. MANUFACTURING-SPECIFIC QUALITY METRICS
            # =================================================================
            
            # Stack-level data coverage
            if 'sm_stack_number' in unified_df.columns:
                stack_coverage = unified_df['sm_stack_number'].notna().mean() * 100
                if stack_coverage < 70:
                    validation_warnings.append(f"Low stack-level data coverage: {stack_coverage:.1f}%")
                    validation_recommendations.append("Consider investigating missing stack assignments")
            
            # Speed aggregation coverage
            if 'speed_readings_count' in unified_df.columns:
                avg_readings = unified_df['speed_readings_count'].mean()
                if avg_readings < 5:
                    validation_warnings.append(f"Low average speed readings per stack: {avg_readings:.1f}")
                    validation_recommendations.append("Consider reviewing speed data collection frequency")
            
            # Production efficiency distribution
            if 'production_efficiency_pct' in unified_df.columns:
                low_efficiency = (unified_df['production_efficiency_pct'] < 80).mean() * 100
                if low_efficiency > 20:
                    validation_warnings.append(f"High proportion of low efficiency stacks: {low_efficiency:.1f}%")
                    validation_recommendations.append("Review stoppage patterns and production optimization opportunities")
            
            # =================================================================
            # 7. PRODUCTION ORDER DISTRIBUTION ANALYSIS
            # =================================================================
            
            # Analyze SM production order patterns
            sm_order_analysis = self._analyze_production_order_distribution('sm_production_order', unified_df)
            if sm_order_analysis:
                validation_warnings.extend(sm_order_analysis['warnings'])
                validation_recommendations.extend(sm_order_analysis['recommendations'])
            
            # Analyze FM production order patterns
            fm_order_analysis = self._analyze_production_order_distribution('fm_production_order', unified_df)
            if fm_order_analysis:
                validation_warnings.extend(fm_order_analysis['warnings'])
                validation_recommendations.extend(fm_order_analysis['recommendations'])
            
            # Cross-analysis: SM-FM production order alignment
            if 'sm_production_order' in unified_df.columns and 'fm_production_order' in unified_df.columns:
                sm_orders = set(unified_df['sm_production_order'].dropna().astype(str))
                fm_orders = set(unified_df['fm_production_order'].dropna().astype(str))
                
                # Check for overlapping production orders
                overlap = sm_orders.intersection(fm_orders)
                overlap_rate = len(overlap) / max(len(sm_orders), 1) * 100
                
                if overlap_rate < 10:
                    validation_warnings.append(f"Very low SM-FM production order overlap: {overlap_rate:.1f}% ({len(overlap)} common orders)")
                    validation_recommendations.append("Review data collection timing - SM and FM datasets may be from different time periods")
                    validation_recommendations.append("Consider collecting data for same production orders across both SM and FM processes")
                
                # Show sample orders for debugging
                if len(sm_orders) > 0 and len(fm_orders) > 0:
                    sample_sm = list(sm_orders)[:3]
                    sample_fm = list(fm_orders)[:3]
                    logger.info(f"Sample SM production orders: {sample_sm}")
                    logger.info(f"Sample FM production orders: {sample_fm}")
                    logger.info(f"Production order overlap analysis: {len(overlap)} common out of {len(sm_orders)} SM + {len(fm_orders)} FM")
            
            # =================================================================
            # 7. CALCULATE OVERALL QUALITY SCORE
            # =================================================================
            
            # Base score from domain validation
            domain_score = (passed_checks / max(domain_checks, 1)) * 100 if domain_checks > 0 else 0
            
            # Completeness score
            completeness_score = np.mean(completeness_scores) if completeness_scores else 0
            
            # Penalty for errors and warnings
            error_penalty = len(validation_errors) * 15  # -15 points per error
            warning_penalty = len(validation_warnings) * 5  # -5 points per warning
            
            # Calculate final quality score (0-100)
            quality_score = max(0, min(100, (domain_score * 0.6 + completeness_score * 0.4) - error_penalty - warning_penalty))
            
            # =================================================================
            # 8. GENERATE RECOMMENDATIONS
            # =================================================================
            
            if quality_score < 70:
                validation_recommendations.append("Data quality is below acceptable threshold - review data collection processes")
            
            if quality_score >= 90:
                validation_recommendations.append("Excellent data quality - suitable for advanced analytics")
            elif quality_score >= 80:
                validation_recommendations.append("Good data quality - minor improvements recommended")
            elif quality_score >= 70:
                validation_recommendations.append("Acceptable data quality - some data quality improvements needed")
            
            # Manufacturing-specific recommendations
            if 'has_fm_match' in unified_df.columns:
                match_rate = unified_df['has_fm_match'].mean() * 100
                if match_rate < 85:
                    validation_recommendations.append("Improve SM-FM stack matching by reviewing production order alignment")
            
            if len(validation_errors) == 0 and quality_score >= 70:
                validation_result.is_valid = True
            else:
                validation_result.is_valid = False
            
            validation_result.errors = validation_errors
            validation_result.warnings = validation_warnings
            validation_result.quality_score = round(quality_score, 1)
            validation_result.recommendations = validation_recommendations
            
            # Log validation summary
            logger.info(f"Validation completed:")
            logger.info(f"  - Valid: {validation_result.is_valid}")
            logger.info(f"  - Quality Score: {validation_result.quality_score}%")
            logger.info(f"  - Errors: {len(validation_errors)}")
            logger.info(f"  - Warnings: {len(validation_warnings)}")
            logger.info(f"  - Domain Checks Passed: {passed_checks:.1f}/{domain_checks}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Validation framework failed: {e}")
            return DataValidationResult(
                is_valid=False,
                errors=[f"Validation system error: {e}"],
                quality_score=0.0,
                recommendations=["Review validation framework implementation"]
            )
    
    def _analyze_production_order_distribution(self, column_name: str, unified_df: pd.DataFrame) -> dict:
        """
        Analyze production order distribution patterns for data quality assessment.
        
        Args:
            column_name: Name of production order column to analyze
            unified_df: Unified DataFrame to analyze
            
        Returns:
            Dictionary with warnings and recommendations
        """
        try:
            if column_name not in unified_df.columns:
                return None
            
            analysis_result = {'warnings': [], 'recommendations': []}
            
            # Get non-null production orders
            orders = unified_df[column_name].dropna().astype(str)
            orders = orders[orders != 'nan']  # Remove string 'nan' values
            
            if len(orders) == 0:
                analysis_result['warnings'].append(f"No valid production orders found in {column_name}")
                analysis_result['recommendations'].append(f"Check {column_name} data quality and format")
                return analysis_result
            
            # Analyze unique orders
            unique_orders = orders.unique()
            order_counts = orders.value_counts()
            
            # Coverage analysis
            coverage = len(orders) / len(unified_df) * 100
            if coverage < 50:
                analysis_result['warnings'].append(f"{column_name} coverage only {coverage:.1f}%")
                analysis_result['recommendations'].append(f"Improve {column_name} data collection coverage")
            
            # Diversity analysis
            if len(unique_orders) < 3:
                analysis_result['warnings'].append(f"Low production order diversity in {column_name}: only {len(unique_orders)} unique orders")
                analysis_result['recommendations'].append("Consider collecting data across more production orders for better analysis")
            
            # Pattern analysis - check for common patterns
            base_orders = set()
            for order in unique_orders:
                if '/' in order:
                    base_orders.add(order.split('/')[0])
                else:
                    base_orders.add(order)
            
            if len(base_orders) != len(unique_orders):
                logger.info(f"{column_name}: {len(unique_orders)} full orders reduce to {len(base_orders)} base orders")
            
            # Concentration analysis
            top_order_pct = order_counts.iloc[0] / len(orders) * 100
            if top_order_pct > 80:
                analysis_result['warnings'].append(f"High concentration in {column_name}: top order represents {top_order_pct:.1f}% of data")
                analysis_result['recommendations'].append("Consider collecting more diverse production order data")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Production order analysis failed for {column_name}: {e}")
            return {'warnings': [f"Failed to analyze {column_name} distribution"], 'recommendations': []}
    
    def generate_data_quality_report(self) -> dict:
        """
        Generate comprehensive data quality report highlighting time/order coverage gaps.
        
        Returns:
            Dictionary with detailed data quality analysis
        """
        try:
            logger.info("Generating comprehensive data quality report...")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'data_sources': {},
                'coverage_analysis': {},
                'alignment_issues': [],
                'recommendations': []
            }
            
            # Analyze each data source
            for source_name, df in self.loaded_data.items():
                source_report = {
                    'records': len(df),
                    'columns': len(df.columns),
                    'date_range': {},
                    'production_orders': {}
                }
                
                # Date range analysis
                timestamp_cols = []
                for col in df.columns:
                    if 'timestamp' in col.lower() or 'time' in col.lower() or 'date' in col.lower():
                        timestamp_cols.append(col)
                
                if timestamp_cols:
                    for col in timestamp_cols[:2]:  # Analyze first 2 timestamp columns
                        try:
                            if df[col].dtype == 'datetime64[ns]':
                                timestamps = df[col].dropna()
                            else:
                                timestamps = pd.to_datetime(df[col], errors='coerce').dropna()
                            
                            if len(timestamps) > 0:
                                source_report['date_range'][col] = {
                                    'min_date': timestamps.min().isoformat(),
                                    'max_date': timestamps.max().isoformat(),
                                    'span_days': (timestamps.max() - timestamps.min()).days,
                                    'valid_timestamps': len(timestamps),
                                    'coverage_pct': len(timestamps) / len(df) * 100
                                }
                        except Exception as e:
                            logger.warning(f"Could not analyze timestamp column {col} in {source_name}: {e}")
                
                # Production order analysis
                prod_order_cols = [col for col in df.columns if 'production' in col.lower() and 'order' in col.lower()]
                for col in prod_order_cols:
                    try:
                        orders = df[col].dropna().astype(str)
                        orders_clean = orders[orders != 'nan']
                        if len(orders_clean) > 0:
                            unique_orders = orders_clean.unique()
                            base_orders = set()
                            for order in unique_orders:
                                if '/' in order:
                                    base_orders.add(order.split('/')[0])
                                else:
                                    base_orders.add(order)
                            
                            source_report['production_orders'][col] = {
                                'total_records': len(orders_clean),
                                'unique_orders': len(unique_orders),
                                'base_orders': len(base_orders),
                                'coverage_pct': len(orders_clean) / len(df) * 100,
                                'sample_orders': list(unique_orders)[:3]
                            }
                    except Exception as e:
                        logger.warning(f"Could not analyze production order column {col} in {source_name}: {e}")
                
                report['data_sources'][source_name] = source_report
            
            # Cross-source alignment analysis
            logger.info("Analyzing cross-source data alignment...")
            
            # Time range alignment
            all_min_dates = []
            all_max_dates = []
            
            for source_name, source_data in report['data_sources'].items():
                for col, date_info in source_data['date_range'].items():
                    all_min_dates.append(pd.to_datetime(date_info['min_date']))
                    all_max_dates.append(pd.to_datetime(date_info['max_date']))
            
            if all_min_dates and all_max_dates:
                global_min = min(all_min_dates)
                global_max = max(all_max_dates)
                total_span = (global_max - global_min).days
                
                report['coverage_analysis']['global_time_range'] = {
                    'min_date': global_min.isoformat(),
                    'max_date': global_max.isoformat(),
                    'span_days': total_span
                }
                
                # Check for time gaps between sources
                for source_name, source_data in report['data_sources'].items():
                    for col, date_info in source_data['date_range'].items():
                        source_min = pd.to_datetime(date_info['min_date'])
                        source_max = pd.to_datetime(date_info['max_date'])
                        
                        gap_before = (source_min - global_min).days
                        gap_after = (global_max - source_max).days
                        
                        if gap_before > 30:  # More than 30 days gap (significant for manufacturing)
                            report['alignment_issues'].append(f"{source_name} starts {gap_before} days after global start")
                        elif gap_before > 7:  # 1-4 weeks gap (concerning but may be acceptable)
                            report['alignment_issues'].append(f"{source_name} starts {gap_before} days after global start (review data collection timing)")
                        
                        if gap_after > 30:  # More than 30 days gap
                            report['alignment_issues'].append(f"{source_name} ends {gap_after} days before global end")
                        elif gap_after > 7:  # 1-4 weeks gap
                            report['alignment_issues'].append(f"{source_name} ends {gap_after} days before global end (review data collection timing)")
            
            # Production order alignment analysis
            sm_orders = set()
            fm_orders = set()
            
            for source_name, source_data in report['data_sources'].items():
                for col, order_info in source_data['production_orders'].items():
                    sample_orders = set(order_info['sample_orders'])
                    if 'sm' in source_name.lower():
                        sm_orders.update(sample_orders)
                    elif 'fm' in source_name.lower():
                        fm_orders.update(sample_orders)
            
            if sm_orders and fm_orders:
                overlap = sm_orders.intersection(fm_orders)
                overlap_rate = len(overlap) / max(len(sm_orders), len(fm_orders)) * 100
                
                report['coverage_analysis']['production_order_overlap'] = {
                    'sm_orders': len(sm_orders),
                    'fm_orders': len(fm_orders),
                    'common_orders': len(overlap),
                    'overlap_rate_pct': overlap_rate
                }
                
                if overlap_rate < 10:
                    report['alignment_issues'].append(f"Very low SM-FM production order overlap: {overlap_rate:.1f}%")
                    report['recommendations'].append("Review data collection timing - ensure SM and FM data cover same production periods")
            
            # Generate recommendations
            if len(report['alignment_issues']) > 0:
                report['recommendations'].extend([
                    "Coordinate data collection across SM and FM systems",
                    "Ensure data represents same time periods and production batches",
                    "Consider implementing real-time data synchronization"
                ])
            
            if report['coverage_analysis'].get('global_time_range', {}).get('span_days', 0) < 7:
                report['recommendations'].append("Consider collecting data over longer time periods for better analysis")
            
            logger.info(f"Data quality report completed: {len(report['alignment_issues'])} issues found")
            return report
            
        except Exception as e:
            logger.error(f"Data quality report generation failed: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'recommendations': ['Review data quality report implementation']
            }

# Example usage function for testing
def create_unified_table(thickness_df=None, speed_df=None, stop_df=None, 
                        sm_stack_df=None, fm_stack_df=None, vm_capacity_df=None):
    """
    Standalone function that matches the user's requested algorithm signature.
    
    This function provides backward compatibility with the algorithm provided
    while leveraging the new loader infrastructure.
    """
    logger.info("Creating unified table using provided DataFrames...")
    
    # Create temporary loader instance
    loader = ManufacturingDataLoader()
    
    # Override loaded data with provided DataFrames (process timestamps if needed)
    if speed_df is not None:
        speed_processed = speed_df.copy()
        if 'timestamp' not in speed_processed.columns:
            # Create timestamp from separate date/time columns
            speed_processed['timestamp'] = speed_processed.apply(
                lambda x: parse_datetime(x.get('Log Date'), x.get('Log Time')), axis=1
            )
        loader.loaded_data['speed'] = speed_processed
        
    if stop_df is not None:
        stop_processed = stop_df.copy()
        if 'stop_timestamp' not in stop_processed.columns:
            # Create stop and restart timestamps
            stop_processed['stop_timestamp'] = stop_processed.apply(
                lambda x: parse_datetime(x.get('Stop Date'), x.get('Stop Time')), axis=1
            )
            stop_processed['restart_timestamp'] = stop_processed.apply(
                lambda x: parse_datetime(x.get('Restart Date'), x.get('Restart Time')), axis=1
            )
        loader.loaded_data['stop'] = stop_processed
        
    if sm_stack_df is not None:
        sm_processed = sm_stack_df.copy()
        if 'first_sheet_timestamp' not in sm_processed.columns:
            # Create timestamps from single datetime columns
            sm_processed['first_sheet_timestamp'] = sm_processed['First Sheet Date Time'].apply(parse_single_datetime)
            sm_processed['last_sheet_timestamp'] = sm_processed['Last Sheet Date Time'].apply(parse_single_datetime)
        loader.loaded_data['sm_stack'] = sm_processed
        
    if fm_stack_df is not None:
        fm_processed = fm_stack_df.copy()
        if 'on_load_timestamp' not in fm_processed.columns:
            # Handle different possible column names
            on_load_col = None
            off_load_col = None
            
            # Check for various column names
            for col in fm_processed.columns:
                if 'on-load' in col.lower() or 'onload' in col.lower():
                    on_load_col = col
                elif 'off-load' in col.lower() or 'offload' in col.lower():
                    off_load_col = col
            
            # Try to extract from the complex header structure
            if on_load_col is None and len(fm_processed.columns) >= 3:
                on_load_col = fm_processed.columns[2]  # 3rd column is typically On-Load
            if off_load_col is None and len(fm_processed.columns) >= 4:
                off_load_col = fm_processed.columns[3]  # 4th column is typically Off-Load
            
            if on_load_col and off_load_col:
                fm_processed['on_load_timestamp'] = fm_processed[on_load_col].apply(parse_single_datetime)
                fm_processed['off_load_timestamp'] = fm_processed[off_load_col].apply(parse_single_datetime)
        loader.loaded_data['fm_stack'] = fm_processed
        
    if vm_capacity_df is not None:
        loader.loaded_data['vm_capacity'] = vm_capacity_df
    
    # If thickness_df is provided, use it as the base instead of deriving from speed
    if thickness_df is not None:
        # Process thickness data with sensor metrics if needed
        base_df = thickness_df.copy()
        
        # Calculate thickness metrics if sensor columns exist
        sensor_cols = [f'sensor_{i:02d}' for i in range(1, 11)]
        if any(col in base_df.columns for col in sensor_cols):
            thickness_metrics = base_df.apply(calculate_thickness_metrics, axis=1)
            base_df = pd.concat([base_df, thickness_metrics], axis=1)
        
        # Continue with integration steps
        if speed_df is not None:
            base_df = loader._integrate_speed_data(base_df, speed_df)
        if stop_df is not None:
            base_df = loader._add_stoppage_features(base_df, stop_df)
        if sm_stack_df is not None:
            base_df = loader._map_sm_stack_data(base_df, sm_stack_df)
        if fm_stack_df is not None:
            base_df = loader._map_fm_stack_data(base_df, fm_stack_df)
        if vm_capacity_df is not None:
            base_df = loader._add_product_specifications(base_df, vm_capacity_df)
        
        base_df = loader._add_temporal_features(base_df)
        base_df = loader._add_sequence_features(base_df)
        
        return base_df
    
    # Otherwise use the standard unified table creation
    return loader.create_unified_table()

if __name__ == "__main__":
    # Example usage
    loader = ManufacturingDataLoader()
    unified_df = loader.create_unified_table()
    print(f"Unified table created with {len(unified_df):,} rows and {len(unified_df.columns)} columns")