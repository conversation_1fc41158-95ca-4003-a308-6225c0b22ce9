"""
Product Encoding Module for Manufacturing Data

Converts string product names into structured numeric features for ML models.
Extracts meaningful manufacturing attributes from product codes and names.
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class ProductEncoder:
    """
    Encodes product information into numeric features for manufacturing forecasting
    
    Extracts features like:
    - Product family/type 
    - Thickness
    - Dimensions (length x width)
    - Product complexity score
    - Material type indicators
    """
    
    def __init__(self):
        self.product_type_mapping = {}
        self.thickness_mapping = {}
        self.dimension_categories = {}
        self.fitted = False
        
    def fit(self, products: pd.Series) -> 'ProductEncoder':
        """
        Fit encoder on product data to create mappings
        
        Args:
            products: Series of product names/codes
            
        Returns:
            self
        """
        logger.info(f"Fitting ProductEncoder on {len(products)} products")
        
        # Extract product types
        product_types = []
        for product in products.dropna().unique():
            product_type = self._extract_product_type(product)
            product_types.append(product_type)
        
        # Create product type mapping
        unique_types = list(set(product_types))
        self.product_type_mapping = {ptype: i for i, ptype in enumerate(sorted(unique_types))}
        
        # Extract thickness values
        thicknesses = []
        for product in products.dropna().unique():
            thickness = self._extract_thickness(product)
            if thickness is not None:
                thicknesses.append(thickness)
        
        # Create thickness categories
        if thicknesses:
            thickness_bins = [0, 8, 12, 16, 20, 100]  # mm categories
            self.thickness_mapping = {
                'thin': 0,      # < 8mm
                'standard': 1,  # 8-12mm  
                'thick': 2,     # 12-16mm
                'extra_thick': 3, # 16-20mm
                'specialty': 4  # > 20mm
            }
        
        # Extract dimension patterns
        dimensions = []
        for product in products.dropna().unique():
            length, width = self._extract_dimensions(product)
            if length and width:
                dimensions.append((length, width))
        
        # Create size categories based on area
        if dimensions:
            areas = [l * w for l, w in dimensions]
            area_percentiles = np.percentile(areas, [25, 50, 75])
            self.dimension_categories = {
                'small': 0,    # < 25th percentile
                'medium': 1,   # 25-50th percentile
                'large': 2,    # 50-75th percentile
                'extra_large': 3  # > 75th percentile
            }
        
        self.fitted = True
        logger.info(f"ProductEncoder fitted:")
        logger.info(f"  - {len(self.product_type_mapping)} product types")
        logger.info(f"  - {len(self.thickness_mapping)} thickness categories")
        logger.info(f"  - {len(self.dimension_categories)} size categories")
        
        return self
    
    def transform(self, products: pd.Series) -> pd.DataFrame:
        """
        Transform product names into numeric features
        
        Args:
            products: Series of product names
            
        Returns:
            DataFrame with numeric product features
        """
        if not self.fitted:
            raise ValueError("ProductEncoder must be fitted before transform")
        
        results = []
        
        for product in products:
            if pd.isna(product):
                # Handle missing products
                features = {
                    'product_type_code': 0,
                    'product_thickness_mm': 10.0,  # Default thickness
                    'product_length_mm': 3000.0,   # Default length  
                    'product_width_mm': 300.0,     # Default width
                    'product_area_m2': 0.9,        # Default area
                    'thickness_category': 1,       # Standard
                    'size_category': 1,            # Medium
                    'product_complexity': 0.5,     # Average complexity
                    'is_cladding': 0,
                    'is_weatherboard': 0,
                    'is_villaboard': 0,
                    'is_specialty': 0
                }
            else:
                features = self._extract_all_features(product)
            
            results.append(features)
        
        return pd.DataFrame(results)
    
    def fit_transform(self, products: pd.Series) -> pd.DataFrame:
        """Fit encoder and transform products in one step"""
        return self.fit(products).transform(products)
    
    def _extract_product_type(self, product: str) -> str:
        """Extract main product type from product name"""
        product_upper = product.upper()
        
        # Define product type patterns
        type_patterns = {
            'CLADDING': ['CLADDING', 'OBLIQUE', 'STRIA', 'AXON'],
            'WEATHERBOARD': ['WEATHERBOARD', 'LINEA'],
            'VILLABOARD': ['VILLABOARD'],
            'PANEL': ['PANEL'],
            'TRIM': ['TRIM'],
            'PLANK': ['PLANK'],
            'SHEET': ['SHEET']
        }
        
        for product_type, patterns in type_patterns.items():
            if any(pattern in product_upper for pattern in patterns):
                return product_type
        
        return 'OTHER'
    
    def _extract_thickness(self, product: str) -> Optional[float]:
        """Extract thickness in mm from product name"""
        # Look for patterns like "14MM", "9MM", "16MM"
        thickness_match = re.search(r'(\d+(?:\.\d+)?)MM', product.upper())
        if thickness_match:
            return float(thickness_match.group(1))
        return None
    
    def _extract_dimensions(self, product: str) -> Tuple[Optional[float], Optional[float]]:
        """Extract length and width dimensions from product name"""
        # Look for patterns like "4200X200", "3000X1200" at the end or with MM before
        # Use word boundaries and prioritize larger numbers (dimensions vs part numbers)
        dimension_matches = re.findall(r'(\d{3,4})X(\d{2,4})', product.upper())
        
        if dimension_matches:
            # If multiple matches, take the one with larger numbers (likely actual dimensions)
            best_match = max(dimension_matches, key=lambda x: int(x[0]) * int(x[1]))
            length = float(best_match[0])
            width = float(best_match[1])
            return length, width
        return None, None
    
    def _categorize_thickness(self, thickness: Optional[float]) -> int:
        """Categorize thickness into bins"""
        if thickness is None:
            return 1  # Standard as default
        
        if thickness < 8:
            return 0  # thin
        elif thickness <= 12:
            return 1  # standard
        elif thickness <= 16:
            return 2  # thick
        elif thickness <= 20:
            return 3  # extra_thick
        else:
            return 4  # specialty
    
    def _categorize_size(self, length: Optional[float], width: Optional[float]) -> int:
        """Categorize product size based on area"""
        if length is None or width is None:
            return 1  # Medium as default
        
        area = length * width
        
        # Rough area categories based on common product sizes
        if area < 1000000:      # < 1 m²
            return 0  # small
        elif area < 5000000:    # < 5 m²
            return 1  # medium
        elif area < 10000000:   # < 10 m²
            return 2  # large
        else:
            return 3  # extra_large
    
    def _calculate_complexity(self, product: str) -> float:
        """Calculate product complexity score based on features"""
        score = 0.5  # Base complexity
        
        product_upper = product.upper()
        
        # Add complexity for specific product types
        if 'OBLIQUE' in product_upper or 'STRIA' in product_upper:
            score += 0.2  # Textured surfaces
        
        if 'WEATHERBOARD' in product_upper:
            score += 0.1  # Profile complexity
        
        # Add complexity for thickness
        thickness = self._extract_thickness(product)
        if thickness:
            if thickness > 16:
                score += 0.2  # Thick products harder to process
            elif thickness < 8:
                score += 0.1  # Thin products need careful handling
        
        # Add complexity for large dimensions
        length, width = self._extract_dimensions(product)
        if length and width:
            area = length * width
            if area > 8000000:  # Large area products
                score += 0.2
        
        return min(1.0, score)  # Cap at 1.0
    
    def _extract_all_features(self, product: str) -> Dict:
        """Extract all features for a single product"""
        product_type = self._extract_product_type(product)
        thickness = self._extract_thickness(product)
        length, width = self._extract_dimensions(product)
        
        # Calculate derived features
        area = (length * width / 1000000) if length and width else 0.9  # Convert to m²
        thickness_cat = self._categorize_thickness(thickness)
        size_cat = self._categorize_size(length, width)
        complexity = self._calculate_complexity(product)
        
        # Product type indicators
        product_upper = product.upper()
        
        return {
            'product_type_code': self.product_type_mapping.get(product_type, 0),
            'product_thickness_mm': thickness or 10.0,
            'product_length_mm': length or 3000.0,
            'product_width_mm': width or 300.0,
            'product_area_m2': area,
            'thickness_category': thickness_cat,
            'size_category': size_cat,
            'product_complexity': complexity,
            'is_cladding': 1 if 'CLADDING' in product_upper else 0,
            'is_weatherboard': 1 if 'WEATHERBOARD' in product_upper else 0,
            'is_villaboard': 1 if 'VILLABOARD' in product_upper else 0,
            'is_specialty': 1 if any(x in product_upper for x in ['TRIM', 'SPECIALTY', 'CUSTOM']) else 0
        }
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names produced by transform"""
        return [
            'product_type_code', 'product_thickness_mm', 'product_length_mm', 
            'product_width_mm', 'product_area_m2', 'thickness_category',
            'size_category', 'product_complexity', 'is_cladding', 
            'is_weatherboard', 'is_villaboard', 'is_specialty'
        ]

def add_product_features(df: pd.DataFrame, product_column: str = 'sm_product') -> pd.DataFrame:
    """
    Add product features to a manufacturing dataset
    
    Args:
        df: DataFrame containing product information
        product_column: Name of column containing product names
        
    Returns:
        DataFrame with added product features
    """
    logger.info(f"Adding product features from column: {product_column}")
    
    # Create encoder and fit
    encoder = ProductEncoder()
    product_features = encoder.fit_transform(df[product_column])
    
    # Add features to original DataFrame
    result_df = df.copy()
    for feature_name in encoder.get_feature_names():
        result_df[feature_name] = product_features[feature_name]
    
    logger.info(f"Added {len(encoder.get_feature_names())} product features")
    
    return result_df

if __name__ == "__main__":
    # Test the encoder
    import pandas as pd
    
    test_products = pd.Series([
        "6X405301 HARDIE OBLIQUE CLADDING 14MM 4200X200",
        "4X405303 HARDIE OBLIQUE CLADDING 14MM 4200X300", 
        "1X403933 AXON CLADDING 133 9MM 3000X1200",
        "7X403912 LINEA WEATHERBOARD 16MM 4200X180",
        "1X400368 VILLABOARD 6MM 3600X1350"
    ])
    
    encoder = ProductEncoder()
    features = encoder.fit_transform(test_products)
    
    print("Product Features:")
    print(features.to_string())