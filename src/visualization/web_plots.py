"""
Web-compatible visualization extensions for Manufacturing Correlation Analysis

Extends the ManufacturingCorrelationPlotter with web-optimized output formats
including base64 encoded images, JSON serializable plotly figures, and
optimized rendering for frontend consumption.
"""

import base64
import io
import json
from typing import Dict, List, Optional, Tuple, Any

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.utils
from plotly.subplots import make_subplots
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for web
import matplotlib.pyplot as plt

from .plots import ManufacturingCorrelationPlotter

class WebManufacturingCorrelationPlotter(ManufacturingCorrelationPlotter):
    """
    Web-optimized version of ManufacturingCorrelationPlotter
    
    Extends the base plotter with web-compatible output formats,
    JSON serialization, and optimized rendering for frontend consumption.
    """
    
    def __init__(self, style: str = 'seaborn-v0_8', figsize: Tuple[int, int] = (10, 6)):
        """
        Initialize the web plotter.
        
        Args:
            style: Matplotlib style
            figsize: Default figure size optimized for web display
        """
        super().__init__(style, figsize)
        
        # Web-optimized settings
        self.web_dpi = 150  # Lower DPI for faster loading
        self.web_figsize = (10, 6)  # Optimal for web display
        
        # Enhanced color palettes for web
        self.web_colors = {
            'primary': '#2563eb',
            'secondary': '#7c3aed',
            'success': '#16a34a',
            'warning': '#d97706',
            'error': '#dc2626',
            'info': '#0891b2'
        }
        
        # Plot type configurations
        self.plot_configs = {
            'correlation_matrix': {
                'default_params': {
                    'figsize': (8, 6),
                    'annot_fontsize': 8,
                    'cmap': 'RdBu_r',
                    'center': 0
                }
            },
            'scatter_plot': {
                'default_params': {
                    'figsize': (8, 6),
                    'alpha': 0.6,
                    'point_size': 20
                }
            },
            'network': {
                'default_params': {
                    'node_size': 25,
                    'edge_width_factor': 3,
                    'layout': 'circle'
                }
            }
        }
    
    def matplotlib_to_base64(self, fig, format: str = 'png', close_fig: bool = True) -> str:
        """
        Convert matplotlib figure to base64 string for web display.
        
        Args:
            fig: Matplotlib figure object
            format: Output format ('png', 'svg')
            close_fig: Whether to close the figure after conversion
            
        Returns:
            Base64 encoded string
        """
        buffer = io.BytesIO()
        fig.savefig(
            buffer, 
            format=format, 
            dpi=self.web_dpi, 
            bbox_inches='tight',
            facecolor='white',
            edgecolor='none'
        )
        buffer.seek(0)
        
        if format == 'png':
            prefix = 'data:image/png;base64,'
        elif format == 'svg':
            prefix = 'data:image/svg+xml;base64,'
        else:
            prefix = f'data:image/{format};base64,'
            
        image_base64 = prefix + base64.b64encode(buffer.getvalue()).decode()
        buffer.close()
        
        if close_fig:
            plt.close(fig)
        
        return image_base64
    
    def plotly_to_json(self, fig) -> Dict[str, Any]:
        """
        Convert plotly figure to JSON-serializable dictionary.
        
        Args:
            fig: Plotly figure object
            
        Returns:
            JSON-serializable dictionary
        """
        return json.loads(json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder))
    
    def create_web_correlation_matrix(self, 
                                    correlation_matrix: pd.DataFrame,
                                    title: str = "Correlation Matrix",
                                    significance_mask: Optional[pd.DataFrame] = None,
                                    significance_threshold: float = 0.05,
                                    output_format: str = 'base64') -> Dict[str, Any]:
        """
        Create web-optimized correlation matrix heatmap.
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            title: Plot title
            significance_mask: Matrix of p-values for significance masking
            significance_threshold: Significance threshold for masking
            output_format: Output format ('base64', 'plotly_json')
            
        Returns:
            Dictionary with plot data and metadata
        """
        if output_format == 'plotly_json':
            # Create interactive plotly heatmap
            fig = go.Figure(data=go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.columns,
                colorscale='RdBu',
                zmid=0,
                text=correlation_matrix.round(3).values,
                texttemplate="%{text}",
                textfont={"size": 10},
                hoverongaps=False,
                hovertemplate='%{x} - %{y}<br>Correlation: %{z:.3f}<extra></extra>'
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title="Variables",
                yaxis_title="Variables",
                width=600,
                height=500,
                font=dict(size=12)
            )
            
            return {
                'format': 'plotly_json',
                'data': self.plotly_to_json(fig),
                'metadata': {
                    'shape': list(correlation_matrix.shape),
                    'variables': list(correlation_matrix.columns),
                    'title': title
                }
            }
        else:
            # Create matplotlib heatmap
            fig = self.plot_correlation_matrix(
                correlation_matrix,
                title=title,
                significance_mask=significance_mask,
                significance_threshold=significance_threshold,
                figsize=self.plot_configs['correlation_matrix']['default_params']['figsize']
            )
            
            return {
                'format': 'base64_png',
                'data': self.matplotlib_to_base64(fig),
                'metadata': {
                    'shape': list(correlation_matrix.shape),
                    'variables': list(correlation_matrix.columns),
                    'title': title
                }
            }
    
    def create_web_scatter_plot(self, 
                              data: pd.DataFrame,
                              x_var: str,
                              y_var: str,
                              title: Optional[str] = None,
                              color_var: Optional[str] = None,
                              output_format: str = 'plotly_json') -> Dict[str, Any]:
        """
        Create web-optimized scatter plot.
        
        Args:
            data: DataFrame containing the variables
            x_var: X-axis variable name
            y_var: Y-axis variable name
            title: Plot title
            color_var: Variable to use for coloring points
            output_format: Output format ('base64', 'plotly_json')
            
        Returns:
            Dictionary with plot data and metadata
        """
        # Clean data
        plot_data = data[[x_var, y_var]].dropna()
        if color_var and color_var in data.columns:
            plot_data = data[[x_var, y_var, color_var]].dropna()
        
        # Calculate correlation
        correlation = plot_data[x_var].corr(plot_data[y_var])
        
        if title is None:
            title = f'{x_var} vs {y_var} (r = {correlation:.3f})'
        
        if output_format == 'plotly_json':
            # Create interactive plotly scatter plot
            fig = go.Figure()
            
            if color_var and color_var in plot_data.columns:
                fig.add_trace(go.Scatter(
                    x=plot_data[x_var],
                    y=plot_data[y_var],
                    mode='markers',
                    marker=dict(
                        color=plot_data[color_var],
                        colorscale='viridis',
                        size=8,
                        opacity=0.7,
                        colorbar=dict(title=color_var)
                    ),
                    text=plot_data[color_var],
                    hovertemplate=f'%{{xaxis.title.text}}: %{{x}}<br>%{{yaxis.title.text}}: %{{y}}<br>{color_var}: %{{text}}<extra></extra>'
                ))
            else:
                fig.add_trace(go.Scatter(
                    x=plot_data[x_var],
                    y=plot_data[y_var],
                    mode='markers',
                    marker=dict(
                        color=self.web_colors['primary'],
                        size=8,
                        opacity=0.7
                    ),
                    hovertemplate='%{xaxis.title.text}: %{x}<br>%{yaxis.title.text}: %{y}<extra></extra>'
                ))
            
            # Add trend line
            z = np.polyfit(plot_data[x_var], plot_data[y_var], 1)
            p = np.poly1d(z)
            x_trend = np.linspace(plot_data[x_var].min(), plot_data[x_var].max(), 100)
            y_trend = p(x_trend)
            
            fig.add_trace(go.Scatter(
                x=x_trend,
                y=y_trend,
                mode='lines',
                line=dict(color='red', dash='dash', width=2),
                name='Trend Line',
                hovertemplate='Trend Line<extra></extra>'
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title=x_var,
                yaxis_title=y_var,
                showlegend=True,
                width=600,
                height=450,
                annotations=[
                    dict(
                        x=0.05,
                        y=0.95,
                        xref='paper',
                        yref='paper',
                        text=f'Correlation: {correlation:.3f}',
                        showarrow=False,
                        bgcolor='rgba(255,255,255,0.8)',
                        bordercolor='black',
                        borderwidth=1
                    )
                ]
            )
            
            return {
                'format': 'plotly_json',
                'data': self.plotly_to_json(fig),
                'metadata': {
                    'correlation': correlation,
                    'x_var': x_var,
                    'y_var': y_var,
                    'color_var': color_var,
                    'data_points': len(plot_data),
                    'title': title
                }
            }
        else:
            # Create matplotlib scatter plot
            fig = self.plot_scatter_with_correlation(
                plot_data, x_var, y_var, title, color_var,
                figsize=self.plot_configs['scatter_plot']['default_params']['figsize']
            )
            
            return {
                'format': 'base64_png',
                'data': self.matplotlib_to_base64(fig),
                'metadata': {
                    'correlation': correlation,
                    'x_var': x_var,
                    'y_var': y_var,
                    'color_var': color_var,
                    'data_points': len(plot_data),
                    'title': title
                }
            }
    
    def create_web_correlation_network(self, 
                                     correlation_matrix: pd.DataFrame,
                                     threshold: float = 0.5,
                                     title: str = "Correlation Network",
                                     layout: str = 'circle') -> Dict[str, Any]:
        """
        Create web-optimized correlation network visualization.
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            threshold: Minimum correlation strength to show
            title: Plot title
            layout: Network layout ('circle', 'spring', 'grid')
            
        Returns:
            Dictionary with plot data and metadata
        """
        # Create network data
        nodes = list(correlation_matrix.columns)
        edges = []
        edge_weights = []
        
        for i, var1 in enumerate(nodes):
            for j, var2 in enumerate(nodes):
                if i < j:
                    corr_val = correlation_matrix.loc[var1, var2]
                    if abs(corr_val) >= threshold:
                        edges.append((i, j, corr_val))
                        edge_weights.append(abs(corr_val))
        
        # Create node positions
        if layout == 'circle':
            node_x = [np.cos(2 * np.pi * i / len(nodes)) for i in range(len(nodes))]
            node_y = [np.sin(2 * np.pi * i / len(nodes)) for i in range(len(nodes))]
        elif layout == 'grid':
            grid_size = int(np.ceil(np.sqrt(len(nodes))))
            node_x = [i % grid_size for i in range(len(nodes))]
            node_y = [i // grid_size for i in range(len(nodes))]
        else:  # spring layout (simplified)
            node_x = np.random.uniform(-1, 1, len(nodes))
            node_y = np.random.uniform(-1, 1, len(nodes))
        
        # Create plotly network graph
        fig = go.Figure()
        
        # Add edges
        for (i, j, corr_val) in edges:
            edge_color = 'red' if corr_val < 0 else 'blue'
            edge_width = abs(corr_val) * self.plot_configs['network']['default_params']['edge_width_factor']
            
            fig.add_trace(go.Scatter(
                x=[node_x[i], node_x[j], None],
                y=[node_y[i], node_y[j], None],
                mode='lines',
                line=dict(width=edge_width, color=edge_color),
                opacity=0.6,
                hoverinfo='none',
                showlegend=False
            ))
        
        # Add nodes
        fig.add_trace(go.Scatter(
            x=node_x,
            y=node_y,
            mode='markers+text',
            marker=dict(
                size=self.plot_configs['network']['default_params']['node_size'],
                color=self.web_colors['primary'],
                line=dict(width=2, color='white')
            ),
            text=nodes,
            textposition='middle center',
            textfont=dict(size=10, color='white'),
            hovertemplate='%{text}<extra></extra>',
            showlegend=False
        ))
        
        fig.update_layout(
            title=title,
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20, l=5, r=5, t=40),
            annotations=[
                dict(
                    text=f"Showing correlations ≥ {threshold}",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.005, y=-0.002,
                    xanchor='left', yanchor='bottom',
                    font=dict(size=10)
                )
            ],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            width=600,
            height=500
        )
        
        return {
            'format': 'plotly_json',
            'data': self.plotly_to_json(fig),
            'metadata': {
                'threshold': threshold,
                'nodes_count': len(nodes),
                'edges_count': len(edges),
                'variables': nodes,
                'title': title,
                'layout': layout
            }
        }
    
    def create_web_dashboard(self, 
                           correlation_matrix: pd.DataFrame,
                           significant_correlations: List[Dict],
                           data: pd.DataFrame,
                           title: str = "Manufacturing Correlation Dashboard") -> Dict[str, Any]:
        """
        Create comprehensive web-optimized correlation dashboard.
        
        Args:
            correlation_matrix: Correlation matrix
            significant_correlations: List of significant correlation results
            data: Original data for scatter plots
            title: Dashboard title
            
        Returns:
            Dictionary with dashboard data and metadata
        """
        # Create subplot layout
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=(
                'Correlation Matrix',
                'Top Correlations',
                'Correlation Distribution',
                'Sample Relationship'
            ),
            specs=[[{"type": "heatmap"}, {"type": "bar"}],
                   [{"type": "histogram"}, {"type": "scatter"}]],
            vertical_spacing=0.08,
            horizontal_spacing=0.08
        )
        
        # 1. Correlation matrix heatmap
        fig.add_trace(
            go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.columns,
                colorscale='RdBu',
                zmid=0,
                text=correlation_matrix.round(3).values,
                texttemplate="%{text}",
                textfont={"size": 8},
                showscale=False,
                hovertemplate='%{x} - %{y}<br>Correlation: %{z:.3f}<extra></extra>'
            ),
            row=1, col=1
        )
        
        # 2. Top correlations bar chart
        if significant_correlations:
            top_corrs = significant_correlations[:8]  # Limit for readability
            corr_labels = [f"{c['variable_1'][:8]}-{c['variable_2'][:8]}" for c in top_corrs]
            corr_values = [c['correlation_coefficient'] for c in top_corrs]
            
            fig.add_trace(
                go.Bar(
                    x=corr_values,
                    y=corr_labels,
                    orientation='h',
                    marker_color=[self.web_colors['error'] if x < 0 else self.web_colors['primary'] for x in corr_values],
                    showlegend=False,
                    hovertemplate='%{y}<br>Correlation: %{x:.3f}<extra></extra>'
                ),
                row=1, col=2
            )
        
        # 3. Correlation distribution
        all_correlations = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)]
        fig.add_trace(
            go.Histogram(
                x=all_correlations,
                nbinsx=15,
                marker_color=self.web_colors['info'],
                opacity=0.7,
                showlegend=False,
                hovertemplate='Range: %{x}<br>Count: %{y}<extra></extra>'
            ),
            row=2, col=1
        )
        
        # 4. Sample scatter plot
        if significant_correlations:
            top_corr = significant_correlations[0]
            var1 = top_corr['variable_1']
            var2 = top_corr['variable_2']
            
            if var1 in data.columns and var2 in data.columns:
                clean_data = data[[var1, var2]].dropna()
                
                fig.add_trace(
                    go.Scatter(
                        x=clean_data[var1],
                        y=clean_data[var2],
                        mode='markers',
                        marker=dict(
                            color=self.web_colors['success'],
                            size=4,
                            opacity=0.6
                        ),
                        showlegend=False,
                        hovertemplate=f'{var1}: %{{x}}<br>{var2}: %{{y}}<extra></extra>'
                    ),
                    row=2, col=2
                )
        
        # Update layout
        fig.update_layout(
            height=700,
            width=1000,
            title_text=title,
            showlegend=False,
            font=dict(size=10)
        )
        
        # Update subplot titles
        fig.update_xaxes(title_text="Correlation Coefficient", row=1, col=2)
        fig.update_xaxes(title_text="Correlation Value", row=2, col=1)
        fig.update_yaxes(title_text="Frequency", row=2, col=1)
        
        if significant_correlations:
            fig.update_xaxes(title_text=significant_correlations[0]['variable_1'], row=2, col=2)
            fig.update_yaxes(title_text=significant_correlations[0]['variable_2'], row=2, col=2)
        
        return {
            'format': 'plotly_json',
            'data': self.plotly_to_json(fig),
            'metadata': {
                'correlations_count': len(significant_correlations),
                'variables': list(correlation_matrix.columns),
                'title': title,
                'dashboard_type': 'comprehensive'
            }
        }
    
    def create_time_series_web_plot(self, 
                                  data: pd.DataFrame,
                                  value_columns: List[str],
                                  time_column: str = 'timestamp',
                                  event_data: Optional[pd.DataFrame] = None,
                                  title: str = "Manufacturing Time Series") -> Dict[str, Any]:
        """
        Create web-optimized time series visualization.
        
        Args:
            data: Time series data DataFrame
            value_columns: List of columns to plot
            time_column: Time column name
            event_data: DataFrame with event information
            title: Plot title
            
        Returns:
            Dictionary with plot data and metadata
        """
        # Create subplots
        fig = make_subplots(
            rows=len(value_columns),
            cols=1,
            shared_xaxes=True,
            subplot_titles=value_columns,
            vertical_spacing=0.02
        )
        
        # Plot each variable
        for i, col in enumerate(value_columns, 1):
            if col in data.columns:
                fig.add_trace(
                    go.Scatter(
                        x=data[time_column],
                        y=data[col],
                        mode='lines',
                        name=col,
                        line=dict(color=self.manufacturing_colors.get(col.lower(), self.web_colors['primary'])),
                        hovertemplate=f'Time: %{{x}}<br>{col}: %{{y}}<extra></extra>'
                    ),
                    row=i, col=1
                )
                
                # Add event markers if provided
                if event_data is not None and len(event_data) > 0:
                    for _, event in event_data.iterrows():
                        if time_column in event:
                            event_time = event[time_column]
                            
                            # Find corresponding y-value at event time
                            try:
                                closest_idx = data[time_column].sub(pd.to_datetime(event_time)).abs().idxmin()
                                y_val = data.loc[closest_idx, col] if col in data.columns else 0
                                
                                fig.add_trace(
                                    go.Scatter(
                                        x=[event_time],
                                        y=[y_val],
                                        mode='markers',
                                        marker=dict(size=8, color=self.web_colors['error'], symbol='x'),
                                        name="Event" if i == 1 else None,
                                        showlegend=(i == 1),
                                        hovertemplate=f"Event: {event.get('Stoppage Reason', 'Unknown')}<br>Time: %{{x}}<extra></extra>"
                                    ),
                                    row=i, col=1
                                )
                            except Exception:
                                pass  # Skip if time matching fails
        
        # Update layout
        fig.update_layout(
            title=title,
            height=150 * len(value_columns),
            width=1000,
            showlegend=True,
            hovermode='x unified',
            font=dict(size=10)
        )
        
        fig.update_xaxes(title_text="Time", row=len(value_columns), col=1)
        
        return {
            'format': 'plotly_json',
            'data': self.plotly_to_json(fig),
            'metadata': {
                'variables': value_columns,
                'time_column': time_column,
                'data_points': len(data),
                'events_count': len(event_data) if event_data is not None else 0,
                'title': title
            }
        }

# Convenience function for web API
def create_web_plot(plot_type: str, 
                   data: pd.DataFrame,
                   correlation_matrix: Optional[pd.DataFrame] = None,
                   parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Factory function to create web-optimized plots.
    
    Args:
        plot_type: Type of plot to create
        data: Data for plotting
        correlation_matrix: Correlation matrix if needed
        parameters: Plot-specific parameters
        
    Returns:
        Dictionary with plot data and metadata
    """
    if parameters is None:
        parameters = {}
    
    plotter = WebManufacturingCorrelationPlotter()
    
    if plot_type == 'correlation_matrix':
        if correlation_matrix is None:
            raise ValueError("Correlation matrix required for correlation_matrix plot")
        return plotter.create_web_correlation_matrix(correlation_matrix, **parameters)
    
    elif plot_type == 'scatter_plot':
        x_var = parameters.get('x_var')
        y_var = parameters.get('y_var')
        if not x_var or not y_var:
            raise ValueError("x_var and y_var required for scatter plot")
        return plotter.create_web_scatter_plot(data, x_var, y_var, **parameters)
    
    elif plot_type == 'correlation_network':
        if correlation_matrix is None:
            raise ValueError("Correlation matrix required for correlation_network plot")
        return plotter.create_web_correlation_network(correlation_matrix, **parameters)
    
    elif plot_type == 'dashboard':
        if correlation_matrix is None:
            raise ValueError("Correlation matrix required for dashboard plot")
        significant_correlations = parameters.get('significant_correlations', [])
        return plotter.create_web_dashboard(correlation_matrix, significant_correlations, data, **parameters)
    
    elif plot_type == 'time_series':
        value_columns = parameters.get('value_columns', [])
        if not value_columns:
            raise ValueError("value_columns required for time series plot")
        return plotter.create_time_series_web_plot(data, value_columns, **parameters)
    
    else:
        raise ValueError(f"Unknown plot type: {plot_type}")