"""
Backend Utilities for matplotlib Configuration

Provides utilities to ensure proper matplotlib backend configuration 
for different execution environments, preventing GUI threading issues
in CLI, agent, and web service contexts.
"""

import matplotlib
import os
import sys
from typing import Optional


def configure_matplotlib_backend(backend: Optional[str] = None, force: bool = False) -> str:
    """
    Configure matplotlib backend for non-GUI environments.
    
    Args:
        backend: Specific backend to use (default: 'Agg')
        force: Force backend change even if already set
        
    Returns:
        The configured backend name
    """
    if backend is None:
        backend = 'Agg'
    
    current_backend = matplotlib.get_backend()
    
    # Only change if needed or forced
    if force or current_backend != backend:
        try:
            matplotlib.use(backend, force=force)
            return backend
        except Exception as e:
            # Fallback to Agg if requested backend fails
            if backend != 'Agg':
                matplotlib.use('Agg', force=force)
                return 'Agg'
            else:
                raise e
    
    return current_backend


def is_gui_backend(backend: Optional[str] = None) -> bool:
    """
    Check if the current or specified backend is a GUI backend.
    
    Args:
        backend: Backend to check (default: current backend)
        
    Returns:
        True if backend requires GUI
    """
    if backend is None:
        backend = matplotlib.get_backend()
    
    gui_backends = {
        'TkAgg', 'Qt5Agg', 'Qt4Agg', 'GTKAgg', 'GTK3Agg', 
        'MacOSX', 'WXAgg', 'WX', 'CocoaAgg'
    }
    
    return backend in gui_backends


def ensure_non_gui_backend():
    """
    Ensure matplotlib is using a non-GUI backend.
    
    Automatically configures Agg backend if a GUI backend is detected,
    which prevents threading issues in CLI and agent environments.
    """
    if is_gui_backend():
        configure_matplotlib_backend('Agg', force=True)


def get_backend_info() -> dict:
    """
    Get information about the current matplotlib configuration.
    
    Returns:
        Dictionary with backend information
    """
    backend = matplotlib.get_backend()
    
    return {
        'backend': backend,
        'is_gui': is_gui_backend(backend),
        'is_interactive': matplotlib.is_interactive(),
        'platform': sys.platform,
        'display_available': bool(os.environ.get('DISPLAY', False)),
        'recommended_backend': 'Agg' if not os.environ.get('DISPLAY') else backend
    }


def safe_show(conditional: bool = True):
    """
    Safely show matplotlib plots only in appropriate environments.
    
    Args:
        conditional: Only show if in interactive environment with GUI backend
    """
    if not conditional:
        return
        
    # Only show if we have a GUI backend and interactive mode
    if is_gui_backend() and matplotlib.is_interactive():
        import matplotlib.pyplot as plt
        plt.show()


# Auto-configure on import if in known non-GUI environment
def _auto_configure():
    """Auto-configure backend based on environment detection."""
    # Check for known non-GUI environments
    non_gui_indicators = [
        'PYTEST_CURRENT_TEST' in os.environ,  # pytest
        'CI' in os.environ,  # CI/CD environments
        not bool(os.environ.get('DISPLAY')),  # No X11 display
        sys.platform.startswith('linux') and not os.environ.get('DISPLAY'),  # Headless Linux
    ]
    
    if any(non_gui_indicators):
        ensure_non_gui_backend()


# Auto-configure on module import
_auto_configure()