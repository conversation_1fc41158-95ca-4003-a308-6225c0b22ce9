"""
Multi-Method Visualization Module for Manufacturing Correlation Analysis

Provides specialized plotting and visualization capabilities for multi-method 
correlation analysis, including side-by-side heatmaps, method comparison 
visualizations, and comprehensive dashboards for <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> methods.
"""

# Configure matplotlib to use non-GUI backend to prevent macOS NSWindow thread errors
import matplotlib
matplotlib.use('Agg')

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Optional, Tuple, Any
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings

# Import base plotting class to extend it
from .plots import ManufacturingCorrelationPlotter

# Import data utilities for safe data handling
from ..agents.data_utils import (
    safe_get_dict_value,
    validate_correlation_results_only
)

warnings.filterwarnings('ignore')


class MultiMethodCorrelationPlotter(ManufacturingCorrelationPlotter):
    """
    Extended plotting class for multi-method correlation analysis visualization
    """
    
    def __init__(self, style: str = 'seaborn-v0_8', figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize the multi-method plotter with enhanced style settings.
        
        Args:
            style: Matplotlib style
            figsize: Default figure size
        """
        super().__init__(style, figsize)
        
        # Method-specific color schemes
        self.method_colors = {
            'pearson': '#1f77b4',    # Blue
            'spearman': '#ff7f0e',   # Orange  
            'kendall': '#2ca02c'     # Green
        }
        
        # Convergence color scheme
        self.convergence_colors = {
            'high': '#2ca02c',       # Green
            'medium': '#ff7f0e',     # Orange
            'low': '#d62728'         # Red
        }
    
    def create_multi_method_heatmaps(self, 
                                   correlation_results: Dict[str, Any],
                                   title: str = "Multi-Method Correlation Analysis",
                                   save_path: Optional[str] = None,
                                   figsize: Optional[Tuple[int, int]] = None,
                                   show_convergence: bool = True) -> plt.Figure:
        """
        Create side-by-side correlation heatmaps for all three methods.
        
        Args:
            correlation_results: Multi-method correlation results
            title: Overall plot title
            save_path: Path to save the figure
            figsize: Figure size override (default: wider for three subplots)
            show_convergence: Whether to include convergence scores
            
        Returns:
            Matplotlib figure object
        """
        # Set figure size (wider for three subplots)
        if figsize is None:
            figsize = (18, 6)
        
        # Extract correlation matrices for each method
        pearson_matrix, spearman_matrix, kendall_matrix = self._extract_correlation_matrices(correlation_results)
        
        if pearson_matrix.empty:
            raise ValueError("No correlation data available for visualization")
        
        # Create subplot structure
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=figsize)
        
        # Common heatmap parameters
        vmin, vmax = -1, 1
        cmap = 'coolwarm'  # Divergent color scheme as specified in PRP
        
        # Create individual heatmaps
        sns.heatmap(pearson_matrix, ax=ax1, cmap=cmap, vmin=vmin, vmax=vmax,
                   annot=True, fmt='.3f', cbar=False, square=True,
                   cbar_kws={'label': 'Correlation Coefficient'})
        ax1.set_title('Pearson Correlation', fontsize=14, fontweight='bold', color=self.method_colors['pearson'])
        
        sns.heatmap(spearman_matrix, ax=ax2, cmap=cmap, vmin=vmin, vmax=vmax,
                   annot=True, fmt='.3f', cbar=False, square=True)
        ax2.set_title('Spearman Correlation', fontsize=14, fontweight='bold', color=self.method_colors['spearman'])
        
        # Only show colorbar on the rightmost plot
        sns.heatmap(kendall_matrix, ax=ax3, cmap=cmap, vmin=vmin, vmax=vmax,
                   annot=True, fmt='.3f', cbar=True, square=True,
                   cbar_kws={'label': 'Correlation Coefficient'})
        ax3.set_title('Kendall Correlation', fontsize=14, fontweight='bold', color=self.method_colors['kendall'])
        
        # Add convergence information if requested
        if show_convergence and 'convergence_analysis' in correlation_results:
            convergence_score = correlation_results['convergence_analysis'].get('overall_convergence_score', 0)
            convergence_text = f'Overall Convergence: {convergence_score:.3f}'
            
            # Add convergence score as subtitle
            fig.suptitle(f'{title}\n{convergence_text}', fontsize=16, fontweight='bold', y=0.98)
        else:
            fig.suptitle(title, fontsize=16, fontweight='bold', y=0.95)
        
        # Adjust layout to prevent overlap
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_method_convergence(self,
                              correlation_results: Dict[str, Any],
                              title: str = "Method Convergence Analysis",
                              save_path: Optional[str] = None) -> go.Figure:
        """
        Create interactive visualization of method convergence patterns.
        
        Args:
            correlation_results: Multi-method correlation results
            title: Plot title
            save_path: Path to save the figure
            
        Returns:
            Plotly figure object
        """
        if 'multi_method_results' not in correlation_results:
            raise ValueError("Multi-method results not found in correlation_results")
        
        # Extract convergence data
        variable_pairs = []
        convergence_scores = []
        pearson_values = []
        spearman_values = []
        kendall_values = []
        recommended_methods = []
        
        for pair_key, result in correlation_results['multi_method_results'].items():
            var1 = safe_get_dict_value(result, 'variable_1', 'unknown')
            var2 = safe_get_dict_value(result, 'variable_2', 'unknown')
            variable_pairs.append(f"{var1} - {var2}")
            
            # Safe extraction with defaults for None values
            conv_score = safe_get_dict_value(result, 'method_convergence_score', 0.0)
            convergence_scores.append(conv_score if conv_score is not None else 0.0)
            
            pearson_val = safe_get_dict_value(result, 'pearson_correlation', 0.0)
            pearson_values.append(pearson_val if pearson_val is not None else 0.0)
            
            spearman_val = safe_get_dict_value(result, 'spearman_correlation', 0.0)
            spearman_values.append(spearman_val if spearman_val is not None else 0.0)
            
            kendall_val = safe_get_dict_value(result, 'kendall_correlation', 0.0)
            kendall_values.append(kendall_val if kendall_val is not None else 0.0)
            
            recommended_methods.append(safe_get_dict_value(result, 'recommended_method', 'pearson'))
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=(
                'Convergence Scores by Variable Pair',
                'Method Correlation Comparison',
                'Convergence Distribution', 
                'Method Recommendation Distribution'
            ),
            specs=[[{"type": "bar"}, {"type": "scatter"}],
                   [{"type": "histogram"}, {"type": "pie"}]]
        )
        
        # 1. Convergence scores bar chart
        convergence_colors = [
            self.convergence_colors['high'] if score > 0.8 else
            self.convergence_colors['medium'] if score > 0.5 else
            self.convergence_colors['low']
            for score in convergence_scores
        ]
        
        fig.add_trace(
            go.Bar(
                x=variable_pairs,
                y=convergence_scores,
                marker_color=convergence_colors,
                name='Convergence Score',
                hovertemplate='<b>%{x}</b><br>Convergence: %{y:.3f}<extra></extra>'
            ),
            row=1, col=1
        )
        
        # 2. Method comparison scatter plot
        fig.add_trace(
            go.Scatter(
                x=pearson_values,
                y=spearman_values,
                mode='markers',
                marker=dict(
                    size=8,
                    color=convergence_scores,
                    colorscale='RdYlGn',
                    showscale=True,
                    colorbar=dict(title="Convergence Score")
                ),
                text=variable_pairs,
                name='Pearson vs Spearman',
                hovertemplate='<b>%{text}</b><br>Pearson: %{x:.3f}<br>Spearman: %{y:.3f}<extra></extra>'
            ),
            row=1, col=2
        )
        
        # Add diagonal reference line
        fig.add_trace(
            go.Scatter(
                x=[-1, 1],
                y=[-1, 1],
                mode='lines',
                line=dict(dash='dash', color='gray'),
                name='Perfect Agreement',
                showlegend=False
            ),
            row=1, col=2
        )
        
        # 3. Convergence distribution histogram
        fig.add_trace(
            go.Histogram(
                x=convergence_scores,
                nbinsx=10,
                marker_color='lightblue',
                opacity=0.7,
                name='Convergence Distribution'
            ),
            row=2, col=1
        )
        
        # 4. Method recommendation pie chart
        method_counts = pd.Series(recommended_methods).value_counts()
        fig.add_trace(
            go.Pie(
                labels=method_counts.index,
                values=method_counts.values,
                marker_colors=[self.method_colors.get(method, '#gray') for method in method_counts.index],
                name='Method Recommendations'
            ),
            row=2, col=2
        )
        
        # Update layout
        fig.update_layout(
            title=title,
            height=800,
            showlegend=True
        )
        
        # Update axis labels
        fig.update_xaxes(title_text="Variable Pairs", row=1, col=1)
        fig.update_yaxes(title_text="Convergence Score", row=1, col=1)
        fig.update_xaxes(title_text="Pearson Correlation", row=1, col=2)
        fig.update_yaxes(title_text="Spearman Correlation", row=1, col=2)
        fig.update_xaxes(title_text="Convergence Score", row=2, col=1)
        fig.update_yaxes(title_text="Frequency", row=2, col=1)
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def create_multi_method_dashboard(self,
                                    correlation_results: Dict[str, Any],
                                    title: str = "Multi-Method Correlation Dashboard",
                                    save_path: Optional[str] = None) -> go.Figure:
        """
        Create comprehensive multi-method correlation dashboard.
        
        Args:
            correlation_results: Multi-method correlation results  
            title: Dashboard title
            save_path: Path to save the figure
            
        Returns:
            Plotly figure object
        """
        if 'multi_method_results' not in correlation_results:
            raise ValueError("Multi-method results not found in correlation_results")
        
        # Extract data for visualization
        results = correlation_results['multi_method_results']
        convergence_data = correlation_results.get('convergence_analysis', {})
        
        # Prepare data structures with safe value extraction
        variable_pairs = list(results.keys())
        pearson_vals = []
        spearman_vals = []
        kendall_vals = []
        convergence_vals = []
        
        for k in variable_pairs:
            result = results[k]
            
            # Safe extraction with None checks
            pearson_val = safe_get_dict_value(result, 'pearson_correlation', 0.0)
            pearson_vals.append(pearson_val if pearson_val is not None else 0.0)
            
            spearman_val = safe_get_dict_value(result, 'spearman_correlation', 0.0)
            spearman_vals.append(spearman_val if spearman_val is not None else 0.0)
            
            kendall_val = safe_get_dict_value(result, 'kendall_correlation', 0.0)
            kendall_vals.append(kendall_val if kendall_val is not None else 0.0)
            
            conv_val = safe_get_dict_value(result, 'method_convergence_score', 0.0)
            convergence_vals.append(conv_val if conv_val is not None else 0.0)
        
        # Create comprehensive dashboard layout
        fig = make_subplots(
            rows=3, cols=3,
            subplot_titles=(
                'Method Correlation Matrices', '', '',
                'Convergence vs Correlation Strength', 'Method Agreement Analysis', 'P-Value Comparison',
                'Method Stability Metrics', 'Recommendation Distribution', 'Cross-Method Correlations'
            ),
            specs=[
                [{"colspan": 3}, None, None],
                [{"type": "scatter"}, {"type": "box"}, {"type": "scatter"}],
                [{"type": "bar"}, {"type": "pie"}, {"type": "heatmap"}]
            ]
        )
        
        # 1. Method comparison heatmap (top row - full width)
        methods = ['Pearson', 'Spearman', 'Kendall']
        correlation_matrix = np.array([pearson_vals, spearman_vals, kendall_vals])
        
        fig.add_trace(
            go.Heatmap(
                z=correlation_matrix,
                x=[f"Pair {i+1}" for i in range(len(variable_pairs))],
                y=methods,
                colorscale='RdBu',
                zmid=0,
                text=correlation_matrix.round(3),
                texttemplate="%{text}",
                textfont={"size": 10},
                name='Method Comparison'
            ),
            row=1, col=1
        )
        
        # 2. Convergence vs correlation strength
        max_abs_corr = [max(abs(pearson_vals[i]), abs(spearman_vals[i]), abs(kendall_vals[i])) 
                       for i in range(len(variable_pairs))]
        
        fig.add_trace(
            go.Scatter(
                x=max_abs_corr,
                y=convergence_vals,
                mode='markers',
                marker=dict(size=8, color='blue', opacity=0.6),
                name='Convergence vs Strength',
                hovertemplate='Max |r|: %{x:.3f}<br>Convergence: %{y:.3f}<extra></extra>'
            ),
            row=2, col=1
        )
        
        # 3. Method agreement box plot
        all_correlations = pearson_vals + spearman_vals + kendall_vals
        method_labels = ['Pearson']*len(pearson_vals) + ['Spearman']*len(spearman_vals) + ['Kendall']*len(kendall_vals)
        
        for method in ['Pearson', 'Spearman', 'Kendall']:
            method_data = [all_correlations[i] for i, label in enumerate(method_labels) if label == method]
            fig.add_trace(
                go.Box(
                    y=method_data,
                    name=method,
                    marker_color=self.method_colors[method.lower()],
                    showlegend=False
                ),
                row=2, col=2
            )
        
        # 4. P-value comparison
        pearson_p = [results[k]['pearson_p_value'] for k in variable_pairs]
        spearman_p = [results[k]['spearman_p_value'] for k in variable_pairs]
        
        # Convert scientific notation strings to float if needed
        pearson_p_float = [float(p) if isinstance(p, (int, float)) else float(p) for p in pearson_p]
        spearman_p_float = [float(p) if isinstance(p, (int, float)) else float(p) for p in spearman_p]
        
        fig.add_trace(
            go.Scatter(
                x=pearson_p_float,
                y=spearman_p_float,
                mode='markers',
                marker=dict(size=6, color='green', opacity=0.6),
                name='P-value Comparison',
                hovertemplate='Pearson p: %{x}<br>Spearman p: %{y}<extra></extra>'
            ),
            row=2, col=3
        )
        
        # 5. Method stability (from convergence analysis)
        if convergence_data and 'method_stability' in convergence_data:
            stability_data = convergence_data['method_stability']
            methods_stab = list(stability_data.keys())
            stability_scores = [stability_data[method] for method in methods_stab]
            
            fig.add_trace(
                go.Bar(
                    x=methods_stab,
                    y=stability_scores,
                    marker_color=[self.method_colors[method.replace('_stability', '')] for method in methods_stab],
                    name='Method Stability'
                ),
                row=3, col=1
            )
        
        # 6. Recommendation distribution
        recommendations = [results[k]['recommended_method'] for k in variable_pairs]
        rec_counts = pd.Series(recommendations).value_counts()
        
        fig.add_trace(
            go.Pie(
                labels=rec_counts.index,
                values=rec_counts.values,
                marker_colors=[self.method_colors[method] for method in rec_counts.index],
                name='Recommendations'
            ),
            row=3, col=2
        )
        
        # 7. Cross-method correlation matrix
        if convergence_data and 'cross_method_correlations' in convergence_data:
            cross_corr = convergence_data['cross_method_correlations']
            
            cross_matrix = np.array([
                [1.0, cross_corr['pearson_spearman'], cross_corr['pearson_kendall']],
                [cross_corr['pearson_spearman'], 1.0, cross_corr['spearman_kendall']],
                [cross_corr['pearson_kendall'], cross_corr['spearman_kendall'], 1.0]
            ])
            
            fig.add_trace(
                go.Heatmap(
                    z=cross_matrix,
                    x=['Pearson', 'Spearman', 'Kendall'],
                    y=['Pearson', 'Spearman', 'Kendall'],
                    colorscale='RdBu',
                    zmid=0,
                    text=cross_matrix.round(3),
                    texttemplate="%{text}",
                    textfont={"size": 12},
                    name='Cross-Method Correlation'
                ),
                row=3, col=3
            )
        
        # Update layout
        fig.update_layout(
            title=title,
            height=1200,
            showlegend=True
        )
        
        # Update axis labels
        fig.update_xaxes(title_text="Max |Correlation|", row=2, col=1)
        fig.update_yaxes(title_text="Convergence Score", row=2, col=1)
        fig.update_yaxes(title_text="Correlation Value", row=2, col=2)
        fig.update_xaxes(title_text="Pearson p-value", row=2, col=3)
        fig.update_yaxes(title_text="Spearman p-value", row=2, col=3)
        fig.update_xaxes(title_text="Method", row=3, col=1)
        fig.update_yaxes(title_text="Stability Score", row=3, col=1)
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def plot_method_comparison_matrix(self,
                                    correlation_results: Dict[str, Any],
                                    method_pair: Tuple[str, str] = ('pearson', 'spearman'),
                                    title: Optional[str] = None,
                                    save_path: Optional[str] = None) -> plt.Figure:
        """
        Create detailed comparison between two specific correlation methods.
        
        Args:
            correlation_results: Multi-method correlation results
            method_pair: Tuple of two methods to compare
            title: Plot title
            save_path: Path to save the figure
            
        Returns:
            Matplotlib figure object
        """
        method1, method2 = method_pair
        
        if title is None:
            title = f'{method1.title()} vs {method2.title()} Correlation Comparison'
        
        # Extract correlation values for the two methods
        results = correlation_results['multi_method_results']
        
        method1_vals = [results[k][f'{method1}_correlation'] for k in results.keys()]
        method2_vals = [results[k][f'{method2}_correlation'] for k in results.keys()]
        convergence_vals = [results[k]['method_convergence_score'] for k in results.keys()]
        variable_pairs = [f"{results[k]['variable_1']} - {results[k]['variable_2']}" for k in results.keys()]
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Scatter plot with convergence coloring
        scatter = ax1.scatter(method1_vals, method2_vals, c=convergence_vals, 
                            cmap='RdYlGn', alpha=0.7, s=60)
        ax1.plot([-1, 1], [-1, 1], 'k--', alpha=0.5, label='Perfect Agreement')
        ax1.set_xlabel(f'{method1.title()} Correlation', fontsize=12)
        ax1.set_ylabel(f'{method2.title()} Correlation', fontsize=12)
        ax1.set_title('Method Correlation Comparison', fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax1)
        cbar.set_label('Convergence Score', fontsize=12)
        
        # 2. Difference analysis
        differences = np.array(method1_vals) - np.array(method2_vals)
        ax2.hist(differences, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(0, color='red', linestyle='--', alpha=0.7, label='No Difference')
        ax2.set_xlabel(f'{method1.title()} - {method2.title()}', fontsize=12)
        ax2.set_ylabel('Frequency', fontsize=12)
        ax2.set_title('Correlation Difference Distribution', fontweight='bold')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 3. Bland-Altman plot
        means = (np.array(method1_vals) + np.array(method2_vals)) / 2
        ax3.scatter(means, differences, alpha=0.7, s=60)
        ax3.axhline(0, color='red', linestyle='-', alpha=0.7)
        ax3.axhline(np.mean(differences), color='blue', linestyle='--', alpha=0.7, 
                   label=f'Mean Diff: {np.mean(differences):.3f}')
        ax3.axhline(np.mean(differences) + 1.96*np.std(differences), color='gray', 
                   linestyle=':', alpha=0.7, label='±1.96 SD')
        ax3.axhline(np.mean(differences) - 1.96*np.std(differences), color='gray', 
                   linestyle=':', alpha=0.7)
        ax3.set_xlabel(f'Mean of {method1.title()} and {method2.title()}', fontsize=12)
        ax3.set_ylabel(f'{method1.title()} - {method2.title()}', fontsize=12)
        ax3.set_title('Bland-Altman Plot', fontweight='bold')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 4. Top differences table
        ax4.axis('off')
        
        # Find top 5 largest absolute differences
        abs_diffs = np.abs(differences)
        top_indices = np.argsort(abs_diffs)[-5:][::-1]
        
        table_data = []
        for idx in top_indices:
            table_data.append([
                variable_pairs[idx][:20] + '...' if len(variable_pairs[idx]) > 20 else variable_pairs[idx],
                f'{method1_vals[idx]:.3f}',
                f'{method2_vals[idx]:.3f}',
                f'{differences[idx]:.3f}'
            ])
        
        table = ax4.table(cellText=table_data,
                         colLabels=['Variable Pair', method1.title(), method2.title(), 'Difference'],
                         cellLoc='center',
                         loc='center',
                         bbox=[0, 0.3, 1, 0.7])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        
        ax4.set_title('Top 5 Largest Method Differences', fontweight='bold', y=0.9)
        
        # Overall title
        fig.suptitle(title, fontsize=16, fontweight='bold', y=0.95)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def save_all_multi_method_plots(self,
                                   correlation_results: Dict[str, Any],
                                   output_dir: str = "multi_method_plots",
                                   prefix: str = ""):
        """
        Generate and save all multi-method correlation plots.
        
        Args:
            correlation_results: Multi-method correlation results
            output_dir: Output directory for plots
            prefix: Prefix for all generated file names
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"Generating multi-method correlation plots in {output_dir}/...")
        
        # 1. Side-by-side heatmaps
        try:
            fig1 = self.create_multi_method_heatmaps(
                correlation_results,
                save_path=os.path.join(output_dir, f"{prefix}multi_method_heatmaps.png")
            )
            plt.close(fig1)
            print("✓ Multi-method heatmaps saved")
        except Exception as e:
            print(f"✗ Failed to create heatmaps: {e}")
        
        # 2. Method convergence analysis
        try:
            self.plot_method_convergence(
                correlation_results,
                save_path=os.path.join(output_dir, f"{prefix}method_convergence.html")
            )
            print("✓ Method convergence analysis saved")
        except Exception as e:
            print(f"✗ Failed to create convergence plot: {e}")
        
        # 3. Comprehensive dashboard
        try:
            self.create_multi_method_dashboard(
                correlation_results,
                save_path=os.path.join(output_dir, f"{prefix}multi_method_dashboard.html")
            )
            print("✓ Multi-method dashboard saved")
        except Exception as e:
            print(f"✗ Failed to create dashboard: {e}")
        
        # 4. Method comparison matrices
        method_pairs = [('pearson', 'spearman'), ('pearson', 'kendall'), ('spearman', 'kendall')]
        
        for method1, method2 in method_pairs:
            try:
                fig4 = self.plot_method_comparison_matrix(
                    correlation_results,
                    method_pair=(method1, method2),
                    save_path=os.path.join(output_dir, f"{prefix}{method1}_vs_{method2}_comparison.png")
                )
                plt.close(fig4)
                print(f"✓ {method1.title()} vs {method2.title()} comparison saved")
            except Exception as e:
                print(f"✗ Failed to create {method1}-{method2} comparison: {e}")
        
        print(f"Multi-method plot generation completed! Check {output_dir}/ for results.")
    
    def _extract_correlation_matrices(self, correlation_results: Dict[str, Any]) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Extract correlation matrices for each method from multi-method results.
        
        Args:
            correlation_results: Multi-method correlation results
            
        Returns:
            Tuple of (pearson_matrix, spearman_matrix, kendall_matrix)
        """
        # Safely extract multi-method results
        results = safe_get_dict_value(correlation_results, 'multi_method_results', {})
        if not results:
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
        
        # Validate and standardize the results
        standardized_results = validate_correlation_results_only(results)
        
        # Get unique variables using safe access
        variables = set()
        for result_data in standardized_results.values():
            var1 = safe_get_dict_value(result_data, 'variable_1', '')
            var2 = safe_get_dict_value(result_data, 'variable_2', '')
            if var1:
                variables.add(var1)
            if var2:
                variables.add(var2)
        
        variables = sorted(list(variables))
        
        # Initialize matrices
        pearson_matrix = pd.DataFrame(index=variables, columns=variables, dtype=float)
        spearman_matrix = pd.DataFrame(index=variables, columns=variables, dtype=float)
        kendall_matrix = pd.DataFrame(index=variables, columns=variables, dtype=float)
        
        # Fill diagonal with 1.0
        for var in variables:
            pearson_matrix.loc[var, var] = 1.0
            spearman_matrix.loc[var, var] = 1.0
            kendall_matrix.loc[var, var] = 1.0
        
        # Fill matrices with correlation values using safe access
        for result_data in standardized_results.values():
            var1 = safe_get_dict_value(result_data, 'variable_1', '')
            var2 = safe_get_dict_value(result_data, 'variable_2', '')
            
            if not var1 or not var2:
                continue
            
            # Safely get correlation values
            pearson_corr = safe_get_dict_value(result_data, 'pearson_correlation', 0.0)
            spearman_corr = safe_get_dict_value(result_data, 'spearman_correlation', 0.0)
            kendall_corr = safe_get_dict_value(result_data, 'kendall_correlation', 0.0)
            
            # Fill both upper and lower triangles
            pearson_matrix.loc[var1, var2] = pearson_corr
            pearson_matrix.loc[var2, var1] = pearson_corr
            
            spearman_matrix.loc[var1, var2] = spearman_corr
            spearman_matrix.loc[var2, var1] = spearman_corr
            
            kendall_matrix.loc[var1, var2] = kendall_corr
            kendall_matrix.loc[var2, var1] = kendall_corr
        
        return pearson_matrix, spearman_matrix, kendall_matrix


# Convenience functions
def quick_multi_method_heatmaps(correlation_results: Dict[str, Any], 
                               title: str = "Multi-Method Correlation Analysis",
                               save_path: Optional[str] = None) -> plt.Figure:
    """Quick multi-method correlation heatmaps"""
    plotter = MultiMethodCorrelationPlotter()
    return plotter.create_multi_method_heatmaps(correlation_results, title=title, save_path=save_path)


def quick_method_convergence_plot(correlation_results: Dict[str, Any], 
                                  title: str = "Method Convergence Analysis",
                                  save_path: Optional[str] = None) -> go.Figure:
    """Quick method convergence analysis"""
    plotter = MultiMethodCorrelationPlotter()
    return plotter.plot_method_convergence(correlation_results, title=title, save_path=save_path)


# Example usage and testing
def main():
    """Example usage of the multi-method plotting module"""
    
    # This would typically use real correlation results
    # For demonstration, create mock results structure
    print("Multi-method correlation plotting module loaded successfully!")
    print("Use create_sample_multi_method_analysis() from multi_correlations.py to generate test data")
    
    # Example of how to use with real data:
    # from ..data.multi_correlations import create_sample_multi_method_analysis
    # sample_results = create_sample_multi_method_analysis()
    # plotter = MultiMethodCorrelationPlotter()
    # fig = plotter.create_multi_method_heatmaps(sample_results)
    # plt.show()


if __name__ == "__main__":
    main()