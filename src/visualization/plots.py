"""
Visualization Module for Manufacturing Correlation Analysis

Provides plotting and visualization capabilities for correlation matrices,
time-series data, and manufacturing process relationships.
"""

# Configure matplotlib to use non-GUI backend to prevent macOS NSWindow thread errors
import matplotlib
matplotlib.use('Agg')

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

class ManufacturingCorrelationPlotter:
    """
    Specialized plotting class for manufacturing correlation analysis
    """
    
    def __init__(self, style: str = 'seaborn-v0_8', figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize the plotter with style settings.
        
        Args:
            style: Matplotlib style
            figsize: Default figure size
        """
        self.style = style
        self.figsize = figsize
        
        # Set matplotlib style
        try:
            plt.style.use(style)
        except Exception:
            plt.style.use('default')
        
        # Color palettes for different plot types
        self.correlation_colors = ['#d73027', '#f46d43', '#fdae61', '#fee08b', 
                                  '#ffffbf', '#e6f598', '#abdda4', '#66c2a5', '#3288bd']
        
        self.manufacturing_colors = {
            'speed': '#1f77b4',
            'temperature': '#ff7f0e', 
            'thickness': '#2ca02c',
            'pressure': '#d62728',
            'quality': '#9467bd',
            'stop': '#8c564b',
            'scrap': '#e377c2'
        }
    
    def plot_correlation_matrix(self, 
                               correlation_matrix: pd.DataFrame,
                               title: str = "Manufacturing Variables Correlation Matrix",
                               save_path: Optional[str] = None,
                               figsize: Optional[Tuple[int, int]] = None,
                               mask_diagonal: bool = True,
                               significance_mask: Optional[pd.DataFrame] = None,
                               significance_threshold: float = 0.05) -> plt.Figure:
        """
        Create a correlation matrix heatmap.
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            title: Plot title
            save_path: Path to save the figure
            figsize: Figure size override
            mask_diagonal: Whether to mask the diagonal
            significance_mask: Matrix of p-values for significance masking
            significance_threshold: Significance threshold for masking
            
        Returns:
            Matplotlib figure object
        """
        # Set figure size
        fig_size = figsize or self.figsize
        fig, ax = plt.subplots(figsize=fig_size)
        
        # Create mask for non-significant correlations
        mask = None
        if significance_mask is not None:
            mask = significance_mask > significance_threshold
        
        # Mask diagonal if requested
        if mask_diagonal:
            diagonal_mask = np.eye(len(correlation_matrix), dtype=bool)
            mask = diagonal_mask if mask is None else (mask | diagonal_mask)
        
        # Create heatmap
        sns.heatmap(
            correlation_matrix,
            annot=True,
            cmap='RdBu_r',
            center=0,
            mask=mask,
            square=True,
            fmt='.3f',
            cbar_kws={'label': 'Correlation Coefficient'},
            ax=ax
        )
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel('Variables', fontsize=12)
        ax.set_ylabel('Variables', fontsize=12)
        
        # Rotate labels for better readability
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_correlation_network(self,
                                correlation_matrix: pd.DataFrame,
                                threshold: float = 0.5,
                                title: str = "Correlation Network",
                                save_path: Optional[str] = None) -> go.Figure:
        """
        Create an interactive correlation network plot.
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            threshold: Minimum correlation strength to show
            title: Plot title
            save_path: Path to save the figure
            
        Returns:
            Plotly figure object
        """
        # Create network data
        nodes = list(correlation_matrix.columns)
        edges = []
        edge_weights = []
        
        for i, var1 in enumerate(nodes):
            for j, var2 in enumerate(nodes):
                if i < j:  # Avoid duplicates
                    corr_val = correlation_matrix.loc[var1, var2]
                    if abs(corr_val) >= threshold:
                        edges.append((i, j))
                        edge_weights.append(abs(corr_val))
        
        # Create plotly network graph
        fig = go.Figure()
        
        # Add edges
        for (i, j), weight in zip(edges, edge_weights):
            x0, y0 = np.cos(2 * np.pi * i / len(nodes)), np.sin(2 * np.pi * i / len(nodes))
            x1, y1 = np.cos(2 * np.pi * j / len(nodes)), np.sin(2 * np.pi * j / len(nodes))
            
            fig.add_trace(go.Scatter(
                x=[x0, x1, None],
                y=[y0, y1, None],
                mode='lines',
                line=dict(width=weight * 5, color='gray'),
                hoverinfo='none',
                showlegend=False
            ))
        
        # Add nodes
        node_x = [np.cos(2 * np.pi * i / len(nodes)) for i in range(len(nodes))]
        node_y = [np.sin(2 * np.pi * i / len(nodes)) for i in range(len(nodes))]
        
        fig.add_trace(go.Scatter(
            x=node_x,
            y=node_y,
            mode='markers+text',
            marker=dict(size=20, color='lightblue'),
            text=nodes,
            textposition='middle center',
            hoverinfo='text',
            showlegend=False
        ))
        
        fig.update_layout(
            title=title,
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20, l=5, r=5, t=40),
            annotations=[
                dict(
                    text=f"Showing correlations ≥ {threshold}",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.005, y=-0.002, 
                    xanchor='left', yanchor='bottom'
                )
            ],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def plot_scatter_with_correlation(self,
                                    data: pd.DataFrame,
                                    x_var: str,
                                    y_var: str,
                                    title: Optional[str] = None,
                                    color_var: Optional[str] = None,
                                    save_path: Optional[str] = None,
                                    show_trendline: bool = True) -> plt.Figure:
        """
        Create scatter plot with correlation information.
        
        Args:
            data: DataFrame containing the variables
            x_var: X-axis variable name
            y_var: Y-axis variable name
            title: Plot title
            color_var: Variable to use for coloring points
            save_path: Path to save the figure
            show_trendline: Whether to show trend line
            
        Returns:
            Matplotlib figure object
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Clean data
        clean_data = data[[x_var, y_var]].dropna()
        if color_var and color_var in data.columns:
            clean_data = data[[x_var, y_var, color_var]].dropna()
        
        # Calculate correlation
        correlation = clean_data[x_var].corr(clean_data[y_var])
        
        # Create scatter plot
        if color_var and color_var in clean_data.columns:
            scatter = ax.scatter(clean_data[x_var], clean_data[y_var], 
                               c=clean_data[color_var], cmap='viridis', alpha=0.6)
            plt.colorbar(scatter, label=color_var)
        else:
            ax.scatter(clean_data[x_var], clean_data[y_var], alpha=0.6)
        
        # Add trend line
        if show_trendline:
            z = np.polyfit(clean_data[x_var], clean_data[y_var], 1)
            p = np.poly1d(z)
            ax.plot(clean_data[x_var].sort_values(), p(clean_data[x_var].sort_values()), 
                   "r--", alpha=0.8, linewidth=2)
        
        # Set labels and title
        ax.set_xlabel(x_var, fontsize=12)
        ax.set_ylabel(y_var, fontsize=12)
        
        if title is None:
            title = f'{x_var} vs {y_var} (r = {correlation:.3f})'
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        # Add correlation annotation
        ax.text(0.05, 0.95, f'Correlation: {correlation:.3f}',
                transform=ax.transAxes, fontsize=12,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_lag_correlation(self,
                           lag_correlations: Dict[int, float],
                           variable_1: str,
                           variable_2: str,
                           title: Optional[str] = None,
                           save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot lag correlation analysis results.
        
        Args:
            lag_correlations: Dictionary of lag -> correlation
            variable_1: First variable name
            variable_2: Second variable name
            title: Plot title
            save_path: Path to save the figure
            
        Returns:
            Matplotlib figure object
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Extract lags and correlations
        lags = sorted(lag_correlations.keys())
        correlations = [lag_correlations[lag] for lag in lags]
        
        # Create bar plot
        colors = ['red' if corr < 0 else 'blue' for corr in correlations]
        bars = ax.bar(lags, correlations, color=colors, alpha=0.7)
        
        # Highlight optimal lag
        optimal_lag = max(lags, key=lambda x: abs(lag_correlations[x]))
        optimal_idx = lags.index(optimal_lag)
        bars[optimal_idx].set_color('gold')
        bars[optimal_idx].set_edgecolor('orange')
        bars[optimal_idx].set_linewidth(2)
        
        # Add horizontal line at zero
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # Set labels and title
        ax.set_xlabel('Lag (time periods)', fontsize=12)
        ax.set_ylabel('Correlation Coefficient', fontsize=12)
        
        if title is None:
            title = f'Lag Correlation: {variable_1} → {variable_2}'
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        # Add optimal lag annotation
        ax.text(optimal_lag, lag_correlations[optimal_lag], 
                f'Optimal\nLag: {optimal_lag}\nr = {lag_correlations[optimal_lag]:.3f}',
                ha='center', va='bottom', fontsize=10,
                bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_time_series_with_events(self,
                                   data: pd.DataFrame,
                                   value_columns: List[str],
                                   time_column: str = 'timestamp',
                                   event_data: Optional[pd.DataFrame] = None,
                                   event_time_column: str = 'timestamp',
                                   title: str = "Manufacturing Time Series",
                                   save_path: Optional[str] = None) -> go.Figure:
        """
        Create interactive time series plot with event markers.
        
        Args:
            data: Time series data DataFrame
            value_columns: List of columns to plot
            time_column: Time column name
            event_data: DataFrame with event information
            event_time_column: Event timestamp column
            title: Plot title
            save_path: Path to save the figure
            
        Returns:
            Plotly figure object
        """
        # Create subplots
        fig = make_subplots(
            rows=len(value_columns),
            cols=1,
            shared_xaxes=True,
            subplot_titles=value_columns,
            vertical_spacing=0.05
        )
        
        # Plot each variable
        for i, col in enumerate(value_columns, 1):
            if col in data.columns:
                fig.add_trace(
                    go.Scatter(
                        x=data[time_column],
                        y=data[col],
                        mode='lines',
                        name=col,
                        line=dict(color=self.manufacturing_colors.get(col.lower(), '#1f77b4'))
                    ),
                    row=i, col=1
                )
        
        # Add event markers if provided
        if event_data is not None and event_time_column in event_data.columns:
            for i, col in enumerate(value_columns, 1):
                for _, event in event_data.iterrows():
                    event_time = event[event_time_column]
                    
                    # Find corresponding y-value at event time
                    closest_idx = data[time_column].sub(event_time).abs().idxmin()
                    y_val = data.loc[closest_idx, col] if col in data.columns else 0
                    
                    fig.add_trace(
                        go.Scatter(
                            x=[event_time],
                            y=[y_val],
                            mode='markers',
                            marker=dict(size=10, color='red', symbol='x'),
                            name="Event" if i == 1 else None,
                            showlegend=(i == 1),
                            hovertext=f"Event: {event.get('Stoppage Reason', 'Unknown')}"
                        ),
                        row=i, col=1
                    )
        
        # Update layout
        fig.update_layout(
            title=title,
            height=200 * len(value_columns),
            showlegend=True,
            hovermode='x unified'
        )
        
        fig.update_xaxes(title_text="Time", row=len(value_columns), col=1)
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def plot_correlation_dashboard(self,
                                 correlation_matrix: pd.DataFrame,
                                 significant_correlations: List[Dict],
                                 data: pd.DataFrame,
                                 save_path: Optional[str] = None) -> go.Figure:
        """
        Create comprehensive correlation dashboard.
        
        Args:
            correlation_matrix: Correlation matrix
            significant_correlations: List of significant correlation results
            data: Original data for scatter plots
            save_path: Path to save the figure
            
        Returns:
            Plotly figure object
        """
        # Create subplot layout
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=(
                'Correlation Matrix',
                'Top Correlations',
                'Correlation Distribution',
                'Sample Scatter Plot'
            ),
            specs=[[{"type": "heatmap"}, {"type": "bar"}],
                   [{"type": "histogram"}, {"type": "scatter"}]]
        )
        
        # 1. Correlation matrix heatmap
        fig.add_trace(
            go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.columns,
                colorscale='RdBu',
                zmid=0,
                text=correlation_matrix.round(3).values,
                texttemplate="%{text}",
                textfont={"size": 8}
            ),
            row=1, col=1
        )
        
        # 2. Top correlations bar chart
        if significant_correlations:
            top_corrs = significant_correlations[:10]
            corr_labels = [f"{c['variable_1']} - {c['variable_2']}" for c in top_corrs]
            corr_values = [c['correlation_coefficient'] for c in top_corrs]
            
            fig.add_trace(
                go.Bar(
                    x=corr_values,
                    y=corr_labels,
                    orientation='h',
                    marker_color=['red' if x < 0 else 'blue' for x in corr_values]
                ),
                row=1, col=2
            )
        
        # 3. Correlation distribution
        all_correlations = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)]
        fig.add_trace(
            go.Histogram(
                x=all_correlations,
                nbinsx=20,
                marker_color='lightblue',
                opacity=0.7
            ),
            row=2, col=1
        )
        
        # 4. Sample scatter plot
        if significant_correlations:
            top_corr = significant_correlations[0]
            var1 = top_corr['variable_1']
            var2 = top_corr['variable_2']
            
            if var1 in data.columns and var2 in data.columns:
                clean_data = data[[var1, var2]].dropna()
                
                fig.add_trace(
                    go.Scatter(
                        x=clean_data[var1],
                        y=clean_data[var2],
                        mode='markers',
                        marker=dict(opacity=0.6),
                        name=f'{var1} vs {var2}'
                    ),
                    row=2, col=2
                )
        
        # Update layout
        fig.update_layout(
            height=800,
            title_text="Manufacturing Correlation Dashboard",
            showlegend=False
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def save_all_plots(self,
                      correlation_matrix: pd.DataFrame,
                      significant_correlations: List[Dict],
                      data: pd.DataFrame,
                      output_dir: str = "correlation_plots"):
        """
        Generate and save all standard correlation plots.
        
        Args:
            correlation_matrix: Correlation matrix
            significant_correlations: List of significant correlations
            data: Original data
            output_dir: Output directory for plots
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. Correlation matrix heatmap
        fig1 = self.plot_correlation_matrix(
            correlation_matrix,
            save_path=os.path.join(output_dir, "correlation_matrix.png")
        )
        plt.close(fig1)
        
        # 2. Interactive correlation network
        self.plot_correlation_network(
            correlation_matrix,
            save_path=os.path.join(output_dir, "correlation_network.html")
        )
        
        # 3. Top correlation scatter plots
        for i, corr in enumerate(significant_correlations[:5]):
            var1 = corr['variable_1']
            var2 = corr['variable_2']
            
            if var1 in data.columns and var2 in data.columns:
                fig3 = self.plot_scatter_with_correlation(
                    data, var1, var2,
                    save_path=os.path.join(output_dir, f"scatter_{i+1}_{var1}_{var2}.png")
                )
                plt.close(fig3)
        
        # 4. Correlation dashboard
        self.plot_correlation_dashboard(
            correlation_matrix,
            significant_correlations,
            data,
            save_path=os.path.join(output_dir, "correlation_dashboard.html")
        )
        
        print(f"All plots saved to {output_dir}/")

# Convenience functions
def quick_correlation_plot(correlation_matrix: pd.DataFrame, 
                          title: str = "Correlation Matrix") -> plt.Figure:
    """Quick correlation matrix plot"""
    plotter = ManufacturingCorrelationPlotter()
    return plotter.plot_correlation_matrix(correlation_matrix, title=title)

def quick_scatter_plot(data: pd.DataFrame, 
                      x_var: str, 
                      y_var: str) -> plt.Figure:
    """Quick scatter plot with correlation"""
    plotter = ManufacturingCorrelationPlotter()
    return plotter.plot_scatter_with_correlation(data, x_var, y_var)

# Example usage
def main():
    """Example usage of the plotting module"""
    
    # Create sample data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate correlated manufacturing data
    speed = np.random.normal(150, 10, n_samples)
    temperature = 80 + 0.1 * speed + np.random.normal(0, 2, n_samples)
    thickness = 12.5 + 0.01 * speed + 0.005 * temperature + np.random.normal(0, 0.2, n_samples)
    quality = 100 - 0.1 * np.abs(speed - 150) - 0.2 * np.abs(temperature - 80) + np.random.normal(0, 1, n_samples)
    
    data = pd.DataFrame({
        'speed': speed,
        'temperature': temperature,
        'thickness': thickness,
        'quality_score': quality
    })
    
    # Calculate correlation matrix
    correlation_matrix = data.corr()
    
    # Create plotter
    plotter = ManufacturingCorrelationPlotter()
    
    # Example plots
    print("Generating example plots...")
    
    # 1. Correlation matrix
    plotter.plot_correlation_matrix(correlation_matrix)
    # Only show in interactive GUI environments
    from .backend_utils import safe_show
    safe_show()
    
    # 2. Scatter plot
    plotter.plot_scatter_with_correlation(data, 'speed', 'thickness')
    # Only show in interactive GUI environments
    safe_show()
    
    # 3. Interactive network (would open in browser)
    # fig3 = plotter.plot_correlation_network(correlation_matrix)
    # fig3.show()
    
    print("Example plots completed!")

if __name__ == "__main__":
    main()