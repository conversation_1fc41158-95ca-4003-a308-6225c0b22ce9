{"description": "Data configuration for unified manufacturing data loader", "version": "1.0", "add_thickness": false, "data_sources": {"vm_capacity": {"file_path": "test-data/data-cyrus/VM Capacity Report.csv", "description": "Product specifications and capacity data", "key_columns": ["SAP Code\t", "Design Capacity", "Design Felt Speed", "Sheet Machine", "Finishing Machine"], "timestamp_columns": [], "data_type": "reference"}, "fm_stack": {"file_path": "test-data/data-cyrus/fm_stack.csv", "description": "Forming machine stack data with quality metrics", "skip_rows": 1, "key_columns": ["MPS ID", "Production Order", "Product Description", "Ok", "<PERSON>j.", "On-Load", "Off-Load"], "timestamp_columns": {"on_load": "On-Load", "off_load": "Off-Load"}, "data_type": "batch"}, "sm_stack": {"file_path": "test-data/data-cyrus/sm_stack.csv", "description": "Sheet machine stack data with scrap percentages", "key_columns": ["Stack Number", "First Sheet Date Time", "Last Sheet Date Time", "Good Sheets", "Scrap%", "Production Order", "Product"], "timestamp_columns": {"first_sheet": "First Sheet Date Time", "last_sheet": "Last Sheet Date Time"}, "data_type": "batch"}, "speed": {"file_path": "test-data/speed.csv", "description": "Production line speed measurements", "key_columns": ["Work Center/Resource", "Log Date", "Log Time", "Speed"], "timestamp_columns": {"log_date": "Log Date", "log_time": "Log Time"}, "data_type": "continuous"}, "stop": {"file_path": "test-data/stop.csv", "description": "Machine stoppage events with duration and reasons", "key_columns": ["Stop ID", "Work Center/Resource", "Stop Date", "Stop Time", "Restart Date", "Restart Time", "MPS Stop Duration", "Stoppage Reason"], "timestamp_columns": {"stop_date": "Stop Date", "stop_time": "Stop Time", "restart_date": "Restart Date", "restart_time": "Restart Time"}, "data_type": "event"}, "tm480_stack": {"file_path": "test-data/data-bikram/ztI_fs_pip Tm480.csv", "description": "TM480 finishing machine stack data with quality metrics", "key_columns": ["Stack Number", "Actual Stack Number", "Production Order", "Finish Start Date", "Finish Start Time", "Finish End Date", "Finish End Time", "Sheet Accepted", "Total Sheet Rejected"], "timestamp_columns": {"finish_start_date": "Finish Start Date", "finish_start_time": "Finish Start Time", "finish_end_date": "Finish End Date", "finish_end_time": "Finish End Time"}, "data_type": "batch"}}, "unified_schema": {"base_table": "speed", "timestamp_column": "timestamp", "work_center_column": "work_center", "features": {"thickness": ["sensor_01", "sensor_02", "sensor_03", "sensor_04", "sensor_05", "sensor_06", "sensor_07", "sensor_08", "sensor_09", "sensor_10", "thickness_avg", "thickness_range", "thickness_std", "wedge_index", "crown_bow_index"], "speed": ["speed", "speed_avg_5min", "speed_change_rate"], "stoppage": ["time_since_last_stop", "is_restart_period", "stop_duration_previous", "stops_last_hour"], "sm_stack": ["sm_stack_number", "sm_production_order", "sm_product", "sm_good_sheets", "sm_scrap_pct"], "fm_stack": ["fm_mps_id", "fm_production_order", "fm_product_description", "fm_ok_sheets", "fm_reject_sheets", "fm_position_pct"], "product_specs": ["product_code", "SAP_Code", "Design Capacity", "Design Felt Speed", "Sheet Machine", "Finishing Machine"], "temporal": ["hour_of_day", "minute_of_hour", "day_of_week", "shift", "hour_sin", "hour_cos"], "sequence": ["sheet_sequence", "time_since_start", "thickness_deviation", "is_within_spec"]}}, "data_validation": {"timestamp_formats": ["%Y.%m.%d %H:%M:%S", "%Y-%m-%d %H:%M:%S", "%d-%%b-%Y %H:%M:%S"], "manufacturing_ranges": {"speed": {"min": 0, "max": 150}, "thickness": {"min": 0, "max": 50}, "stop_duration": {"min": 0, "max": 10080}}}}