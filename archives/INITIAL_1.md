## FEATURE:

- **Fiber Cement Manufacturing Correlation Analysis System**: A comprehensive data analysis platform that correlates machine stoppages, speed variations, and thickness deviations with scrap rates in fiber cement production.
- **Multi-Phase Analysis Pipeline**: Implements a 3-phase approach from foundational event-to-continuous correlation analysis to predictive modeling using Temporal Fusion Transformers (TFT).
- **AI-Powered Root Cause Analysis Agent**: An LLM-powered diagnostic tool that performs automated root cause analysis for scrap events using causal inference and time-series data.
- **Unified Time-Series Data Processing**: Aligns multiple data sources (stoppages, speed, thickness, scrap) onto a common minute-by-minute timeline for comprehensive analysis.
- **Interactive Visualization Dashboard**: Provides event-triggered average plots and correlation matrices to visualize the impact of stoppages on process stability.

## EXAMPLES:

In the `test-data/` folder, there are sample CSV files that demonstrate the data structure:

- `stop.csv` - Machine stoppage events with timestamps and durations
- `speed.csv` - Continuous speed measurements from production line
- `thickness.csv` - Thickness measurements from quality control sensors
- `fm_stack.csv` and `sm_stack.csv` - Scrap event data from different production stacks

These files will be used to:
- Demonstrate the unified time-series construction process
- Show event-driven feature engineering around stoppage events
- Validate the correlation between process variables and scrap rates
- Train and test the predictive models

## DOCUMENTATION:

- **Temporal Fusion Transformer (TFT)**: Research papers and implementation guides for advanced time-series forecasting
- **PCMCI Causal Discovery**: Documentation for causal inference algorithms to build causal graphs
- **Pandas Time-Series**: Documentation for time-series data manipulation and resampling
- **Scikit-learn**: For baseline model implementations (Logistic Regression, XGBoost)
- **Matplotlib/Plotly**: For creating event-triggered average plots and correlation visualizations
- **Process Engineering Domain Knowledge**: Fiber cement manufacturing process documentation and quality control standards

## OTHER CONSIDERATIONS:

- **Data Alignment Critical**: The single most important step is aligning all four data sources onto a common minute-by-minute timeline. This requires careful handling of different sampling rates and event-based vs. continuous data.
- **Feature Engineering Focus**: The core value lies in creating event-driven features that capture the system's behavior around critical stoppage events (15 minutes before to 60 minutes after).
- **Model Interpretability**: The TFT model must provide Variable Importance scores and Attention Maps to identify which process variables are most predictive of scrap events.
- **Validation with Domain Experts**: All visualizations and model outputs should be validated with process engineers who understand the manufacturing process.
- **Performance Baseline**: Expect >20% improvement over baseline models (Logistic Regression/XGBoost) due to TFT's ability to capture complex temporal patterns.
- **Real-time Capability**: The system should be designed to process new data and trigger RCA analysis in near real-time when scrap events occur.
- **Causal Graph Simplicity**: Keep the causal discovery focused on the top 5-7 most important features to maintain interpretability and avoid overfitting.
- **Expert Validation Target**: The RCA Agent should achieve an average expert score of 4/5 or higher for accuracy and usefulness of generated reports. 