# Manufacturing Multi-Method Correlation Analysis Agent System - Comprehensive Technical Report

## Executive Summary

The `/src/agents` folder contains a sophisticated **Manufacturing Multi-Method Correlation Analysis Agent System** built on PydanticAI. This is a production-ready industrial analytics system specifically designed for fiber cement manufacturing data analysis, featuring advanced statistical correlation analysis, intelligent method selection, and comprehensive visualization capabilities.

## System Architecture Mind Map

```
Manufacturing Multi-Method Correlation Analysis Agent System
├── Core Agent (correlation_agent.py)
│   ├── PydanticAI Framework
│   ├── Manufacturing Domain Knowledge
│   ├── Multi-Provider LLM Support (Anthropic/Vertex AI)
│   └── Structured Output (CorrelationAnalysis)
│
├── Tool Ecosystem (20+ Tools)
│   ├── Core Correlation Tools (8 tools)
│   │   ├── calculate_correlation_matrix_tool
│   │   ├── find_significant_correlations_tool
│   │   ├── analyze_lag_correlations_tool
│   │   ├── calculate_thickness_metrics_tool
│   │   ├── analyze_process_correlations_tool
│   │   ├── calculate_data_quality_score_tool
│   │   ├── filter_data_by_time_range_tool
│   │   └── get_variable_summary_tool
│   │
│   ├── Multi-Method Analysis Tools (4 tools)
│   │   ├── calculate_multi_method_correlations_tool
│   │   ├── analyze_method_convergence_tool
│   │   ├── recommend_correlation_method_tool
│   │   └── calculate_robustness_metrics_tool
│   │
│   ├── Visualization Tools (5 tools)
│   │   ├── create_multi_method_heatmaps_tool
│   │   ├── plot_method_convergence_tool
│   │   ├── create_multi_method_dashboard_tool
│   │   ├── plot_method_comparison_matrix_tool
│   │   └── generate_all_visualizations_tool
│   │
│   └── Data Safety Utilities
│       ├── safe_get_dict_value
│       ├── standardize_p_value
│       ├── format_p_value_for_display
│       └── validate_correlation_results_only
│
├── Intelligent Prompt System (8+ Prompts)
│   ├── CORRELATION_SYSTEM_PROMPT (Manufacturing Base)
│   ├── MULTI_METHOD_CORRELATION_PROMPT
│   ├── LAG_CORRELATION_PROMPT
│   ├── PROCESS_OPTIMIZATION_PROMPT
│   ├── QUALITY_ANALYSIS_PROMPT
│   ├── EQUIPMENT_ANALYSIS_PROMPT
│   ├── ROOT_CAUSE_ANALYSIS_PROMPT
│   └── DATA_QUALITY_ASSESSMENT_PROMPT
│
└── Manufacturing Domain Integration
    ├── Fiber Cement Process Knowledge
    ├── Equipment Relationships (FM/SM/Stackers)
    ├── Thickness Sensor Processing (10 sensors)
    └── Quality Metrics & Process Optimization
```

## 1. Agent Architecture Overview

### Core Components
- **Main Agent**: `correlation_agent.py` - Central PydanticAI agent with 20+ tools
- **Tool Modules**: 4 specialized tool categories across 5 files
- **Prompt System**: 8+ domain-specific prompt templates
- **Data Utilities**: Safety and validation utilities for robust operation
- **Visualization Engine**: 5 comprehensive visualization tools

### Agent Configuration
```python
correlation_agent = Agent(
    model=get_model_config(),  # Supports Anthropic Claude & Google Vertex AI
    system_prompt=CORRELATION_SYSTEM_PROMPT,
    output_type=CorrelationAnalysis,  # Structured Pydantic output
    deps_type=ManufacturingDataDependencies
)
```

## 2. Core Agent Logic and Decision Patterns

### Manufacturing Domain Intelligence Mind Map

```
Manufacturing Process Understanding
├── Equipment Flow
│   ├── Forming Machines (FM) → Create thickness profile
│   ├── Sheet Machines (SM) → Cut and process
│   ├── Stackers → Handle products
│   └── Curing Equipment → Control temperature/moisture
│
├── Process Timing
│   ├── Immediate Effects (0-5 min)
│   ├── Short Delays (5-15 min) → Speed to thickness
│   ├── Medium Delays (15-30 min) → Temperature effects
│   └── Long Delays (30-60 min) → Stoppage recovery
│
├── Quality Metrics
│   ├── Thickness Analysis
│   │   ├── thickness_avg = mean(Sensor 01-10)
│   │   ├── thickness_uniformity = std(Sensor 01-10)
│   │   └── thickness_range = max - min sensors
│   ├── Scrap Rates
│   ├── Strength & Density
│   └── Surface Quality
│
└── Critical Variables
    ├── Speed vs Quality Trade-offs
    ├── Temperature → Material Properties
    ├── Pressure → Density Control
    └── Environmental Factors
```

The agent operates with specialized knowledge of fiber cement manufacturing:

**Process Understanding:**
- Forming machines (FM) → Sheet machines (SM) → Stackers → Curing
- Typical process delays: 5-60 minutes between cause and effect
- **Thickness Measurement Logic**: Automatically calculates `thickness_avg` from 10 sensors (Sensor 01-10)
- Quality metrics: thickness uniformity, strength, density, scrap rates

**Analysis Patterns:**
1. **Auto-Detection**: Recognizes multi-method analysis requests from natural language
2. **Context-Aware Prompting**: Selects specialized prompts based on analysis type
3. **Statistical Rigor**: 8-decimal internal precision, 6-decimal display, scientific notation for p < 1e-6
4. **Manufacturing Interpretation**: Physical process explanations for correlation findings

### Decision Flow
```python
# Auto-detect multi-method analysis requests
multi_method_keywords = ['all three methods', 'three methods', 'pearson spearman kendall', 
                        'compare methods', 'method comparison', 'multi-method']
if any(keyword.lower() in query.lower() for keyword in multi_method_keywords):
    analysis_type = 'multi_method'
```

## 3. Tool Ecosystem (20+ Specialized Tools)

### Tool Categories Mind Map

```
Tool Ecosystem (20+ Tools)
├── Core Correlation Tools (tools.py) - 8 tools
│   ├── Basic Analysis
│   │   ├── calculate_correlation_matrix_tool
│   │   ├── find_significant_correlations_tool
│   │   └── get_variable_summary_tool
│   ├── Time-Based Analysis
│   │   ├── analyze_lag_correlations_tool
│   │   ├── analyze_process_correlations_tool
│   │   └── filter_data_by_time_range_tool
│   └── Manufacturing Specific
│       ├── calculate_thickness_metrics_tool
│       └── calculate_data_quality_score_tool
│
├── Multi-Method Analysis (multi_tools.py) - 4 tools
│   ├── calculate_multi_method_correlations_tool
│   ├── analyze_method_convergence_tool
│   ├── recommend_correlation_method_tool
│   └── calculate_robustness_metrics_tool
│
├── Visualization Suite (visualization_tools.py) - 5 tools
│   ├── Static Plots (PNG)
│   │   ├── create_multi_method_heatmaps_tool
│   │   └── plot_method_comparison_matrix_tool
│   ├── Interactive Plots (HTML)
│   │   ├── plot_method_convergence_tool
│   │   └── create_multi_method_dashboard_tool
│   └── Batch Generation
│       └── generate_all_visualizations_tool
│
└── Data Safety (data_utils.py) - Critical utilities
    ├── safe_get_dict_value → Handle mixed object/dict access
    ├── standardize_p_value → Convert various formats to float
    ├── format_p_value_for_display → Handle extreme values
    └── validate_correlation_results_only → Type-safe validation
```

### 3.1 Core Correlation Tools (`tools.py`)
**8 Manufacturing-Focused Tools:**

1. **`calculate_correlation_matrix_tool`**
   - **When Used**: Basic correlation analysis requests
   - **Logic**: Calculates correlation matrix with configurable methods (pearson/spearman/kendall)
   - **Manufacturing Context**: Handles mixed-frequency sensor data

2. **`find_significant_correlations_tool`**
   - **When Used**: Discovering statistically significant relationships
   - **Logic**: Filters correlations above threshold with p-value validation
   - **Key Feature**: Reports ALL correlations ≥0.01 (never rounds to zero)

3. **`analyze_lag_correlations_tool`**
   - **When Used**: Time-delayed relationship analysis
   - **Logic**: Tests correlations across 0-60 minute lags
   - **Manufacturing Context**: Accounts for process propagation delays

4. **`calculate_thickness_metrics_tool`**
   - **When Used**: Automatically triggered for thickness analysis
   - **Logic**: 
     ```python
     thickness_avg = mean(Sensor_01 to Sensor_10)
     thickness_uniformity = std(Sensor_01 to Sensor_10)
     thickness_range = max(sensors) - min(sensors)
     ```

5. **`calculate_data_quality_score_tool`**
   - **When Used**: Data validation and quality assessment
   - **Logic**: Combines completeness (40%) + consistency (30%) + validity (30%)

6. **Additional Tools**: `analyze_process_correlations_tool`, `filter_data_by_time_range_tool`, `get_variable_summary_tool`

### 3.2 Multi-Method Analysis Tools (`multi_tools.py`)

### Multi-Method Analysis Decision Tree

```
Multi-Method Analysis Logic
├── Data Distribution Assessment
│   ├── Normality Testing
│   │   ├── Normal Distribution → Pearson preferred
│   │   └── Non-Normal → Spearman/Kendall
│   ├── Outlier Detection
│   │   ├── Significant Outliers → Robust methods
│   │   └── Clean Data → Any method suitable
│   └── Relationship Type
│       ├── Linear → Pearson optimal
│       ├── Monotonic → Spearman optimal
│       └── Non-linear → Kendall most robust
│
├── Method Selection Decision Tree
│   ├── IF significant_outliers OR non_normal:
│   │   ├── IF monotonic: RECOMMEND Spearman
│   │   └── ELSE: RECOMMEND Kendall
│   ├── ELIF normal AND linear: RECOMMEND Pearson
│   ├── ELIF monotonic: RECOMMEND Spearman
│   └── ELSE: RECOMMEND Kendall
│
└── Convergence Analysis
    ├── High Convergence (>0.8) → Robust relationship
    ├── Medium Convergence (0.5-0.8) → Method differences
    └── Low Convergence (<0.5) → Method-dependent results
```

**4 Advanced Statistical Tools:**

1. **`calculate_multi_method_correlations_tool`**
   - **When Used**: "Compare all three methods", "multi-method analysis"
   - **Logic**: Calculates Pearson, Spearman, Kendall simultaneously with convergence analysis
   - **Output**: Method convergence scores, intelligent recommendations

2. **`analyze_method_convergence_tool`**
   - **When Used**: Assessing method agreement patterns
   - **Logic**: 
     ```python
     convergence_score = 1 - normalized_variance(pearson, spearman, kendall)
     if convergence > 0.8: "robust relationship"
     elif convergence > 0.5: "moderate agreement"  
     else: "method-dependent results"
     ```

3. **`recommend_correlation_method_tool`**
   - **When Used**: Method selection guidance requests
   - **Logic**: Data distribution assessment → decision tree algorithm

4. **`calculate_robustness_metrics_tool`**
   - **When Used**: Stability analysis with bootstrap sampling
   - **Logic**: 50-100 bootstrap samples for stability scoring

### 3.3 Visualization Tools (`visualization_tools.py`)

### Visualization Suite Mind Map

```
Visualization Suite (5 Tools)
├── Static Visualizations (PNG)
│   ├── create_multi_method_heatmaps_tool
│   │   ├── Side-by-side correlation heatmaps
│   │   ├── All three methods (Pearson/Spearman/Kendall)
│   │   ├── Convergence scores overlay
│   │   └── Manufacturing-themed styling
│   └── plot_method_comparison_matrix_tool
│       ├── 4-panel comparison analysis
│       ├── Scatter plots & Bland-Altman
│       ├── Difference statistics
│       └── Agreement measurements
│
├── Interactive Visualizations (HTML)
│   ├── plot_method_convergence_tool
│   │   ├── 4-panel convergence analysis
│   │   ├── Cross-method correlations
│   │   ├── Convergence distributions
│   │   └── Method recommendation charts
│   └── create_multi_method_dashboard_tool
│       ├── 9-panel comprehensive dashboard
│       ├── Interactive scatter plots
│       ├── Method stability charts
│       └── P-value comparisons
│
└── Batch Generation
    └── generate_all_visualizations_tool
        ├── Complete suite (6 files)
        ├── 3 PNG static plots
        ├── 3 HTML interactive dashboards
        └── Automated file management
```

**5 Comprehensive Visualization Tools:**

1. **`create_multi_method_heatmaps_tool`**
   - **Output**: Side-by-side PNG heatmaps for all three methods
   - **When Used**: Visualization requests, automatic generation

2. **`plot_method_convergence_tool`** 
   - **Output**: Interactive HTML with 4-panel convergence analysis
   - **Features**: Cross-method correlations, convergence distributions

3. **`create_multi_method_dashboard_tool`**
   - **Output**: 9-panel interactive HTML dashboard
   - **Panels**: Matrices, scatter plots, box plots, distributions, recommendations

4. **`plot_method_comparison_matrix_tool`**
   - **Output**: 4-panel detailed method comparison
   - **Features**: Scatter plots, Bland-Altman analysis, difference statistics

5. **`generate_all_visualizations_tool`**
   - **Output**: Complete visualization suite (6 files: 3 PNG + 3 HTML)
   - **When Used**: Comprehensive analysis requests
