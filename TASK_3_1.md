# TASK_3_1.md

## FEATURE:
**PatchTST Training Stability and Performance Improvements**

Goal: To fix critical training issues in the existing PatchTST forecasting model by implementing research-backed training methods from paper 2211.14730v2 and advanced time series transformer best practices, resolving infinite evaluation loss and prediction variance problems.

### Current Issues Identified:
- **Infinite Evaluation Loss**: Model shows `eval_loss: Infinity` indicating validation failure
- **No Prediction Variation**: All forecasts ~16.001 (std: 0.000) suggesting overfitting/gradient collapse  
- **Missing Validation Infrastructure**: No proper validation loop or early stopping
- **Training Instability**: Exploding gradients leading to NaN values

### Core Implementation Tasks:

#### 1. Gradient Stabilization Framework:
- **Gradient Clipping Implementation**: Add gradient norm clipping (max norm 1.0) to prevent exploding gradients
- **Learning Rate Scheduling**: Implement warmup and decay schedules for stable convergence
- **Mixed Precision Training**: Use automatic mixed precision for numerical stability
- **Gradient Monitoring**: Add comprehensive gradient norm tracking and visualization
- **Optimizer Enhancements**: Switch to AdamW with proper weight decay and bias correction

#### 2. Advanced Normalization Techniques:
- **UnitNorm Integration**: Replace BatchNorm with UnitNorm specifically designed for time series transformers
- **Layer Normalization**: Add proper layer normalization to transformer blocks
- **Input Scaling**: Implement robust manufacturing sensor data normalization
- **Temporal Normalization**: Handle distribution shift in manufacturing time series data
- **Feature-wise Scaling**: Apply channel-independent normalization for manufacturing variables

#### 3. Robust Training Pipeline:
- **Validation Loop Repair**: Fix dataset creation to resolve infinite evaluation loss
- **Early Stopping**: Implement patience-based early stopping with validation loss monitoring
- **Cross-Validation**: Add time series-aware cross-validation for manufacturing data
- **Checkpoint Management**: Save best models based on multiple metrics
- **Training Monitoring**: Comprehensive logging with tensorboard integration

#### 4. Data Quality Enhancement:
- **Manufacturing Data Validation**: Add robust checks for sensor data anomalies
- **Temporal Augmentation**: Implement time series data augmentation techniques
- **Missing Value Handling**: Advanced imputation for manufacturing sensor gaps
- **Outlier Detection**: Robust outlier handling for industrial sensor data
- **Feature Engineering**: Add derived manufacturing features for better predictions

#### 5. Architecture Improvements:
- **Model Size Reduction**: Reduce complexity (fewer layers, smaller d_model) for stability
- **Dropout Regularization**: Add comprehensive dropout (0.2-0.3) throughout architecture
- **Residual Connections**: Implement proper residual connections with persistence initialization
- **Attention Stabilization**: Add attention weight regularization and temperature scaling
- **Head Architecture**: Optimize prediction head for manufacturing forecasting

#### 6. Transfer Learning Integration:
- **Pre-trained Model Loading**: Integration with IBM Granite and HuggingFace pre-trained models
- **Linear Probing**: Fast adaptation using frozen backbone with trainable head
- **Fine-tuning Strategies**: Full fine-tuning with manufacturing domain adaptation
- **Zero-shot Forecasting**: Immediate forecasting capability without training
- **Domain Adaptation**: Manufacturing-specific transfer learning techniques

### Enhanced Configuration (config.json):

```json
{
  "forecasting_config": {
    "input_variables": [
      "thickness_avg", 
      "thickness_uniformity", 
      "speed", 
      "temperature", 
      "pressure",
      "minutes_since_last_stop"
    ],
    "target_variables": [
      "thickness_avg",
      "scrap_rate", 
      "quality_index"
    ],
    "forecast_horizons": [15, 60, 240],
    "lookback_window": 240,
    "patch_size": 16,
    "patch_stride": 8,
    "model_params": {
      "d_model": 64,
      "num_attention_heads": 4,
      "num_hidden_layers": 2,
      "dropout": 0.3,
      "head_dropout": 0.3,
      "attention_dropout": 0.2,
      "activation": "gelu",
      "norm_first": true,
      "scaling": "unit_norm"
    },
    "training_stability": {
      "gradient_clipping": {
        "enabled": true,
        "max_norm": 1.0,
        "norm_type": 2.0
      },
      "learning_rate_schedule": {
        "initial_lr": 0.0001,
        "warmup_steps": 100,
        "decay_factor": 0.95,
        "decay_patience": 5
      },
      "mixed_precision": {
        "enabled": true,
        "loss_scale": "dynamic"
      },
      "early_stopping": {
        "patience": 15,
        "min_delta": 0.001,
        "restore_best_weights": true
      }
    },
    "data_quality": {
      "outlier_detection": {
        "method": "isolation_forest",
        "contamination": 0.1
      },
      "missing_value_strategy": "forward_fill",
      "temporal_augmentation": {
        "noise_level": 0.01,
        "scaling_factor": 0.1
      }
    }
  }
}
```

### Research-Based Training Methods:

#### **Gradient Stabilization Implementation**
```python
import torch
from torch.nn.utils import clip_grad_norm_
from transformers import get_linear_schedule_with_warmup

class StabilizedTrainer(Trainer):
    """Enhanced trainer with gradient stabilization"""
    
    def __init__(self, *args, gradient_clip_norm=1.0, **kwargs):
        super().__init__(*args, **kwargs)
        self.gradient_clip_norm = gradient_clip_norm
        self.gradient_norms = []
        
    def training_step(self, model, inputs):
        """Training step with gradient clipping and monitoring"""
        model.train()
        inputs = self._prepare_inputs(inputs)
        
        # Forward pass
        outputs = model(**inputs)
        loss = outputs.loss
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping and monitoring
        grad_norm = clip_grad_norm_(
            model.parameters(), 
            self.gradient_clip_norm,
            norm_type=2.0
        )
        self.gradient_norms.append(grad_norm.item())
        
        # Log gradient information
        if self.state.global_step % 10 == 0:
            self.log({
                "grad_norm": grad_norm.item(),
                "grad_norm_avg": np.mean(self.gradient_norms[-100:])
            })
        
        return loss.detach()

# Learning rate scheduler with warmup
def create_lr_scheduler(optimizer, num_training_steps, warmup_steps=100):
    """Create learning rate scheduler with warmup"""
    return get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=warmup_steps,
        num_training_steps=num_training_steps
    )
```

#### **UnitNorm Implementation for Time Series**
```python
import torch
import torch.nn as nn

class UnitNorm(nn.Module):
    """UnitNorm layer specifically designed for time series transformers"""
    
    def __init__(self, d_model, eps=1e-5):
        super().__init__()
        self.d_model = d_model
        self.eps = eps
        self.scale = nn.Parameter(torch.ones(d_model))
        
    def forward(self, x):
        """
        Apply UnitNorm: scale input vectors by their norms
        
        Args:
            x: Input tensor [batch, seq_len, d_model]
        Returns:
            Normalized tensor with same shape
        """
        # Calculate L2 norm along feature dimension
        norm = torch.norm(x, dim=-1, keepdim=True)
        
        # Avoid division by zero
        norm = torch.clamp(norm, min=self.eps)
        
        # Normalize and scale
        normalized = x / norm
        scaled = normalized * self.scale.unsqueeze(0).unsqueeze(0)
        
        return scaled

# Enhanced PatchTST with UnitNorm
class StabilizedPatchTSTConfig(PatchTSTConfig):
    """Enhanced configuration with stability parameters"""
    
    def __init__(self, **kwargs):
        # Set conservative defaults for stability
        stability_defaults = {
            'd_model': 64,              # Reduced from 128
            'num_attention_heads': 4,   # Reduced from 8  
            'num_hidden_layers': 2,     # Reduced from 3
            'dropout': 0.3,             # Increased from 0.1
            'head_dropout': 0.3,
            'attention_dropout': 0.2,
            'norm_type': 'unit_norm',   # Use UnitNorm instead of BatchNorm
            'activation': 'gelu',
            'norm_first': True
        }
        
        # Update with stability defaults
        for key, value in stability_defaults.items():
            kwargs.setdefault(key, value)
            
        super().__init__(**kwargs)
```

#### **Robust Data Preprocessing Pipeline**
```python
from sklearn.preprocessing import RobustScaler
from sklearn.ensemble import IsolationForest
import numpy as np

class ManufacturingDataProcessor:
    """Enhanced data processor with stability features"""
    
    def __init__(self, config):
        self.config = config
        self.scaler = RobustScaler()  # Robust to outliers
        self.outlier_detector = IsolationForest(
            contamination=config.get('outlier_contamination', 0.1),
            random_state=42
        )
        
    def preprocess_manufacturing_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Robust preprocessing for manufacturing time series
        
        Args:
            data: Raw manufacturing data
            
        Returns:
            Processed data with stability enhancements
        """
        # 1. Outlier detection and handling
        outlier_mask = self.detect_outliers(data)
        data_clean = self.handle_outliers(data, outlier_mask)
        
        # 2. Missing value imputation
        data_imputed = self.impute_missing_values(data_clean)
        
        # 3. Robust scaling
        data_scaled = self.apply_robust_scaling(data_imputed)
        
        # 4. Feature engineering for stability
        data_enhanced = self.add_stability_features(data_scaled)
        
        # 5. Temporal validation
        self.validate_temporal_consistency(data_enhanced)
        
        return {
            'processed_data': data_enhanced,
            'outlier_percentage': outlier_mask.mean() * 100,
            'missing_percentage': data.isnull().mean().mean() * 100,
            'stability_score': self.calculate_stability_score(data_enhanced)
        }
    
    def detect_outliers(self, data: pd.DataFrame) -> np.ndarray:
        """Detect outliers using Isolation Forest"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        if len(numeric_columns) == 0:
            return np.zeros(len(data), dtype=bool)
            
        # Fit outlier detector
        outlier_scores = self.outlier_detector.fit_predict(data[numeric_columns])
        
        return outlier_scores == -1  # -1 indicates outlier
    
    def handle_outliers(self, data: pd.DataFrame, outlier_mask: np.ndarray) -> pd.DataFrame:
        """Handle outliers using winsorization"""
        data_clean = data.copy()
        
        for column in data.select_dtypes(include=[np.number]).columns:
            # Winsorize outliers to 5th and 95th percentiles
            q05, q95 = data[column].quantile([0.05, 0.95])
            data_clean.loc[outlier_mask, column] = np.clip(
                data_clean.loc[outlier_mask, column], 
                q05, q95
            )
        
        return data_clean
    
    def add_stability_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add features that improve training stability"""
        data_enhanced = data.copy()
        
        # Add rolling statistics for stability
        for column in ['thickness_avg', 'speed']:
            if column in data.columns:
                # Rolling mean (smoothing)
                data_enhanced[f'{column}_rolling_mean'] = (
                    data[column].rolling(window=10, min_periods=1).mean()
                )
                
                # Rolling std (volatility indicator)
                data_enhanced[f'{column}_rolling_std'] = (
                    data[column].rolling(window=10, min_periods=1).std().fillna(0)
                )
                
                # Trend indicator
                data_enhanced[f'{column}_trend'] = (
                    data[column].diff().rolling(window=5, min_periods=1).mean().fillna(0)
                )
        
        return data_enhanced
```

#### **Enhanced Training Pipeline with Monitoring**
```python
import wandb
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt

class ManufacturingTrainingPipeline:
    """Production-ready training pipeline with comprehensive monitoring"""
    
    def __init__(self, config):
        self.config = config
        self.writer = SummaryWriter(log_dir=config.log_dir)
        self.training_metrics = []
        
    def train_stable_model(self, model, train_dataset, val_dataset):
        """Train model with stability enhancements"""
        
        # Enhanced training arguments
        training_args = TrainingArguments(
            output_dir=self.config.output_dir,
            num_train_epochs=self.config.max_epochs,
            per_device_train_batch_size=16,  # Smaller batch for stability
            per_device_eval_batch_size=32,
            gradient_accumulation_steps=2,   # Effective batch size 32
            warmup_steps=100,
            weight_decay=0.01,
            learning_rate=5e-5,              # Conservative learning rate
            logging_steps=10,
            eval_steps=50,
            evaluation_strategy="steps",
            save_strategy="steps",
            save_steps=100,
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            dataloader_num_workers=2,
            fp16=True,                       # Mixed precision
            fp16_opt_level="O1",
            dataloader_pin_memory=True,
            report_to=None,  # Disable automatic logging
            seed=42,
            remove_unused_columns=False
        )
        
        # Custom trainer with stability features
        trainer = StabilizedTrainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            gradient_clip_norm=1.0,
            callbacks=[
                EarlyStoppingCallback(
                    early_stopping_patience=15,
                    early_stopping_threshold=0.001
                ),
                self.create_monitoring_callback()
            ]
        )
        
        # Train with monitoring
        try:
            training_result = trainer.train()
            
            # Comprehensive evaluation
            eval_results = self.comprehensive_evaluation(trainer, val_dataset)
            
            # Save training artifacts
            self.save_training_artifacts(trainer, training_result, eval_results)
            
            return trainer, training_result, eval_results
            
        except Exception as e:
            self.log_training_failure(e)
            raise
    
    def comprehensive_evaluation(self, trainer, val_dataset):
        """Comprehensive model evaluation"""
        
        # Standard evaluation
        eval_results = trainer.evaluate()
        
        # Manufacturing-specific metrics
        predictions = trainer.predict(val_dataset)
        manufacturing_metrics = self.calculate_manufacturing_metrics(
            predictions.predictions, 
            predictions.label_ids
        )
        
        # Combine results
        eval_results.update(manufacturing_metrics)
        
        # Generate evaluation visualizations
        self.create_evaluation_plots(predictions, eval_results)
        
        return eval_results
    
    def calculate_manufacturing_metrics(self, predictions, labels):
        """Calculate manufacturing-specific evaluation metrics"""
        
        # Flatten predictions and labels
        pred_flat = predictions.flatten()
        label_flat = labels.flatten()
        
        # Remove any NaN or infinite values
        valid_mask = np.isfinite(pred_flat) & np.isfinite(label_flat)
        pred_valid = pred_flat[valid_mask]
        label_valid = label_flat[valid_mask]
        
        if len(pred_valid) == 0:
            return {"error": "No valid predictions for evaluation"}
        
        # Calculate metrics
        mse = np.mean((pred_valid - label_valid) ** 2)
        mae = np.mean(np.abs(pred_valid - label_valid))
        rmse = np.sqrt(mse)
        
        # MAPE (avoid division by zero)
        mape = np.mean(np.abs((label_valid - pred_valid) / (label_valid + 1e-8))) * 100
        
        # Directional accuracy
        pred_direction = np.diff(pred_valid) > 0
        label_direction = np.diff(label_valid) > 0
        directional_accuracy = np.mean(pred_direction == label_direction) * 100
        
        # Prediction variance (stability check)
        prediction_variance = np.var(pred_valid)
        label_variance = np.var(label_valid)
        variance_ratio = prediction_variance / (label_variance + 1e-8)
        
        return {
            "manufacturing_mse": float(mse),
            "manufacturing_mae": float(mae), 
            "manufacturing_rmse": float(rmse),
            "manufacturing_mape": float(mape),
            "directional_accuracy": float(directional_accuracy),
            "prediction_variance": float(prediction_variance),
            "variance_ratio": float(variance_ratio),
            "stability_score": float(1.0 / (1.0 + abs(variance_ratio - 1.0)))
        }
```

#### **Transfer Learning Implementation**
```python
class TransferLearningPipeline:
    """Transfer learning pipeline for manufacturing domain adaptation"""
    
    def __init__(self, config):
        self.config = config
        
    def load_pretrained_model(self, model_name: str = "ibm-granite/granite-timeseries-patchtst"):
        """Load pre-trained model for transfer learning"""
        try:
            model = PatchTSTForPrediction.from_pretrained(model_name)
            return model
        except Exception as e:
            print(f"Failed to load {model_name}, using fresh model: {e}")
            return None
    
    def linear_probing(self, model, train_dataset, val_dataset):
        """Fast adaptation using linear probing"""
        
        # Freeze backbone parameters
        for name, param in model.named_parameters():
            if 'prediction_head' not in name and 'head' not in name:
                param.requires_grad = False
        
        # Training with only head parameters
        training_args = TrainingArguments(
            output_dir=f"{self.config.output_dir}/linear_probe",
            num_train_epochs=10,        # Fewer epochs for linear probing
            learning_rate=0.001,        # Higher learning rate for head only
            per_device_train_batch_size=32,
            evaluation_strategy="epoch",
            save_strategy="epoch",
            load_best_model_at_end=True,
            logging_steps=10
        )
        
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset
        )
        
        return trainer.train()
    
    def full_fine_tuning(self, model, train_dataset, val_dataset):
        """Full model fine-tuning with manufacturing data"""
        
        # Unfreeze all parameters
        for param in model.parameters():
            param.requires_grad = True
        
        # Use stabilized training pipeline
        pipeline = ManufacturingTrainingPipeline(self.config)
        return pipeline.train_stable_model(model, train_dataset, val_dataset)
```

### Testing & Validation Improvements:

#### **Manufacturing-Specific Validation**
```python
class ManufacturingValidator:
    """Comprehensive validation for manufacturing forecasting models"""
    
    def __init__(self, config):
        self.config = config
        
    def validate_model_stability(self, model, test_dataset):
        """Validate model stability and robustness"""
        
        validation_results = {
            'stability_tests': {},
            'robustness_tests': {},
            'manufacturing_compliance': {}
        }
        
        # 1. Prediction consistency test
        validation_results['stability_tests']['consistency'] = (
            self.test_prediction_consistency(model, test_dataset)
        )
        
        # 2. Gradient stability test
        validation_results['stability_tests']['gradient_stability'] = (
            self.test_gradient_stability(model, test_dataset)
        )
        
        # 3. Noise robustness test
        validation_results['robustness_tests']['noise_tolerance'] = (
            self.test_noise_robustness(model, test_dataset)
        )
        
        # 4. Manufacturing range compliance
        validation_results['manufacturing_compliance']['range_compliance'] = (
            self.test_manufacturing_range_compliance(model, test_dataset)
        )
        
        return validation_results
    
    def test_prediction_consistency(self, model, dataset):
        """Test if model produces consistent predictions"""
        model.eval()
        
        predictions_1 = []
        predictions_2 = []
        
        with torch.no_grad():
            for batch in DataLoader(dataset, batch_size=1):
                # Run same input twice
                pred_1 = model(**batch).prediction_outputs.cpu().numpy()
                pred_2 = model(**batch).prediction_outputs.cpu().numpy()
                
                predictions_1.append(pred_1)
                predictions_2.append(pred_2)
        
        # Calculate consistency score
        predictions_1 = np.concatenate(predictions_1)
        predictions_2 = np.concatenate(predictions_2)
        
        consistency_score = np.mean(np.abs(predictions_1 - predictions_2))
        
        return {
            'consistency_score': float(consistency_score),
            'is_consistent': consistency_score < 1e-6,
            'max_deviation': float(np.max(np.abs(predictions_1 - predictions_2)))
        }
```

### Success Criteria:

#### **Training Stability Metrics**
- **Finite Validation Loss**: `eval_loss` must be finite and decreasing
- **Gradient Stability**: Gradient norms < 1.0 throughout training
- **Prediction Variance**: Model predictions show appropriate variance (std > 0.1)
- **Training Convergence**: Loss converges within 50 epochs with early stopping

#### **Manufacturing Performance Metrics**
- **Forecast Accuracy**: RMSE < 0.5, MAE < 0.3, MAPE < 5%
- **Directional Accuracy**: >70% correct trend prediction
- **Range Compliance**: 95% of predictions within manufacturing specifications
- **Uncertainty Quantification**: Confidence intervals cover 90% of actual values

#### **Production Readiness Criteria**
- **Model Loading**: Successful model save/load without errors
- **Inference Speed**: <100ms for single forecast on standard hardware
- **Memory Usage**: <2GB GPU memory for inference
- **Robustness**: Stable performance with ±10% input noise

### Implementation Workflow:

#### **Phase 1: Immediate Stability Fixes (Week 1)**
1. Implement gradient clipping in training loop
2. Fix validation dataset creation to resolve infinite loss
3. Add UnitNorm layers to replace BatchNorm
4. Reduce model complexity (fewer layers, smaller dimensions)

#### **Phase 2: Enhanced Training Pipeline (Week 2)** 
1. Implement learning rate scheduling with warmup
2. Add comprehensive training monitoring and logging
3. Integrate early stopping with multiple metrics
4. Add data quality validation and preprocessing

#### **Phase 3: Transfer Learning Integration (Week 3)**
1. Integrate pre-trained model loading from HuggingFace
2. Implement linear probing for fast adaptation
3. Add full fine-tuning with manufacturing domain expertise
4. Create zero-shot forecasting capabilities

#### **Phase 4: Production Deployment (Week 4)**
1. Comprehensive model validation and testing
2. Performance benchmarking against baseline methods
3. Documentation and deployment guidelines
4. Integration with existing Phase 2.1 infrastructure

### Expected Improvements:

#### **Training Stability**
- **Finite Evaluation Loss**: Resolves infinite loss with proper validation
- **Stable Convergence**: Consistent training without gradient explosions
- **Prediction Variation**: Forecasts show natural variation matching data distribution
- **Robust Performance**: Stable across different data conditions

#### **Forecasting Quality**
- **15% Accuracy Improvement**: Better than baseline ARIMA/Linear Regression
- **Manufacturing Insights**: Actionable predictions for process optimization
- **Uncertainty Quantification**: Reliable confidence intervals for decisions
- **Multi-horizon Capability**: Accurate forecasts from 15 minutes to 24 hours

#### **Production Readiness**
- **Agent Integration**: Seamless integration with correlation agent tools
- **Manufacturing Domain**: Specialized insights for fiber cement production
- **Scalability**: Handle enterprise-scale data (262K+ records)
- **Monitoring**: Comprehensive performance tracking and alerting

## DOCUMENTATION:

### Research Foundation:
- **PatchTST Paper 2211.14730v2**: "A Time Series is Worth 64 Words: Long-term Forecasting with Transformers"
- **UnitNorm Research**: Advanced normalization for time series transformers
- **Gradient Stabilization**: Best practices for transformer training stability
- **Manufacturing Time Series**: Domain-specific forecasting considerations

### Implementation References:
- **HuggingFace Transformers**: PatchTSTConfig and PatchTSTForPrediction
- **IBM Granite Models**: Pre-trained time series transformers for transfer learning
- **PyTorch Training**: Advanced training techniques and optimization
- **Manufacturing Integration**: Phase 2.1 correlation agent infrastructure

### Validation Framework:
- **Time Series Cross-Validation**: Proper temporal validation techniques
- **Manufacturing Metrics**: Domain-specific performance evaluation
- **Stability Testing**: Comprehensive robustness validation
- **Production Monitoring**: Real-time performance tracking

This comprehensive improvement plan addresses all critical training issues identified in the current PatchTST implementation, providing research-backed solutions for stable, accurate, and production-ready manufacturing forecasting.