#!/usr/bin/env python3
"""
Simple Model Usage Example

Shows how to load and inspect trained models without complex forecasting.
"""

import pandas as pd
from pathlib import Path
from src.forecasting.patchtst_model import ManufacturingPatchTSTModel
import json

def show_model_info(model_name: str):
    """Show information about a trained model"""
    
    print(f"🔮 Model Information: {model_name}")
    print("=" * 50)
    
    # Model path
    model_path = f"./models/patchtst_manufacturing_{model_name}"
    
    if not Path(model_path).exists():
        print(f"❌ Model not found at {model_path}")
        return
    
    # Check model files
    print("📁 Model Files:")
    for file in Path(model_path).iterdir():
        size_mb = file.stat().st_size / (1024 * 1024)
        print(f"  - {file.name}: {size_mb:.1f} MB")
    
    # Load model configuration
    config_file = Path(model_path) / "config.json"
    if config_file.exists():
        print("\n⚙️ Model Configuration:")
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print(f"  - Model Type: {config.get('model_type', 'PatchTST')}")
        print(f"  - Target Variable: {config.get('target_variable', model_name)}")
        print(f"  - Input Features: {config.get('num_input_channels', 'N/A')}")
        print(f"  - Context Length: {config.get('context_length', 'N/A')}")
        print(f"  - Prediction Length: {config.get('prediction_length', 'N/A')}")
    
    # Load training metadata
    metadata_file = Path(model_path) / "metadata.json"
    if metadata_file.exists():
        print("\n📊 Training Metadata:")
        with open(metadata_file, 'r') as f:
            metadata = json.load(f)
        
        print(f"  - Training Date: {metadata.get('training_date', 'N/A')}")
        training_records = metadata.get('training_records', 'N/A')
        if isinstance(training_records, (int, float)):
            print(f"  - Training Records: {training_records:,}")
        else:
            print(f"  - Training Records: {training_records}")
        print(f"  - Best Loss: {metadata.get('best_loss', 'N/A')}")
        print(f"  - Training Time: {metadata.get('training_time', 'N/A')}")
    
    # Try to load the actual model
    print("\n🤖 Model Loading Test:")
    try:
        model = ManufacturingPatchTSTModel.load_pretrained(model_path)
        print("  ✅ Model loaded successfully")
        
        # Show model architecture info
        if hasattr(model, 'model') and model.model:
            try:
                total_params = sum(p.numel() for p in model.model.parameters())
                trainable_params = sum(p.numel() for p in model.model.parameters() if p.requires_grad)
                print(f"  - Total Parameters: {total_params:,}")
                print(f"  - Trainable Parameters: {trainable_params:,}")
            except:
                pass
        
        # Show input feature information
        if hasattr(model, 'forecast_config'):
            input_vars = model.forecast_config.input_variables
            print(f"  - Expected Input Features: {len(input_vars)}")
            print(f"  - First 5 features: {input_vars[:5]}")
            if len(input_vars) > 5:
                print(f"  - ... and {len(input_vars) - 5} more")
        
    except Exception as e:
        print(f"  ❌ Failed to load model: {e}")

def list_all_models():
    """List all available trained models with basic info"""
    
    print("🔮 Available Trained Models")
    print("=" * 50)
    
    models_dir = Path("./models")
    if not models_dir.exists():
        print("❌ No models directory found")
        return
    
    model_dirs = list(models_dir.glob("patchtst_manufacturing_*"))
    
    if not model_dirs:
        print("❌ No trained models found")
        return
    
    print(f"Found {len(model_dirs)} trained models:\n")
    
    for model_dir in sorted(model_dirs):
        target = model_dir.name.replace("patchtst_manufacturing_", "")
        
        # Get model size
        total_size = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        
        # Check if config exists
        has_config = (model_dir / "config.json").exists()
        has_metadata = (model_dir / "metadata.json").exists()
        
        status_icons = []
        if has_config:
            status_icons.append("⚙️")
        if has_metadata:
            status_icons.append("📊")
        
        print(f"  📦 {target}")
        print(f"     Size: {size_mb:.1f} MB")
        print(f"     Status: {' '.join(status_icons) if status_icons else '❓'}")
        print()

def show_usage_examples():
    """Show examples of how to use the models"""
    
    print("🎯 Usage Examples")
    print("=" * 50)
    
    print("1. Show model information:")
    print("   python simple_model_usage.py info fm_reject_pct")
    print()
    
    print("2. List all models:")
    print("   python simple_model_usage.py list")
    print()
    
    print("3. Python code example:")
    print("   ```python")
    print("   from src.forecasting.patchtst_model import ManufacturingPatchTSTModel")
    print("   ")
    print("   # Load trained model")
    print("   model = ManufacturingPatchTSTModel.load_pretrained(")
    print("       './models/patchtst_manufacturing_fm_reject_pct'")
    print("   )")
    print("   ")
    print("   # Model is ready for forecasting")
    print("   # (requires properly formatted data)")
    print("   ```")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        show_usage_examples()
    elif sys.argv[1] == "list":
        list_all_models()
    elif sys.argv[1] == "info" and len(sys.argv) > 2:
        show_model_info(sys.argv[2])
    elif sys.argv[1] == "help":
        show_usage_examples()
    else:
        print("Usage: python simple_model_usage.py [list|info <model_name>|help]")
        print("Available commands:")
        print("  list      - List all trained models")
        print("  info      - Show detailed model information")
        print("  help      - Show usage examples")