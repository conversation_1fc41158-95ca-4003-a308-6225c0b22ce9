#!/usr/bin/env python3
"""
Fix PatchTST output head to predict only 1 target variable
"""

import torch
import torch.nn as nn
from transformers import PatchTSTForPrediction, PatchTSTConfig
import logging

logger = logging.getLogger(__name__)

class FixedPatchTSTForPrediction(PatchTSTForPrediction):
    """
    Fixed PatchTST model that correctly outputs only 1 target variable
    """
    
    def __init__(self, config):
        super().__init__(config)
        
        # Replace the original head with a custom one that outputs 1 target
        if hasattr(self, 'head'):
            # Get input dimension of the original head
            original_head_input_dim = self.head.in_features if hasattr(self.head, 'in_features') else config.d_model
            
            # Create new head that outputs only 1 target
            self.head = nn.Linear(original_head_input_dim, config.prediction_length)
            logger.info(f"Replaced head: {original_head_input_dim} -> {config.prediction_length} (single target)")
        
        elif hasattr(self, 'prediction_head'):
            # Some versions use prediction_head
            original_input_dim = self.prediction_head.in_features if hasattr(self.prediction_head, 'in_features') else config.d_model
            self.prediction_head = nn.Linear(original_input_dim, config.prediction_length)
            logger.info(f"Replaced prediction_head: {original_input_dim} -> {config.prediction_length} (single target)")
        
        else:
            logger.warning("Could not find prediction head to replace")
    
    def forward(self, past_values, attention_mask=None, head_mask=None, output_attentions=None, output_hidden_states=None, return_dict=None):
        """
        Forward pass with corrected output shape
        """
        # Call parent forward method
        outputs = super().forward(
            past_values=past_values,
            attention_mask=attention_mask,
            head_mask=head_mask,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict
        )
        
        # Ensure prediction outputs have correct shape [batch, prediction_length, 1]
        if hasattr(outputs, 'prediction_outputs'):
            pred_outputs = outputs.prediction_outputs
            
            # If shape is [batch, prediction_length, num_input_channels], take only first channel
            if len(pred_outputs.shape) == 3 and pred_outputs.shape[-1] > 1:
                logger.debug(f"Reshaping prediction outputs from {pred_outputs.shape} to single target")
                # Take only the first channel and add dimension for single target
                pred_outputs = pred_outputs[:, :, 0:1]  # [batch, prediction_length, 1]
                outputs.prediction_outputs = pred_outputs
        
        return outputs

def test_fixed_model():
    """Test the fixed model"""
    logging.basicConfig(level=logging.INFO)
    
    logger.info("🔧 Testing Fixed PatchTST Model...")
    
    # Create config
    config = PatchTSTConfig(
        num_input_channels=6,
        context_length=240,
        prediction_length=240,
        patch_length=16,
        patch_stride=16,
        d_model=64,
        num_attention_heads=4,
        num_hidden_layers=2,
        dropout=0.1,
        num_targets=1,
        loss="mse"
    )
    
    # Create fixed model
    model = FixedPatchTSTForPrediction(config)
    model.eval()
    
    # Test forward pass
    batch_size = 2
    past_values = torch.randn(batch_size, 240, 6)
    
    logger.info(f"Input shape: {past_values.shape}")
    
    with torch.no_grad():
        outputs = model(past_values=past_values)
    
    # Check output shape
    if hasattr(outputs, 'prediction_outputs'):
        pred_shape = outputs.prediction_outputs.shape
        logger.info(f"Fixed prediction output shape: {pred_shape}")
        
        expected_shape = (batch_size, 240, 1)
        if pred_shape == expected_shape:
            logger.info("✅ Fixed model output shape is correct!")
            return True
        else:
            logger.error(f"❌ Fixed model still has wrong shape: got {pred_shape}, expected {expected_shape}")
            return False
    else:
        logger.error("❌ No prediction_outputs found")
        return False

if __name__ == "__main__":
    success = test_fixed_model()
    if success:
        print("✅ Fix is working correctly!")
    else:
        print("❌ Fix needs more work")