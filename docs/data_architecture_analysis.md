# Fiber Cement Manufacturing Data Architecture Analysis

## Data Collection Framework Overview

Based on the SAP-SCADA integration diagram, the manufacturing process generates multi-frequency data streams that flow from SCADA systems through SAP-MPS to BW (Business Warehouse). This creates a complex temporal data landscape requiring sophisticated correlation analysis approaches.

## Data Source Mapping by Manufacturing Stage

### 1. Raw Material Processing & Mixing
**Data Sources:**
- **Mixing Machine Batch Data** (ZTI_BATCH)
  - Frequency: 15 minutes
  - Metrics: Batch delivery, batch percentage
  - Type: Continuous process monitoring

**Key Correlations to Investigate:**
- Batch composition variations → downstream quality issues
- Mixing consistency → scrap rate correlation
- Raw material ratios → thickness variations

### 2. Sheet Formation Process
**Data Sources:**
- **Sheet Machine Stoppages** (ZTI_STOP)
  - Frequency: 15 minutes
  - Metrics: Machine stop/start events, user interactions
  - Type: Event-based

- **Sheet Machine Stacks** (ZTI_SM_STK)
  - Frequency: 15 minutes
  - Metrics: Stack begin/end, barcode tracking, scrap reasons, off-roller changes
  - Type: Mixed (continuous + event-based)

- **Felt Speed** (ZTI_FLT_SPD)
  - Frequency: 15 minutes
  - Metrics: Felt belt speed measurements
  - Type: Continuous

- **Sheet Thickness** (ZTI_SHT_THK)
  - Frequency: 15 minutes
  - Metrics: Thickness measurements
  - Type: Continuous

**Critical Correlations:**
- Felt speed variations → thickness inconsistencies
- Stoppage events → scrap rate spikes
- Stack management efficiency → overall throughput

### 3. Curing Process (Autoclaving)
**Data Sources:**
- **Autoclave Cycle** (AV Cycle)
  - Frequency: 5 minutes (high-resolution) + 15 minutes (summary)
  - Metrics: Cycle timing and parameters
  - Type: Continuous process control

- **Autoclave Pressure** (AC Pressure)
  - Frequency: 15 minutes
  - Metrics: Pressure readings during curing
  - Type: Continuous

**Key Correlations:**
- Pressure variations → final product strength
- Cycle timing → quality consistency
- Temperature-pressure profiles → defect rates

### 4. Finishing Operations
**Data Sources:**
- **Finishing Machine Stoppages** (ZTI_STOP)
  - Frequency: 15 minutes
  - Metrics: Machine stop/start events
  - Type: Event-based

- **Finishing Machine Stacks** (ZTI_FS_PIP)
  - Frequency: 15 minutes
  - Metrics: Onload/offload operations, count changes
  - Type: Event-based

- **Dust Collector Performance**
  - Frequency: 15 minutes
  - Type: Continuous monitoring

- **Run Rate**
  - Frequency: 15 minutes
  - Type: Continuous throughput measurement

- **Mocseal Operations**
  - Frequency: 15 minutes
  - Type: Process monitoring

- **FIN & Cal Sander**
  - Frequency: 10 minutes
  - Type: Surface finishing monitoring

## Data Frequency Classification

### High-Frequency Data (≤10 minutes)
- Autoclave cycles (5 minutes)
- FIN & Cal Sander (10 minutes)
- Production orders (5 minutes)
- PO BOM (5 minutes)

### Medium-Frequency Data (15 minutes)
- Most SCADA measurements
- Sheet thickness, felt speed
- Stoppage events
- Stack operations

### Low-Frequency Data (Hourly/Daily)
- Master data updates
- Design capacity
- PIP data
- Stack master data

## Data Integration Challenges

### Temporal Alignment Issues
1. **Mixed Frequency Synchronization**: Correlating 5-minute autoclave data with 15-minute sheet formation data
2. **Event-Based vs Continuous**: Aligning stoppage events with continuous process measurements
3. **Lag Effects**: Manufacturing delays between process stages (e.g., sheet formation → autoclaving → finishing)

### Data Quality Considerations
1. **Missing Data**: Equipment downtime affecting data collection
2. **Sensor Drift**: Calibration issues affecting measurement accuracy
3. **Process Variations**: Shift changes, maintenance schedules affecting baseline measurements