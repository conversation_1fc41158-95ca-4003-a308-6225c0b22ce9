# Multi-Method Correlation Analysis Guide

## Overview

This guide provides comprehensive documentation for using the multi-method correlation analysis system in fiber cement manufacturing. The system supports **<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlation methods** with intelligent method selection, convergence analysis, and enhanced visualization capabilities.

## 🔬 Correlation Methods Explained

### Pearson Correlation
- **Type**: Parametric correlation
- **Measures**: Linear relationships between continuous variables
- **Range**: -1 to +1
- **Best for**: Normally distributed data, linear relationships
- **Sensitive to**: Outliers, non-linear relationships
- **Use when**: Data is continuous, normally distributed, and you expect linear relationships

### Spearman Correlation  
- **Type**: Non-parametric correlation (rank-based)
- **Measures**: Monotonic relationships between variables
- **Range**: -1 to +1
- **Best for**: Ordinal data, non-normal distributions, monotonic relationships
- **Robust to**: Outliers, non-linear but monotonic relationships
- **Use when**: Data has outliers, or when ranks matter more than exact values

### Kendall Correlation
- **Type**: Non-parametric correlation (rank-based)
- **Measures**: Ordinal associations and concordance
- **Range**: -1 to +1  
- **Best for**: Small sample sizes, highly robust analysis, ordinal data
- **Most robust to**: Outliers, non-normal distributions, non-linear relationships
- **Use when**: Maximum robustness needed, working with ordinal data

## 🏭 Manufacturing Context

### When to Use Each Method

**Production Speed vs Quality Metrics:**
- **Linear relationship expected** → **Pearson** (if normally distributed)
- **Speed affects quality but with outliers** → **Spearman** (robust to outliers)
- **Ordinal quality rankings** → **Kendall** (handles ordinal data best)

**Temperature vs Material Properties:**
- **Continuous measurements, linear physics** → **Pearson**
- **Non-linear thermal effects** → **Spearman** (captures monotonic patterns)
- **Threshold-based responses** → **Kendall** (robust to non-linear patterns)

**Equipment Performance Rankings:**
- **Machine efficiency scores** → **Spearman** or **Kendall**
- **Sensor calibration issues (outliers)** → **Spearman** or **Kendall**
- **Maintenance schedule correlations** → **Kendall** (robust analysis)

## 📊 Using the Multi-Method Analysis System

### Basic Multi-Method Analysis

```python
from src.data.multi_correlations import MultiMethodCorrelationAnalyzer

# Initialize analyzer
analyzer = MultiMethodCorrelationAnalyzer(significance_level=0.05)

# Calculate multi-method correlations
results = analyzer.calculate_multi_method_correlations(
    df, 
    variables=['speed', 'thickness_avg', 'temperature'],
    min_periods=30
)

# Each result contains all three methods
for pair_key, result in results.items():
    print(f"{result.variable_1} ↔ {result.variable_2}:")
    print(f"  Pearson:  r={result.pearson_correlation:.6f}, p={result.pearson_p_value:.6f}")
    print(f"  Spearman: r={result.spearman_correlation:.6f}, p={result.spearman_p_value:.6f}")
    print(f"  Kendall:  r={result.kendall_correlation:.6f}, p={result.kendall_p_value:.6f}")
    print(f"  Convergence: {result.method_convergence_score:.6f}")
    print(f"  Recommended: {result.recommended_method}")
```

### Method Convergence Analysis

```python
# Analyze how well the three methods agree
convergence_analysis = analyzer.analyze_method_convergence(results)

print(f"Overall convergence: {convergence_analysis['overall_convergence_score']:.6f}")
print(f"High convergence pairs: {convergence_analysis['convergence_distribution']['high_convergence_pairs']}")

# Cross-method correlations
cross_corr = convergence_analysis['cross_method_correlations']
print(f"Pearson-Spearman agreement: {cross_corr['pearson_spearman']:.6f}")
print(f"Pearson-Kendall agreement: {cross_corr['pearson_kendall']:.6f}")
print(f"Spearman-Kendall agreement: {cross_corr['spearman_kendall']:.6f}")
```

### Robustness Analysis

```python
# Bootstrap stability analysis
robustness_metrics = analyzer.calculate_robustness_metrics(
    df, 
    results, 
    bootstrap_samples=100
)

# Check stability for each method
for pair_key, pair_metrics in robustness_metrics.items():
    print(f"\nRobustness for {pair_key}:")
    for method in ['pearson', 'spearman', 'kendall']:
        if method in pair_metrics:
            stability = pair_metrics[method]['stability_score']
            print(f"  {method.title()}: {stability:.6f} stability")
```

## 🎯 Intelligent Method Selection

The system automatically assesses data characteristics to recommend the optimal correlation method:

### Data Distribution Assessment

```python
# The system automatically performs these assessments:
assessment = analyzer._assess_data_distribution(x_variable, y_variable)

# Results include:
# - Normality tests (Jarque-Bera)
# - Outlier detection (IQR method) 
# - Linearity assessment (R²)
# - Monotonicity testing
```

### Recommendation Logic

```
IF significant_outliers OR non_normal_distribution:
    IF monotonic_relationship:
        RECOMMEND: Spearman (robust + handles monotonic)
    ELSE:
        RECOMMEND: Kendall (most robust to outliers)
        
ELIF normal_distribution AND linear_relationship:
    RECOMMEND: Pearson (optimal for linear normal data)
    
ELIF monotonic_relationship:
    RECOMMEND: Spearman (captures monotonic patterns)
    
ELSE:
    RECOMMEND: Kendall (general association measure)
```

## 📈 Visualization

### Side-by-Side Heatmaps

```python
from src.visualization.multi_plots import MultiMethodCorrelationPlotter

plotter = MultiMethodCorrelationPlotter()

# Create side-by-side comparison heatmaps
fig = plotter.create_multi_method_heatmaps(
    correlation_results,
    title="Multi-Method Correlation Analysis",
    show_convergence=True
)
```

### Method Convergence Plots

```python
# Interactive convergence analysis
fig = plotter.plot_method_convergence(correlation_results)

# Comprehensive dashboard
dashboard = plotter.create_multi_method_dashboard(correlation_results)
```

### Method Comparison Matrix

```python
# Compare specific methods
comparison_fig = plotter.plot_method_comparison_matrix(
    correlation_results,
    method_pair=('pearson', 'spearman'),
    title="Pearson vs Spearman Comparison"
)
```

## 🧪 Agent Integration

### Natural Language Queries

```python
# Example queries the AI agent understands:
queries = [
    "Compare Pearson, Spearman, and Kendall correlations for all variables",
    "Which correlation method should I use for speed vs thickness analysis?", 
    "Show me method convergence analysis for this dataset",
    "Calculate robustness metrics using bootstrap sampling",
    "Recommend optimal correlation methods based on data characteristics"
]
```

### Agent Tools

The system provides specialized agent tools:

1. **`calculate_multi_method_correlations_tool`** - Calculate all three methods
2. **`analyze_method_convergence_tool`** - Assess method agreement
3. **`recommend_correlation_method_tool`** - Get method recommendations
4. **`calculate_robustness_metrics_tool`** - Bootstrap stability analysis

## 📊 Interpretation Guidelines

### Convergence Scores

- **High Convergence (>0.8)**: All methods agree - robust relationship regardless of analysis approach
- **Medium Convergence (0.5-0.8)**: Some method differences - data characteristics matter for interpretation  
- **Low Convergence (<0.5)**: Significant method disagreement - careful method selection critical

### Manufacturing Implications

**High Convergence Example:**
```
Speed ↔ Thickness:
  Pearson: 0.301, Spearman: 0.304, Kendall: 0.298
  Convergence: 0.998
  → Robust relationship regardless of method choice
```

**Low Convergence Example:**
```
Temperature ↔ Defects:
  Pearson: 0.156, Spearman: 0.423, Kendall: 0.387  
  Convergence: 0.234
  → Non-linear relationship, use Spearman or Kendall
```

### Robustness Interpretation

- **Stability Score >0.8**: Highly robust - reliable for process control
- **Stability Score 0.5-0.8**: Moderately stable - good for trend analysis
- **Stability Score <0.5**: Low stability - use caution in decision making

## 🎯 Best Practices

### 1. Always Start with Multi-Method Analysis
```python
# Don't assume one method - compare all three
results = analyzer.calculate_multi_method_correlations(data)
```

### 2. Check Method Convergence
```python
# Assess how well methods agree
convergence = analyzer.analyze_method_convergence(results)
if convergence['overall_convergence_score'] > 0.8:
    print("High method agreement - robust relationships")
```

### 3. Use Intelligent Recommendations
```python
# Let the system recommend optimal methods
for result in results.values():
    print(f"Recommended method: {result.recommended_method}")
    print(f"Reasoning: {result.data_distribution_assessment}")
```

### 4. Consider Manufacturing Context
- **Process Control**: Prefer high-convergence correlations
- **Quality Prediction**: Spearman often best for manufacturing data
- **Root Cause Analysis**: Kendall most robust for outlier-prone data
- **Equipment Optimization**: Use method recommendations based on sensor characteristics

### 5. Validate with Bootstrap Analysis
```python
# For critical decisions, check robustness
robustness = analyzer.calculate_robustness_metrics(data, results)
```

## 🔧 Troubleshooting

### Common Issues

**Low Convergence Scores:**
- Check for outliers in your data
- Consider non-linear relationships
- Review data quality and sensor calibration

**Inconsistent Method Recommendations:**
- Examine data distribution characteristics
- Check for missing values or data gaps
- Consider time-based patterns in manufacturing data

**Poor Stability Scores:**
- Increase bootstrap sample size
- Check for seasonal patterns
- Consider data preprocessing and cleaning

### Performance Optimization

**Large Datasets:**
- Use `min_periods` to filter insufficient data
- Consider sampling for initial analysis
- Kendall correlation is O(n²) - may be slow for very large datasets

**Memory Management:**
- Process data in chunks for very large datasets
- Cache results for repeated analysis
- Use appropriate precision settings (8-decimal internal, 6-decimal display)

## 📚 Additional Resources

- **API Reference**: See `docs/api_reference_multi_method.md`
- **Method Selection Guide**: See `docs/method_selection_guide.md`
- **Example Scripts**: See `examples/` directory
- **Test Cases**: See `tests/test_multi_*` for comprehensive examples

## 🏭 Manufacturing-Specific Considerations

### Fiber Cement Production

**Typical Variable Relationships:**
- **Speed vs Thickness**: Often linear → Pearson (if no outliers)
- **Temperature vs Curing**: May be non-linear → Spearman
- **Equipment Rankings**: Ordinal → Kendall
- **Stoppage vs Quality**: Often has outliers → Spearman/Kendall

**Sensor Data Characteristics:**
- **Thickness sensors (10-sensor array)**: May have calibration outliers → Use robust methods
- **Speed sensors**: Usually continuous and linear → Pearson appropriate
- **Temperature sensors**: May have non-linear effects → Check method recommendations

**Time-Based Considerations:**
- **Shift patterns**: May affect correlation stability
- **Maintenance cycles**: Can introduce periodic outliers
- **Production ramp-up**: May show non-linear patterns initially

This guide provides the foundation for effective multi-method correlation analysis in manufacturing environments. Always consider both statistical properties and manufacturing domain knowledge when interpreting results.