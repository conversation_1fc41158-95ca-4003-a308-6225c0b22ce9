### **Focused Sub-Plan: Correlating Stoppages, Speed, and Thickness with Scrap Rates**

**Objective:** To precisely model and validate how machine stoppages, speed variations, and thickness deviations directly influence scrap rates, moving from raw data to a predictive and diagnostic framework in a condensed timeframe.

---

#### **Phase 1: Foundational Analysis - Event-to-Continuous Correlation (Weeks 1-3)**

**Goal:** To establish the direct, quantifiable impact of stoppage events on the continuous process variables (speed, thickness) and the resulting scrap rate. This phase is about creating a rich, unified feature set that tells the story of an event.

**Tasks & Implementation:**

1.  **Unified Time-Series Construction:**
    *   Align all four data sources onto a common **minute-by-minute timeline**. This is the single most critical step.
    *   **Thickness & Speed Reports (Continuous):** These are straightforward; use the minute-by-minute readings.
    *   **Stoppage Data (Event):** For each minute on the timeline, create corresponding features:
        *   `is_stopped`: A binary (1/0) flag indicating if a stoppage is active during that minute.
        *   `minutes_since_last_stop`: A counter that resets to 0 when a stoppage begins.
        *   `stoppage_duration`: The total length of the current stoppage event.
    *   **Scrap Report (Event):** For each minute, create a `scrap_event_count` feature, tallying the scrap events that occurred in that interval.

2.  **Event-Driven Feature Engineering (The Core of this Phase):**
    *   The goal is to analyze the system's behavior *around* critical events.
    *   For every **stoppage event**, create an analytical window (e.g., 15 minutes before to 60 minutes after).
    *   Calculate key metrics within this window:
        *   **Pre-Stoppage Stability:** Average and standard deviation of `thickness` and `speed` in the 15 minutes *before* the stop.
        *   **Post-Stoppage Impact:** Average and standard deviation of `thickness` and `speed` in the 60 minutes *after* the machine restarts.
        *   **Recovery Time:** How many minutes did it take for `thickness` and `speed` to return to their pre-stoppage stability levels?
        *   **Scrap Correlation:** What was the total `scrap_event_count` during and immediately following the stoppage?

3.  **Baseline Correlation and Visualization:**
    *   Generate a correlation matrix on the unified dataset from step 1.
    *   **Crucial Visualization:** Create an "event-triggered average" plot. This plot will show the average trend of `thickness` and `speed` in the minutes surrounding all stoppage events. This single visual will powerfully demonstrate the impact of a stoppage.

**Testing & Validation:**

*   **Test:** Does the "event-triggered average" plot show a clear dip in speed to zero, followed by a period of thickness instability upon restart?
*   **Validate:** Present this plot to process engineers. Their feedback ("Yes, that's exactly what happens after a restart, the first 20 minutes are always unstable") provides immediate validation of the data model.
*   **Success Criteria:** A unified, feature-rich dataset is created. The relationship between stoppages and process instability is visually and statistically validated.

---

#### **Phase 2: Predictive Modeling with Transformers (Weeks 4-7)**

**Goal:** To build a highly accurate model that predicts the likelihood of a scrap event occurring based on the recent history of stoppages, speed, and thickness.

**Tasks & Implementation:**

1.  **Problem Formulation for Scrap Prediction:**
    *   **Target Variable:** `scrap_in_next_15_mins` (a binary flag).
    *   **Predictor Features:** Use a 60-minute lookback window of the features created in Phase 1: `thickness`, `speed`, `is_stopped`, and `minutes_since_last_stop`.

2.  **Model Implementation (TFT):**
    *   Implement the **Temporal Fusion Transformer (TFT)**. It is perfectly suited for this task because it can natively handle the different types of input data: continuous sensor readings (`thickness`, `speed`) and event-based features (`is_stopped`).
    *   Train the model to predict the binary target variable.

3.  **Model Training and Evaluation:**
    *   Establish a baseline using a simpler model like Logistic Regression or XGBoost on the same dataset.
    *   Train the TFT and compare its performance (using metrics like F1-score and Precision-Recall AUC) against the baseline. A >20% improvement is expected due to the TFT's ability to capture complex temporal patterns.

**Testing & Validation:**

*   **Test:** Evaluate the trained TFT on a hold-out test set of data it has never seen.
*   **Validate:** The real value is in the TFT's interpretability.
    *   Use its **Variable Importance** scores to definitively rank the predictors. Is a sudden change in speed more predictive of scrap than a gradual thickness drift? The model will answer this.
    *   Use its **Attention Maps** to visualize *when* the model focuses. This can reveal critical insights, such as "the model pays most attention to thickness stability 5-10 minutes after a stoppage ends."

*   **Success Criteria:** A predictive model for scrap is created with quantifiable accuracy. The model provides a ranked list of process drivers, giving the team a clear focus for optimization efforts.

---

#### **Phase 3: Causal Inference and the Diagnostic "Scrap RCA" Agent (Weeks 8-10)**

**Goal:** To go beyond prediction and create a tool that can perform a root cause analysis (RCA) for why a specific scrap event occurred.

**Tasks & Implementation:**

1.  **Focused Causal Discovery:**
    *   Use the top 5-7 most important features identified by the TFT in Phase 2 as inputs to a causal discovery algorithm (e.g., PCMCI).
    *   The expected output is a simple, powerful causal graph, e.g.: `Stoppage Event → Speed Instability → Thickness Deviation → Scrap Event`.

2.  **Develop the "Scrap RCA Agent":**
    *   This is an LLM-powered agent specifically trained for this one task.
    *   **Prompt Engineering:** When a scrap event occurs, the agent is triggered with a carefully crafted prompt.

    *   ***Sample Prompt Template:***
        > "You are a senior process engineer analyzing a fiber cement production line.
        >
        > **Context:** A scrap event was recorded at [Timestamp].
        >
        > **Provided Data:**
        > - **Causal Graph:** Stoppage -> Speed Instability -> Thickness Deviation -> Scrap.
        > - **Time-Series Data (15 mins prior):**
        >   - Thickness: [List of thickness values]
        >   - Speed: [List of speed values]
        >   - Stoppage Status: [List of stoppage flags]
        >
        > **Task:** Based *only* on the provided data and causal graph, write a concise root cause analysis report. Explain the most likely sequence of events that led to the scrap. Pinpoint the likely initial trigger."

**Testing & Validation:**

*   **Test:** "Replay" 5-10 historical scrap events, feeding the data into the RCA Agent.
*   **Validate:** Provide the 5-10 generated RCA reports to a senior engineer. Have them score the accuracy and usefulness of each report on a scale of 1 to 5.
*   **Success Criteria:** The RCA Agent achieves an average expert score of 4/5 or higher, demonstrating its ability to provide fast, accurate, and data-driven diagnostics that can help operators make better decisions.