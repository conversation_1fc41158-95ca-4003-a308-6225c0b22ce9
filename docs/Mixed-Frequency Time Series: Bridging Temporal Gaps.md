# Mixed-Frequency Time Series: Bridging Temporal Gaps

Mixed-frequency time series analysis represents a fundamental advancement in manufacturing analytics, addressing the critical challenge of reconciling continuous sensor measurements with discrete event data. This comprehensive examination explores the mathematical foundations, methodological frameworks, and practical applications of advanced techniques that bridge temporal gaps in industrial data processing.

## Overview

The manufacturing sector generates vast quantities of data at varying temporal frequencies, creating significant analytical challenges. High-frequency sensor measurements capturing humidity, drum speed, and thickness occur alongside discrete events such as scrap rates and production stoppages[1][2]. Traditional approaches require aggregating high-frequency data to match lower-frequency observations, resulting in substantial information loss and reduced analytical precision[3][4].

This temporal mismatch problem has driven the development of sophisticated statistical frameworks capable of preserving information across frequency domains while maintaining computational efficiency[5][6]. The emergence of Mixed-Data Sampling (MIDAS) regression and Dynamic Factor Models (DFMs) has revolutionized how manufacturers approach multi-frequency data integration[7][8].

## Mixed-Data Sampling (MIDAS) Regression Framework

### Mathematical Foundations

MIDAS regression enables direct modeling of relationships between low-frequency quality indicators and high-frequency process variables without requiring temporal aggregation[9][10]. The fundamental MIDAS model specification takes the form:

$$ y_t = \beta_0 + \beta_1 B(L^{1/m}; \theta) x_t^{(m)} + \varepsilon_t^{(m)} $$

where $$y_t$$ represents the low-frequency dependent variable, $$x_t^{(m)}$$ denotes the high-frequency regressor sampled $$m$$ times more frequently, and $$B(L^{1/m}; \theta)$$ constitutes the distributed lag polynomial[9]. This formulation preserves high-frequency information while avoiding parameter proliferation through the use of restricted weighting schemes[1][11].

The distributed lag polynomials serve as the mathematical core of MIDAS methodology, enabling flexible specification of temporal relationships[12]. Common functional forms include the Beta polynomial and Almon lag structures, which provide parsimonious parameterizations of potentially complex lag distributions[9][12]. These polynomials ensure that the model captures both immediate and delayed effects of high-frequency variables on low-frequency outcomes[1].

### Performance and Extensions

Empirical applications demonstrate that MIDAS regression achieves 10-15% improvement over traditional aggregation methods in manufacturing contexts[4][13]. This performance enhancement stems from the framework's ability to incorporate individual high-frequency observations directly into the regression, solving problems of information loss and model misspecification[10]. The method excels particularly when differences in sampling frequencies are moderate, making it well-suited for many manufacturing applications[12][14].

Recent extensions have expanded MIDAS capabilities significantly. MIDAS-SVR (Support Vector Regression) addresses nonlinear pattern recognition in manufacturing processes[15]. Factor-MIDAS models handle high-dimensional data by first extracting factors through principal component analysis before applying MIDAS weighting schemes[16][17]. Bayesian approaches, including the Bayesian Nested Lasso (BNL), provide principled parameter selection while incorporating uncertainty quantification[4].

### Specialized MIDAS Variants

The Unrestricted MIDAS (U-MIDAS) approach offers computational advantages when frequency differences are small[12][14]. U-MIDAS eliminates the need for functional distributed lag specifications, allowing ordinary least squares estimation while maintaining forecasting accuracy[8][13]. This variant proves particularly effective in manufacturing applications where monthly and weekly data frequencies are common[12].

Advanced hybrid models combine MIDAS with other methodologies. The ARDL-MIDAS-Transformer framework integrates autoregressive distributed lag models with deep learning attention mechanisms[5]. This approach maintains interpretability while exploiting neural network capabilities for higher-order interaction analysis[5].

## Dynamic Factor Models (DFMs) for Manufacturing Analytics

### State-Space Representation

Dynamic Factor Models provide an alternative framework for mixed-frequency analysis through state-space representations[18][19]. DFMs extract common latent factors from mixed-frequency data, naturally handling arbitrary patterns of missing data through Kalman filtering and smoothing[20][21]. The state-space formulation enables efficient handling of high-dimensional datasets while preserving temporal structure[22].

The mathematical foundation of DFMs rests on the assumption that observed variables can be decomposed into common factors and idiosyncratic components[21]. This decomposition proves particularly valuable in manufacturing contexts where multiple sensors may respond to common underlying process variations[23]. The factor structure reduces dimensionality while preserving essential information content[18].

### Missing Data Handling

Manufacturing environments frequently experience data gaps due to sensor failures, maintenance windows, or environmental conditions[24]. DFMs excel in these scenarios through Kalman filtering techniques that provide optimal estimates for missing observations[25][26]. The precision-based sampling approach enables efficient handling of complex missing data patterns without compromising statistical inference[25].

Recent developments in mixed-frequency DFMs incorporate uncertainty quantification directly into the estimation process[22][27]. Bayesian approaches provide credible intervals for factor estimates, enabling more robust decision-making in manufacturing applications[27]. These methods prove particularly valuable when dealing with sparse or irregularly sampled data[25].

### Advanced DFM Variants

Markov-Switching DFMs capture regime changes common in manufacturing processes[28][29]. These models identify distinct operational states such as startup, steady-state production, and shutdown phases[30]. The regime-switching capability enables more accurate modeling of time-varying relationships between process variables[29].

Mixed-frequency portfolio allocation models demonstrate DFM applications beyond traditional time series forecasting[31]. These approaches combine high-frequency and low-frequency variations to improve covariance matrix estimation, with direct applications to manufacturing resource allocation and scheduling[31].

## Temporal Alignment Techniques

### Model-Based Approaches

Accurate temporal alignment proves critical for effective mixed-frequency analysis in manufacturing[32][33]. Traditional timestamp-based methods often fail in high-resolution applications where sub-millisecond phenomena require precise synchronization[34]. Advanced model-based approaches using Unscented Transformation propagate uncertainty through coordinate transformations[35][36].

The Unscented Transform reduces computational requirements compared to Monte Carlo methods while maintaining accuracy for nonlinear uncertainty propagation[35][36]. This technique proves particularly valuable in manufacturing applications where measurement uncertainties must be properly propagated through complex transformation processes[36].

### Process Signature-Driven Alignment

HiRA-Pro represents a novel approach to high spatio-temporal resolution alignment in manufacturing contexts[34]. This method discerns and synchronizes process signatures of salient kinematic and dynamic events across disparate sensor channels[34]. Testing in additive manufacturing environments demonstrates 35% improvement in classification accuracy through precise multimodal data alignment[34].

The process signature approach addresses limitations of traditional alignment methods that rely on timestamps or statistical correlations[34]. By focusing on physical process characteristics, HiRA-Pro achieves temporal-spatial resolutions of 10-1000 microseconds and 100 micrometers[34]. These resolutions prove essential for modern manufacturing applications requiring precise localization of process artifacts[34].

### Dynamic Time Warping Applications

Dynamic Time Warping (DTW) provides robust alignment for manufacturing data with temporal variations[32][37]. DTW algorithms optimize point-by-point correspondence between time series while accommodating non-linear temporal distortions[32]. Manufacturing applications demonstrate DTW's effectiveness in aligning numerical control data with execution logs[32].

Recent advances incorporate machine learning techniques to automate tool identification and refine alignment processes[32]. K-means clustering algorithms combined with DTW achieve superior registration accuracy compared to traditional alignment methods[32]. These improvements prove essential for developing automated industrial digital threads[32].

## Gaussian Process Interpolation for Gap Filling

### Uncertainty-Aware Interpolation

Gaussian Process (GP) regression provides uncertainty-aware interpolation for missing manufacturing data[38][39]. Unlike deterministic interpolation methods, GP approaches quantify uncertainty in predicted values, enabling more informed decision-making[40]. This capability proves crucial in manufacturing applications where data quality varies significantly across sensors and time periods[24].

GP methods demonstrate superior performance compared to traditional interpolation techniques in multiple manufacturing contexts[39][40]. Applications to wastewater treatment monitoring show that GP with sequential Monte Carlo estimation (GP-SMC) outperforms linear interpolation in noisy flow rate estimation[40]. The method's ability to incorporate measurement uncertainty directly into the interpolation process enhances reliability[40].

### Advanced GP Applications

Recent developments extend GP capabilities to degradation monitoring in manufacturing systems[41]. Multi-sensor integration approaches assign dynamic weights based on sensor clustering, improving degradation modeling accuracy[41]. Stochastic bridge models capture nonlinearity and uncertainty in degradation processes, providing more reliable life predictions[41].

GP applications to GNSS position time series demonstrate the value of incorporating spatial relationships[42]. Improved GP methods that consider adjacent station influences achieve 21% reduction in root mean square error compared to conventional GP approaches[42]. These improvements highlight the importance of spatial correlation in manufacturing sensor networks[42].

## Deep Learning Approaches for Irregular Time Series

### GRU-D Architecture

Gated Recurrent Units with Decay (GRU-D) represent one of the earliest deep learning approaches specifically designed for irregular time series with missing values[43][44]. GRU-D incorporates two representations of missing patterns: masking indicators and time intervals between observations[43][45]. This architecture captures long-term temporal dependencies while utilizing missing patterns to improve prediction performance[44].

The GRU-D framework proves particularly effective in healthcare and manufacturing applications where missing data patterns carry informative content[43][45]. Experimental results on clinical datasets demonstrate state-of-the-art performance compared to traditional imputation methods[44]. The model's ability to handle arbitrary missing patterns without preprocessing steps provides significant advantages in manufacturing environments[43].

### Neural Ordinary Differential Equations

Neural ODEs offer flexible architectures for continuous-time modeling of irregular observations[46][47]. These approaches parameterize the derivative of latent states using neural networks, enabling continuous-time dynamics generation[46]. Time-aware Neural ODEs (TN-ODE) specifically address incomplete time series modeling by supporting imputation at arbitrary time points[46].

Recent extensions include Neural Chronos ODEs (Neural CODE) that fit continuous-time dynamics for bidirectional time series prediction[47]. These models outperform traditional Neural ODEs in learning spiral dynamics both forward and backward in time[47]. Manufacturing applications benefit from the ability to extrapolate process behavior beyond observed time windows[47].

### Hybrid Deep Learning Approaches

Integration of deep learning with traditional time series methods produces powerful hybrid frameworks[5][8]. The QRNN-U-MIDAS model combines Quantile Regression Neural Networks with unrestricted MIDAS regression[8]. This approach leverages neural network optimization capabilities while maintaining the mixed-frequency handling advantages of MIDAS[8].

Experimental results demonstrate that hybrid approaches consistently outperform individual methods across various forecasting tasks[8]. The QRNN-U-MIDAS model achieves superior prediction accuracy across conditional distributions of response variables[8]. These improvements prove particularly valuable in manufacturing applications requiring robust uncertainty quantification[8].

## Advanced State Estimation Techniques

### Kalman Filtering and Smoothing

Kalman filtering provides optimal state estimation for linear dynamical systems with Gaussian noise[48][26]. In manufacturing contexts, Kalman filters enable real-time monitoring of process states while handling measurement uncertainties[49][50]. The recursive nature of Kalman filtering makes it computationally efficient for online applications[51].

Kalman smoothing extends filtering capabilities by incorporating future observations to refine past state estimates[48][50]. This backward-forward processing approach proves valuable in manufacturing applications where post-processing analysis can improve understanding of process dynamics[26]. Recent developments include one-step-ahead smoothing for ensemble Kalman filters[52].

### Robust Filtering Approaches

Manufacturing environments often contain outliers and model misspecification that can degrade traditional filtering performance[53][54]. Outlier-robust extended Kalman filtering incorporates adaptive mechanisms to handle non-Gaussian disturbances[53]. These approaches combine Kalman smoothers with approximate inference methods to improve robustness[53].

Randomized missing data (RMD) approaches provide alternative robustness strategies[54]. RMD methods purposely randomize the subset of utilized measurements to achieve robustness against outliers and misspecification[54]. This approach proves particularly effective when latent processes exhibit high persistence through time[54].

### High-Dimensional Extensions

Rank-reduced Kalman filters address computational challenges in high-dimensional manufacturing systems[55]. These methods propagate low-rank approximations of covariance matrices, reducing computational complexity from cubic to quadratic in state-space size[55]. The approach maintains exactness as the low-rank dimension approaches true system dimensionality[55].

Mixed-frequency applications benefit from specialized state-space formulations that handle arbitrary missing data patterns[25]. Precision-based sampling approaches enable efficient computation in high-dimensional conditionally Gaussian systems[25]. These methods prove essential for modern manufacturing systems with hundreds of sensor channels[25].

## Industrial Applications and Case Studies

### Manufacturing Process Monitoring

Real-world applications demonstrate the practical value of mixed-frequency techniques in manufacturing environments[23][24]. Semiconductor manufacturing benefits from MIDAS approaches that relate high-frequency process measurements to low-frequency yield indicators[23]. The integration of physics-based models with statistical learning improves prediction accuracy over traditional neural network approaches[23].

Seagate's manufacturing analysis illustrates the challenges of missing data in industrial settings[24]. Key Process Input Variables (KPIVs) exhibit complex missing patterns due to machine faults, sensor additions, and environmental factors[24]. Statistical analysis of missingness patterns reveals important relationships between machine fleets previously assumed to operate independently[24].

### Quality Control Applications

Steel manufacturing demonstrates Kalman filter applications to thickness control during rolling processes[49]. Noisy thickness gauge measurements benefit from filtering approaches that account for vibrations, temperature changes, and mechanical imperfections[49]. The filtered estimates provide more accurate process control compared to raw measurements[49].

Multi-stage manufacturing systems with remote quality information feedback present unique modeling challenges[28]. Markov models capture quality propagation through production stages while accounting for both quality degradation and correction[28]. These approaches enable identification of quality bottlenecks and continuous improvement strategies[28].

### Digital Twin Development

Digital twin evolution analysis requires precise temporal alignment of multi-dimensional asynchronous data[33]. BiLSTM-based interpolation methods provide flexible data granularity adjustment while maintaining temporal consistency[33]. These approaches support reliable data processing for anomaly detection and decision-making in digital twin applications[33].

Standards-based digital threads benefit from spatiotemporal alignment techniques[56]. IEEE 802.1AS time synchronization enables temporal alignment of ethernet-connected manufacturing devices[56]. Machine learning-based alignment of disparate sensor streams complements synchronization protocols[56].

## Performance Metrics and Validation

### Forecasting Accuracy

Empirical evaluations consistently demonstrate superior performance of mixed-frequency methods over traditional approaches[1][4]. MIDAS regression achieves 10-15% improvement in forecasting accuracy compared to temporal aggregation methods[4]. Factor-MIDAS approaches show particular strength in high-dimensional applications[16].

Cross-validation studies reveal that preselection strategies enhance dynamic factor model performance[18]. Elastic net variable selection combined with novel cross-validation approaches outperforms benchmark models using all available variables[18]. These improvements prove particularly important in manufacturing applications with limited historical data[18].

### Computational Efficiency

U-MIDAS demonstrates computational advantages in scenarios with moderate frequency differences[12][14]. Ordinary least squares estimation eliminates the need for nonlinear optimization required by traditional MIDAS[12]. This computational efficiency enables real-time applications in manufacturing environments[14].

Rank-reduced Kalman filters achieve linear computational complexity when state-space models satisfy certain criteria[55]. This scalability proves essential for modern manufacturing systems with hundreds of sensor channels and high-frequency sampling requirements[55].

### Industrial Validation

HiRA-Pro validation in additive manufacturing demonstrates 35% improvement in classification accuracy through precise data alignment[34]. These improvements enable accurate localization of manufacturing artifacts at resolutions previously unattainable[34]. The method's ability to handle sub-millisecond phenomena proves crucial for advanced manufacturing processes[34].

Real-world GDP forecasting applications validate mixed-frequency approaches in macroeconomic contexts[4][6]. These validation studies provide confidence in the methods' applicability to manufacturing economic indicators such as production efficiency and resource utilization[4].

## Future Directions and Emerging Trends

### Integration with Machine Learning

Hybrid approaches combining traditional time series methods with modern machine learning continue to evolve[5][8]. Deep learning architectures increasingly incorporate temporal structure awareness while maintaining interpretability[5]. These developments promise to enhance manufacturing analytics capabilities while preserving domain knowledge[8].

Transformer architectures adapted for time series analysis show promise for mixed-frequency applications[5]. Multi-head attention mechanisms can potentially capture complex temporal relationships across frequency domains[5]. Integration with MIDAS frameworks may provide powerful new analytical capabilities[5].

### Real-Time Implementation

Real-time mixed-frequency analysis becomes increasingly important as Industry 4.0 initiatives advance[32][33]. Streaming data processing frameworks must accommodate varying temporal frequencies while maintaining analytical accuracy[33]. Edge computing implementations may enable local processing of high-frequency sensor data[34].

Adaptive algorithms that automatically adjust to changing frequency patterns show promise for dynamic manufacturing environments[53]. These approaches could automatically reconfigure analytical models as production processes evolve[53].

### Uncertainty Quantification

Bayesian approaches to mixed-frequency analysis continue to develop[4][57]. These methods provide principled uncertainty quantification essential for manufacturing decision-making[57]. Integration with process physics models may enhance both accuracy and interpretability[57].

Monte Carlo and variational inference techniques enable scalable uncertainty quantification in high-dimensional manufacturing systems[58][59]. These approaches support robust decision-making under uncertainty while maintaining computational feasibility[58].

## Conclusion

Mixed-frequency time series analysis has emerged as a critical capability for modern manufacturing analytics. The integration of MIDAS regression, Dynamic Factor Models, and advanced temporal alignment techniques provides manufacturers with powerful tools for extracting value from multi-frequency data streams. These methods preserve high-frequency information while maintaining computational efficiency, enabling more accurate process monitoring, quality control, and predictive maintenance.

The demonstrated improvements of 10-15% in forecasting accuracy, combined with enhanced uncertainty quantification capabilities, provide clear value propositions for industrial implementation[4][13]. As manufacturing systems become increasingly instrumented and connected, the ability to effectively analyze mixed-frequency data will prove essential for maintaining competitive advantage.

Future developments in hybrid machine learning approaches, real-time implementation strategies, and uncertainty quantification methods promise to further enhance the capabilities of mixed-frequency analysis[5][57]. The convergence of traditional statistical methods with modern computational techniques creates opportunities for breakthrough advances in manufacturing analytics[8][59].

The mathematical foundations established through distributed lag polynomials, state-space representations, and advanced filtering techniques provide robust theoretical underpinnings for continued innovation[9][21]. As Industry 4.0 initiatives accelerate, mixed-frequency time series analysis will play an increasingly central role in bridging temporal gaps and unlocking the full potential of manufacturing data[32][33].

Sources
[1] Forecasting Mixed Frequency Time Series with ECM-MIDAS Models https://onlinelibrary.wiley.com/doi/10.1002/for.2286
[2] A Midas Regression Approach to Measure the Impacts of the Brent Oil and Gold Financial Returns on Iran’s Macroeconomic Indicators https://dergipark.org.tr/en/doi/10.54427/ijisef.1529319
[3] The Analysis of Residential Property Price Bubble and Sharia Bank Financing Using MIDAS Regression Model https://www.macrothink.org/journal/index.php/ifb/article/view/20893
[4] The Bayesian nested lasso for mixed frequency regression models https://projecteuclid.org/journals/annals-of-applied-statistics/volume-17/issue-3/The-Bayesian-nested-lasso-for-mixed-frequency-regression-models/10.1214/22-AOAS1718.full
[5] Hybrid ARDL-MIDAS-Transformer time-series regressions for multi-topic crypto market sentiment driven by price and technology factors https://link.springer.com/10.1007/s42521-023-00079-9
[6] Predicting the Country Commodity Imports Using Mixed Frequency Data Sampling (MIDAS) Model https://ier.ut.ac.ir/article_67847.html
[7] High-Mixed-Frequency Dynamic Latent Factor Forecasting Models for GDP in the Philippines https://ojs.ual.es/ojs/index.php/eea/article/view/3112
[8] Improving forecasting accuracy using quantile regression neural network combined with unrestricted mixed data sampling https://journal.nsps.org.ng/index.php/jnsps/article/view/1394
[9] [PDF] The MIDAS Touch: Mixed Data Sampling Regression Models∗ https://rady.ucsd.edu/_files/faculty-research/valkanov/midas-touch.pdf
[10] Mixed-data sampling - Wikipedia https://en.wikipedia.org/wiki/Mixed-data_sampling
[11] Forecasting mixed-frequency time series with ECM-MIDAS models https://cris.maastrichtuniversity.nl/en/publications/forecasting-mixed-frequency-time-series-with-ecm-midas-models-2
[12] DISCUSSION PAPER SERIES https://repec.cepr.org/repec/cpr/ceprdp/DP8828.pdf
[13] Data-driven optimization for production planning with multiple demand features https://www.emerald.com/insight/content/doi/10.1108/k-04-2023-0690/full/html
[14] U-MIDAS https://books.google.com/books/about/U_MIDAS.html?id=eTQxmwEACAAJ
[15] A non-linear SVR-based cascade model for improving prediction accuracy of biomedical data analysis https://www.aimspress.com/aimspress-data/mbe/2023/7/PDF/mbe-20-07-597.pdf
[16] [PDF] Factor-augmented sparse MIDAS regressions with an application to ... https://arxiv.org/pdf/2306.13362.pdf
[17] [PDF] Inference for Factor-MIDAS Regression Models - Yookyung Julia Koh https://www.yookyungjuliakoh.com/assets/img/draft_July2024.pdf
[18] Predictor Preselection for Mixed‐Frequency Dynamic Factor Models: A Simulation Study With an Empirical Application to GDP Nowcasting https://onlinelibrary.wiley.com/doi/10.1002/for.3193
[19] Quantifying Drivers of Forecasted Returns Using Approximate Dynamic Factor Models for Mixed-Frequency Panel Data https://www.mdpi.com/2571-9394/3/1/5
[20] Bridging Dynamic Factor Models and Neural Controlled Differential Equations for Nowcasting GDP https://dl.acm.org/doi/10.1145/3627673.3680074
[21] [PDF] Dynamic Factor Models A Very Short Introduction https://cran.r-project.org/web/packages/dfms/vignettes/dynamic_factor_models.pdf
[22] Low Frequency and Weighted Likelihood Solutions for Mixed Frequency Dynamic Factor Models https://www.ssrn.com/abstract=2479172
[23] Analytics-statistics mixed training and its fitness to semisupervised manufacturing https://pmc.ncbi.nlm.nih.gov/articles/PMC6692054/
[24] Missingness analysis of manufacturing systems: A case study - Caoimhe M Carbery, Roger Woods, Cormac McAteer, David M Ferguson, 2022 https://journals.sagepub.com/doi/full/10.1177/*****************
[25] [PDF] High-Dimensional Conditionally Gaussian State Space Models with ... https://joshuachan.org/papers/BVAR-MF-R1.pdf
[26] [PDF] Kalman filter and smoother https://jwmi.github.io/ASM/6-KalmanFilter.pdf
[27] [PDF] Short-term GDP forecasting with a mixed frequency dynamic factor ... https://www.bundesbank.de/resource/blob/635774/87588bc928a63e5774b38b9f3901528f/mL/2012-06-01-eltville-13-porqueddu-presentation-data.pdf
[28] Markov modeling and analysis of multi-stage manufacturing systems with remote quality information feedback https://me.sjtu.edu.cn/upload/ueditor/file/********/****************.pdf
[29] Accelerating peak dating in a dynamic factor Markov-switching model https://www.sciencedirect.com/science/article/pii/S0169207023000365
[30] Markov-switching dynamic factor models in real time - ScienceDirect https://www.sciencedirect.com/science/article/abs/pii/S0169207018300773
[31] Large dimensional portfolio allocation based on a mixed frequency dynamic factor model https://www.tandfonline.com/doi/full/10.1080/********.2021.1983327
[32] An Automated Approach for Segmenting Numerical Control Data With Controller Data for Machine Tools https://asmedigitalcollection.asme.org/computingengineering/article/24/4/041003/1170005/An-Automated-Approach-for-Segmenting-Numerical
[33] Temporal Alignment of Multi-Dimensional Asynchronous Data for Digital Twin Evolutionary Analysis https://ieeexplore.ieee.org/document/10911555/
[34] Process signature-driven high spatio-temporal resolution alignment of multimodal data https://arxiv.org/abs/2403.06888
[35] [PDF] Automatic Uncertainty Propagation Based on the Unscented ... https://www.aau.at/wp-content/uploads/2020/03/UT_Uncertainty_Propagation.pdf
[36] Gaussian Mixture-Unscented Transform propagation during re-entry fragmentation for impact uncertainty quantification https://conference.sdo.esoc.esa.int/proceedings/sdc9/paper/221
[37] Heart sound classification based on temporal alignment techniques http://www.cinc.org/archives/2016/pdf/170-317.pdf
[38] Using Gaussian Process Regression for the interpolation of missing 2.5D environment modelling data https://ieeexplore.ieee.org/document/8704780/
[39] Cleaning Images with Gaussian Process Regression https://iopscience.iop.org/article/10.3847/1538-3881/ac1348
[40] Gaussian process regression for monitoring and fault detection of wastewater treatment processes. https://iwaponline.com/wst/article/75/12/2952/20467/Gaussian-process-regression-for-monitoring-and
[41] Missing data interpolation and multi‐sensors integration and its application in accelerated degradation data https://onlinelibrary.wiley.com/doi/10.1002/qre.3365
[42] An improved Gaussian process for filling the missing data in GNSS ... https://www.nature.com/articles/s41598-024-70421-7
[43] Under review as a conference paper at ICLR 2017 https://openreview.net/pdf/74b3010f71f7266247dc56506f38ad2d235eb359.pdf
[44] Recurrent Neural Networks for Multivariate Time Series with Missing Values - PubMed https://pubmed.ncbi.nlm.nih.gov/29666385/
[45] Recurrent Neural Networks for Multivariate Time Series with Missing Values | MIT Clinical ML https://clinicalml.org/publication/che-et-al-nature-sr-18/
[46] Time-aware neural ordinary differential equations for incomplete time series modeling - PubMed https://pubmed.ncbi.nlm.nih.gov/37359342/
[47] Neural Chronos ODE: Unveiling Temporal Patterns and Forecasting ... https://arxiv.org/abs/2307.01023
[48] Bayesian Filtering and Smoothing https://www.cambridge.org/core/product/identifier/9781108917407/type/book
[49] Understanding the Kalman Filter: A Powerful Tool in smoothing the ... https://www.linkedin.com/pulse/understanding-kalman-filter-powerful-tool-smoothing-noisy-mishra-gf7hc
[50] Comprehensive Guide to Kalman Smoothing for Accurate Estimation https://www.numberanalytics.com/blog/comprehensive-kalman-smoothing-guide
[51] Kalman filtering, smoothing, and recursive robot arm forward and inverse dynamics http://ieeexplore.ieee.org/document/1087147/
[52] Ensemble Kalman Filtering with One-Step-Ahead Smoothing https://journals.ametsoc.org/doi/10.1175/MWR-D-17-0175.1
[53] Outlier-Robust Extended Kalman Filtering for Bioinspired Integrated Navigation System https://ieeexplore.ieee.org/document/10272379/
[54] A Randomized Missing Data Approach to Robust https://www2.gwu.edu/~forcpgm/Dobrev_FFC2021.pdf
[55] The Rank-Reduced Kalman Filter: Approximate Dynamical-Low-Rank Filtering In High Dimensions https://arxiv.org/abs/2306.07774
[56] [PDF] Spatial and Temporal Data Alignment from Disparate Sources for ... https://nvlpubs.nist.gov/nistpubs/gcr/2024/NIST.GCR.24-050.pdf
[57] Bayesian uncertainty quantification for data-driven equation learning https://ora.ox.ac.uk/objects/uuid:50948f1c-9395-4396-b3e6-736789cd4529/files/rmp48sd290
[58] A NONPARAMETRIC BAYESIAN FRAMEWORK FOR UNCERTAINTY https://arxiv.org/pdf/1910.03766.pdf
[59] Bayesian deep learning framework for uncertainty quantification in ... https://arxiv.org/abs/2210.11737
[60] midas_nlpr: Non-linear parametric MIDAS regression - rdrr.io https://rdrr.io/github/mpiktas/midasr/man/midas_nlpr.html
[61]  https://arxiv.org/pdf/1906.04025.pdf
[62] Forecasting Baden‐Württemberg's GDP growth: MIDAS regressions versus dynamic mixed‐frequency factor models https://onlinelibrary.wiley.com/doi/10.1002/for.2743
[63] Identification of Generalized Dynamic Factor Models from mixed-frequency data https://linkinghub.elsevier.com/retrieve/pii/S240589631831721X
[64] Weighted Maximum Likelihood Estimator for Mixed Frequency Dynamic Factor Models https://www.semanticscholar.org/paper/ec1261ba734ab857a036df28b375fbe5b9749806
[65] A mixed-frequency dynamic factor model for nowcasting consumer ... https://www.sciencedirect.com/science/article/pii/S0169207021001795
[66] Improving Accuracy and Robustness in HF-RFID-Based Indoor Positioning With Kalman Filtering and Tukey Smoothing https://ieeexplore.ieee.org/document/9094698/
[67] Full four-dimensional change analysis of topographic point cloud time series using Kalman filtering https://esurf.copernicus.org/articles/11/593/2023/
[68] Dynamic displacement estimation by fusing LDV and LiDAR measurements via smoothing based Kalman filtering https://linkinghub.elsevier.com/retrieve/pii/S0888327016301121
[69] State-of-Health Prognosis for Lithium-Ion Batteries Considering the Limitations in Measurements via Maximal Information Entropy and Collective Sparse Variational Gaussian Process https://ieeexplore.ieee.org/document/9216154/
[70] Revisiting Gaussian Process Dynamical Models https://www.semanticscholar.org/paper/10a32b3f920a55d57407ae57d65eadf636894c16
[71] Statistical Interpolation of Spatially Varying but Sparsely Measured 3D Geo-Data Using Compressive Sensing and Variational Bayesian Inference https://link.springer.com/10.1007/s11004-020-09913-x
[72] Inference for Continuous Stochastic Processes Using Gaussian Process Regression https://www.semanticscholar.org/paper/a5c359b652ca43251c17947a26d53e28d29558ab
[73] Gaussian Processes with missing data - Julia Discourse https://discourse.julialang.org/t/gaussian-processes-with-missing-data/53987
[74] Interpolate / Impute time series (sparse measurements) https://stats.stackexchange.com/questions/594732/interpolate-impute-time-series-sparse-measurements
[75] Foundations, techniques, algorithms and tools for allowing autonomous AI agents to decide and learn how to act - D5.2 Report https://www.semanticscholar.org/paper/786fce5f91791590d13ca34b3fc53e331c844dc5
[76] UWB Channel Impulse Response Alignment Techniques for Accurate Indoor Radio Sensing https://ieeexplore.ieee.org/document/10721882/
[77] Emotion Recognition From Multimodal Physiological Signals via Discriminative Correlation Fusion With a Temporal Alignment Mechanism https://ieeexplore.ieee.org/document/10288375/
[78] WINNER: Weakly-supervised hIerarchical decompositioN and aligNment for spatio-tEmporal video gRounding https://ieeexplore.ieee.org/document/10203473/
[79] An Approach to Data Modeling via Temporal and Spatial Alignment https://www.mdpi.com/2227-9717/12/1/62
[80] Spatial-temporal alignment of time series with different sampling ... https://www.sciencedirect.com/science/article/abs/pii/S0306457322002242
[81] Mixed-frequency models for tracking short-term economic developments in Switzerland https://www.semanticscholar.org/paper/a135b0904b7f659e9450fd933ef41cf78152f057
[82] High-mixed-frequency forecasting models for GDP and inflation https://www.worldscientific.com/doi/abs/10.1142/9789813220447_0002
[83] A novel (U)MIDAS-SVR model with multi-source market sentiment ... https://dl.acm.org/doi/10.1007/s00521-019-04063-6
[84] Nowcasting with MIDAS regressions - Macrosynergy https://macrosynergy.com/research/nowcasting-with-midas-regressions/
[85] Approximate dynamic factor models for mixed frequency data https://www.semanticscholar.org/paper/1318bbedc6a771446cf38d3d76ff3c1a4bbf53e2
[86] Working Paper No. 2010-17R1 https://uhero.hawaii.edu/RePEc/hae/wpaper/WP_2010-17R1.pdf
[87] Factor-augmented sparse https://feb.kuleuven.be/research/economics/Documents/DPS2024/DPS%2024.14.pdf
[88] Nonlinear Regression Huber–Kalman Filtering and Fixed-Interval Smoothing https://arc.aiaa.org/doi/10.2514/1.G000799
[89] A Robust Kalman Framework with Resampling and Optimal Smoothing https://www.mdpi.com/1424-8220/15/3/4975/pdf
[90] A more robust unscented transform https://www.mitre.org/sites/default/files/pdf/vanzandt_unscented.pdf
[91] Kalman filter - Wikipedia https://en.wikipedia.org/wiki/Kalman_filter
[92] Generative interpolation via diffusion probabilistic model https://library.seg.org/doi/10.1190/geo2023-0182.1
[93] Fast, Approximate Maximum Likelihood Estimation of Log-Gaussian Cox Processes https://www.tandfonline.com/doi/full/10.1080/10618600.2023.2182311
[94] ON THE BALANCE BETWEEN THE TRAINING TIME AND https://arxiv.org/pdf/2206.03304.pdf
[95] Gaussian processes for missing value imputation - ScienceDirect.com https://www.sciencedirect.com/science/article/pii/S0950705123003532
[96] A pre-whitening with block-bootstrap cross-correlation procedure for temporal alignment of data sampled by eddy covariance systems https://link.springer.com/10.1007/s10651-024-00615-9
[97] MF2Summ: Multimodal Fusion for Video Summarization with Temporal Alignment https://www.semanticscholar.org/paper/ee68e7848e8df9a6c980fc292bf5ef70f5c61dc6
