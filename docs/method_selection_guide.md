# Correlation Method Selection Guide

## Quick Decision Matrix

| Data Characteristics | Recommended Method | Confidence Level |
|---------------------|-------------------|------------------|
| **Normal distribution + Linear relationship** | **Pearson** | High |
| **Outliers present + Monotonic relationship** | **<PERSON><PERSON>man** | High |
| **Highly contaminated data + Ordinal variables** | **Kendall** | High |
| **Mixed characteristics** | **Multi-method comparison** | Medium |
| **Uncertain data quality** | **Start with Kendall** | Medium |

## 🔬 Method Selection Decision Tree

```
START: Assess your data characteristics

├── Is your data normally distributed?
│   ├── YES: Are there significant outliers?
│   │   ├── NO: Is the relationship linear?
│   │   │   ├── YES → **PEARSON** (optimal for linear normal data)
│   │   │   └── NO: Is it monotonic?
│   │   │       ├── YES → **SPEARMAN** (captures monotonic patterns)
│   │   │       └── NO → **KENDALL** (general association)
│   │   └── YES → Go to outlier handling
│   └── NO: Are there significant outliers?
│       ├── YES → Go to outlier handling
│       └── NO: Is the relationship monotonic?
│           ├── YES → **SPEARMAN** (rank-based, robust)
│           └── NO → **KENDALL** (most robust)

OUTLIER HANDLING:
├── Is the relationship monotonic despite outliers?
│   ├── YES → **SPEARMAN** (robust to outliers + monotonic)
│   └── NO → **KENDALL** (maximum robustness)
```

## 🏭 Manufacturing-Specific Guidelines

### By Variable Type

#### **Continuous Process Variables**
- **Speed, Temperature, Pressure**: Usually **Pearson** (if linear and normal)
- **Thickness measurements**: Check for sensor outliers → **Spearman** if present
- **Flow rates**: Often linear → **Pearson**, but check for equipment issues

#### **Quality Metrics**
- **Scrap rates (%)**: Often non-normal → **Spearman** or **Kendall**
- **Quality scores (1-10)**: Ordinal → **Spearman** or **Kendall**
- **Pass/fail rates**: Binary outcomes → **Kendall** (handles ties well)

#### **Equipment Performance**
- **Efficiency rankings**: Ordinal → **Spearman** or **Kendall**
- **Maintenance scores**: Often subjective → **Kendall**
- **Alarm frequencies**: Count data → **Spearman** or **Kendall**

#### **Time-Based Variables**
- **Production cycles**: May have periodic patterns → **Spearman**
- **Downtime duration**: Often right-skewed → **Spearman** or **Kendall**
- **Lead times**: Usually non-normal → **Spearman**

### By Data Quality

#### **High-Quality Sensor Data**
- **Calibrated instruments**: **Pearson** (if relationship is linear)
- **Regular maintenance**: **Pearson** or **Spearman** based on relationship type
- **Controlled environment**: **Pearson** preferred for efficiency

#### **Moderate-Quality Data**
- **Occasional outliers**: **Spearman** (robust to outliers)
- **Missing calibration dates**: **Spearman** (safer choice)
- **Mixed sensor types**: **Spearman** (handles different scales)

#### **Poor-Quality Data**
- **Frequent outliers**: **Kendall** (maximum robustness)
- **Suspect calibration**: **Kendall** (least affected by measurement errors)
- **Legacy systems**: **Kendall** (handles inconsistent data best)

## 📊 Practical Examples

### Example 1: Production Speed vs Thickness

**Data Assessment:**
```python
# Speed: Continuous, normally distributed, few outliers
# Thickness: Continuous, but sensor calibration issues (outliers)

assessment = {
    'normality': {'speed': True, 'thickness': False},
    'outliers': {'speed': False, 'thickness': True},
    'linearity': True
}
```

**Recommendation:** **Spearman**
- **Reasoning**: Linear relationship expected, but thickness outliers make Pearson unreliable
- **Alternative**: Kendall if outliers are severe

### Example 2: Temperature vs Material Strength

**Data Assessment:**
```python
# Temperature: Continuous, normal
# Strength: Continuous, normal, linear physics relationship expected

assessment = {
    'normality': {'temperature': True, 'strength': True},
    'outliers': {'temperature': False, 'strength': False},
    'linearity': True
}
```

**Recommendation:** **Pearson**
- **Reasoning**: Both variables normal, linear relationship, no outliers
- **Confidence**: High

### Example 3: Operator Shift vs Quality Score

**Data Assessment:**
```python
# Shift: Categorical (1=day, 2=evening, 3=night)
# Quality: Ordinal scale (1-10)

assessment = {
    'normality': {'shift': False, 'quality': False},
    'outliers': {'shift': False, 'quality': True},  # Some extreme scores
    'data_type': 'ordinal'
}
```

**Recommendation:** **Kendall**
- **Reasoning**: Ordinal data, outliers present, no assumption of equal intervals
- **Alternative**: Spearman if intervals are roughly equal

### Example 4: Equipment Age vs Maintenance Cost

**Data Assessment:**
```python
# Age: Continuous, but equipment replacement creates gaps
# Cost: Right-skewed, occasional expensive repairs (outliers)

assessment = {
    'normality': {'age': True, 'cost': False},
    'outliers': {'age': False, 'cost': True},
    'relationship': 'monotonic_nonlinear'  # Cost increases, but not linearly
}
```

**Recommendation:** **Spearman**
- **Reasoning**: Monotonic relationship, cost outliers, non-linear pattern
- **Confidence**: High

## 🎯 Using the Automated System

### System Recommendations

The multi-method analyzer automatically assesses data and provides recommendations:

```python
from src.data.multi_correlations import MultiMethodCorrelationAnalyzer

analyzer = MultiMethodCorrelationAnalyzer()
results = analyzer.calculate_multi_method_correlations(data)

for pair_key, result in results.items():
    print(f"{result.variable_1} ↔ {result.variable_2}")
    print(f"Recommended: {result.recommended_method}")
    
    # Examine the reasoning
    assessment = result.data_distribution_assessment
    if 'normality' in assessment:
        x_normal = assessment['normality']['x_variable']['is_normal']
        y_normal = assessment['normality']['y_variable']['is_normal']
        print(f"Normal distribution: X={x_normal}, Y={y_normal}")
    
    if 'outliers' in assessment:
        x_outliers = assessment['outliers']['x_variable']['has_significant_outliers']
        y_outliers = assessment['outliers']['y_variable']['has_significant_outliers']
        print(f"Significant outliers: X={x_outliers}, Y={y_outliers}")
```

### Override Recommendations

When domain knowledge suggests a different approach:

```python
# Use specific method regardless of recommendation
from scipy.stats import pearsonr, spearmanr, kendalltau

# Force Pearson for known linear physics relationship
correlation, p_value = pearsonr(x, y)

# Force Kendall for robustness in critical decisions
correlation, p_value = kendalltau(x, y)
```

## 🧪 Validation Strategies

### Method Comparison

Always compare methods when unsure:

```python
methods = ['pearson', 'spearman', 'kendall']
results = {}

for method in methods:
    if method == 'pearson':
        r, p = pearsonr(x, y)
    elif method == 'spearman':
        r, p = spearmanr(x, y)
    else:
        r, p = kendalltau(x, y)
    
    results[method] = {'correlation': r, 'p_value': p}

# Check convergence
correlations = [results[m]['correlation'] for m in methods]
convergence = 1 - np.var(correlations)  # High convergence = low variance

if convergence > 0.8:
    print("High method agreement - any method reliable")
else:
    print("Methods disagree - choose based on data characteristics")
```

### Bootstrap Validation

Test stability of your chosen method:

```python
def bootstrap_correlation(x, y, method='pearson', n_bootstrap=1000):
    bootstrap_correlations = []
    n = len(x)
    
    for _ in range(n_bootstrap):
        # Resample with replacement
        indices = np.random.choice(n, n, replace=True)
        x_boot, y_boot = x[indices], y[indices]
        
        if method == 'pearson':
            r, _ = pearsonr(x_boot, y_boot)
        elif method == 'spearman':
            r, _ = spearmanr(x_boot, y_boot)
        else:
            r, _ = kendalltau(x_boot, y_boot)
        
        bootstrap_correlations.append(r)
    
    # Calculate stability
    stability = 1 - np.std(bootstrap_correlations)
    return stability, bootstrap_correlations

# Test each method
for method in ['pearson', 'spearman', 'kendall']:
    stability, _ = bootstrap_correlation(x, y, method)
    print(f"{method.title()} stability: {stability:.3f}")
```

## ⚠️ Common Pitfalls

### 1. **Assuming Normality**
```python
# DON'T: Automatically use Pearson
correlation = pearsonr(x, y)[0]

# DO: Test normality first
from scipy.stats import jarque_bera
_, p_x = jarque_bera(x)
_, p_y = jarque_bera(y)

if p_x > 0.05 and p_y > 0.05:
    # Data appears normal
    correlation = pearsonr(x, y)[0]
else:
    # Use robust method
    correlation = spearmanr(x, y)[0]
```

### 2. **Ignoring Outliers**
```python
# DON'T: Ignore obvious outliers
correlation = pearsonr(x, y)[0]

# DO: Check for outliers
Q1_x, Q3_x = np.percentile(x, [25, 75])
IQR_x = Q3_x - Q1_x
outliers_x = np.sum((x < Q1_x - 1.5*IQR_x) | (x > Q3_x + 1.5*IQR_x))

if outliers_x > len(x) * 0.1:  # More than 10% outliers
    correlation = spearmanr(x, y)[0]  # Use robust method
```

### 3. **Misinterpreting Kendall Values**
```python
# Kendall correlations are typically smaller than Pearson/Spearman
# DON'T: Compare absolute values directly

kendall_r = kendalltau(x, y)[0]    # e.g., 0.45
pearson_r = pearsonr(x, y)[0]      # e.g., 0.65

# DO: Compare relative strength within each method
# Both may indicate "moderate" correlation in their respective scales
```

## 📈 Manufacturing Decision Framework

### Process Control
- **Real-time monitoring**: **Pearson** (fastest, if data quality good)
- **Alarm thresholds**: **Spearman** (robust to sensor drift)
- **Critical safety systems**: **Kendall** (maximum robustness)

### Quality Analysis
- **Daily quality reports**: **Spearman** (handles various data quality)
- **Root cause analysis**: **Kendall** (robust to outliers and anomalies)
- **Trend analysis**: **Pearson** (if relationships are linear and stable)

### Equipment Optimization
- **Performance benchmarking**: **Spearman** (handles different equipment types)
- **Maintenance scheduling**: **Kendall** (robust to irregular patterns)
- **Efficiency comparisons**: Method based on data characteristics

### Continuous Improvement
- **Six Sigma projects**: Start with **multi-method analysis**
- **Statistical process control**: **Pearson** (if assumptions met)
- **Design of experiments**: **Pearson** (controlled conditions)

## 🔧 Implementation Checklist

### Before Analysis
- [ ] Examine data distributions (histograms, Q-Q plots)
- [ ] Check for outliers (box plots, IQR analysis)
- [ ] Assess relationship type (scatter plots)
- [ ] Consider manufacturing domain knowledge
- [ ] Review data quality and sensor calibration status

### During Analysis
- [ ] Use multi-method comparison for new relationships
- [ ] Check method convergence scores
- [ ] Validate with bootstrap analysis for critical decisions
- [ ] Document reasoning for method selection
- [ ] Consider practical significance vs statistical significance

### After Analysis
- [ ] Interpret results in manufacturing context
- [ ] Validate findings with process engineers
- [ ] Document method selection rationale
- [ ] Monitor stability over time
- [ ] Update method choice if data characteristics change

This guide provides a systematic approach to correlation method selection in manufacturing environments, balancing statistical rigor with practical manufacturing considerations.