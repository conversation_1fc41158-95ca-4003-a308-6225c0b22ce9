### **Consolidated Action Plan for Correlation Discovery in Fiber Cement Manufacturing**

**1. Executive Summary**

This document outlines a phased, actionable plan to discover and validate correlations within the fiber cement manufacturing process. The core challenge lies in analyzing multi-frequency data streams from SCADA and SAP systems to understand the complex interplay between process variables and final product quality.

Our approach is to build a sophisticated analytical framework incrementally. We will begin by establishing a robust data foundation and baseline models, then advance to state-of-the-art Transformer architectures for deep pattern recognition, and finally deploy LLM-powered agents for causal inference and automated root cause analysis. Each phase includes specific testing protocols and validation steps to ensure the generated insights are accurate, interpretable, and drive measurable business value in terms of quality improvement, efficiency gains, and cost reduction.

**2. Objective and Key Hypotheses**

The primary objective is to create a data-driven system that identifies, quantifies, and validates the causal relationships between manufacturing process parameters and key performance indicators (KPIs) like scrap rate, production stoppages, and final product quality.

Based on the provided documentation, our initial key hypotheses to investigate are:

*   **Sheet Formation:** Variations in Felt Speed (`ZTI_FLT_SPD`) and Batch Composition (`ZTI_BATCH`) are primary drivers of Sheet Thickness (`ZTI_SHT_THK`) inconsistencies and scrap events (`ZTI_SM_STK`).
*   **Curing Process (Mixed-Frequency Challenge):** High-frequency (5-minute) fluctuations in the Autoclave Cycle (`AV Cycle`) have a lagged, causal impact on final product quality, which can be correlated with lower-frequency (15-minute) pressure readings (`AC Pressure`).
*   **Event-Driven Impact:** Machine stoppage events (`ZTI_STOP`) in both sheet formation and finishing create immediate downstream quality issues and contribute significantly to the overall scrap rate.
*   **Cross-Stage Dependencies:** Raw material mixing consistency has a measurable, lagged effect on defect rates that emerge only after the finishing operations.

**3. Phased Implementation and Testing Plan**

This plan is structured in three phases, moving from foundational work to advanced, cutting-edge implementation.

---

#### **Phase 1: Data Foundation and Baseline Modeling (Weeks 1-4)**

**Goal:** To create a unified, time-aligned dataset and establish a baseline performance metric with traditional correlation methods.

**Tasks & Implementation:**

1.  **Data Ingestion and Integration:**
    *   Establish robust pipelines to extract data from SCADA tables and SAP-MPS (ZTI tables).
    *   Consolidate all data streams into a centralized time-series database.
    *   Implement data quality checks to identify and flag missing data, sensor drift, and outliers.

2.  **Temporal Alignment and Synchronization (Critical Step):**
    *   **Upsample & Interpolate:** Convert all time-series data to the highest common frequency (5 minutes, from `AV Cycle`). Use spline interpolation for continuous data like `ZTI_SHT_THK` and forward-fill for step-based data like `ZTI_FLT_SPD`.
    *   **Event Feature Engineering:** Convert event-based data (e.g., `ZTI_STOP`) into continuous features. For each 5-minute interval, create features like `time_since_last_stoppage`, `stoppage_duration_in_window`, and a binary `stoppage_occurred` flag.

3.  **Feature Engineering:**
    *   **Stability Features:** Calculate rolling statistics (e.g., 30-minute rolling coefficient of variation for `ZTI_SHT_THK`) to quantify process stability.
    *   **Lag Features:** Create lagged versions of key variables (e.g., `batch_composition_lag_60min`) to account for physical delays between manufacturing stages.

4.  **Baseline Correlation Analysis:**
    *   Calculate Pearson/Spearman correlation matrices for all engineered features.
    *   Apply Granger Causality tests to identify initial directional relationships.

**Testing & Validation:**

*   **Test:** Validate the temporal alignment by plotting aligned signals and confirming with process engineers.
*   **Validate:** Present the baseline correlation matrix and Granger causality results to domain experts. Confirm if the identified strong correlations (e.g., Felt Speed and Thickness) align with their process knowledge.
*   **Success Criteria:** A complete, time-aligned dataset is created. A baseline report of statistically significant correlations is documented and validated by the operations team.

---

#### **Phase 2: Advanced Correlation Discovery with Transformer Models (Weeks 5-10)**

**Goal:** To leverage Transformer models to uncover complex, non-linear, and long-range dependencies that baseline models miss.

**Tasks & Implementation:**

1.  **Model Selection and Implementation:**
    *   **Primary Model:** Implement the **Temporal Fusion Transformer (TFT)**. Its architecture is ideal for this use case due to its ability to handle static metadata (e.g., equipment type), known future inputs, and time-varying data.
    *   **Secondary Model (for cross-stage analysis):** Implement **CrossFormer**. Its two-stage attention mechanism is specifically designed to find lagged dependencies between different process stages (e.g., linking mixing parameters to curing outcomes).

2.  **Model Training:**
    *   Train the TFT to forecast a key quality metric (e.g., `scrap_rate` or `thickness_deviation`) using the feature-rich dataset from Phase 1.
    *   Leverage the TFT's quantile loss function to predict not just the value, but also the uncertainty range.

**Testing & Validation:**

*   **Test:** Compare the TFT's forecasting accuracy (e.g., Mean Absolute Error on predicting scrap rate) against a simple linear regression or ARIMA model from Phase 1. An improvement of >15% is targeted.
*   **Validate:** Use the TFT's built-in **Variable Selection Networks** and **interpretable attention heatmaps**. Present these to engineers to validate *why* the model is making its predictions. For example, the model should place high attention on `Autoclave Pressure` right before a predicted quality drop.
*   **Success Criteria:** The Transformer model demonstrates a significant performance lift over the baseline. Its interpretability outputs provide new, validated insights into process drivers.

---

#### **Phase 3: Causal Inference and LLM-Powered Root Cause Analysis (Weeks 11-16)**

**Goal:** To move from correlation to validated causation and to build an AI-powered agent that can assist in root cause analysis (RCA).

**Tasks & Implementation:**

1.  **LLM-Guided Causal Discovery:**
    *   Feed the most important variables identified by the TFT (from Phase 2) into a causal discovery algorithm like **PCMCI** or **NOTEARS** to generate a preliminary causal graph.
    *   Develop a **Causal Discovery LLM Agent**. Use the prompt templates from the `implementation_strategy.md` file to ask an LLM (e.g., GPT-4) to refine this graph. The prompt will include the statistical correlations, the preliminary graph, and a rich context of the fiber cement manufacturing process.

2.  **RAG System for Domain Knowledge:**
    *   Build a **Retrieval-Augmented Generation (RAG)** knowledge base containing equipment manuals, process documentation, historical incident reports, and expert knowledge gathered in prior phases.

3.  **Root Cause Analysis (RCA) Agent:**
    *   Develop an **Anomaly Detection Agent** that uses the trained TFT model to flag deviations from normal operation.
    *   When an anomaly is detected, trigger the **RCA Agent**. This agent will use Chain-of-Thought (CoT) prompting and the RAG system to generate a human-readable report that hypothesizes the root cause.
    *   *Example RCA Prompt:* "Anomaly detected: Sheet thickness increased by 10% for 30 minutes. Analyze the provided time-series data for [Thickness, Felt Speed, Batch Composition] and consult the RAG knowledge base to propose the top 3 most likely root causes, citing evidence for each."

**Testing & Validation:**

*   **Test:** Present the LLM-generated causal hypotheses to a panel of senior process engineers for scoring on plausibility and actionability.
*   **Validate:** "Re-play" 5-10 historical quality incidents. Feed the data to the RCA agent and compare its output to the actual, human-determined root cause.
*   **Success Criteria:** The final causal graph is validated and adopted by the engineering team. The RCA Agent achieves >80% accuracy in identifying the correct root cause as determined by domain experts.

**4. Final Validation and Business Impact KPIs**

Throughout the project, we will track a set of technical and business metrics to ensure the project delivers tangible value.

*   **Technical Metrics:**
    *   **Correlation Discovery:** Precision and Recall of validated correlations.
    *   **Prediction Accuracy (TFT):** >15% reduction in Mean Squared Error (MSE) for key quality forecasts compared to baseline.
    *   **RCA Agent Accuracy:** >80% accuracy on historical incident analysis.

*   **Business Impact Metrics:**
    *   **Quality Improvement:** Target a 15-25% reduction in scrap rate.
    *   **Operational Efficiency:** Target a 10-20% reduction in unplanned downtime through early anomaly detection.
    *   **Cost Savings:** Quantify the financial impact of reduced material waste and improved quality.