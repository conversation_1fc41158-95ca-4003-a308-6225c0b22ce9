#!/usr/bin/env python3
"""
Quick Forecasting with Trained Models

Simple interface to generate forecasts using trained models.
"""

import pandas as pd
from pathlib import Path
from src.forecasting.patchtst_model import ManufacturingPatchTSTModel
from datetime import datetime

def quick_forecast(target_variable: str = "fm_reject_pct", horizon: int = 15):
    """Quick forecast using trained model"""
    
    print(f"🔮 Quick Manufacturing Forecast")
    print(f"Target: {target_variable}")
    print(f"Horizon: {horizon} minutes")
    print("=" * 50)
    
    # Check if model exists
    model_path = f"./models/patchtst_manufacturing_{target_variable}"
    if not Path(model_path).exists():
        print(f"❌ No trained model found for {target_variable}")
        print("\nAvailable models:")
        models_dir = Path("./models")
        for model_dir in models_dir.glob("patchtst_manufacturing_*"):
            target = model_dir.name.replace("patchtst_manufacturing_", "")
            print(f"  - {target}")
        return
    
    # Load model
    print("📥 Loading trained model...")
    model = ManufacturingPatchTSTModel.load_pretrained(model_path)
    
    # Load recent data
    print("📊 Loading manufacturing data...")
    data = pd.read_csv("test-data/consolidated/test_matched_stacks.csv", 
                      comment='#', low_memory=False)
    
    # Clean data types to ensure numeric compatibility
    print("🔧 Cleaning data types...")
    
    # Convert object columns to numeric where possible
    for col in data.columns:
        if data[col].dtype == 'object' and col != 'timestamp':
            # Try to convert to numeric
            data[col] = pd.to_numeric(data[col], errors='coerce')
    
    # Fill any remaining NaN values
    data = data.fillna(0)
    
    # Use recent data for context
    recent_data = data.tail(1000)
    print(f"✅ Using {len(recent_data)} recent records")
    
    # Generate forecast
    print(f"🎯 Generating forecast...")
    result = model.forecast(recent_data, target_variable, horizon)
    
    # Display results
    print("\n📈 FORECAST RESULTS")
    print("=" * 50)
    
    current_value = recent_data[target_variable].iloc[-1]
    print(f"Current {target_variable}: {current_value:.4f}")
    
    print(f"\nNext {horizon} minutes:")
    for i, (timestamp, value) in enumerate(zip(result.forecast_timestamps[:5], result.forecast_values[:5])):
        change = value - current_value
        trend = "📈" if change > 0 else "📉" if change < 0 else "➡️"
        print(f"  {timestamp}: {value:.4f} {trend} ({change:+.4f})")
    
    if len(result.forecast_values) > 5:
        print(f"  ... and {len(result.forecast_values) - 5} more predictions")
    
    # Summary statistics
    import numpy as np
    predictions = result.forecast_values
    avg_change = np.mean(predictions) - current_value
    
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    print(f"Average predicted value: {np.mean(predictions):.4f}")
    print(f"Expected change: {avg_change:+.4f}")
    print(f"Prediction range: {np.min(predictions):.4f} - {np.max(predictions):.4f}")
    
    if avg_change > 0.01:
        print("⚠️  ALERT: Significant increase predicted")
    elif avg_change < -0.01:
        print("✅ GOOD: Decrease predicted")
    else:
        print("➡️  STABLE: Minimal change predicted")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help":
            print("Usage: python quick_forecast.py [target] [horizon]")
            print("\nExamples:")
            print("  python quick_forecast.py fm_reject_pct 15")
            print("  python quick_forecast.py Speed 60")
            print("  python quick_forecast.py sm_scrap_pct 240")
            sys.exit(0)
        
        target = sys.argv[1]
        horizon = int(sys.argv[2]) if len(sys.argv) > 2 else 15
        quick_forecast(target, horizon)
    else:
        quick_forecast()  # Use defaults