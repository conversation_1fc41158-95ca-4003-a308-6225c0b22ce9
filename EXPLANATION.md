# Manufacturing Multi-Method Correlation Analysis Agent System - Comprehensive Technical Report

## Executive Summary

The `/src/agents` folder contains a sophisticated **Manufacturing Multi-Method Correlation Analysis Agent System** built on PydanticAI. This is a production-ready industrial analytics system specifically designed for fiber cement manufacturing data analysis, featuring advanced statistical correlation analysis, intelligent method selection, and comprehensive visualization capabilities.

## System Architecture Mind Map

```
Manufacturing Multi-Method Correlation Analysis Agent System
├── Core Agent (correlation_agent.py)
│   ├── PydanticAI Framework
│   ├── Manufacturing Domain Knowledge
│   ├── Multi-Provider LLM Support (Anthropic/Vertex AI)
│   └── Structured Output (CorrelationAnalysis)
│
├── Tool Ecosystem (20+ Tools)
│   ├── Core Correlation Tools (8 tools)
│   │   ├── calculate_correlation_matrix_tool
│   │   ├── find_significant_correlations_tool
│   │   ├── analyze_lag_correlations_tool
│   │   ├── calculate_thickness_metrics_tool
│   │   ├── analyze_process_correlations_tool
│   │   ├── calculate_data_quality_score_tool
│   │   ├── filter_data_by_time_range_tool
│   │   └── get_variable_summary_tool
│   │
│   ├── Multi-Method Analysis Tools (4 tools)
│   │   ├── calculate_multi_method_correlations_tool
│   │   ├── analyze_method_convergence_tool
│   │   ├── recommend_correlation_method_tool
│   │   └── calculate_robustness_metrics_tool
│   │
│   ├── Visualization Tools (5 tools)
│   │   ├── create_multi_method_heatmaps_tool
│   │   ├── plot_method_convergence_tool
│   │   ├── create_multi_method_dashboard_tool
│   │   ├── plot_method_comparison_matrix_tool
│   │   └── generate_all_visualizations_tool
│   │
│   └── Data Safety Utilities
│       ├── safe_get_dict_value
│       ├── standardize_p_value
│       ├── format_p_value_for_display
│       └── validate_correlation_results_only
│
├── Intelligent Prompt System (8+ Prompts)
│   ├── CORRELATION_SYSTEM_PROMPT (Manufacturing Base)
│   ├── MULTI_METHOD_CORRELATION_PROMPT
│   ├── LAG_CORRELATION_PROMPT
│   ├── PROCESS_OPTIMIZATION_PROMPT
│   ├── QUALITY_ANALYSIS_PROMPT
│   ├── EQUIPMENT_ANALYSIS_PROMPT
│   ├── ROOT_CAUSE_ANALYSIS_PROMPT
│   └── DATA_QUALITY_ASSESSMENT_PROMPT
│
└── Manufacturing Domain Integration
    ├── Fiber Cement Process Knowledge
    ├── Equipment Relationships (FM/SM/Stackers)
    ├── Thickness Sensor Processing (10 sensors)
    ├── Quality Metrics & Process Optimization
    └── Advanced Forecasting Capabilities
        ├── PatchTST Model Training & Inference
        ├── Stability Validation & Monitoring
        ├── Transfer Learning with IBM Granite
        └── Multi-Horizon Prediction Systems
```

## 1. Agent Architecture Overview

### Core Components
- **Main Agent**: `correlation_agent.py` - Central PydanticAI agent with 25+ tools
- **Tool Modules**: 5 specialized tool categories across 6 files
- **Forecasting Engine**: Advanced PatchTST models with stability validation
- **Prompt System**: 8+ domain-specific prompt templates
- **Data Utilities**: Safety and validation utilities for robust operation
- **Visualization Engine**: 5 comprehensive visualization tools
- **Stability Framework**: Comprehensive training stability and validation system

### Agent Configuration
```python
correlation_agent = Agent(
    model=get_model_config(),  # Supports Anthropic Claude & Google Vertex AI
    system_prompt=CORRELATION_SYSTEM_PROMPT,
    output_type=CorrelationAnalysis,  # Structured Pydantic output
    deps_type=ManufacturingDataDependencies
)
```

## 2. Core Agent Logic and Decision Patterns

### Manufacturing Domain Intelligence Mind Map

```
Manufacturing Process Understanding
├── Equipment Flow
│   ├── Forming Machines (FM) → Create thickness profile
│   ├── Sheet Machines (SM) → Cut and process
│   ├── Stackers → Handle products
│   └── Curing Equipment → Control temperature/moisture
│
├── Process Timing
│   ├── Immediate Effects (0-5 min)
│   ├── Short Delays (5-15 min) → Speed to thickness
│   ├── Medium Delays (15-30 min) → Temperature effects
│   └── Long Delays (30-60 min) → Stoppage recovery
│
├── Quality Metrics
│   ├── Thickness Analysis
│   │   ├── thickness_avg = mean(Sensor 01-10)
│   │   ├── thickness_uniformity = std(Sensor 01-10)
│   │   └── thickness_range = max - min sensors
│   ├── Scrap Rates
│   ├── Strength & Density
│   └── Surface Quality
│
└── Critical Variables
    ├── Speed vs Quality Trade-offs
    ├── Temperature → Material Properties
    ├── Pressure → Density Control
    └── Environmental Factors
```

The agent operates with specialized knowledge of fiber cement manufacturing:

**Process Understanding:**
- Forming machines (FM) → Sheet machines (SM) → Stackers → Curing
- Typical process delays: 5-60 minutes between cause and effect
- **Thickness Measurement Logic**: Automatically calculates `thickness_avg` from 10 sensors (Sensor 01-10)
- Quality metrics: thickness uniformity, strength, density, scrap rates

**Analysis Patterns:**
1. **Auto-Detection**: Recognizes multi-method analysis requests from natural language
2. **Context-Aware Prompting**: Selects specialized prompts based on analysis type
3. **Statistical Rigor**: 8-decimal internal precision, 6-decimal display, scientific notation for p < 1e-6
4. **Manufacturing Interpretation**: Physical process explanations for correlation findings

### Decision Flow
```python
# Auto-detect multi-method analysis requests
multi_method_keywords = ['all three methods', 'three methods', 'pearson spearman kendall', 
                        'compare methods', 'method comparison', 'multi-method']
if any(keyword.lower() in query.lower() for keyword in multi_method_keywords):
    analysis_type = 'multi_method'
```

## 3. Tool Ecosystem (20+ Specialized Tools)

### Tool Categories Mind Map

```
Tool Ecosystem (20+ Tools)
├── Core Correlation Tools (tools.py) - 8 tools
│   ├── Basic Analysis
│   │   ├── calculate_correlation_matrix_tool
│   │   ├── find_significant_correlations_tool
│   │   └── get_variable_summary_tool
│   ├── Time-Based Analysis
│   │   ├── analyze_lag_correlations_tool
│   │   ├── analyze_process_correlations_tool
│   │   └── filter_data_by_time_range_tool
│   └── Manufacturing Specific
│       ├── calculate_thickness_metrics_tool
│       └── calculate_data_quality_score_tool
│
├── Multi-Method Analysis (multi_tools.py) - 4 tools
│   ├── calculate_multi_method_correlations_tool
│   ├── analyze_method_convergence_tool
│   ├── recommend_correlation_method_tool
│   └── calculate_robustness_metrics_tool
│
├── Visualization Suite (visualization_tools.py) - 5 tools
│   ├── Static Plots (PNG)
│   │   ├── create_multi_method_heatmaps_tool
│   │   └── plot_method_comparison_matrix_tool
│   ├── Interactive Plots (HTML)
│   │   ├── plot_method_convergence_tool
│   │   └── create_multi_method_dashboard_tool
│   └── Batch Generation
│       └── generate_all_visualizations_tool
│
├── Advanced Forecasting (forecasting_tools.py) - 4 tools
│   ├── Direct Forecasting
│   │   ├── create_forecast_tool
│   │   └── create_forecast_with_agent_tool
│   ├── Manufacturing-Specific
│   │   ├── forecast_manufacturing_parameter_tool
│   │   └── create_multi_horizon_forecast_tool
│
└── Data Safety (data_utils.py) - Critical utilities
    ├── safe_get_dict_value → Handle mixed object/dict access
    ├── standardize_p_value → Convert various formats to float
    ├── format_p_value_for_display → Handle extreme values
    └── validate_correlation_results_only → Type-safe validation
```

### 3.1 Core Correlation Tools (`tools.py`)
**8 Manufacturing-Focused Tools:**

1. **`calculate_correlation_matrix_tool`**
   - **When Used**: Basic correlation analysis requests
   - **Logic**: Calculates correlation matrix with configurable methods (pearson/spearman/kendall)
   - **Manufacturing Context**: Handles mixed-frequency sensor data

2. **`find_significant_correlations_tool`**
   - **When Used**: Discovering statistically significant relationships
   - **Logic**: Filters correlations above threshold with p-value validation
   - **Key Feature**: Reports ALL correlations ≥0.01 (never rounds to zero)

3. **`analyze_lag_correlations_tool`**
   - **When Used**: Time-delayed relationship analysis
   - **Logic**: Tests correlations across 0-60 minute lags
   - **Manufacturing Context**: Accounts for process propagation delays

4. **`calculate_thickness_metrics_tool`**
   - **When Used**: Automatically triggered for thickness analysis
   - **Logic**: 
     ```python
     thickness_avg = mean(Sensor_01 to Sensor_10)
     thickness_uniformity = std(Sensor_01 to Sensor_10)
     thickness_range = max(sensors) - min(sensors)
     ```

5. **`calculate_data_quality_score_tool`**
   - **When Used**: Data validation and quality assessment
   - **Logic**: Combines completeness (40%) + consistency (30%) + validity (30%)

6. **Additional Tools**: `analyze_process_correlations_tool`, `filter_data_by_time_range_tool`, `get_variable_summary_tool`

### 3.2 Multi-Method Analysis Tools (`multi_tools.py`)

### Multi-Method Analysis Decision Tree

```
Multi-Method Analysis Logic
├── Data Distribution Assessment
│   ├── Normality Testing
│   │   ├── Normal Distribution → Pearson preferred
│   │   └── Non-Normal → Spearman/Kendall
│   ├── Outlier Detection
│   │   ├── Significant Outliers → Robust methods
│   │   └── Clean Data → Any method suitable
│   └── Relationship Type
│       ├── Linear → Pearson optimal
│       ├── Monotonic → Spearman optimal
│       └── Non-linear → Kendall most robust
│
├── Method Selection Decision Tree
│   ├── IF significant_outliers OR non_normal:
│   │   ├── IF monotonic: RECOMMEND Spearman
│   │   └── ELSE: RECOMMEND Kendall
│   ├── ELIF normal AND linear: RECOMMEND Pearson
│   ├── ELIF monotonic: RECOMMEND Spearman
│   └── ELSE: RECOMMEND Kendall
│
└── Convergence Analysis
    ├── High Convergence (>0.8) → Robust relationship
    ├── Medium Convergence (0.5-0.8) → Method differences
    └── Low Convergence (<0.5) → Method-dependent results
```

**4 Advanced Statistical Tools:**

1. **`calculate_multi_method_correlations_tool`**
   - **When Used**: "Compare all three methods", "multi-method analysis"
   - **Logic**: Calculates Pearson, Spearman, Kendall simultaneously with convergence analysis
   - **Output**: Method convergence scores, intelligent recommendations

2. **`analyze_method_convergence_tool`**
   - **When Used**: Assessing method agreement patterns
   - **Logic**: 
     ```python
     convergence_score = 1 - normalized_variance(pearson, spearman, kendall)
     if convergence > 0.8: "robust relationship"
     elif convergence > 0.5: "moderate agreement"  
     else: "method-dependent results"
     ```

3. **`recommend_correlation_method_tool`**
   - **When Used**: Method selection guidance requests
   - **Logic**: Data distribution assessment → decision tree algorithm

4. **`calculate_robustness_metrics_tool`**
   - **When Used**: Stability analysis with bootstrap sampling
   - **Logic**: 50-100 bootstrap samples for stability scoring

### 3.3 Visualization Tools (`visualization_tools.py`)

### Visualization Suite Mind Map

```
Visualization Suite (5 Tools)
├── Static Visualizations (PNG)
│   ├── create_multi_method_heatmaps_tool
│   │   ├── Side-by-side correlation heatmaps
│   │   ├── All three methods (Pearson/Spearman/Kendall)
│   │   ├── Convergence scores overlay
│   │   └── Manufacturing-themed styling
│   └── plot_method_comparison_matrix_tool
│       ├── 4-panel comparison analysis
│       ├── Scatter plots & Bland-Altman
│       ├── Difference statistics
│       └── Agreement measurements
│
├── Interactive Visualizations (HTML)
│   ├── plot_method_convergence_tool
│   │   ├── 4-panel convergence analysis
│   │   ├── Cross-method correlations
│   │   ├── Convergence distributions
│   │   └── Method recommendation charts
│   └── create_multi_method_dashboard_tool
│       ├── 9-panel comprehensive dashboard
│       ├── Interactive scatter plots
│       ├── Method stability charts
│       └── P-value comparisons
│
└── Batch Generation
    └── generate_all_visualizations_tool
        ├── Complete suite (6 files)
        ├── 3 PNG static plots
        ├── 3 HTML interactive dashboards
        └── Automated file management
```

**5 Comprehensive Visualization Tools:**

1. **`create_multi_method_heatmaps_tool`**
   - **Output**: Side-by-side PNG heatmaps for all three methods
   - **When Used**: Visualization requests, automatic generation

2. **`plot_method_convergence_tool`** 
   - **Output**: Interactive HTML with 4-panel convergence analysis
   - **Features**: Cross-method correlations, convergence distributions

3. **`create_multi_method_dashboard_tool`**
   - **Output**: 9-panel interactive HTML dashboard
   - **Panels**: Matrices, scatter plots, box plots, distributions, recommendations

4. **`plot_method_comparison_matrix_tool`**
   - **Output**: 4-panel detailed method comparison
   - **Features**: Scatter plots, Bland-Altman analysis, difference statistics

5. **`generate_all_visualizations_tool`**
   - **Output**: Complete visualization suite (6 files: 3 PNG + 3 HTML)
   - **When Used**: Comprehensive analysis requests

### 3.4 Advanced Forecasting Tools (`forecasting_tools.py`)
**4 Manufacturing-Specific Forecasting Tools:**

1. **`create_forecast_tool`**
   - **When Used**: Direct forecasting requests with data
   - **Logic**: Creates PatchTST model, trains, and generates forecasts
   - **Output**: Forecast values with confidence intervals

2. **`create_forecast_with_agent_tool`**
   - **When Used**: Natural language forecasting requests
   - **Logic**: AI-powered interpretation and forecast generation
   - **Features**: Intelligent parameter selection, manufacturing insights

3. **`forecast_manufacturing_parameter_tool`**
   - **When Used**: Manufacturing-specific parameter forecasting
   - **Logic**: Specialized for thickness, speed, quality metrics
   - **Manufacturing Context**: Process-aware predictions and recommendations

4. **`create_multi_horizon_forecast_tool`**
   - **When Used**: Multiple time horizon predictions
   - **Logic**: 15-min, 1-hour, 4-hour, 24-hour forecasts
   - **Output**: Comprehensive forecasting suite with uncertainty quantification

**Enhanced Features:**
- **Stability Validation**: Comprehensive training stability checks
- **Transfer Learning**: IBM Granite model integration
- **Manufacturing Intelligence**: Process-specific insights and optimization
- **Production-Ready**: Robust error handling and fallback mechanisms

### 3.5 Data Safety Utilities (`data_utils.py`)
**Critical Production Safety Features:**
- **`safe_get_dict_value`**: Handles mixed object/dictionary access
- **`standardize_p_value`**: Converts various p-value formats to float
- **`format_p_value_for_display`**: Handles extremely small p-values (< 1e-15)
- **`validate_correlation_results_only`**: Type-safe data structure validation

## 4. Intelligent Prompt System

### Prompt Selection Mind Map

```
Intelligent Prompt System (8+ Specialized Prompts)
├── Core Manufacturing Prompt
│   ├── CORRELATION_SYSTEM_PROMPT (Base)
│   │   ├── Fiber cement process knowledge
│   │   ├── Equipment relationships
│   │   ├── Thickness sensor logic (10 sensors)
│   │   └── Statistical requirements
│   └── Manufacturing Domain Rules
│       ├── Report ALL correlations (never round to zero)
│       ├── Include small correlations (0.01-0.1)
│       ├── Auto-use multi-method tools
│       └── Generate visualizations when requested
│
├── Analysis-Specific Prompts
│   ├── MULTI_METHOD_CORRELATION_PROMPT
│   │   ├── Three method expertise
│   │   ├── Convergence interpretation
│   │   ├── Method selection guidance
│   │   └── Manufacturing context
│   ├── LAG_CORRELATION_PROMPT
│   │   ├── Process delay understanding
│   │   ├── Propagation patterns
│   │   └── Timing optimization
│   ├── QUALITY_ANALYSIS_PROMPT
│   │   ├── Scrap reduction focus
│   │   ├── Thickness uniformity
│   │   └── Quality metrics
│   └── PROCESS_OPTIMIZATION_PROMPT
│       ├── Efficiency targets
│       ├── Operating ranges
│       └── Early warning systems
│
├── Specialized Analysis Types
│   ├── EQUIPMENT_ANALYSIS_PROMPT
│   ├── ROOT_CAUSE_ANALYSIS_PROMPT
│   ├── DATA_QUALITY_ASSESSMENT_PROMPT
│   ├── METHOD_CONVERGENCE_ANALYSIS_PROMPT
│   ├── METHOD_SELECTION_GUIDANCE_PROMPT
│   └── ROBUSTNESS_ANALYSIS_PROMPT
│
└── Dynamic Selection Logic
    ├── Auto-detection from query keywords
    ├── Analysis type classification
    ├── Context-aware prompt enhancement
    └── Manufacturing domain adaptation
```

### 4.1 Core Manufacturing Prompt (`CORRELATION_SYSTEM_PROMPT`)
**Manufacturing Domain Knowledge:**
```
- Fiber cement production: forming → cutting → stacking → curing
- Key quality metrics: thickness uniformity, strength, density, scrap rates
- Typical process delays: 5-60 minutes between cause and effect
- Thickness = average of Sensor 01-10 measurements
```

**Analysis Requirements:**
- Report ALL correlations with full precision (never round to zero)
- Include small correlations (0.01-0.1) as industrially significant
- For multi-method requests: Use multi-method tools automatically
- For visualization requests: Generate appropriate plots

### 4.2 Specialized Analysis Prompts (8 Types)

1. **`LAG_CORRELATION_PROMPT`**
   - **When Used**: Time-delay analysis requests
   - **Focus**: Process propagation delays, optimal lag identification

2. **`PROCESS_OPTIMIZATION_PROMPT`**
   - **When Used**: "optimize", "improve efficiency" requests
   - **Focus**: Target operating ranges, early warning indicators

3. **`QUALITY_ANALYSIS_PROMPT`**
   - **When Used**: Quality, scrap, defect analysis
   - **Focus**: Thickness uniformity, scrap reduction strategies

4. **`MULTI_METHOD_CORRELATION_PROMPT`**
   - **When Used**: Multi-method analysis detection
   - **Requirements**: 
     ```
     1. ALWAYS use calculate_multi_method_correlations_tool_wrapper first
     2. Present ALL THREE correlation values with confidence intervals
     3. When visualizations requested: Use visualization tools
     4. Manufacturing process interpretation using recommended method
     ```

5. **Additional Prompts**: Equipment analysis, root cause analysis, data quality assessment, method convergence, robustness analysis

### 4.3 Dynamic Prompt Selection
```python
def get_correlation_prompt(analysis_type: str) -> str:
    prompts = {
        "general": CORRELATION_SYSTEM_PROMPT,
        "multi_method": MULTI_METHOD_CORRELATION_PROMPT,
        "lag": LAG_CORRELATION_PROMPT,
        "optimization": PROCESS_OPTIMIZATION_PROMPT,
        # ... 8 total specialized prompts
    }
    return prompts.get(analysis_type, CORRELATION_SYSTEM_PROMPT)
```

## 5. Data Flow and Processing Patterns

### Data Flow Mind Map

```
Data Flow & Processing Patterns
├── Input Data Structure
│   ├── ManufacturingDataDependencies
│   │   ├── data: pd.DataFrame (Manufacturing sensor data)
│   │   ├── time_column: str = 'timestamp'
│   │   ├── target_variables: Optional[List[str]]
│   │   ├── significance_threshold: float = 0.05
│   │   ├── min_correlation: float = 0.01
│   │   └── analysis_type: str = 'general'
│   └── Auto-Detection Logic
│       ├── Multi-method keywords → analysis_type = 'multi_method'
│       ├── Lag keywords → analysis_type = 'lag'
│       └── Quality keywords → analysis_type = 'quality'
│
├── Processing Pipeline
│   ├── Data Validation
│   │   ├── Structure validation
│   │   ├── Missing value assessment
│   │   └── Quality scoring
│   ├── Analysis Execution
│   │   ├── Tool selection based on query
│   │   ├── Multi-method calculation
│   │   └── Visualization generation
│   └── Safety Mechanisms
│       ├── Type validation
│       ├── Error handling
│       └── Graceful degradation
│
└── Output Data Structure
    ├── CorrelationAnalysis
    │   ├── dataset_summary: Dataset characteristics
    │   ├── significant_correlations: ALL methods when multi-method
    │   ├── correlation_matrix: Full correlation matrix
    │   ├── insights: Manufacturing process insights
    │   ├── recommendations: Actionable improvements
    │   ├── data_quality_score: 0-1 composite score
    │   └── analysis_metadata: Parameters & timestamps
    └── MultiMethodCorrelationResult (for detailed analysis)
        ├── Three correlation values (Pearson/Spearman/Kendall)
        ├── Three p-values with confidence intervals
        ├── Method convergence score (0-1)
        ├── Recommended method with justification
        └── Data distribution assessment
```

### 5.1 Input Data Structure
```python
class ManufacturingDataDependencies(BaseModel):
    data: pd.DataFrame  # Manufacturing sensor data
    time_column: str = 'timestamp'
    target_variables: Optional[List[str]] = None
    significance_threshold: float = 0.05
    min_correlation: float = 0.01  # Captures small but significant correlations
    analysis_type: str = 'general'
```

### 5.2 Output Data Structure
```python
class CorrelationAnalysis(BaseModel):
    dataset_summary: Dict[str, Any]
    significant_correlations: List[Dict[str, Any]]  # ALL methods when multi-method
    correlation_matrix: Optional[Dict[str, Dict[str, float]]]
    insights: List[CorrelationInsight]  # Manufacturing process insights
    recommendations: List[str]  # Actionable process improvements
    data_quality_score: float  # 0-1 composite score
    analysis_metadata: Dict[str, Any]
```

### 5.3 Multi-Method Data Structure
```python
class MultiMethodCorrelationResult(BaseModel):
    variable_1: str
    variable_2: str
    pearson_correlation: float
    spearman_correlation: float  
    kendall_correlation: float
    pearson_p_value: float
    spearman_p_value: float
    kendall_p_value: float
    method_convergence_score: float  # 0-1 agreement measure
    recommended_method: str  # Based on data characteristics
    data_distribution_assessment: Dict  # Normality, outliers, linearity
```

## 6. Error Handling and Production Reliability

### Production Reliability Mind Map

```
Error Handling & Production Reliability
├── Critical Error Prevention (Phase 2.1 Lessons)
│   ├── GUI Threading Issues
│   │   ├── matplotlib.use('Agg') before imports
│   │   ├── Headless operation for servers
│   │   └── NSWindow thread error prevention
│   ├── Data Structure Validation
│   │   ├── Mixed object/dictionary handling
│   │   ├── Type-specific validation
│   │   └── None/NoneType safety checks
│   └── P-value Handling
│       ├── Numeric vs formatted separation
│       ├── Extremely small value handling (<1e-15)
│       └── Scientific notation formatting
│
├── Safety Patterns
│   ├── safe_get_dict_value() for mixed access
│   ├── validate_correlation_results_only() for type safety
│   ├── standardize_p_value() for format consistency
│   └── Backend detection & safe plotting
│
├── Cross-Platform Compatibility
│   ├── macOS: NSWindow threading fixes
│   ├── Linux/Windows: Consistent behavior
│   ├── Container deployment: Headless config
│   └── CLI vs Web: Context-aware backends
│
└── Production Deployment Checklist
    ├── ✅ Matplotlib backend configured
    ├── ✅ Safe data access methods
    ├── ✅ P-value formatting handles extremes
    ├── ✅ Visualization None checks
    ├── ✅ Error messages include debug info
    └── ✅ String vs float comparisons eliminated
```

### 6.1 Critical Error Prevention Patterns
```python
# GUI Threading Protection (Phase 2.1 Production Issue)
import matplotlib
matplotlib.use('Agg')  # Prevents NSWindow threading issues on macOS

# Safe Data Access Pattern
def safe_get_dict_value(data: Any, key: str, default: Any = None):
    if hasattr(data, key): return getattr(data, key)
    if isinstance(data, dict): return data.get(key, default)
    return default

# P-value Safety
def format_p_value_for_display(p_value: float) -> str:
    if p_value < 1e-15: return "< 1e-15"
    if p_value >= 1e-3: return f"{p_value:.6f}"
    return f"{p_value:.3e}"
```

### 6.2 Data Structure Validation
```python
def validate_correlation_results_only(correlation_results: Any) -> Dict[str, Dict[str, Any]]:
    """Production-safe validation with type conversion"""
    standardized_results = {}
    for pair_key, result_data in correlation_results.items():
        if hasattr(result_data, 'variable_1'):  # MultiMethodCorrelationResult
            # Convert object to dict with safe extraction
        elif isinstance(result_data, dict):
            # Validate dictionary structure
        # Standardize all p-values to float
```

## 7. Manufacturing Domain Integration

### Manufacturing Integration Mind Map

```
Manufacturing Domain Integration
├── Fiber Cement Manufacturing Expertise
│   ├── Equipment Relationships
│   │   ├── Forming Machines (FM) → Initial thickness profile
│   │   ├── Sheet Machines (SM) → Cut and process materials
│   │   ├── Stackers → Handle finished products
│   │   └── Curing Equipment → Temperature/moisture control
│   ├── Process Dependencies
│   │   ├── Upstream quality impacts downstream
│   │   ├── Equipment efficiency interdependencies
│   │   ├── Maintenance scheduling impacts
│   │   └── Capacity bottlenecks identification
│   └── Critical Process Variables
│       ├── Speed vs Quality Trade-offs
│       ├── Temperature → Material Properties
│       ├── Pressure → Density Control
│       └── Environmental Factors
│
├── Thickness Measurement System
│   ├── 10-Sensor Array (Sensor 01-10)
│   ├── thickness_avg = mean(all sensors)
│   ├── thickness_uniformity = std(all sensors)
│   ├── thickness_range = max - min sensors
│   └── Manufacturing Context
│       ├── Thickness = average across product width
│       ├── Uniformity indicates quality consistency
│       └── Range analysis for quality assessment
│
├── Quality Metrics & KPIs
│   ├── Primary Metrics
│   │   ├── Thickness uniformity
│   │   ├── Scrap rates
│   │   ├── Strength measurements
│   │   └── Density control
│   ├── Process Indicators
│   │   ├── Equipment efficiency
│   │   ├── Energy consumption
│   │   ├── Production speed
│   │   └── Environmental conditions
│   └── Quality Assurance
│       ├── Defect detection
│       ├── Surface quality
│       ├── Dimensional accuracy
│       └── Material properties
│
└── Insight Generation Patterns
    ├── Statistical Evidence + Manufacturing Context
    ├── Process Physics Explanations
    ├── Actionable Recommendations
    └── Confidence Level Assessment
```

### 7.1 Fiber Cement Manufacturing Expertise
**Equipment Relationships:**
- **Forming Machines (FM)**: Create initial thickness profile
- **Sheet Machines (SM)**: Cut and process materials  
- **Stackers**: Handle finished products
- **Process Dependencies**: Upstream quality impacts downstream operations

**Critical Variables:**
- **Thickness**: `thickness_avg = mean(Sensor_01 to Sensor_10)`
- **Uniformity**: `thickness_uniformity = std(Sensor_01 to Sensor_10)`
- **Speed vs Quality**: Higher speeds increase variation
- **Stoppage Recovery**: 15-60 minute quality impact windows

### 7.2 Manufacturing Insights Generation
```python
class CorrelationInsight(BaseModel):
    insight_type: str  # "statistical", "process", "quality", etc.
    description: str  # Human-readable manufacturing insight
    supporting_evidence: Dict[str, Any]  # Statistical backing
    confidence_level: str  # "high", "medium", "low"
    actionable_recommendation: Optional[str]  # Specific process action
```

**Example Manufacturing Insights:**
- "Speed increases above 160 m/min correlate with thickness variation (r=0.34, p<0.001)"
- "15-minute lag correlation between temperature and thickness suggests thermal propagation delay"
- "Sensor 03 shows highest correlation with reject rates, indicating potential calibration issue"

## 8. Provider Support and Configuration

### Configuration Mind Map

```
Provider Support & Configuration
├── Multi-Provider LLM Support
│   ├── Anthropic Configuration
│   │   ├── ANTHROPIC_API_KEY
│   │   ├── ANTHROPIC_MODEL
│   │   └── Example: claude-3-5-sonnet-20241022
│   └── Google Vertex AI Configuration
│       ├── VERTEX_AI_PROJECT
│       ├── VERTEX_AI_LOCATION
│       ├── GEMINI_MODEL
│       └── Example: gemini-1.5-pro
│
├── Environment Variables (Required)
│   ├── LLM_PROVIDER (ANTHROPIC/VERTEX_AI)
│   ├── API Keys & Credentials
│   ├── Model Specifications
│   └── Regional Settings (for Vertex AI)
│
├── Configuration Logic
│   ├── Provider Detection
│   ├── Credential Validation
│   ├── Model Configuration
│   └── Error Handling for Missing Config
│
└── Deployment Flexibility
    ├── Local Development
    ├── Cloud Deployment
    ├── Container Support
    └── CI/CD Integration
```

### 8.1 Multi-Provider LLM Support
```python
def get_model_config():
    provider = os.getenv('LLM_PROVIDER', 'ANTHROPIC').upper()
    
    if provider == 'ANTHROPIC':
        return os.getenv('ANTHROPIC_MODEL')  # e.g., 'claude-3-5-sonnet-20241022'
    elif provider == 'VERTEX_AI':
        return VertexAIModel(model, project=project, location=location)
```

### 8.2 Required Environment Variables
```bash
# Anthropic Configuration
ANTHROPIC_API_KEY=sk-ant-...
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# Vertex AI Configuration  
VERTEX_AI_PROJECT=your-project-id
VERTEX_AI_LOCATION=us-central1
GEMINI_MODEL=gemini-1.5-pro

# Agent Configuration
LLM_PROVIDER=ANTHROPIC  # or VERTEX_AI
```

## 9. Usage Patterns and API

### Usage Patterns Mind Map

```
Usage Patterns & API
├── Main Analysis Function
│   ├── analyze_manufacturing_correlations()
│   ├── Input: DataFrame + Query + Parameters
│   ├── Output: CorrelationAnalysis (structured)
│   └── Auto-detection: analysis_type from query
│
├── Query Auto-Detection Examples
│   ├── Multi-Method Triggers
│   │   ├── "Compare Pearson, Spearman, and Kendall"
│   │   ├── "Show me all three correlation methods"
│   │   ├── "Which correlation method should I use?"
│   │   └── "Multi-method analysis"
│   ├── Lag Analysis Triggers
│   │   ├── "Analyze lag correlations between X and Y"
│   │   ├── "How long does X take to affect Y?"
│   │   └── "Time-delayed relationships"
│   ├── Quality Analysis Triggers
│   │   ├── "What factors correlate with scrap rates?"
│   │   ├── "Analyze thickness variation correlations"
│   │   └── "Quality improvement analysis"
│   └── Visualization Triggers
│       ├── "Show me heatmaps"
│       ├── "Create dashboard"
│       └── "Generate all visualizations"
│
├── Agent Factory Functions
│   ├── create_correlation_agent() → Main agent
│   ├── create_specialized_correlation_agent() → Custom analysis
│   └── Prompt customization support
│
└── Response Formats
    ├── Structured Pydantic Models
    ├── Manufacturing Insights
    ├── Statistical Evidence
    └── Actionable Recommendations
```

### 9.1 Main Analysis Function
```python
async def analyze_manufacturing_correlations(
    data: pd.DataFrame,
    query: str = "Analyze correlations in manufacturing data",
    time_column: str = 'timestamp',
    target_variables: Optional[List[str]] = None,
    analysis_type: str = 'general'  # Auto-detected from query
) -> CorrelationAnalysis
```

### 9.2 Auto-Detection Examples
```python
# These queries trigger multi-method analysis:
"Compare Pearson, Spearman, and Kendall correlations"
"Show me all three correlation methods"
"Which correlation method should I use?"

# These queries trigger lag analysis:
"Analyze lag correlations between speed and thickness"
"How long does temperature take to affect quality?"

# These queries trigger quality analysis:
"What factors correlate with high scrap rates?"
"Analyze thickness variation correlations"
```

## 10. Performance and Scalability

### Performance & Scalability Mind Map

```
Performance & Scalability
├── Production Optimizations
│   ├── Matplotlib Backend
│   │   ├── matplotlib.use('Agg') for headless
│   │   ├── Prevents GUI threading issues
│   │   └── Server/container compatible
│   ├── Memory Management
│   │   ├── Automatic figure cleanup (plt.close)
│   │   ├── Streaming correlation calculations
│   │   └── Efficient data structure handling
│   ├── Bootstrap Sampling
│   │   ├── Configurable sample sizes
│   │   ├── 50-100 samples for production
│   │   └── Performance vs accuracy trade-off
│   └── Precision Control
│       ├── 8-decimal internal calculations
│       ├── 6-decimal display formatting
│       └── Scientific notation for small p-values
│
├── Data Handling Capacity
│   ├── Validated Scale: 262K+ records
│   ├── Memory Efficient: Streaming calculations
│   ├── Robust Error Handling: Graceful degradation
│   └── Caching Strategy: Processed datasets
│
├── Scalability Features
│   ├── Parallel Tool Execution
│   ├── Batch Visualization Generation
│   ├── Configurable Analysis Depth
│   └── Resource Usage Monitoring
│
└── Performance Monitoring
    ├── Analysis Time Tracking
    ├── Memory Usage Assessment
    ├── API Response Times
    └── Error Rate Monitoring
```

### 10.1 Production Optimizations
- **Matplotlib Backend**: `matplotlib.use('Agg')` for headless operation
- **Memory Management**: Automatic figure cleanup with `plt.close(fig)`
- **Bootstrap Sampling**: Configurable sample sizes (50-100 for production)
- **Precision Control**: 8-decimal calculations, 6-decimal display
- **Caching Strategy**: Processed datasets cached to avoid recomputation

### 10.2 Data Handling Capacity
- **Validated Scale**: 262K+ manufacturing records in testing
- **Memory Efficient**: Streaming correlation calculations
- **Robust Error Handling**: Graceful degradation with informative error messages

## 11. Testing and Validation

### Testing & Validation Mind Map

```
Testing & Validation (Phase 2.1)
├── Test Coverage Statistics
│   ├── 73 Comprehensive Tests
│   ├── Real API Integration (No Mocking)
│   ├── Complete Visualization Suite Testing
│   └── Robust Error Handling Validation
│
├── Multi-Method Testing Requirements
│   ├── All Three Methods Tested
│   │   ├── Pearson correlation validation
│   │   ├── Spearman correlation validation
│   │   └── Kendall correlation validation
│   ├── Method Convergence Testing
│   │   ├── Convergence score calculation
│   │   ├── Agreement pattern analysis
│   │   └── Robustness metrics validation
│   ├── Intelligent Method Selection
│   │   ├── Data distribution assessment
│   │   ├── Decision tree validation
│   │   └── Recommendation accuracy
│   └── Bootstrap Sampling Validation
│       ├── Stability analysis testing
│       ├── Sample size optimization
│       └── Confidence interval validation
│
├── Visualization Testing
│   ├── All 5 Tools Validated
│   │   ├── PNG generation testing
│   │   ├── HTML output validation
│   │   ├── Interactive feature testing
│   │   └── File management verification
│   ├── Agent Integration Testing
│   │   ├── Automatic tool selection
│   │   ├── Parameter passing validation
│   │   └── Output format consistency
│   └── Cross-Platform Compatibility
│       ├── macOS testing
│       ├── Linux validation
│       └── Windows compatibility
│
└── Production Reliability Testing
    ├── Error Scenario Coverage
    │   ├── Data quality edge cases
    │   ├── API failure handling
    │   ├── Memory limitation testing
    │   └── Concurrent request handling
    ├── Data Structure Validation
    │   ├── Type checking verification
    │   ├── Format conversion testing
    │   └── Safety mechanism validation
    └── Performance Testing
        ├── Large dataset handling (262K+ records)
        ├── Memory usage profiling
        └── Response time optimization
```

### 11.1 Test Coverage (Phase 2.1)
- **73 Comprehensive Tests**: Real API integration, no mocking
- **Multi-Method Validation**: All three correlation methods tested
- **Visualization Testing**: All 5 visualization tools validated
- **Error Scenario Coverage**: Data quality, edge cases, API failures

### 11.2 Production Reliability Features
- **Data Structure Safety**: Type checking and validation
- **Cross-Platform Compatibility**: macOS, Linux, Windows support
- **Container Ready**: Docker/cloud deployment configuration
- **Audit Logging**: All agent decisions and outputs logged

## 12. Key Operational Patterns

### Operational Patterns Mind Map

```
Key Operational Patterns
├── Tool Selection Logic
│   ├── Query Analysis
│   │   ├── Keyword detection
│   │   ├── Intent classification
│   │   └── Context understanding
│   ├── Auto-Tool Selection
│   │   ├── Multi-method keywords → multi_tools
│   │   ├── Visualization requests → viz_tools
│   │   ├── Lag analysis → lag_tools
│   │   └── Quality focus → quality_tools
│   └── Intelligent Cascading
│       ├── Primary analysis tools first
│       ├── Visualization tools when appropriate
│       └── Follow-up analysis as needed
│
├── Data Processing Workflow
│   ├── Input Validation
│   │   ├── DataFrame structure check
│   │   ├── Required columns verification
│   │   └── Data quality assessment
│   ├── Manufacturing Context Application
│   │   ├── Thickness sensor processing
│   │   ├── Time column alignment
│   │   └── Process variable identification
│   ├── Statistical Analysis Execution
│   │   ├── Method selection based on data
│   │   ├── Correlation calculation
│   │   └── Significance testing
│   └── Insight Generation
│       ├── Statistical interpretation
│       ├── Manufacturing context application
│       └── Actionable recommendation creation
│
├── Error Recovery Patterns
│   ├── Graceful Degradation
│   │   ├── Partial analysis on tool failure
│   │   ├── Alternative method selection
│   │   └── Informative error messages
│   ├── Data Quality Adaptation
│   │   ├── Missing value handling
│   │   ├── Outlier detection and treatment
│   │   └── Minimum sample size enforcement
│   └── Visualization Fallbacks
│       ├── Static plots when interactive fails
│       ├── Simplified visualizations for limited data
│       └── Text-based summaries as backup
│
└── Manufacturing Integration Patterns
    ├── Domain Knowledge Application
    │   ├── Process timing understanding
    │   ├── Equipment relationship awareness
    │   └── Quality metric interpretation
    ├── Contextual Analysis
    │   ├── Lag correlation for process delays
    │   ├── Thickness uniformity calculations
    │   └── Equipment efficiency analysis
    └── Actionable Insight Generation
        ├── Process optimization recommendations
        ├── Quality improvement strategies
        └── Predictive maintenance insights
```

## Conclusion

This Manufacturing Multi-Method Correlation Analysis Agent System represents a production-ready industrial analytics platform with:

### Core Strengths

1. **Advanced Statistical Capabilities**: Three correlation methods with intelligent selection
2. **Manufacturing Domain Expertise**: Fiber cement process knowledge integration  
3. **Comprehensive Visualization**: 5 professional visualization tools
4. **Production Reliability**: Robust error handling and safety mechanisms
5. **Flexible Configuration**: Multi-provider LLM support
6. **Scalable Architecture**: Handles large-scale manufacturing datasets

### System Benefits

- **Operational Intelligence**: Converts raw manufacturing data into actionable insights
- **Quality Improvement**: Identifies factors affecting product quality and scrap rates
- **Process Optimization**: Provides specific recommendations for efficiency improvements
- **Predictive Capabilities**: Lag correlation analysis enables proactive quality control
- **Statistical Rigor**: High-precision analysis with proper significance testing
- **Visual Communication**: Professional visualizations for stakeholder presentations

### Production Readiness

The system successfully bridges advanced statistical analysis with practical manufacturing insights, providing actionable recommendations for:

- **Process Optimization**: Speed, temperature, and pressure settings
- **Quality Improvement**: Thickness uniformity and scrap reduction
- **Predictive Maintenance**: Equipment performance correlation patterns
- **Root Cause Analysis**: Statistical evidence for quality issues
- **Operational Efficiency**: Equipment interdependency optimization

This represents a sophisticated yet practical solution for manufacturing analytics, combining academic-level statistical rigor with industry-focused domain expertise and production-ready reliability.