name: "Enhanced Multi-Method Correlation Analysis - Feature 2 Implementation"
description: |

## Purpose
Implement comprehensive multi-method correlation analysis supporting <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlation methods with comparative reporting, advanced visualization, and intelligent analysis recommendations.

## Core Principles
1. **Context is King**: Include ALL necessary documentation, examples, and caveats
2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
3. **Information Dense**: Use keywords and patterns from the codebase
4. **Progressive Success**: Start simple, validate, then enhance
5. **Global rules**: Be sure to follow all rules in CLAUDE.md

---

## Goal
Build a comprehensive multi-method correlation analysis system that extends the existing single-method correlation capabilities to support all three major correlation methods (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>) with side-by-side comparison, automatic method selection, robustness analysis, and enhanced visualization suite.

## Why
- **Enhanced Statistical Accuracy**: Manufacturing data often contains outliers and non-normal distributions; different correlation methods provide varying robustness levels
- **Business Intelligence**: Process engineers need to understand which correlation method provides the most reliable insights for specific manufacturing scenarios  
- **Comprehensive Analysis**: Current system only supports single-method analysis; multi-method comparison reveals data characteristics and correlation stability
- **Manufacturing Domain Expertise**: Fiber cement production involves mixed-frequency data that benefits from robust correlation methods (<PERSON><PERSON><PERSON>/<PERSON>) when dealing with sensor outliers

## What
Extend the existing correlation analysis system with multi-method capabilities, comparative reporting, and intelligent analysis features.

### Success Criteria
- [ ] Support all three correlation methods (Pearson, Spearman, Kendall) with statistical significance testing
- [ ] Side-by-side correlation heatmaps with method comparison visualizations
- [ ] Automatic data distribution analysis and correlation method recommendations
- [ ] Method convergence analysis and robustness metrics across different time periods
- [ ] Enhanced agent tools and new PydanticAI response types for multi-method analysis
- [ ] Comprehensive test suite covering all methods and edge cases
- [ ] Production validation with existing 295K+ manufacturing records

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://realpython.com/numpy-scipy-pandas-correlation-python/
  why: Comprehensive guide to correlation methods in Python with practical examples
  
- url: https://seaborn.pydata.org/generated/seaborn.heatmap.html
  why: Official seaborn heatmap documentation for side-by-side visualization patterns
  
- url: https://pingouin-stats.org/build/html/generated/pingouin.corr.html
  why: Robust correlation methods including biweight midcorrelation and percentage bend
  critical: Pingouin provides TabWil, Taba, and other robust methods for outlier-resistant analysis
  
- url: https://library.virginia.edu/data/articles/correlation-pearson-spearman-and-kendalls-tau
  why: When to use each correlation method based on data characteristics
  
- file: src/agents/correlation_agent.py
  why: Existing agent patterns, tool registration, and PydanticAI structure to follow
  
- file: src/data/correlations.py
  why: Current correlation calculation patterns and ManufacturingCorrelationAnalyzer class structure
  
- file: src/agents/tools.py  
  why: Tool function patterns and context handling for PydanticAI agents
  critical: Use 8-decimal internal precision, 6-decimal display for consistency
  
- file: tests/test_correlations.py
  why: Existing test patterns and fixture setup for correlation analysis testing
  
- file: src/visualization/plots.py
  why: Current visualization patterns and matplotlib/seaborn usage conventions
```

### Current Codebase Tree
```bash
test_1/                                # ✅ Production-ready project
├── src/                               # ✅ Core source code
│   ├── agents/                        # ✅ AI agent system
│   │   ├── correlation_agent.py       # ✅ Main PydanticAI agent (295K+ records validated)
│   │   ├── tools.py                   # ✅ 7 specialized analysis tools
│   │   └── prompts.py                 # ✅ Manufacturing domain prompts
│   ├── data/                          # ✅ Enterprise data processing
│   │   ├── loader.py                  # ✅ Advanced CSV loader (99.5% alignment success)
│   │   └── correlations.py            # ✅ Statistical correlation engine
│   └── visualization/                 # ✅ Comprehensive visualization suite
│       └── plots.py                   # ✅ Heatmaps, scatter plots, dashboards
├── tests/                             # ✅ Comprehensive testing suite (71 tests)
│   ├── test_data_loader.py            # ✅ Data loading validation tests (22 tests)
│   ├── test_correlations.py           # ✅ Statistical analysis tests (32 tests)  
│   └── test_agent.py                  # ✅ Agent functionality tests (17 tests)
```

### Desired Codebase Tree with Files to be Added
```bash
test_1/
├── src/
│   ├── data/
│   │   └── multi_correlations.py      # NEW: MultiMethodCorrelationAnalyzer class
│   ├── agents/
│   │   └── multi_tools.py             # NEW: Multi-method agent tools  
│   └── visualization/
│       └── multi_plots.py             # NEW: Multi-method visualization functions
├── tests/
│   ├── test_multi_correlations.py     # NEW: Multi-method analysis tests
│   ├── test_multi_tools.py            # NEW: Multi-method tool tests
│   └── test_multi_plots.py            # NEW: Multi-method visualization tests
```

### Known Gotchas of our Codebase & Library Quirks
```python
# CRITICAL: PydanticAI requires output_type instead of deprecated result_type
# Example: Agent(model=model, output_type=CorrelationAnalysis)  # CORRECT

# CRITICAL: Use load_dotenv(override=True) for reliable configuration loading
# Example: load_dotenv(override=True)  # Not just load_dotenv()

# CRITICAL: Manufacturing data precision requirements
# Internal calculations: 8-decimal precision for correlation coefficients
# Display values: 6-decimal precision for readability
# P-values: Scientific notation for very small values (< 1e-6)

# CRITICAL: Environment variables must be set - no fallback values
# LLM_PROVIDER, ANTHROPIC_API_KEY/VERTEX_AI_PROJECT required - system fails with clear errors

# CRITICAL: Test pattern requirements  
# Use REAL API KEYS in tests - no mocking for LLM services
# Each test file needs fixtures mirroring main app structure
# Minimum tests: 1 expected use, 1 edge case, 1 failure case

# CRITICAL: Correlation method computational complexity
# Kendall correlation is O(n²) - significantly slower than Pearson/Spearman
# For large datasets (>10K records), consider sampling or parallel processing

# CRITICAL: Statistical significance testing requirements
# Must calculate p-values for all correlation methods
# Use scipy.stats functions: pearsonr, spearmanr, kendalltau for consistency
# Handle NaN values gracefully in correlation calculations
```

## Implementation Blueprint

### Data Models and Structure
Create enhanced data models for multi-method correlation analysis ensuring type safety and consistency.

```python
# Enhanced Pydantic models for multi-method analysis
class MultiMethodCorrelationResult(BaseModel):
    variable_1: str
    variable_2: str
    pearson_correlation: float
    spearman_correlation: float  
    kendall_correlation: float
    pearson_p_value: float
    spearman_p_value: float
    kendall_p_value: float
    sample_size: int
    method_convergence_score: float  # How similar are the three methods
    recommended_method: str          # Based on data characteristics
    data_distribution_assessment: Dict[str, Any]

class MethodComparisonAnalysis(BaseModel):
    correlation_results: List[MultiMethodCorrelationResult]
    method_stability_analysis: Dict[str, float]  # Variance across methods
    data_quality_assessment: Dict[str, Any]
    visualization_paths: List[str]
    insights: List[CorrelationInsight]
    recommendations: List[str]
```

### List of Tasks to be Completed (in order)

```yaml
Task 1: CREATE src/data/multi_correlations.py
  - EXTEND ManufacturingCorrelationAnalyzer patterns from src/data/correlations.py
  - ADD MultiMethodCorrelationAnalyzer class with all three correlation methods
  - IMPLEMENT data distribution assessment (normality tests, outlier detection)
  - ADD method convergence analysis and robustness metrics
  - PRESERVE existing method signatures and error handling patterns

Task 2: CREATE src/agents/multi_tools.py  
  - MIRROR tool patterns from src/agents/tools.py
  - ADD calculate_multi_method_correlations tool function
  - ADD analyze_method_convergence tool function
  - ADD recommend_correlation_method tool function
  - KEEP 8-decimal precision and context handling patterns identical

Task 3: MODIFY src/agents/correlation_agent.py
  - REGISTER new multi-method tools from Task 2
  - ADD MultiMethodCorrelationResult to output types
  - PRESERVE existing tool registration patterns
  - MAINTAIN backward compatibility with existing tools

Task 4: CREATE src/visualization/multi_plots.py
  - FOLLOW visualization patterns from src/visualization/plots.py  
  - IMPLEMENT side-by-side correlation heatmaps with subplots
  - ADD method comparison matrices and convergence plots
  - USE consistent color schemes (coolwarm divergent) and formatting
  - MAINTAIN export functionality (PNG/PDF) patterns

Task 5: CREATE tests/test_multi_correlations.py
  - MIRROR test patterns from tests/test_correlations.py
  - ADD fixtures for multi-method testing using manufacturing data
  - TEST all three correlation methods with significance testing
  - INCLUDE edge cases: missing data, outliers, small samples
  - VALIDATE against real manufacturing data patterns

Task 6: CREATE tests/test_multi_tools.py
  - FOLLOW agent tool test patterns from existing test files
  - TEST multi-method tool functions with real context data
  - VALIDATE precision requirements (8-decimal internal, 6-decimal display)
  - INCLUDE error handling and edge case validation

Task 7: CREATE tests/test_multi_plots.py
  - TEST visualization generation with sample correlation data
  - VALIDATE subplot creation and color scheme consistency
  - CHECK export functionality and file format support
  - ENSURE plots handle different data sizes and correlation ranges

Task 8: UPDATE src/agents/prompts.py
  - ADD specialized prompts for multi-method correlation analysis
  - INCLUDE guidance for method selection and interpretation
  - PRESERVE existing prompt patterns and manufacturing domain expertise
```

### Per Task Pseudocode

```python
# Task 1: MultiMethodCorrelationAnalyzer
class MultiMethodCorrelationAnalyzer(ManufacturingCorrelationAnalyzer):
    def calculate_multi_method_correlations(self, df: pd.DataFrame) -> Dict[str, Any]:
        # PATTERN: Always validate input first (see existing correlations.py)
        numeric_df = df.select_dtypes(include=[np.number])
        
        results = {}
        for i, col1 in enumerate(numeric_df.columns):
            for col2 in numeric_df.columns[i+1:]:
                # CRITICAL: Calculate all three methods with p-values
                pearson_r, pearson_p = pearsonr(numeric_df[col1], numeric_df[col2])
                spearman_r, spearman_p = spearmanr(numeric_df[col1], numeric_df[col2])
                kendall_r, kendall_p = kendalltau(numeric_df[col1], numeric_df[col2])
                
                # PATTERN: Use existing confidence interval calculation
                conf_int = self._calculate_confidence_interval(pearson_r, len(numeric_df))
                
                # NEW: Calculate method convergence score
                method_variance = np.var([pearson_r, spearman_r, kendall_r])
                convergence_score = 1 - min(method_variance, 1.0)  # Normalize to [0,1]
                
                results[f"{col1}_{col2}"] = MultiMethodCorrelationResult(...)
        
        return results

# Task 2: Multi-method agent tools  
def calculate_multi_method_correlations_tool(
    ctx: RunContext[ManufacturingDataDependencies],
    variables: Optional[List[str]] = None
) -> Dict[str, Any]:
    # PATTERN: Follow existing tool structure from tools.py
    try:
        data = ctx.deps.data
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # CRITICAL: Maintain 8-decimal precision for internal calculations
        results = analyzer.calculate_multi_method_correlations(data)
        
        # PATTERN: Return standardized response format
        return {
            "multi_method_results": results,
            "analysis_timestamp": datetime.now().isoformat(),
            "methods_analyzed": ["pearson", "spearman", "kendall"]
        }
    except Exception as e:
        logger.error(f"Multi-method correlation calculation failed: {e}")
        return {"error": f"Analysis failed: {str(e)}"}

# Task 4: Side-by-side visualization
def create_multi_method_heatmaps(correlation_results: Dict[str, Any]) -> plt.Figure:
    # PATTERN: Use subplot structure for side-by-side comparison
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
    
    # CRITICAL: Use consistent color scheme across all heatmaps
    vmin, vmax = -1, 1  # Correlation range
    cmap = 'coolwarm'   # Divergent color scheme
    
    # Extract correlation matrices for each method
    pearson_matrix = extract_correlation_matrix(correlation_results, 'pearson')
    spearman_matrix = extract_correlation_matrix(correlation_results, 'spearman') 
    kendall_matrix = extract_correlation_matrix(correlation_results, 'kendall')
    
    # PATTERN: Use seaborn heatmap with consistent parameters
    sns.heatmap(pearson_matrix, ax=ax1, cmap=cmap, vmin=vmin, vmax=vmax, 
                annot=True, cbar=False, square=True)
    ax1.set_title('Pearson Correlation')
    
    # Similar for Spearman and Kendall...
    
    plt.tight_layout()
    return fig
```

### Integration Points
```yaml
EXISTING_AGENT:
  - modify: src/agents/correlation_agent.py
  - pattern: "@correlation_agent.tool" decorator registration
  - preserve: existing tool compatibility and output types
  
EXISTING_ANALYZER:
  - extend: src/data/correlations.py ManufacturingCorrelationAnalyzer
  - pattern: inheritance and method override approach
  - maintain: existing caching and performance optimizations
  
VISUALIZATION_SYSTEM:
  - integrate: src/visualization/plots.py export patterns
  - pattern: consistent color schemes and figure sizing
  - preserve: PNG/PDF export functionality
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
ruff check src/data/multi_correlations.py --fix
ruff check src/agents/multi_tools.py --fix
ruff check src/visualization/multi_plots.py --fix
mypy src/data/multi_correlations.py
mypy src/agents/multi_tools.py

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests with Real API Integration
```python
# CREATE test_multi_correlations.py with these test cases:
def test_multi_method_correlation_calculation():
    """All three methods produce valid correlation coefficients"""
    analyzer = MultiMethodCorrelationAnalyzer()
    results = analyzer.calculate_multi_method_correlations(sample_manufacturing_data)
    
    for result in results.values():
        assert -1 <= result.pearson_correlation <= 1
        assert -1 <= result.spearman_correlation <= 1  
        assert -1 <= result.kendall_correlation <= 1
        assert 0 <= result.pearson_p_value <= 1

def test_method_convergence_with_outliers():
    """Methods show different behavior with outlier data"""
    # Create data with known outliers
    outlier_data = create_outlier_manufacturing_data()
    analyzer = MultiMethodCorrelationAnalyzer()
    results = analyzer.calculate_multi_method_correlations(outlier_data)
    
    # Pearson should be more affected by outliers than Spearman/Kendall
    # Test that convergence score decreases with outliers

def test_correlation_method_recommendation():
    """System recommends appropriate method based on data characteristics"""
    normal_data = create_normal_manufacturing_data()
    skewed_data = create_skewed_manufacturing_data()
    
    # Normal data should recommend Pearson
    # Skewed data should recommend Spearman or Kendall
```

```bash
# Run and iterate until passing:
uv run pytest tests/test_multi_correlations.py -v
uv run pytest tests/test_multi_tools.py -v
uv run pytest tests/test_multi_plots.py -v

# CRITICAL: Tests must use real API keys - no mocking
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Test with Manufacturing Data
```bash
# Test with real manufacturing data (295K+ records)
uv run python -c "
from src.data.loader import load_manufacturing_data
from src.agents.correlation_agent import analyze_manufacturing_correlations
import asyncio

async def test_multi_method():
    data = load_manufacturing_data('test-data/')
    result = await analyze_manufacturing_correlations(
        data=data,
        query='Compare correlation methods for thickness and speed relationship',
        analysis_type='multi_method'
    )
    print(f'Multi-method analysis completed: {len(result.significant_correlations)} correlations found')

asyncio.run(test_multi_method())
"

# Expected: Successful analysis with method comparison results
# If error: Check logs and validate data loading process
```

## Final Validation Checklist
- [ ] All tests pass: `uv run pytest tests/ -v`
- [ ] No linting errors: `uv run ruff check src/`  
- [ ] No type errors: `uv run mypy src/`
- [ ] Multi-method analysis works with real manufacturing data
- [ ] Side-by-side visualizations generate correctly
- [ ] Method recommendations align with data characteristics
- [ ] Performance acceptable with 295K+ record datasets
- [ ] Backward compatibility maintained with existing correlation agent

---

## Anti-Patterns to Avoid
- ❌ Don't break existing single-method correlation functionality  
- ❌ Don't ignore computational complexity of Kendall correlation with large datasets
- ❌ Don't use inconsistent precision across correlation methods
- ❌ Don't create new visualization patterns when subplot approach works
- ❌ Don't mock API calls in tests - use real integration validation
- ❌ Don't ignore statistical significance testing for any method
- ❌ Don't assume normal distribution - implement robust data assessment

---

## Quality Score: 9/10

**Confidence Level for One-Pass Implementation Success**: This PRP provides comprehensive context including:
- ✅ Complete codebase analysis with existing patterns to follow
- ✅ External research on correlation methods and visualization techniques  
- ✅ Detailed implementation blueprint with pseudocode
- ✅ Real manufacturing data context (295K+ records) for validation
- ✅ Executable validation gates with specific commands
- ✅ Anti-patterns and gotchas clearly documented
- ✅ Statistical best practices and precision requirements
- ✅ Integration points with existing production system

**Potential Risk Areas**: 
- Performance optimization for Kendall correlation with large datasets may require iterative refinement
- Visualization layout adjustments might need fine-tuning based on actual correlation matrices

The comprehensive context and existing codebase patterns provide a strong foundation for successful implementation.