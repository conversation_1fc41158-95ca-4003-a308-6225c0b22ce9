name: "PatchTST Manufacturing Forecasting Agent - Phase 3.0 Implementation"
description: |

## Purpose
Implement a production-ready PatchTST-based forecasting agent integrated with the existing correlation analysis infrastructure to provide advanced time series forecasting capabilities for fiber cement manufacturing parameters.

## Core Principles
1. **Context is King**: Leverage existing Phase 2.1 infrastructure and proven agent patterns
2. **Validation Loops**: Real API integration testing with comprehensive model validation
3. **Information Dense**: Manufacturing domain expertise with transformer architecture
4. **Progressive Success**: Start with model training, validate forecasting, then enhance agent integration
5. **Global rules**: Follow all patterns established in CLAUDE.md and existing codebase

---

## Goal
Build a comprehensive PatchTST-based forecasting system that extends the existing correlation analysis capabilities to include predictive modeling for manufacturing parameters (thickness, speed, scrap rates, quality metrics) with natural language agent interface and production-ready deployment.

## Why
- **Predictive Manufacturing Intelligence**: Move beyond reactive correlation analysis to proactive forecasting for process optimization
- **Business Value**: Reduce scrap rates through advance warning of quality degradation and production parameter forecasting
- **Integration Benefits**: Leverage existing 262K+ validated manufacturing records and proven agent infrastructure
- **Advanced ML Capabilities**: PatchTST provides 21% MSE reduction over existing transformer models for long-term forecasting
- **Manufacturing Domain Fit**: Patch-based architecture handles mixed-frequency manufacturing data (sensor readings + event data) naturally

## What
Extend the existing manufacturing analysis ecosystem with PatchTST forecasting capabilities integrated through PydanticAI agent tools.

### Success Criteria
- [ ] Production-ready PatchTST model training pipeline using HuggingFace Transformers
- [ ] Agent tool integration following existing @correlation_agent.tool patterns
- [ ] Multi-horizon forecasting (15 minutes, 1 hour, 4 hours, 24 hours) for manufacturing parameters
- [ ] Natural language interface: "Forecast thickness for next 2 hours" → structured ForecastResult
- [ ] Model interpretability with attention visualization for manufacturing insights
- [ ] 15% improvement over baseline methods (ARIMA, Linear Regression) on test data
- [ ] Complete visualization integration with existing 5-tool visualization suite
- [ ] Comprehensive test suite with real model training and inference validation
- [ ] Configuration-driven approach supporting multiple target variables and forecast horizons

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://huggingface.co/docs/transformers/en/model_doc/patchtst
  why: Official PatchTST documentation with configuration examples and model architecture
  critical: PatchTSTConfig and PatchTSTForPrediction class usage patterns
  
- url: https://huggingface.co/blog/patchtst
  why: HuggingFace tutorial with electricity forecasting examples and practical implementation
  critical: ForecastDFDataset usage and model training patterns
  
- url: https://github.com/yuqinie98/PatchTST
  why: Original PatchTST implementation with supervised and self-supervised learning
  critical: Manufacturing-specific hyperparameter tuning and validation approaches
  
- url: https://huggingface.co/ibm-granite/granite-timeseries-patchtst
  why: Pre-trained IBM Granite model for transfer learning and zero-shot forecasting
  critical: Transfer learning patterns for manufacturing domain adaptation
  
- url: https://ai.pydantic.dev/tools/
  why: PydanticAI tool registration patterns and output_type configuration
  critical: Use output_type instead of deprecated result_type
  
- file: src/agents/correlation_agent.py
  why: Proven agent architecture patterns, tool registration, and output types
  critical: ManufacturingDataDependencies, CorrelationAnalysis patterns, error handling
  
- file: src/data/loader.py
  why: Established manufacturing data processing and time alignment infrastructure
  critical: ManufacturingDataLoader class, timestamp handling, data validation patterns
  
- file: tests/test_agent.py
  why: Real API integration testing patterns without mocking
  critical: Environment variable handling, async testing patterns, production validation
  
- file: docs/patch_tst_getting_started.ipynb
  why: Basic PatchTST model setup and training examples
  critical: ForecastDFDataset usage, training configuration
  
- file: docs/patch_tst_transfer.ipynb
  why: Transfer learning and model adaptation techniques
  critical: Zero-shot forecasting and fine-tuning approaches
  
- file: TASK_3.md
  why: Comprehensive feature requirements and manufacturing-specific implementation examples
  critical: Complete agent tool implementation patterns and configuration management
```

### Current Codebase Structure
```bash
src/
├── agents/
│   ├── correlation_agent.py          # 547 lines - Main agent with 16 tools registered
│   ├── tools.py                      # Correlation analysis tools
│   ├── multi_tools.py                # Multi-method correlation tools  
│   ├── visualization_tools.py        # 5 visualization tools integrated
│   ├── prompts.py                    # Agent system prompts
│   └── data_utils.py                 # Data processing utilities
├── data/
│   ├── loader.py                     # 636 lines - Manufacturing data processing
│   ├── correlations.py               # Correlation analysis engine
│   └── multi_correlations.py         # Multi-method correlation support
├── visualization/
│   ├── plots.py                      # Core plotting functionality
│   ├── multi_plots.py                # Multi-method visualizations
│   ├── backend_utils.py              # Matplotlib backend configuration
│   └── web_plots.py                  # Web-based interactive plots
tests/
├── test_agent.py                     # Real API integration tests
├── test_multi_tools.py               # Multi-method testing
├── test_correlations.py              # Correlation engine tests
├── test_data_loader.py               # Data processing tests
└── test_multi_correlations.py        # Multi-method validation
```

### Desired Codebase Structure (New Additions)
```bash
src/
├── forecasting/                      # NEW - Forecasting module
│   ├── __init__.py
│   ├── patchtst_model.py             # PatchTST model training and inference
│   ├── preprocessing.py              # Time series preprocessing for PatchTST
│   ├── trainer.py                    # Model training pipeline
│   └── config.py                     # Forecasting configuration management
├── agents/
│   ├── forecasting_tools.py          # NEW - PatchTST agent tools
│   └── correlation_agent.py          # EXTEND - Add forecasting tools
tests/
├── test_forecasting_model.py         # NEW - PatchTST model tests
├── test_forecasting_tools.py         # NEW - Agent tool tests
└── test_forecasting_agent.py         # NEW - Complete workflow tests
models/                               # NEW - Model storage
├── patchtst_manufacturing_base/
└── patchtst_manufacturing_thickness/
config/
└── forecasting_config.json          # NEW - Forecasting configuration
```

### Known Gotchas & Critical Patterns
```python
# CRITICAL: PydanticAI agent patterns from existing codebase
# Use output_type instead of deprecated result_type
correlation_agent = Agent(
    model=get_model_config(),
    system_prompt=SYSTEM_PROMPT,
    output_type=ForecastResult,  # NOT result_type
    deps_type=ManufacturingDataDependencies,
)

# CRITICAL: Environment variable handling (strict configuration)
# System fails with clear error messages instead of fallback values
api_key = os.getenv('ANTHROPIC_API_KEY')
if not api_key:
    raise ValueError("ANTHROPIC_API_KEY not found in environment")

# CRITICAL: Matplotlib backend configuration for server environments
# Apply lessons from Phase 2.1 production issues
import matplotlib
matplotlib.use('Agg')  # MUST be before any matplotlib imports
import matplotlib.pyplot as plt

# CRITICAL: Data structure validation patterns
# Use targeted validation functions to avoid mixed object/dictionary errors
def validate_forecast_results_only(data: Dict[str, Any]) -> bool:
    """Validate actual forecast data, not response metadata"""
    required_fields = ['forecast_values', 'confidence_intervals', 'forecast_timestamps']
    return all(field in data for field in required_fields)

# CRITICAL: PatchTST configuration patterns from HuggingFace
from transformers import PatchTSTConfig, PatchTSTForPrediction, Trainer
from transformers import TrainingArguments, EarlyStoppingCallback

# Manufacturing-optimized configuration
config = PatchTSTConfig(
    num_input_channels=6,           # thickness, speed, temperature, pressure, quality, stops
    context_length=512,             # 8.5 hours of historical data (1 min intervals)  
    prediction_length=96,           # 1.6 hours forecast horizon
    patch_length=16,                # 16-minute patches for shift-level patterns
    patch_stride=16,                # Non-overlapping patches
    d_model=128,                    # Transformer dimension
    num_attention_heads=16,         # Multi-head attention
    num_hidden_layers=3,            # Encoder layers
    dropout=0.2,                    # Regularization
    channel_attention=False,        # Channel independence for manufacturing
    scaling="std",                  # Standard scaling for sensor data
    loss="mse"                      # Mean squared error for regression
)

# CRITICAL: Time series preprocessing patterns
from tsfm_public.toolkit.dataset import ForecastDFDataset
from tsfm_public.toolkit.time_series_preprocessor import TimeSeriesPreprocessor

# CRITICAL: Manufacturing data temporal splitting (chronological order)
num_records = len(data)
train_end = int(num_records * 0.7)      # First 70% chronologically
valid_end = int(num_records * 0.85)     # Next 15% chronologically
# Test data: Final 15% chronologically
```

## Implementation Blueprint

### Data Models and Structure
```python
# Core Pydantic models following existing agent patterns
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
from datetime import datetime

class ForecastResult(BaseModel):
    """Structured forecast result following correlation agent patterns"""
    target_variable: str = Field(description="Variable being forecasted")
    forecast_horizon: int = Field(description="Number of time steps forecasted")
    forecast_values: List[float] = Field(description="Predicted future values")
    confidence_intervals: Dict[str, List[float]] = Field(description="95% confidence intervals")
    forecast_timestamps: List[str] = Field(description="Timestamps for forecast period")
    model_performance: Dict[str, float] = Field(description="Model accuracy metrics")
    attention_insights: Dict[str, Any] = Field(description="Key temporal patterns identified")
    manufacturing_insights: List[str] = Field(description="Process optimization recommendations")

class ForecastConfig(BaseModel):
    """Manufacturing forecasting configuration"""
    input_variables: List[str] = Field(default=[
        "thickness_avg", "thickness_uniformity", "speed", 
        "temperature", "pressure", "minutes_since_last_stop"
    ])
    target_variables: List[str] = Field(default=["thickness_avg", "scrap_rate", "quality_index"])
    forecast_horizons: List[int] = Field(default=[15, 60, 240])  # 15min, 1hr, 4hr
    lookback_window: int = Field(default=240)
    patch_size: int = Field(default=16)
    model_params: Dict[str, Any] = Field(default={
        "d_model": 128,
        "n_heads": 8, 
        "num_layers": 3,
        "dropout": 0.1
    })

class PatchTSTTrainingConfig(BaseModel):
    """PatchTST training configuration"""
    batch_size: int = Field(default=32)
    learning_rate: float = Field(default=0.0001)
    max_epochs: int = Field(default=100)
    early_stopping_patience: int = Field(default=10)
    validation_split: float = Field(default=0.15)
    test_split: float = Field(default=0.15)
```

### Implementation Tasks (In Order)

```yaml
Task 1: Create Forecasting Module Foundation
CREATE src/forecasting/__init__.py:
  - IMPORT all forecasting components
  - EXPOSE main classes for agent integration

CREATE src/forecasting/config.py:
  - MIRROR pattern from: src/data/loader.py configuration handling
  - IMPLEMENT ForecastConfig and PatchTSTTrainingConfig
  - ADD JSON-based configuration loading with validation

Task 2: Implement PatchTST Model Training Pipeline  
CREATE src/forecasting/patchtst_model.py:
  - IMPORT HuggingFace transformers: PatchTSTConfig, PatchTSTForPrediction
  - IMPLEMENT ManufacturingPatchTSTModel class
  - FOLLOW pattern from: TASK_3.md examples (lines 146-314)
  - INCLUDE model initialization, training, inference methods
  - ADD manufacturing-specific data preprocessing

CREATE src/forecasting/preprocessing.py:
  - MIRROR pattern from: src/data/loader.py _preprocess_manufacturing_data
  - IMPLEMENT time series preprocessing for PatchTST input format
  - ADD ForecastDFDataset creation for manufacturing data
  - INCLUDE temporal splitting with chronological order preservation

Task 3: Build Training Infrastructure
CREATE src/forecasting/trainer.py:
  - IMPORT HuggingFace Trainer, TrainingArguments, EarlyStoppingCallback
  - IMPLEMENT ManufacturingForecastTrainer class
  - FOLLOW pattern from: TASK_3.md training pipeline (lines 254-314)
  - ADD model versioning and checkpoint management
  - INCLUDE validation and test evaluation

Task 4: Extend Correlation Agent with Forecasting Tools
CREATE src/agents/forecasting_tools.py:
  - MIRROR pattern from: src/agents/multi_tools.py
  - IMPLEMENT forecast_manufacturing_parameter_tool
  - IMPLEMENT multi_horizon_forecast_tool
  - IMPLEMENT compare_forecast_scenarios_tool
  - ADD manufacturing domain insight generation

MODIFY src/agents/correlation_agent.py:
  - FIND pattern: "# Multi-method correlation analysis tools"
  - INJECT after existing tool registrations
  - ADD forecasting tool imports and registrations
  - PRESERVE existing tool patterns and error handling

Task 5: Create Configuration Management
CREATE config/forecasting_config.json:
  - FOLLOW pattern from: TASK_3.md configuration example (lines 649-717)
  - INCLUDE input_variables, target_variables, forecast_horizons
  - ADD model_params and training_params
  - IMPLEMENT deployment configuration

Task 6: Build Comprehensive Test Suite
CREATE tests/test_forecasting_model.py:
  - MIRROR pattern from: tests/test_agent.py (real API integration)
  - TEST model training pipeline with sample data
  - VALIDATE forecast accuracy metrics
  - INCLUDE transfer learning tests

CREATE tests/test_forecasting_tools.py:
  - FOLLOW pattern from: tests/test_multi_tools.py
  - TEST agent tool functionality with real model inference
  - VALIDATE structured output formats
  - INCLUDE error handling and edge cases

CREATE tests/test_forecasting_agent.py:
  - MIRROR pattern from: tests/test_agent.py complete workflow tests
  - TEST end-to-end forecasting workflow
  - VALIDATE natural language interface
  - INCLUDE multi-horizon forecasting validation

Task 7: Integrate Visualization Suite
MODIFY src/agents/visualization_tools.py:
  - FIND pattern: "generate_all_visualizations_tool"
  - ADD forecast visualization tools
  - IMPLEMENT forecast_timeline_plots
  - ADD attention_heatmap_visualization

Task 8: Production Deployment Preparation
CREATE models/ directory structure:
  - IMPLEMENT model storage organization
  - ADD model versioning and metadata
  - INCLUDE deployment scripts

MODIFY src/cli.py:
  - FIND pattern: correlation analysis CLI commands
  - ADD forecasting CLI commands
  - IMPLEMENT model training and inference endpoints
```

### Per Task Pseudocode

```python
# Task 2: PatchTST Model Implementation
class ManufacturingPatchTSTModel:
    def __init__(self, config: ForecastConfig):
        # PATTERN: Configuration validation from correlation_agent.py
        self.config = config
        self.model = None
        self.preprocessor = None
        
    def initialize_model(self):
        # CRITICAL: HuggingFace PatchTST configuration
        patchtst_config = PatchTSTConfig(
            num_input_channels=len(self.config.input_variables),
            context_length=self.config.lookback_window,
            prediction_length=max(self.config.forecast_horizons),
            patch_length=self.config.patch_size,
            # Manufacturing-specific parameters
            channel_attention=False,  # Independent channels
            scaling="std",            # Sensor data normalization
            loss="mse"               # Regression loss
        )
        
        self.model = PatchTSTForPrediction(patchtst_config)
        
    def prepare_training_data(self, data: pd.DataFrame):
        # PATTERN: Manufacturing data preprocessing from loader.py
        # CRITICAL: Temporal splitting (chronological order)
        num_records = len(data)
        train_end = int(num_records * 0.7)
        valid_end = int(num_records * 0.85)
        
        # Split data chronologically
        train_data = data.iloc[:train_end]
        valid_data = data.iloc[train_end-self.config.lookback_window:valid_end]
        test_data = data.iloc[valid_end-self.config.lookback_window:]
        
        # CRITICAL: ForecastDFDataset creation
        train_dataset = ForecastDFDataset(
            preprocessed_data,
            target_columns=self.config.input_variables,
            context_length=self.config.lookback_window,
            prediction_length=self.config.forecast_horizons[0]
        )
        
        return train_dataset, valid_dataset, test_dataset

# Task 4: Agent Tool Integration
@correlation_agent.tool
async def forecast_manufacturing_parameter(
    ctx: RunContext[ManufacturingDataDependencies],
    target_variable: str,
    forecast_horizon: int,
    lookback_window: int = 240,
    include_confidence_intervals: bool = True
) -> ForecastResult:
    """
    Forecast future values of manufacturing parameters using PatchTST.
    
    PATTERN: Follow existing tool patterns from multi_tools.py
    CRITICAL: Manufacturing domain validation and insights
    """
    try:
        # PATTERN: Access unified data from context (Phase 2.1 infrastructure)
        unified_data = ctx.deps.data
        
        # PATTERN: Data validation from existing tools
        if unified_data.empty:
            raise ValueError("No unified manufacturing data available")
            
        # CRITICAL: Load pre-trained model
        model_path = f"./models/patchtst_manufacturing_{target_variable}/"
        model = ManufacturingPatchTSTModel.load_pretrained(model_path)
        
        # PATTERN: Manufacturing data preparation
        input_data = unified_data.tail(lookback_window)
        
        # CRITICAL: Model inference
        forecast_values = model.forecast(input_data, forecast_horizon)
        
        # PATTERN: Manufacturing insights generation (domain expertise)
        manufacturing_insights = generate_manufacturing_insights(
            target_variable, forecast_values, input_data
        )
        
        return ForecastResult(
            target_variable=target_variable,
            forecast_horizon=forecast_horizon,
            forecast_values=forecast_values,
            manufacturing_insights=manufacturing_insights,
            # ... other fields
        )
        
    except Exception as e:
        # PATTERN: Error handling from existing agent tools
        logger.error(f"Forecasting error for {target_variable}: {str(e)}")
        raise ValueError(f"Failed to generate forecast: {str(e)}")
```

### Integration Points
```yaml
AGENT_INTEGRATION:
  - extend: src/agents/correlation_agent.py
  - pattern: "@correlation_agent.tool" decorator registration
  - preserve: Existing tool patterns and error handling
  
DATA_PIPELINE:
  - leverage: src/data/loader.py ManufacturingDataLoader
  - extend: Time series preprocessing for PatchTST format
  - maintain: 262K+ record processing capability

CONFIGURATION:
  - add: config/forecasting_config.json
  - pattern: JSON-based configuration with Pydantic validation
  - integrate: Environment variable management

VISUALIZATION:
  - extend: src/agents/visualization_tools.py
  - pattern: Existing 5-tool visualization suite
  - add: Forecast timeline plots and attention visualizations

MODEL_STORAGE:
  - create: models/ directory with versioning
  - pattern: HuggingFace model serialization
  - include: Model metadata and performance metrics

TESTING:
  - extend: tests/ with forecasting test suites
  - pattern: Real API integration (no mocking)
  - validate: Production readiness with actual model training
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
ruff check src/forecasting/ --fix
ruff check src/agents/forecasting_tools.py --fix
mypy src/forecasting/
mypy src/agents/forecasting_tools.py

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests (Real API Integration - No Mocking)
```python
# CREATE test_forecasting_model.py with these test cases:
@pytest.mark.asyncio
async def test_patchtst_model_training():
    """Test complete model training pipeline with real data"""
    loader = ManufacturingDataLoader()
    data = loader.load_all_manufacturing_data()
    unified_data = loader.create_unified_dataset()
    
    model = ManufacturingPatchTSTModel(config)
    trained_model = model.train(unified_data)
    
    assert trained_model is not None
    assert os.path.exists(f"./models/test_model/")

@pytest.mark.asyncio  
async def test_forecasting_agent_tool():
    """Test agent tool with real model inference"""
    deps = ManufacturingDataDependencies(data=real_manufacturing_data)
    
    result = await forecast_manufacturing_parameter(
        ctx=RunContext(deps=deps),
        target_variable="thickness_avg",
        forecast_horizon=96
    )
    
    assert isinstance(result, ForecastResult)
    assert len(result.forecast_values) == 96
    assert result.target_variable == "thickness_avg"

@pytest.mark.asyncio
async def test_model_accuracy_validation():
    """Validate 15% improvement over baseline methods"""
    # Test against ARIMA and Linear Regression baselines
    patchtst_mse = model.evaluate_test_set()
    baseline_mse = baseline_model.evaluate_test_set()
    
    improvement = (baseline_mse - patchtst_mse) / baseline_mse
    assert improvement >= 0.15, f"Model improvement {improvement:.3f} below 15% threshold"
```

```bash
# Run and iterate until passing:
uv run pytest tests/test_forecasting_model.py -v
uv run pytest tests/test_forecasting_tools.py -v
uv run pytest tests/test_forecasting_agent.py -v

# If failing: Read error, understand root cause, fix code, re-run
# CRITICAL: Use real model training and inference - no mocking
```

### Level 3: Integration Test
```bash
# Test model training pipeline
uv run python -c "
from src.forecasting.patchtst_model import ManufacturingPatchTSTModel
from src.data.loader import ManufacturingDataLoader

loader = ManufacturingDataLoader()
data = loader.create_unified_dataset()
model = ManufacturingPatchTSTModel.from_config('config/forecasting_config.json')
model.train(data)
print('Model training successful')
"

# Test agent integration
uv run python -c "
import asyncio
from src.agents.correlation_agent import correlation_agent
from src.data.loader import ManufacturingDataLoader

async def test():
    loader = ManufacturingDataLoader()
    data = loader.create_unified_dataset()
    result = await correlation_agent.run(
        'Forecast thickness for the next 2 hours',
        deps={'data': data}
    )
    print(f'Forecast generated: {len(result.output.forecast_values)} values')

asyncio.run(test())
"

# Expected: Model trains successfully, agent generates structured forecasts
# If error: Check logs for stack trace, validate environment variables
```

### Level 4: Production Validation
```bash
# Test with full 262K+ manufacturing dataset
uv run python -c "
from src.data.loader import ManufacturingDataLoader
loader = ManufacturingDataLoader()
all_data = loader.load_all_manufacturing_data()
unified_data = loader.create_unified_dataset()
print(f'Dataset shape: {unified_data.shape}')

# Train model on full dataset
from src.forecasting.patchtst_model import ManufacturingPatchTSTModel
model = ManufacturingPatchTSTModel.from_config('config/forecasting_config.json')
trained_model = model.train(unified_data)
print('Production model training complete')
"

# Expected: Successful training on full production dataset
```

## Final Validation Checklist
- [ ] All tests pass: `uv run pytest tests/ -v` (including new forecasting tests)
- [ ] No linting errors: `uv run ruff check src/`
- [ ] No type errors: `uv run mypy src/`
- [ ] Model training successful on full dataset (262K+ records)
- [ ] Agent tools respond to natural language queries
- [ ] 15% improvement validated over baseline methods
- [ ] Forecast accuracy metrics within acceptable ranges
- [ ] Visualization integration working with existing suite
- [ ] Configuration management operational
- [ ] Error cases handled gracefully with informative messages
- [ ] Manufacturing domain insights generated appropriately

---

## Anti-Patterns to Avoid
- ❌ Don't create new agent patterns - follow existing @correlation_agent.tool patterns
- ❌ Don't mock model training/inference in tests - use real API integration
- ❌ Don't ignore matplotlib backend configuration - apply Phase 2.1 lessons
- ❌ Don't use deprecated result_type - use output_type for PydanticAI agents
- ❌ Don't skip model validation - ensure 15% improvement over baselines
- ❌ Don't hardcode model paths - use configuration management
- ❌ Don't break existing correlation agent functionality - preserve all existing tools
- ❌ Don't ignore manufacturing domain expertise - generate actionable insights

## Confidence Score: 9/10

This PRP provides comprehensive context for one-pass implementation success:

✅ **Complete Context**: Includes all necessary HuggingFace documentation, existing codebase patterns, and manufacturing domain expertise

✅ **Proven Patterns**: Follows established Phase 2.1 infrastructure with 547-line correlation agent and 636-line data loader

✅ **Real Integration**: Uses actual model training and inference with comprehensive test validation

✅ **Manufacturing Focus**: Leverages 262K+ validated manufacturing records and domain-specific insights

✅ **Production Ready**: Includes deployment configuration, error handling, and visualization integration

The implementation blueprint provides sufficient detail for autonomous execution while maintaining compatibility with existing infrastructure and following proven patterns from the successful Phase 2.1 deployment.