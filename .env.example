# Manufacturing Correlation Analysis Configuration
# Copy this file to .env and fill in your values

# LLM Provider Selection (ANTHROPIC or VERTEX_AI)
LLM_PROVIDER=ANTHROPIC

# Anthropic Configuration (Required if LLM_PROVIDER=ANTHROPIC)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-sonnet-4-********

# Google Vertex AI Configuration (Required if LLM_PROVIDER=VERTEX_AI)
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
VERTEX_AI_PROJECT=your-gcp-project-id
VERTEX_AI_LOCATION=us-central1
GEMINI_MODEL=gemini-2.5-pro

# IMPORTANT: ALL REQUIRED VARIABLES MUST BE SET
# The system will fail with clear error messages if any required
# environment variable is missing - no fallback values are used.

# Data Configuration
DATA_DIRECTORY=test-data
CACHE_DIRECTORY=.cache

# Analysis Settings
DEFAULT_CORRELATION_METHOD=pearson
DEFAULT_TIME_WINDOW=60
DEFAULT_SIGNIFICANCE_THRESHOLD=0.05
DEFAULT_MIN_CORRELATION=0.3

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=correlation_analysis.log

# Optional: Database Configuration (for future extensions)
# DATABASE_URL=sqlite:///manufacturing_data.db
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432
# POSTGRES_DB=manufacturing
# POSTGRES_USER=user
# POSTGRES_PASSWORD=password

# Optional: Monitoring and Observability
# ENABLE_TELEMETRY=false
# OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317

# Security Settings
# ENABLE_AUTH=false
# JWT_SECRET_KEY=your-secret-key-here
# SESSION_TIMEOUT=3600