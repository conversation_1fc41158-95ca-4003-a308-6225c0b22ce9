#!/usr/bin/env python3
"""
Training Data Validation Tool

Validates that training data has sufficient variance and is suitable for model training.
"""

import pandas as pd
import numpy as np
from pathlib import Path

def validate_training_data(target_variable: str = "fm_reject_pct"):
    """
    Validate training data for modeling
    
    Args:
        target_variable: Target variable to validate
    """
    print(f"🔍 Validating Training Data for {target_variable}")
    print("=" * 60)
    
    # Load data
    data_file = "test-data/consolidated/test_matched_stacks.csv"
    if not Path(data_file).exists():
        print(f"❌ Data file not found: {data_file}")
        return False
    
    print(f"📊 Loading data from {data_file}")
    data = pd.read_csv(data_file, comment='#', low_memory=False)
    print(f"✅ Loaded {len(data):,} records")
    
    # Check target variable
    if target_variable not in data.columns:
        print(f"❌ Target variable '{target_variable}' not found in data")
        print(f"Available columns: {list(data.columns)}")
        return False
    
    target_data = data[target_variable].dropna()
    
    print(f"\n📈 Target Variable Analysis: {target_variable}")
    print("-" * 40)
    print(f"Records: {len(target_data):,}")
    print(f"Min: {target_data.min():.4f}")
    print(f"Max: {target_data.max():.4f}")
    print(f"Mean: {target_data.mean():.4f}")
    print(f"Std: {target_data.std():.4f}")
    print(f"Variance: {target_data.var():.6f}")
    print(f"Unique values: {target_data.nunique()}")
    print(f"Range: {target_data.max() - target_data.min():.4f}")
    
    # Validation checks
    issues = []
    
    # 1. Variance check
    if target_data.var() < 0.01:
        issues.append("❌ CRITICAL: Very low variance - model may not learn")
    elif target_data.var() < 1.0:
        issues.append("⚠️  WARNING: Low variance - may affect model learning")
    else:
        print("✅ Variance: Good")
    
    # 2. Range check
    range_val = target_data.max() - target_data.min()
    if range_val < 0.1:
        issues.append("❌ CRITICAL: Very small range - model may produce constant predictions")
    else:
        print("✅ Range: Good")
    
    # 3. Unique values check
    if target_data.nunique() < 10:
        issues.append("⚠️  WARNING: Few unique values - may limit prediction granularity")
    else:
        print("✅ Unique values: Good")
    
    # 4. Distribution check
    # Check for too many zeros or constant values
    most_common_value = target_data.value_counts().iloc[0]
    if most_common_value / len(target_data) > 0.8:
        issues.append("❌ CRITICAL: Target variable is >80% constant values")
    elif most_common_value / len(target_data) > 0.5:
        issues.append("⚠️  WARNING: Target variable is >50% one value")
    else:
        print("✅ Distribution: Good diversity")
    
    # 5. Missing values
    missing_pct = (len(data) - len(target_data)) / len(data) * 100
    if missing_pct > 50:
        issues.append("❌ CRITICAL: >50% missing values")
    elif missing_pct > 20:
        issues.append("⚠️  WARNING: >20% missing values")
    else:
        print(f"✅ Missing values: {missing_pct:.1f}% (acceptable)")
    
    # Show value distribution
    print(f"\n📊 Value Distribution (top 10):")
    print("-" * 40)
    value_counts = target_data.value_counts().head(10)
    for value, count in value_counts.items():
        pct = count / len(target_data) * 100
        print(f"  {value:8.4f}: {count:6,} ({pct:5.1f}%)")
    
    # Show validation summary
    print(f"\n🎯 Validation Summary")
    print("=" * 40)
    
    if not issues:
        print("✅ ALL CHECKS PASSED - Data suitable for training")
        return True
    else:
        print("❌ ISSUES FOUND:")
        for issue in issues:
            print(f"  {issue}")
        
        # Check if critical issues exist
        critical_issues = [i for i in issues if "CRITICAL" in i]
        if critical_issues:
            print(f"\n🚨 CRITICAL ISSUES DETECTED - Training may fail or produce poor results")
            return False
        else:
            print(f"\n⚠️  WARNINGS ONLY - Training may proceed but monitor closely")
            return True

def validate_input_features(input_variables: list):
    """Validate input features for training"""
    print(f"\n🔧 Input Features Validation")
    print("-" * 40)
    
    data_file = "test-data/consolidated/test_matched_stacks.csv"
    data = pd.read_csv(data_file, comment='#', low_memory=False)
    
    missing_features = []
    low_variance_features = []
    constant_features = []
    
    for feature in input_variables:
        if feature not in data.columns:
            missing_features.append(feature)
            continue
            
        feature_data = data[feature].dropna()
        if len(feature_data) == 0:
            constant_features.append(feature)
            continue
            
        variance = feature_data.var()
        if variance < 1e-10:
            constant_features.append(feature)
        elif variance < 0.01:
            low_variance_features.append(feature)
    
    print(f"Total features: {len(input_variables)}")
    print(f"Available: {len(input_variables) - len(missing_features)}")
    
    if missing_features:
        print(f"❌ Missing features ({len(missing_features)}): {missing_features[:5]}...")
    
    if constant_features:
        print(f"⚠️  Constant features ({len(constant_features)}): {constant_features[:5]}...")
    
    if low_variance_features:
        print(f"⚠️  Low variance features ({len(low_variance_features)}): {low_variance_features[:5]}...")
    
    usable_features = len(input_variables) - len(missing_features) - len(constant_features)
    print(f"✅ Usable features: {usable_features}")
    
    return usable_features > 10  # Need at least 10 good features

if __name__ == "__main__":
    import sys
    
    target = sys.argv[1] if len(sys.argv) > 1 else "fm_reject_pct"
    
    # Validate target variable
    target_ok = validate_training_data(target)
    
    # Validate input features (load from config)
    try:
        import json
        with open("config/forecasting_config.json", 'r') as f:
            config = json.load(f)
        
        input_vars = config['forecasting_config']['input_variables']
        features_ok = validate_input_features(input_vars)
        
        print(f"\n🎯 FINAL VALIDATION")
        print("=" * 40)
        print(f"Target variable ({target}): {'✅ PASS' if target_ok else '❌ FAIL'}")
        print(f"Input features: {'✅ PASS' if features_ok else '❌ FAIL'}")
        
        if target_ok and features_ok:
            print(f"\n🎉 READY FOR TRAINING")
            sys.exit(0)
        else:
            print(f"\n🚨 NOT READY - Fix issues before training")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error validating features: {e}")
        sys.exit(1)