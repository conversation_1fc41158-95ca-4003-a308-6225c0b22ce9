#!/usr/bin/env python3
"""
Model Management Tool

Helps you manage trained models: list, backup, delete, and organize.
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json

def list_models():
    """List all available models with details"""
    print("🔮 Current Trained Models")
    print("=" * 60)
    
    models_dir = Path("./models")
    if not models_dir.exists():
        print("❌ No models directory found")
        return []
    
    model_dirs = list(models_dir.glob("patchtst_manufacturing_*"))
    
    if not model_dirs:
        print("❌ No trained models found")
        return []
    
    models_info = []
    
    for model_dir in sorted(model_dirs):
        target = model_dir.name.replace("patchtst_manufacturing_", "")
        
        # Get model size
        total_size = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        
        # Get creation date
        creation_time = model_dir.stat().st_mtime
        creation_date = datetime.fromtimestamp(creation_time).strftime("%Y-%m-%d %H:%M")
        
        # Check model files
        has_model = (model_dir / "model.safetensors").exists()
        has_config = (model_dir / "config.json").exists()
        has_metadata = (model_dir / "metadata.json").exists()
        
        status = "✅" if has_model and has_config else "⚠️"
        
        models_info.append({
            'name': target,
            'path': model_dir,
            'size_mb': size_mb,
            'created': creation_date,
            'status': status,
            'complete': has_model and has_config and has_metadata
        })
        
        print(f"  {status} {target}")
        print(f"     Size: {size_mb:.1f} MB")
        print(f"     Created: {creation_date}")
        print(f"     Files: {'Model' if has_model else '❌'} {'Config' if has_config else '❌'} {'Metadata' if has_metadata else '❌'}")
        print()
    
    return models_info

def backup_model(model_name: str, backup_dir: str = "./models_backup"):
    """Backup a specific model before deletion"""
    source_dir = Path(f"./models/patchtst_manufacturing_{model_name}")
    
    if not source_dir.exists():
        print(f"❌ Model {model_name} not found")
        return False
    
    # Create backup directory
    backup_path = Path(backup_dir)
    backup_path.mkdir(exist_ok=True)
    
    # Create timestamped backup
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_model_dir = backup_path / f"patchtst_manufacturing_{model_name}_{timestamp}"
    
    try:
        shutil.copytree(source_dir, backup_model_dir)
        print(f"✅ Backed up {model_name} to {backup_model_dir}")
        return True
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return False

def delete_model(model_name: str, force: bool = False):
    """Delete a specific model"""
    model_dir = Path(f"./models/patchtst_manufacturing_{model_name}")
    
    if not model_dir.exists():
        print(f"❌ Model {model_name} not found")
        return False
    
    if not force:
        response = input(f"⚠️  Are you sure you want to delete {model_name}? [y/N]: ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Deletion cancelled")
            return False
    
    try:
        shutil.rmtree(model_dir)
        print(f"✅ Deleted model: {model_name}")
        return True
    except Exception as e:
        print(f"❌ Deletion failed: {e}")
        return False

def cleanup_models(keep_models: list, backup_before_delete: bool = True):
    """Keep only specified models, delete the rest"""
    print(f"🧹 Model Cleanup: Keeping {keep_models}")
    print("=" * 60)
    
    models_info = list_models()
    
    if not models_info:
        print("❌ No models to clean up")
        return
    
    models_to_delete = [m for m in models_info if m['name'] not in keep_models]
    models_to_keep = [m for m in models_info if m['name'] in keep_models]
    
    print(f"📦 Models to keep ({len(models_to_keep)}):")
    for model in models_to_keep:
        print(f"  ✅ {model['name']} ({model['size_mb']:.1f} MB)")
    
    print(f"\n🗑️  Models to delete ({len(models_to_delete)}):")
    for model in models_to_delete:
        print(f"  ❌ {model['name']} ({model['size_mb']:.1f} MB)")
    
    if not models_to_delete:
        print("✅ No models to delete - you're all set!")
        return
    
    # Calculate space savings
    total_space_saved = sum(m['size_mb'] for m in models_to_delete)
    print(f"\n💾 Space to be freed: {total_space_saved:.1f} MB")
    
    # Confirm deletion
    print(f"\n⚠️  This will permanently delete {len(models_to_delete)} models.")
    if backup_before_delete:
        print("📦 Models will be backed up before deletion.")
    
    response = input("Continue? [y/N]: ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Cleanup cancelled")
        return
    
    # Perform cleanup
    deleted_count = 0
    backed_up_count = 0
    
    for model in models_to_delete:
        model_name = model['name']
        
        # Backup if requested
        if backup_before_delete:
            if backup_model(model_name):
                backed_up_count += 1
        
        # Delete model
        if delete_model(model_name, force=True):
            deleted_count += 1
    
    print(f"\n🎉 Cleanup Complete!")
    print(f"  ✅ Deleted: {deleted_count} models")
    if backup_before_delete:
        print(f"  📦 Backed up: {backed_up_count} models")
    print(f"  💾 Space freed: {total_space_saved:.1f} MB")
    
    # Show remaining models
    print(f"\n📦 Remaining Models:")
    remaining_models = list_models()

def main():
    import sys
    
    if len(sys.argv) < 2:
        print("🔮 Model Management Tool")
        print("=" * 40)
        print("Usage: python manage_models.py <command> [options]")
        print()
        print("Commands:")
        print("  list                     - List all models")
        print("  delete <model_name>      - Delete specific model")
        print("  backup <model_name>      - Backup specific model")
        print("  keep <model1> [model2]   - Keep only specified models")
        print("  cleanup                  - Interactive cleanup")
        print()
        print("Examples:")
        print("  python manage_models.py list")
        print("  python manage_models.py keep fm_reject_pct")
        print("  python manage_models.py delete Speed")
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        list_models()
    
    elif command == "delete" and len(sys.argv) > 2:
        model_name = sys.argv[2]
        delete_model(model_name)
    
    elif command == "backup" and len(sys.argv) > 2:
        model_name = sys.argv[2]
        backup_model(model_name)
    
    elif command == "keep" and len(sys.argv) > 2:
        models_to_keep = sys.argv[2:]
        cleanup_models(models_to_keep, backup_before_delete=True)
    
    elif command == "cleanup":
        print("🧹 Interactive Model Cleanup")
        print("=" * 40)
        
        models_info = list_models()
        if not models_info:
            return
        
        print("\nWhich models do you want to keep?")
        print("Enter model names separated by spaces:")
        print("(or press Enter to keep all)")
        
        keep_input = input("> ").strip()
        if keep_input:
            models_to_keep = keep_input.split()
            cleanup_models(models_to_keep, backup_before_delete=True)
        else:
            print("✅ Keeping all models")
    
    else:
        print("❌ Invalid command. Use 'python manage_models.py' for help.")

if __name__ == "__main__":
    main()