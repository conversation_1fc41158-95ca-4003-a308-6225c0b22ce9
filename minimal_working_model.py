#!/usr/bin/env python3
"""
Minimal Working Model Solution

Creates the simplest possible working forecasting model to validate the approach.
Uses basic LSTM/GRU instead of PatchTST to eliminate architecture complexity.
"""

import json
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error
import matplotlib
matplotlib.use('Agg')  # Headless backend
import matplotlib.pyplot as plt

class SimpleManufacturingLSTM(nn.Module):
    """Simple LSTM model for manufacturing forecasting"""
    
    def __init__(self, input_size, hidden_size=64, num_layers=2, output_size=1, dropout=0.2):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)
        
    def forward(self, x):
        # LSTM forward pass
        lstm_out, _ = self.lstm(x)
        
        # Take the last output
        last_output = lstm_out[:, -1, :]
        
        # Apply dropout and final layer
        output = self.dropout(last_output)
        output = self.fc(output)
        
        return output

def load_and_prepare_data():
    """Load and prepare manufacturing data"""
    print("📊 Loading manufacturing data...")
    
    # Load unified data
    data = pd.read_csv("test-data/consolidated/test_matched_stacks.csv", comment='#', low_memory=False)
    print(f"✅ Loaded {len(data):,} records")
    
    # Select most reliable features
    feature_columns = [
        'Speed', 'speed_avg_5min', 'speed_change_rate',
        'sm_production_rate', 'fm_processing_rate', 'production_efficiency_pct',
        'sm_scrap_pct', 'sm_quality_efficiency', 'fm_quality_efficiency',
        'hour_of_day', 'day_of_week', 'speed_stability', 'speed_momentum',
        'fm_position_pct', 'position_in_stack', 'time_since_start'
    ]
    
    target_column = 'fm_reject_pct'
    
    # Filter available columns
    available_features = [col for col in feature_columns if col in data.columns]
    print(f"✅ Using {len(available_features)} features: {available_features}")
    
    # Clean data
    model_data = data[available_features + [target_column]].copy()
    
    # Convert to numeric and handle missing values
    for col in model_data.columns:
        model_data[col] = pd.to_numeric(model_data[col], errors='coerce')
    
    # Fill missing values
    model_data = model_data.ffill().fillna(0)
    
    # Remove any infinite values
    model_data = model_data.replace([np.inf, -np.inf], np.nan).fillna(0)
    
    print(f"✅ Clean data shape: {model_data.shape}")
    print(f"Target stats: min={model_data[target_column].min():.3f}, max={model_data[target_column].max():.3f}, std={model_data[target_column].std():.3f}")
    
    return model_data, available_features, target_column

def create_sequences(data, features, target, lookback=30, forecast_horizon=15):
    """Create sequences for time series forecasting"""
    print(f"🔄 Creating sequences (lookback={lookback}, horizon={forecast_horizon})...")
    
    X, y = [], []
    
    for i in range(lookback, len(data) - forecast_horizon + 1):
        # Features: lookback window
        feature_seq = data[features].iloc[i-lookback:i].values
        
        # Target: next forecast_horizon values (we'll predict just the last one for simplicity)
        target_val = data[target].iloc[i + forecast_horizon - 1]
        
        X.append(feature_seq)
        y.append(target_val)
    
    X = np.array(X, dtype=np.float32)
    y = np.array(y, dtype=np.float32)
    
    print(f"✅ Created {len(X)} sequences")
    print(f"   Input shape: {X.shape}")
    print(f"   Target shape: {y.shape}")
    
    return X, y

def train_simple_model():
    """Train simple LSTM model for manufacturing forecasting"""
    print("🚀 MINIMAL WORKING MODEL TRAINING")
    print("=" * 60)
    
    # Load data
    data, features, target = load_and_prepare_data()
    
    # Create sequences
    X, y = create_sequences(data, features, target, lookback=30, forecast_horizon=15)
    
    # Train/test split
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    print(f"📊 Data splits:")
    print(f"   Training: {X_train.shape[0]} samples")
    print(f"   Testing: {X_test.shape[0]} samples")
    
    # Scale targets
    target_scaler = StandardScaler()
    y_train_scaled = target_scaler.fit_transform(y_train.reshape(-1, 1)).flatten()
    y_test_scaled = target_scaler.transform(y_test.reshape(-1, 1)).flatten()
    
    # Convert to PyTorch
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train_scaled)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test_scaled)
    
    # Create model
    input_size = X_train.shape[2]
    model = SimpleManufacturingLSTM(input_size=input_size, hidden_size=32, num_layers=2)
    
    print(f"🤖 Model created with {input_size} input features")
    
    # Training setup
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # Training loop
    epochs = 20  # Reduced for testing
    batch_size = 64  # Larger batch for faster training
    best_loss = float('inf')
    patience = 15
    patience_counter = 0
    
    print(f"🏋️ Training for {epochs} epochs...")
    
    train_losses = []
    
    for epoch in range(epochs):
        model.train()
        epoch_loss = 0
        num_batches = 0
        
        # Mini-batch training
        for i in range(0, len(X_train_tensor), batch_size):
            batch_X = X_train_tensor[i:i+batch_size]
            batch_y = y_train_tensor[i:i+batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch_X).squeeze()
            loss = criterion(outputs, batch_y)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            epoch_loss += loss.item()
            num_batches += 1
        
        avg_loss = epoch_loss / num_batches
        train_losses.append(avg_loss)
        
        # Validation
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_test_tensor).squeeze()
            val_loss = criterion(val_outputs, y_test_tensor).item()
        
        scheduler.step(val_loss)
        
        if epoch % 10 == 0:
            print(f"   Epoch {epoch:3d}: Train Loss = {avg_loss:.6f}, Val Loss = {val_loss:.6f}")
        
        # Early stopping
        if val_loss < best_loss:
            best_loss = val_loss
            patience_counter = 0
            # Save best model
            torch.save(model.state_dict(), 'models/simple_lstm_best.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"   Early stopping at epoch {epoch}")
                break
    
    # Load best model
    model.load_state_dict(torch.load('models/simple_lstm_best.pth'))
    
    # Final evaluation
    model.eval()
    with torch.no_grad():
        train_pred = model(X_train_tensor).squeeze().numpy()
        test_pred = model(X_test_tensor).squeeze().numpy()
    
    # Inverse transform predictions
    train_pred_orig = target_scaler.inverse_transform(train_pred.reshape(-1, 1)).flatten()
    test_pred_orig = target_scaler.inverse_transform(test_pred.reshape(-1, 1)).flatten()
    
    # Calculate metrics
    train_mse = mean_squared_error(y_train, train_pred_orig)
    test_mse = mean_squared_error(y_test, test_pred_orig)
    train_mae = mean_absolute_error(y_train, train_pred_orig)
    test_mae = mean_absolute_error(y_test, test_pred_orig)
    
    print(f"\n📊 FINAL RESULTS:")
    print(f"   Training MSE: {train_mse:.4f}")
    print(f"   Testing MSE:  {test_mse:.4f}")
    print(f"   Training MAE: {train_mae:.4f}")
    print(f"   Testing MAE:  {test_mae:.4f}")
    
    # Check prediction variance
    pred_variance = np.var(test_pred_orig)
    pred_range = test_pred_orig.max() - test_pred_orig.min()
    
    print(f"\n🔍 PREDICTION ANALYSIS:")
    print(f"   Prediction variance: {pred_variance:.6f}")
    print(f"   Prediction range: {pred_range:.4f}")
    print(f"   Mean prediction: {test_pred_orig.mean():.4f}")
    print(f"   Actual mean: {y_test.mean():.4f}")
    
    # Save results
    results = {
        'model_type': 'Simple LSTM',
        'input_features': len(features),
        'train_mse': float(train_mse),
        'test_mse': float(test_mse),
        'train_mae': float(train_mae),
        'test_mae': float(test_mae),
        'prediction_variance': float(pred_variance),
        'prediction_range': float(pred_range),
        'convergence_epochs': epoch,
        'best_val_loss': float(best_loss)
    }
    
    with open('models/simple_lstm_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"✅ Results saved to models/simple_lstm_results.json")
    
    # Success criteria
    success = (
        test_mse < 50 and  # Reasonable MSE
        pred_variance > 1e-3 and  # Some variance in predictions
        pred_range > 0.5 and  # Reasonable prediction range
        np.isfinite(test_mse)  # No NaN/inf values
    )
    
    print(f"\n🎯 SUCCESS CRITERIA:")
    print(f"   MSE < 50: {'✅' if test_mse < 50 else '❌'} ({test_mse:.2f})")
    print(f"   Variance > 1e-3: {'✅' if pred_variance > 1e-3 else '❌'} ({pred_variance:.2e})")
    print(f"   Range > 0.5: {'✅' if pred_range > 0.5 else '❌'} ({pred_range:.2f})")
    print(f"   Finite values: {'✅' if np.isfinite(test_mse) else '❌'}")
    
    print(f"\n{'🎉 MINIMAL MODEL SUCCESS!' if success else '⚠️ NEEDS ADJUSTMENT'}")
    
    return success, results

if __name__ == "__main__":
    try:
        success, results = train_simple_model()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)