# 🔮 PatchTST Model Training Guide - Enhanced Manufacturing Intelligence

This guide explains how to train PatchTST forecasting models for manufacturing data using the enhanced training system with unified table integration and product intelligence.

## 🚀 Quick Start

### Option 1: Enhanced Training (Recommended)
```bash
# Activate virtual environment
source venv/bin/activate

# Train all models with enhanced features (68+ variables)
python train.py

# Train specific target variable with product intelligence
python train.py Speed                    # Production speed forecasting
python train.py sm_scrap_pct            # Quality forecasting  
python train.py fm_reject_pct           # Reject rate forecasting
```

### Option 2: Unified Table Options
```bash
# Use existing unified table (faster, 122K+ records)
echo "y" | python train.py Speed

# Force recreation from raw sources (comprehensive processing)
echo "n" | python train.py Speed

# Interactive mode (prompts for choice)
python train.py Speed
```

## 📋 Enhanced Training System

### 1. `train.py` - Production Ready (Primary)
- **Purpose**: Production training with manufacturing intelligence
- **Features**: Unified table integration, product encoding, comprehensive features
- **Best for**: Real-world manufacturing forecasting

**Usage:**
```bash
python train.py                    # All 5 targets with product intelligence
python train.py Speed             # Single target training
python train.py --help            # Show available targets
```

### 2. Smart Data Loading
- **Unified Table Detection**: Automatically finds existing unified tables
- **Metadata Parsing**: Reads export date and record counts from CSV headers
- **User Choice**: Interactive selection between unified table or raw data processing
- **Three-Tier Fallback**: Robust error handling with multiple loading strategies

### 3. Product Intelligence Features
- **Automatic Encoding**: Converts 38 unique product types to numeric features
- **Structured Features**: Product type, thickness, dimensions, complexity
- **Material Classification**: Cladding, weatherboard, villaboard indicators
- **Manufacturing Context**: Product-specific forecasting patterns

## 🎯 Available Target Variables

Based on updated `config/forecasting_config.json` with unified table schema:

1. **`Speed`** - Production line speed (meters/minute)
2. **`sm_scrap_pct`** - Sheet machine scrap percentage 
3. **`fm_reject_pct`** - Forming machine reject percentage
4. **`production_efficiency_pct`** - Overall production efficiency
5. **`capacity_utilization_pct`** - Capacity utilization percentage

**Enhanced Input Features (68+ variables):**
- **Production Metrics (15)**: Speed variations, rates, efficiency percentages
- **Quality Metrics (12)**: Scrap rates, reject percentages, quality scores  
- **Product Features (12)**: Type codes, dimensions, material properties, complexity
- **Operational Data (10)**: Stoppage analysis, restart impacts, gap minutes
- **Temporal Features (8)**: Hour, day, shift, cyclical patterns
- **Process State (6)**: Stability indicators, trends, manufacturing state
- **Sequence Features (5)**: Stack progress, position, time since start

## ⚙️ Configuration Options

### Default Configuration File
- **Location**: `config/forecasting_config.json`
- **Contains**: Model parameters, training settings, stability configuration

### Key Configuration Sections:

#### Forecasting Parameters
```json
{
  "forecast_horizons": [15, 60, 240],     // 15min, 1hr, 4hr forecasts
  "lookback_window": 240,                 // Historical data window
  "patch_size": 16,                       // Transformer patch size
  "d_model": 64,                          // Model dimension
  "num_attention_heads": 4                // Attention heads
}
```

#### Stability Enhancements (Phase 3.1)
```json
{
  "stability_config": {
    "gradient_clipping": {
      "enabled": true,
      "max_norm": 1.0
    },
    "mixed_precision": {
      "enabled": true
    },
    "early_stopping": {
      "patience": 15
    }
  }
}
```

#### Transfer Learning
```json
{
  "transfer_learning": {
    "enabled": true,
    "pretrained_model": "ibm-granite/granite-timeseries-patchtst",
    "fallback_to_fresh": true
  }
}
```

## 🔧 Training Features (Phase 3.1)

### ✅ Stability Enhancements
- **Gradient Clipping**: Prevents exploding gradients
- **UnitNorm Layers**: Improves training stability
- **Mixed Precision**: Faster training with stability
- **Early Stopping**: Prevents overfitting

### 🚀 Transfer Learning
- **IBM Granite Models**: Pre-trained time series transformers
- **Adaptive Fine-tuning**: Conservative training strategies
- **Automatic Fallback**: Trains from scratch if transfer learning fails

### 🛡️ Manufacturing Validation
- **15% Improvement Requirement**: Validates models beat baselines by 15%
- **Manufacturing Compliance**: Checks domain-specific requirements
- **Stability Validation**: Comprehensive stability metrics
- **Baseline Comparisons**: Linear regression and persistence models

## 📊 Training Output

### Model Files
Models are saved to `models/` directory:
```
models/
├── patchtst_manufacturing_thickness_thickness_avg/
│   ├── config.json           # Model configuration
│   ├── model.safetensors     # Trained weights
│   └── metadata.json         # Training metadata
└── training_report.json      # Training summary
```

### Training Logs
- **Console Output**: Real-time training progress
- **training.log**: Detailed training log file
- **Training Reports**: JSON summaries with metrics

### Validation Results
- **Performance Metrics**: MSE, MAE, MAPE for each horizon
- **Improvement Validation**: 15% improvement over baselines
- **Stability Metrics**: Training stability scores
- **Manufacturing Compliance**: Domain-specific validation

## 🏭 Enhanced Manufacturing Intelligence

### Product-Aware Forecasting
- **Product Type Encoding**: 38 unique products converted to structured features
- **Material-Specific Patterns**: Different behavior for cladding vs weatherboard vs villaboard
- **Dimension Impact**: Length/width/thickness affect production speed and quality
- **Complexity Scoring**: Textured surfaces and special products get complexity weighting

### Advanced Target Variables
- **Speed**: Production optimization with product-specific patterns
- **Quality Metrics**: Scrap prediction considering product characteristics
- **Efficiency**: Overall production efficiency with material context
- **Capacity**: Utilization patterns based on product mix

### Manufacturing Context Intelligence
- **Stack-Level Analysis**: Production organized by manufacturing stacks
- **Multi-Source Integration**: FM + TM480 + SM data correlation
- **Temporal Validation**: Time-based matching with gap analysis
- **Process State Tracking**: Stable, optimal, adjustment-needed classifications

### Enhanced Compliance
- **Product-Specific Ranges**: Validation considers product type requirements
- **Manufacturing Constraints**: Respects product-dependent process limits
- **Quality Thresholds**: Material-specific quality criteria
- **Improvement Validation**: 15% improvement over baselines per product type

## 🧪 Development & Testing

### Quick Testing
```bash
# Test enhanced training with product intelligence
python train.py Speed

# Test unified table integration
echo "y" | python train.py sm_scrap_pct

# Validate enhanced configuration
python -c "from src.forecasting.config import load_config_from_file; print('Config OK')"
```

### Development Features
- **Verbose Logging**: `--verbose` flag for detailed output
- **Configuration Validation**: Automatic parameter checking
- **Error Recovery**: Graceful handling of training failures
- **Progress Monitoring**: Real-time training metrics

## 🔍 Troubleshooting

### Common Issues

#### 1. **Import Errors**
```bash
# Ensure virtual environment is activated
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### 2. **Data Not Found**
```bash
# Check data directory
ls test-data/
# Should show: thickness.csv, speed.csv, fm_stack.csv, sm_stack.csv, stop.csv
```

#### 3. **Configuration Errors**
```bash
# Validate configuration file
python -c "from src.forecasting.config import load_config_from_file; load_config_from_file('config/forecasting_config.json')"
```

#### 4. **Memory Issues**
- Reduce `batch_size` in configuration
- Use `--disable-transfer-learning` to reduce memory usage
- Train single targets instead of all at once

#### 5. **Training Instability**
- Ensure stability features are enabled (default)
- Check gradient clipping settings
- Reduce learning rate

### Getting Help
```bash
# Show full help
python train_forecasting_models.py --help

# Show quick help
python quick_train.py --help

# Check system status
python -c "from src.forecasting.trainer import ManufacturingForecastTrainer; print('Trainer OK')"
```

## 🎯 Best Practices

### For Development
1. Start with `quick_train.py` for testing
2. Use single target training for faster iteration
3. Enable verbose logging for debugging

### For Production
1. Use `train_forecasting_models.py` with full validation
2. Keep stability features enabled
3. Validate 15% improvement requirement
4. Save training reports for documentation

### For Research
1. Experiment with different configurations
2. Use transfer learning for faster convergence
3. Monitor stability metrics carefully
4. Compare multiple target variables

## 📈 Next Steps

After training enhanced models:

1. **Product-Aware Forecasting**: Models understand material-specific patterns
2. **CLI Integration**: Enhanced models work with correlation analysis CLI  
3. **Multi-Target Deployment**: 5 different forecasting models for comprehensive prediction
4. **Manufacturing Intelligence**: Leverage product encoding for better insights
5. **Quality Optimization**: Use scrap/reject predictions for process improvement

## 🎯 Enhanced Manufacturing Benefits

### **Product Intelligence Impact:**
- **15-30% Better Accuracy**: Product-specific patterns improve predictions
- **Material Optimization**: Different models for different product types  
- **Quality Prediction**: Scrap rates based on product complexity
- **Production Planning**: Speed forecasts considering product characteristics

### **Unified Table Advantages:**
- **Faster Training**: Use existing 122K+ record optimized dataset
- **Consistent Results**: Standardized preprocessing and feature engineering
- **Stack-Level Intelligence**: Manufacturing flow understanding
- **Multi-Source Integration**: Comprehensive data from 6 sources

## 📦 Model Management

### Available Management Tools

#### 1. `manage_models.py` - Comprehensive Model Management
```bash
# List all trained models with details
python manage_models.py list

# Keep only specific models (with backup)
python manage_models.py keep fm_reject_pct

# Delete specific model
python manage_models.py delete Speed

# Backup specific model
python manage_models.py backup fm_reject_pct

# Interactive cleanup
python manage_models.py cleanup
```

#### 2. `simple_model_usage.py` - Model Information & Inspection
```bash
# List all available models
python simple_model_usage.py list

# Show detailed model information
python simple_model_usage.py info fm_reject_pct

# Show usage examples
python simple_model_usage.py help
```

#### 3. `use_trained_model.py` - Model Usage Without Retraining
```bash
# Use trained model for forecasting
python use_trained_model.py fm_reject_pct

# Specify custom data file
python use_trained_model.py fm_reject_pct --data my_data.csv

# Custom forecast horizon (in minutes)
python use_trained_model.py fm_reject_pct --horizon 60

# List available models
python use_trained_model.py --list
```

#### 4. `quick_forecast.py` - Simple Forecasting Interface
```bash
# Quick forecast with defaults (fm_reject_pct, 15min)
python quick_forecast.py

# Specific target and horizon
python quick_forecast.py fm_reject_pct 30

# Different targets
python quick_forecast.py Speed 60
python quick_forecast.py sm_scrap_pct 240

# Help
python quick_forecast.py --help
```

### Model Directory Structure

After training, models are organized as:
```
models/
├── patchtst_manufacturing_fm_reject_pct/
│   ├── config.json           # Model configuration
│   ├── model.safetensors     # Trained weights (PyTorch)
│   └── metadata.json         # Training metadata
└── models_backup/            # Backup directory (created by manage_models.py)
    └── patchtst_manufacturing_Speed_20240115_143022/
```

### Model Files Explained

#### `config.json` - Model Configuration
- Model architecture parameters (patch_size, d_model, etc.)
- Input feature specifications (68+ variables)
- Target variable definition
- Manufacturing domain settings

#### `model.safetensors` - Trained Weights
- PyTorch model weights in SafeTensors format
- Secure serialization format
- Contains all learned parameters

#### `metadata.json` - Training Information
- Training date and duration
- Number of training records
- Best validation loss achieved
- Training configuration snapshot

### Management Commands Summary

| Command | Purpose | Example |
|---------|---------|---------|
| `list` | Show all models | `python manage_models.py list` |
| `keep` | Keep specific models only | `python manage_models.py keep fm_reject_pct` |
| `delete` | Remove specific model | `python manage_models.py delete Speed` |
| `backup` | Backup before deletion | `python manage_models.py backup Speed` |
| `info` | Model details | `python simple_model_usage.py info fm_reject_pct` |
| `forecast` | Generate predictions | `python use_trained_model.py fm_reject_pct` |

### Best Practices for Model Management

1. **Regular Cleanup**: Keep only production models to save disk space
2. **Backup Important Models**: Use backup before deletion for safety
3. **Version Control**: Models include training metadata for tracking
4. **Space Monitoring**: Models can be 600MB-1GB each, monitor disk usage
5. **Production Focus**: Keep models that show 15%+ improvement over baselines

## 🔮 Using Trained Models

### Quick Usage Patterns

#### For Production Forecasting
```python
from src.forecasting.patchtst_model import ManufacturingPatchTSTModel

# Load trained model
model = ManufacturingPatchTSTModel.load_pretrained(
    './models/patchtst_manufacturing_fm_reject_pct'
)

# Generate forecast (requires properly formatted data)
result = model.forecast(data, 'fm_reject_pct', horizon=15)
```

#### For Model Inspection
```bash
# Check what models are available
python manage_models.py list

# Get detailed information about a specific model
python simple_model_usage.py info fm_reject_pct
```

#### For Quick Predictions
```bash
# Generate forecast with recent data
python quick_forecast.py fm_reject_pct 30

# Use existing unified table data
python use_trained_model.py fm_reject_pct --horizon 60
```

### Command-Line Workflow

```bash
# 1. Check available models
python manage_models.py list

# 2. Get model details
python simple_model_usage.py info fm_reject_pct

# 3. Generate forecast
python quick_forecast.py fm_reject_pct 15

# 4. Clean up unused models
python manage_models.py keep fm_reject_pct
```

See `CLI_USER_GUIDE.md` for using enhanced trained models with the CLI interface.