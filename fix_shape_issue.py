#!/usr/bin/env python3
"""
Quick fix for PatchTST shape mismatch issue
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.forecasting.patchtst_model import ManufacturingDataset

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dataset_shape():
    """Test and debug the dataset shape issue"""
    
    # Create a sample sequence to test the dataset
    sample_sequences = [{
        'features': [[1.0, 2.0, 3.0, 4.0, 5.0, 6.0] for _ in range(240)],  # 240 timesteps, 6 features
        'targets': {
            'horizon_240': [7.0] * 240  # 240 target values (single target variable)
        }
    }]
    
    logger.info("Creating test dataset...")
    dataset = ManufacturingDataset(sample_sequences, max_horizon=240)
    
    logger.info(f"Dataset length: {len(dataset)}")
    
    # Get first sample
    sample = dataset[0]
    
    logger.info(f"past_values shape: {sample['past_values'].shape}")
    logger.info(f"future_values shape: {sample['future_values'].shape}")
    
    # Expected shapes:
    # past_values: [240, 6] (sequence_length, num_features)
    # future_values: [240, 1] (prediction_length, 1) for univariate prediction
    
    expected_past_shape = (240, 6)
    expected_future_shape = (240, 1)
    
    if sample['past_values'].shape == expected_past_shape:
        logger.info("✅ past_values shape is correct")
    else:
        logger.error(f"❌ past_values shape mismatch: got {sample['past_values'].shape}, expected {expected_past_shape}")
    
    if sample['future_values'].shape == expected_future_shape:
        logger.info("✅ future_values shape is correct")
    else:
        logger.error(f"❌ future_values shape mismatch: got {sample['future_values'].shape}, expected {expected_future_shape}")
    
    return sample

def main():
    """Main function to test and fix shape issues"""
    logger.info("🔧 Testing PatchTST dataset shape configuration...")
    
    try:
        sample = test_dataset_shape()
        
        logger.info("\n📊 Sample data inspection:")
        logger.info(f"Features (first 3 timesteps): {sample['past_values'][:3].tolist()}")
        logger.info(f"Targets (first 3 timesteps): {sample['future_values'][:3].tolist()}")
        
        logger.info("\n✅ Dataset shape test completed successfully!")
        
        # Quick model compatibility test
        logger.info("\n🤖 Testing model compatibility...")
        
        import torch
        from transformers import PatchTSTConfig, PatchTSTForPrediction
        
        # Create model config
        config = PatchTSTConfig(
            num_input_channels=6,
            context_length=240,
            prediction_length=240,
            patch_length=16,
            patch_stride=16,
            d_model=64,
            num_attention_heads=4,
            num_hidden_layers=2,
            dropout=0.1,
            num_targets=1,  # CRITICAL: Only predict 1 target variable
            loss="mse"
        )
        
        # Create model
        model = PatchTSTForPrediction(config)
        model.eval()
        
        # Test forward pass
        batch_size = 2
        past_values = torch.randn(batch_size, 240, 6)  # [batch, context_length, num_input_channels]
        
        logger.info(f"Input shape: {past_values.shape}")
        
        with torch.no_grad():
            outputs = model(past_values=past_values)
            
        # Check output shape
        if hasattr(outputs, 'prediction_outputs'):
            pred_shape = outputs.prediction_outputs.shape
            logger.info(f"Prediction output shape: {pred_shape}")
            
            # Expected: [batch_size, prediction_length, num_targets] = [2, 240, 1]
            expected_shape = (batch_size, 240, 1)
            if pred_shape == expected_shape:
                logger.info("✅ Model output shape is correct!")
            else:
                logger.error(f"❌ Model output shape mismatch: got {pred_shape}, expected {expected_shape}")
        else:
            logger.info(f"Raw output type: {type(outputs)}")
            if hasattr(outputs, 'last_hidden_state'):
                logger.info(f"Last hidden state shape: {outputs.last_hidden_state.shape}")
        
        logger.info("\n🎉 Shape compatibility test completed!")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())