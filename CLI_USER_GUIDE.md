# 🏭 Manufacturing Correlation Analysis & Forecasting CLI - User Guide

Welcome to the AI-powered correlation analysis and advanced forecasting system for fiber cement manufacturing! This comprehensive guide will help you effectively use the CLI to discover insights and generate predictions from your manufacturing data.

## Table of Contents

1. [Introduction & Overview](#introduction--overview)
2. [Quick Start](#quick-start)
3. [Installation & Configuration](#installation--configuration)
4. [Command Reference](#command-reference)
5. [Interactive Mode Guide](#interactive-mode-guide)
6. [Analysis Mode Guide](#analysis-mode-guide)
7. [Analysis Types](#analysis-types)
8. [Data Management](#data-management)
9. [Settings Configuration](#settings-configuration)
10. [Output & Export](#output--export)
11. [Example Workflows](#example-workflows)
12. [Troubleshooting](#troubleshooting)
13. [Advanced Usage](#advanced-usage)

---

## Introduction & Overview

The Manufacturing Correlation Analysis & Forecasting CLI provides AI-powered statistical analysis and advanced time series forecasting for manufacturing data with a focus on fiber cement production. It combines advanced statistical methods, state-of-the-art transformer models, and domain-specific expertise to uncover correlations, predict future trends, identify quality issues, and provide actionable insights.

### Key Capabilities

**🏭 Enhanced Unified Table Analysis (Phase 4.0):**
- **🏗️ 83-Column Manufacturing Intelligence**: Complete analysis across unified manufacturing table with 122K+ records
- **📊 Stratified Analysis Engine**: Multi-dimensional correlation discovery by time lags, production shifts, efficiency ranges, machine types
- **🔍 Pattern Identification System**: Quality threshold analysis, operational risk detection, statistical anomaly identification
- **🤖 ML Quality Prediction**: RandomForest-based quality prediction with feature importance and optimization insights
- **⏰ Time Series Analysis**: Hourly aggregation, rolling correlations, temporal pattern discovery
- **🧠 Intelligent Auto-Detection**: Automatic analysis type selection based on natural language query patterns

**🔍 Multi-Method Correlation Discovery:**
- **🔬 Three Correlation Methods**: Automatic detection of significant relationships using Pearson, Spearman, and Kendall methods
- **🧠 Intelligent Method Selection**: Data-driven recommendations for optimal correlation method based on distribution characteristics
- **📊 Method Convergence Analysis**: Assessment of robustness and stability across different correlation approaches
- **🎯 Bootstrap Robustness Testing**: Stability analysis with confidence intervals and reliability metrics

**🔮 Advanced Time Series Forecasting (Phase 3.1):**
- **🏭 PatchTST Forecasting**: State-of-the-art transformer architecture with stability enhancements
- **⏰ Multi-Horizon Predictions**: 15-minute, 1-hour, 4-hour, and 24-hour forecasting capabilities
- **🗣️ Natural Language Interface**: "Create a 1-hour forecast for thickness" - direct CLI integration
- **🎯 Manufacturing Intelligence**: Process-specific insights and optimization recommendations
- **📈 Uncertainty Quantification**: Confidence intervals and prediction reliability metrics
- **🔧 Stability-Enhanced Training**: Gradient clipping, UnitNorm layers, and StabilizedTrainer
- **🚀 Transfer Learning**: IBM Granite model integration with adaptive fine-tuning
- **✅ Production-Ready Validation**: Comprehensive stability testing and manufacturing compliance

**🤖 AI-Powered Analysis:**
- **🤖 AI-Powered Insights**: LLM-driven analysis with manufacturing domain knowledge
- **🏭 Unified Manufacturing Intelligence**: 83-column analysis across complete SM-FM manufacturing flows
- **📊 Stratified Process Analysis**: Multi-dimensional analysis by shifts, efficiency ranges, machine types
- **🔍 Quality Pattern Recognition**: Automated detection of quality issues and operational risk factors
- **🤖 ML-Driven Predictions**: RandomForest quality prediction with feature importance insights
- **⏱️ Time-Lag Analysis**: Detection of delayed correlations between process variables
- **🎯 Quality Focus**: Specialized analysis for quality issues and root cause analysis
- **📈 Process Optimization**: Identification of optimization opportunities with predictive modeling

**📊 Complete Visualization & Export:**
- **📊 Complete Visualization Suite**: 5 agent-integrated tools for automatic visualization generation
- **🎨 Professional Output**: PNG and HTML formats with manufacturing-specific styling
- **🤖 Automatic Generation**: Agent creates visualizations during correlation analysis
- **💾 Export & Integration**: JSON export for integration with other systems

### Supported Data Types

**Unified Manufacturing Table (83 Columns):**
- **Complete SM-FM Flow Analysis**: End-to-end manufacturing process correlation analysis
- **Production Metrics**: Speed, throughput, efficiency calculations, cycle times
- **Quality Measurements**: Thickness (10-sensor arrays), acceptance/rejection rates, scrap rates with off-roller factor
- **Machine Events**: Stoppages, maintenance events, operational status, downtime analysis
- **Process Variables**: Temperature, pressure, material properties, sensor readings
- **Time-Based Features**: Shift patterns, lag analysis, temporal correlations
- **Manufacturing Orders**: Complete traceability from SM to FM to final products

---

## Quick Start

Get up and running in 3 minutes with your first correlation analysis.

### 1. Environment Setup

```bash
# Activate virtual environment
source venv/bin/activate

# Verify CLI is available
python -m src.cli --help
```

### 2. Set API Configuration

Ensure your `.env` file contains all required variables:
```env
LLM_PROVIDER=ANTHROPIC
ANTHROPIC_API_KEY=your_api_key_here
ANTHROPIC_MODEL=claude-sonnet-4-20250514
```

⚠️ **All required environment variables must be set** - the system will fail with clear error messages if any are missing.

### 3. First Analysis

```bash
# Run unified table analysis
python -m src.cli analyze -q "What are the key quality correlations across complete SM-FM manufacturing flows?"

# Stratified analysis by production shifts
python -m src.cli analyze -q "How do correlations vary across different production shifts and efficiency ranges?"

# Pattern identification for quality issues
python -m src.cli analyze -q "Identify quality patterns and operational risk factors"

# ML quality prediction analysis
python -m src.cli analyze -q "Predict quality using machine learning and show feature importance"

# Time series correlation analysis
python -m src.cli analyze -q "Analyze temporal patterns and rolling correlations in manufacturing data"

# With output file
python -m src.cli analyze -q "What correlations exist between speed and thickness?" -o results.json
```

### 4. Interactive Mode

```bash
# Start interactive session
python -m src.cli interactive

# Then type your questions:
# > What process variables affect quality?
# > /analyze quality
# > /export analysis_results.json
```

---

## Installation & Configuration

### Prerequisites

- Python 3.8+
- Virtual environment activated
- Required packages installed via `requirements.txt`

### Environment Configuration

Create or update your `.env` file in the project root:

```env
# LLM Provider Selection
LLM_PROVIDER=ANTHROPIC

# Anthropic Configuration (Required if LLM_PROVIDER=ANTHROPIC)
ANTHROPIC_API_KEY=sk-ant-your-api-key-here
ANTHROPIC_MODEL=claude-sonnet-4-20250514

# Google Vertex AI Configuration (Required if LLM_PROVIDER=VERTEX_AI)
# LLM_PROVIDER=VERTEX_AI
# VERTEX_AI_PROJECT=your-project-id
# VERTEX_AI_LOCATION=us-east5
# GEMINI_MODEL=gemini-2.5-pro
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials.json

# Data Configuration
DATA_PATH=test-data/
CACHE_PATH=.cache/

# Analysis Settings (optional)
DEFAULT_CORRELATION_METHOD=pearson
DEFAULT_TIME_WINDOW=60
DEFAULT_SIGNIFICANCE_THRESHOLD=0.05
DEFAULT_MIN_CORRELATION=0.3
```

### ⚠️ **Important: Strict Configuration Required**

**All required environment variables must be set:**
- **For Anthropic**: Both `ANTHROPIC_API_KEY` and `ANTHROPIC_MODEL` are required
- **For Vertex AI**: `VERTEX_AI_PROJECT`, `GEMINI_MODEL`, and `GOOGLE_APPLICATION_CREDENTIALS` are required

**The system will fail with descriptive error messages if any required variable is missing - no fallback values are used.**

### Data Directory Structure

Organize your data files in the specified directory (default: `test-data/`):

```
test-data/
├── stop.csv          # Machine stoppage events
├── speed.csv         # Production line speed data
├── thickness.csv     # Product thickness measurements
├── fm_stack.csv      # Forming machine scrap/stack data
└── sm_stack.csv      # Sheet machine scrap/stack data
```

### Verification

```bash
# Test configuration
python -m src.cli analyze -q "Test analysis" --data-dir test-data

# Check data loading
python -m src.cli interactive
# Then type: /data
```

---

## Command Reference

### Main CLI Commands

```bash
python -m src.cli [OPTIONS] COMMAND [ARGS]...
```

**Global Options:**
- `--help`: Show help message

**Available Commands:**
- `interactive`: Start interactive correlation analysis session
- `analyze`: Run single correlation analysis query

### Interactive Command

```bash
python -m src.cli interactive [OPTIONS]
```

**Options:**
- `-d, --data-dir TEXT`: Data directory path (default: test-data)

**Example:**
```bash
python -m src.cli interactive --data-dir /path/to/manufacturing/data
```

### Analyze Command

```bash
python -m src.cli analyze [OPTIONS]
```

**Options:**
- `-d, --data-dir TEXT`: Data directory path
- `-q, --query TEXT`: Analysis query (required)
- `-o, --output TEXT`: Output file for results
- `-t, --analysis-type TEXT`: Analysis type (default: general)

**Example:**
```bash
python -m src.cli analyze \
  -q "Find correlations between speed and quality metrics" \
  -t quality \
  -o speed_quality_analysis.json \
  --data-dir production-data/
```

---

## Interactive Mode Guide

Interactive mode provides a rich, conversational interface for exploring your manufacturing data.

### Starting Interactive Mode

```bash
python -m src.cli interactive
```

### Welcome Screen

Upon startup, you'll see:
- Data auto-loading progress
- Available datasets summary
- Command overview
- API configuration status

### Interactive Commands

All interactive commands start with `/`:

#### `/help` - Show Help
```
> /help
```
Displays comprehensive help with commands, analysis types, and example questions.

#### `/load` - Load Data
```
> /load
```
(Re)loads manufacturing data files and creates unified dataset. Shows:
- Dataset summary table
- Record counts and time ranges
- Data quality indicators
- Unified dataset dimensions

#### `/data` - Data Summary
```
> /data
```
Shows detailed information about loaded datasets:
- Total records and datasets
- Numeric variables available for correlation
- Individual dataset details
- Data quality scores
- Time range coverage

#### `/settings` - Configuration
```
> /settings
```
View and modify analysis settings:
- Significance threshold (p-value cutoff)
- Minimum correlation coefficient
- Correlation method (pearson, spearman, kendall)
- Time window for analysis

**Interactive Setting Modification:**
```
> /settings
Do you want to modify any settings? [y/N]: y
Enter new value for significance_threshold (current: 0.05): 0.01
Enter new value for min_correlation (current: 0.3): 0.5
...
```

#### `/analyze [type]` - Run Analysis
```
> /analyze unified_table
> /analyze stratified
> /analyze pattern_identification
> /analyze ml_prediction
> /analyze time_series
> /analyze general
> /analyze quality
> /analyze lag
```
Prompts for analysis query and runs specified analysis type.

#### `/export [filename]` - Export Results
```
> /export
> /export my_analysis_results.json
```
Exports conversation history and analysis results to JSON file.

#### `/clear` - Clear History
```
> /clear
```
Clears conversation history (data remains loaded).

#### `/quit` - Exit
```
> /quit
```
Exit the interactive session.

### Natural Language Queries

Beyond commands, ask questions in natural language:

**🔮 Advanced Forecasting Queries:**
```
> Create a 1-hour forecast for thickness
> Generate multi-horizon forecasts for speed and thickness
> Predict quality metrics for the next 4 hours
> What will scrap rates look like in 24 hours?
> Compare forecast scenarios across multiple variables
> Train new forecasting models for all manufacturing parameters
> Show uncertainty quantification for thickness predictions
> Generate manufacturing insights from forecast trends
```

**Multi-Method Analysis with Visualization:**
```
> Compare Pearson, Spearman, and Kendall correlations for all manufacturing variables
> Which correlation method should I use for speed vs thickness analysis?
> Show me side-by-side correlation heatmaps for all three methods
> Calculate multi-method correlations and recommend the best method for each variable pair
> Analyze method convergence patterns in this dataset
> Create comprehensive multi-method dashboard with visualizations
> Generate all visualizations for my correlation analysis
```

**Method Selection & Assessment:**
```
> What correlation method is optimal for this data distribution?
> Show me correlations that are consistent across all three methods
> Calculate robustness metrics using bootstrap sampling
> Assess data distribution and recommend correlation methods
> Find correlations with high method convergence scores
```

**Traditional Analysis:**
```
> What correlations exist between speed and thickness?
> Analyze the impact of stoppages on scrap rates
> Show me variables that predict quality issues
> Find strong correlations above 0.6
> What happens to quality after production stops?
```

### Example Interactive Session

```bash
$ python -m src.cli interactive

🏭 Manufacturing Correlation Analysis CLI
✓ Automatically loaded 5 datasets
  stop: 47508 records
  speed: 78449 records  
  thickness: 78002 records
  fm_stack: 60009 records
  sm_stack: 70829 records
✓ Created unified dataset: (262028, 6)

You > What correlations exist between speed and thickness?

=== CORRELATION ANALYSIS RESULTS ===
Data Quality Score: 0.981

Significant Correlations Found: 2
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Variable 1        │ Variable 2           │ Correlation │ P-value │ Significance ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ speed_Speed       │ fm_stack_Sheet Qty   │ 0.038      │ 0.0001  │ high         │
│ speed_Speed       │ fm_stack_Accepted    │ 0.005      │ 0.3421  │ low          │
└───────────────────┴──────────────────────┴─────────────┴─────────┴──────────────┘

Key Insights:
• HIGH Production speed shows weak positive correlation with sheet quantity
• MEDIUM Speed has minimal correlation with sheet acceptance rates
• HIGH Time-lagged analysis shows maximum correlation at 40-minute delay

Recommendations:
1. Monitor speed impacts on production volume during optimization
2. Speed control appears effective - quality not significantly impacted
3. Consider 40-minute delay effects in process control algorithms

You > /export speed_analysis.json
✓ Results exported to speed_analysis.json

You > /quit
Thank you for using Manufacturing Correlation Analysis!
```

---

## Analysis Mode Guide

Analysis mode enables single-command correlation analysis perfect for automation and quick queries.

### Basic Usage

```bash
python -m src.cli analyze -q "Your analysis question"
```

### Query Examples

**🏭 Enhanced Unified Table Analysis (Phase 4.0):**
```bash
python -m src.cli analyze -q "What are the key quality correlations across complete SM-FM manufacturing flows?" -t unified_table

python -m src.cli analyze -q "How do correlations vary across different production shifts and efficiency ranges?" -t stratified

python -m src.cli analyze -q "Identify quality patterns and operational risk factors" -t pattern_identification

python -m src.cli analyze -q "Predict quality using machine learning and show feature importance" -t ml_prediction

python -m src.cli analyze -q "Analyze temporal patterns and rolling correlations in manufacturing data" -t time_series

python -m src.cli analyze -q "Stratify correlations by time lag bins and machine types" -t stratified

python -m src.cli analyze -q "Find quality threshold violations and statistical anomalies" -t pattern_identification

python -m src.cli analyze -q "Train RandomForest models for quality prediction with SHAP analysis" -t ml_prediction
```

**🔮 Advanced Forecasting Analysis:**
```bash
python -m src.cli analyze -q "Create a 1-hour forecast for thickness" -t forecasting

python -m src.cli analyze -q "Generate multi-horizon forecasts for speed and quality metrics" -t forecasting

python -m src.cli analyze -q "Predict manufacturing parameters for the next 4 hours" -t forecasting

python -m src.cli analyze -q "Train forecasting models for all target variables" -t forecasting

python -m src.cli analyze -q "Compare forecast scenarios across multiple manufacturing variables" -t forecasting
```

**Multi-Method Analysis with Visualization:**
```bash
python -m src.cli analyze -q "Compare Pearson, Spearman, and Kendall correlations for all variables" -t multi_method

python -m src.cli analyze -q "Calculate multi-method correlations and recommend optimal methods" -t method_selection

python -m src.cli analyze -q "Show method convergence analysis for manufacturing variables" -t method_convergence

python -m src.cli analyze -q "Create comprehensive multi-method dashboard with visualizations" -t visualization

python -m src.cli analyze -q "Generate side-by-side correlation heatmaps for all three methods" -t visualization
```

**Method-Specific Analysis:**
```bash
python -m src.cli analyze -q "Which correlation method should I use for speed vs thickness?" -t method_selection

python -m src.cli analyze -q "Find correlations that are robust across all three methods" -t method_convergence

python -m src.cli analyze -q "Assess data distribution and recommend correlation methods" -t method_selection
```

**General Correlations:**
```bash
python -m src.cli analyze -q "What are the strongest correlations in the data?"
```

**Specific Variable Relationships:**
```bash
python -m src.cli analyze -q "How does production speed correlate with quality metrics?"
```

**Process Impact Analysis:**
```bash
python -m src.cli analyze -q "What impact do stoppages have on subsequent production quality?"
```

**Threshold-Based Analysis:**
```bash
python -m src.cli analyze -q "Find all correlations above 0.5 with high statistical significance"
```

### Analysis Types

Specify analysis focus with the `-t` parameter:

```bash
# Advanced forecasting (NEW)
python -m src.cli analyze -q "Create forecasts for manufacturing parameters" -t forecasting

# Multi-method correlation analysis
python -m src.cli analyze -q "Compare all three correlation methods" -t multi_method

# Method convergence analysis
python -m src.cli analyze -q "Assess method stability and convergence" -t method_convergence

# Method selection guidance
python -m src.cli analyze -q "Recommend optimal correlation methods" -t method_selection

# Complete visualization suite
python -m src.cli analyze -q "Generate comprehensive dashboards" -t visualization

# General correlation discovery
python -m src.cli analyze -q "Analyze manufacturing relationships" -t general

# Quality-focused analysis
python -m src.cli analyze -q "Find quality issues" -t quality

# Time-lag analysis
python -m src.cli analyze -q "Delayed correlations" -t lag

# Process optimization
python -m src.cli analyze -q "Optimization opportunities" -t optimization

# Root cause analysis
python -m src.cli analyze -q "Root causes of defects" -t rca
```

### Output Control

**Console Output Only:**
```bash
python -m src.cli analyze -q "Your question"
```

**Export to File:**
```bash
python -m src.cli analyze -q "Your question" -o analysis_results.json
```

**Custom Data Directory:**
```bash
python -m src.cli analyze -q "Your question" --data-dir /path/to/data
```

### Automation Examples

**Scheduled Analysis:**
```bash
#!/bin/bash
# Daily correlation analysis
DATE=$(date +%Y%m%d)
python -m src.cli analyze \
  -q "Daily quality correlation analysis" \
  -t quality \
  -o "daily_analysis_${DATE}.json"
```

**Batch Processing:**
```bash
# Multiple analyses
QUERIES=(
  "Speed vs Quality correlations"
  "Stoppage impact analysis"  
  "Thickness variation analysis"
)

for query in "${QUERIES[@]}"; do
  python -m src.cli analyze -q "$query" -o "${query// /_}.json"
done
```

---

## Analysis Types

The CLI supports **15 specialized analysis types**, including **5 new Phase 4.0 unified table analysis types**, advanced forecasting, multi-method capabilities, and complete visualization suite, each optimized for different manufacturing insights.

### 🏭 Enhanced Unified Table Analysis (Phase 4.0)

#### Unified Table Analysis (`unified_table`) 🆕

**Purpose:** Complete 83-column manufacturing table analysis across all process variables
**Best For:** Comprehensive manufacturing intelligence, complete SM-FM flow analysis
**Output:** Full correlation matrix with intelligent feature grouping and manufacturing insights

**Example:**
```bash
python -m src.cli analyze -q "What are the key quality correlations across complete SM-FM manufacturing flows?" -t unified_table
```

**Capabilities:**
- 83-column correlation analysis with intelligent feature grouping
- Complete SM-FM manufacturing flow analysis
- Material-specific correlation patterns
- Process variable importance ranking
- Manufacturing domain-specific insights

#### Stratified Analysis (`stratified`) 🆕

**Purpose:** Multi-dimensional correlation analysis by production context
**Best For:** Understanding how correlations vary across different operational conditions
**Output:** Stratified correlation matrices by time lags, shifts, efficiency ranges, machine types

**Example:**
```bash
python -m src.cli analyze -q "How do correlations vary across different production shifts and efficiency ranges?" -t stratified
```

**Stratification Dimensions:**
- Time lag bins (0-15min, 15-30min, 30-60min, >60min)
- Production shifts (morning, afternoon, night)
- Efficiency ranges (low <0.7, medium 0.7-0.85, high >0.85)
- Machine types (FM vs TM480 vs other configurations)

#### Pattern Identification (`pattern_identification`) 🆕

**Purpose:** Automated detection of quality issues and operational risk factors
**Best For:** Quality control, risk assessment, operational anomaly detection
**Output:** Quality threshold analysis, statistical anomaly identification, risk factor ranking

**Example:**
```bash
python -m src.cli analyze -q "Identify quality patterns and operational risk factors" -t pattern_identification
```

**Detection Capabilities:**
- Quality threshold violations (scrap rate >10%, >20%)
- Statistical anomaly identification using Z-scores
- Operational risk factor ranking
- Process control limit analysis
- Quality pattern recognition across shifts and materials

#### ML Quality Prediction (`ml_prediction`) 🆕

**Purpose:** Machine learning-based quality prediction with feature importance
**Best For:** Predictive quality control, process optimization, feature importance analysis
**Output:** RandomForest quality prediction models with feature importance rankings and SHAP analysis

**Example:**
```bash
python -m src.cli analyze -q "Predict quality using machine learning and show feature importance" -t ml_prediction
```

**ML Capabilities:**
- RandomForest quality prediction models
- Feature importance analysis for process optimization
- SHAP (SHapley Additive exPlanations) value analysis
- Cross-validation performance metrics
- Prediction confidence intervals

#### Time Series Analysis (`time_series`) 🆕

**Purpose:** Temporal pattern analysis with rolling correlations
**Best For:** Trend analysis, seasonal patterns, temporal correlation discovery
**Output:** Rolling correlation analysis, hourly aggregations, temporal pattern identification

**Example:**
```bash
python -m src.cli analyze -q "Analyze temporal patterns and rolling correlations in manufacturing data" -t time_series
```

**Temporal Capabilities:**
- Rolling correlation analysis with multiple window sizes
- Hourly and daily aggregation patterns
- Shift-based correlation variations
- Temporal trend identification
- Seasonality and cycle detection

### Advanced PatchTST Forecasting (`forecasting`) 🆕

**Purpose:** Time series forecasting using state-of-the-art transformer architecture
**Best For:** Predictive maintenance, production planning, quality prediction, trend analysis
**Output:** Multi-horizon forecasts with confidence intervals, manufacturing insights, and process recommendations

**Example:**
```bash
python -m src.cli analyze -q "Create a 1-hour forecast for thickness" -t forecasting
```

**Typical Insights:**
- Multi-horizon predictions (15 minutes, 1 hour, 4 hours, 24 hours)
- Uncertainty quantification with confidence intervals
- Manufacturing-specific trend analysis and recommendations
- Process optimization opportunities based on forecasts
- Predictive quality alerts and maintenance scheduling
- Model performance metrics and baseline comparisons

**Forecasting Capabilities:**
- **Target Variables**: thickness_avg, speed, scrap_rate, quality metrics
- **Natural Language Interface**: "Predict quality for next 4 hours"
- **Automatic Model Training**: HuggingFace PatchTST with manufacturing optimization
- **Manufacturing Intelligence**: Process-specific insights and recommendations
- **Integration with Phase 4.0**: Works seamlessly with unified table analysis and stratified insights

### Multi-Method Analysis (`multi_method`)

**Purpose:** Comprehensive comparison of Pearson, Spearman, and Kendall correlation methods
**Best For:** Robust correlation discovery, method validation, comprehensive analysis
**Output:** Side-by-side correlation results for all three methods with convergence analysis and automatic visualizations

**Example:**
```bash
python -m src.cli analyze -q "Compare Pearson, Spearman, and Kendall correlations for all variables" -t multi_method
```

**Typical Insights:**
- Method-specific correlation strengths
- Method convergence scores and stability
- Robust correlations consistent across methods
- Method-specific p-values and confidence intervals
- Comprehensive correlation matrix comparisons

### Complete Visualization Suite (`visualization`)

**Purpose:** Automatic generation of comprehensive visualization dashboards
**Best For:** Visual analysis, presentation materials, comprehensive visual reports
**Output:** PNG and HTML visualizations with professional manufacturing styling

**Example:**
```bash
python -m src.cli analyze -q "Create comprehensive visualization dashboard for correlation analysis" -t visualization
```

**Generated Visualizations:**
- Side-by-side multi-method correlation heatmaps
- Method convergence analysis dashboards
- Interactive correlation comparison matrices
- Comprehensive multi-method analysis dashboards
- Bootstrap robustness visualization plots
- **NEW:** Forecasting trend visualizations and prediction confidence plots

**Output Files:**
- `multi_method_heatmaps.png` - Side-by-side correlation matrices
- `method_convergence_dashboard.html` - Interactive convergence analysis
- `multi_method_dashboard.html` - Comprehensive analysis dashboard
- `method_comparison_matrix.png` - Detailed method comparisons
- `forecast_trends.png` - Time series predictions with confidence intervals
- Organized in timestamped directories for easy access

### Method Convergence Analysis (`method_convergence`)

**Purpose:** Assessment of correlation method stability and agreement
**Best For:** Validating correlation robustness, stability analysis
**Output:** Method convergence scores, cross-method correlations, stability metrics

**Example:**
```bash
python -m src.cli analyze -q "How stable are correlations across different methods?" -t method_convergence
```

**Typical Insights:**
- Overall method convergence scores
- Cross-method correlation agreements
- Method stability rankings
- Bootstrap robustness metrics
- High/medium/low convergence classifications

### Method Selection Guidance (`method_selection`)

**Purpose:** Data-driven recommendation for optimal correlation method
**Best For:** Choosing the right method, understanding data characteristics
**Output:** Method recommendations with data distribution assessment

**Example:**
```bash
python -m src.cli analyze -q "Which correlation method should I use for this data?" -t method_selection
```

**Typical Insights:**
- Optimal method recommendations per variable pair
- Data distribution characteristics (normality, outliers, linearity)
- Method selection reasoning and confidence levels
- Data quality assessment for method selection
- Manufacturing-specific method guidance

### General Analysis (`general`)

**Purpose:** Comprehensive correlation discovery across all variables
**Best For:** Initial data exploration, discovering unexpected relationships
**Output:** Broad correlation matrix with statistical significance testing

**Example:**
```bash
python -m src.cli analyze -q "What are the key relationships in this manufacturing data?" -t general
```

**Typical Insights:**
- Strong positive/negative correlations
- Variable clustering patterns
- Overall data quality assessment
- Unexpected relationships

### Lag Correlation Analysis (`lag`)

**Purpose:** Time-delayed correlation discovery
**Best For:** Understanding process delays, cause-effect timing
**Output:** Optimal lag periods, time-shifted correlation coefficients

**Example:**
```bash
python -m src.cli analyze -q "How do temperature changes affect quality with time delays?" -t lag
```

**Typical Insights:**
- Optimal delay periods between cause and effect
- Process response times
- Propagation delays through production line
- Predictive lead times

### Quality Analysis (`quality`)

**Purpose:** Quality-focused correlation discovery
**Best For:** Quality improvement, defect reduction, acceptance rate optimization
**Output:** Quality-specific correlations, defect predictors

**Example:**
```bash
python -m src.cli analyze -q "What process variables predict quality issues?" -t quality
```

**Typical Insights:**
- Quality metric correlations
- Defect rate predictors
- Acceptance/rejection patterns
- Quality control effectiveness

### Process Optimization (`optimization`)

**Purpose:** Efficiency and throughput optimization
**Best For:** Production optimization, bottleneck identification
**Output:** Efficiency correlations, optimization opportunities

**Example:**
```bash
python -m src.cli analyze -q "How can we optimize production efficiency?" -t optimization
```

**Typical Insights:**
- Throughput optimization factors
- Efficiency bottlenecks
- Speed vs quality trade-offs
- Resource utilization patterns

### Root Cause Analysis (`rca`)

**Purpose:** Problem investigation and root cause identification
**Best For:** Investigating specific issues, failure analysis
**Output:** Causal relationships, root cause hypotheses

**Example:**
```bash
python -m src.cli analyze -q "What causes the high scrap rate in sector 3?" -t rca
```

**Typical Insights:**
- Potential root causes
- Contributing factor analysis
- Failure mode correlations
- Diagnostic recommendations

---

## Data Management

### Data Requirements

The CLI expects CSV files with specific naming conventions and structures:

#### Required Files

| File | Purpose | Key Columns |
|------|---------|-------------|
| `stop.csv` | Machine stoppages | Stop Date, Stop Time, MPS Stop Duration |
| `speed.csv` | Production speed | Log Date, Log Time, Speed |
| `thickness.csv` | Product thickness | Sensor Date, Sensor Time, Thickness |
| `fm_stack.csv` | Forming machine data | Finish Start Date, Finish Start ime*, Sheet Quantity |
| `sm_stack.csv` | Sheet machine data | First Sheet Date, First Sheet Time, Sheet data |

*Note: "Finish Start ime" is the actual column name (typo in source data)

#### Timestamp Handling

The CLI automatically combines date and time columns:
- `stop.csv`: `Stop Date` + `Stop Time` → `timestamp`
- `speed.csv`: `Log Date` + `Log Time` → `timestamp`
- `thickness.csv`: `Sensor Date` + `Sensor Time` → `timestamp`
- `fm_stack.csv`: `Finish Start Date` + `Finish Start ime` → `timestamp`
- `sm_stack.csv`: `First Sheet Date` + `First Sheet Time` → `timestamp`

### Data Loading Process

1. **File Discovery**: Scans data directory for CSV files
2. **Schema Validation**: Checks for required columns
3. **Timestamp Creation**: Combines date/time columns
4. **Data Type Conversion**: Converts numeric columns
5. **Quality Assessment**: Calculates completeness and validity
6. **Unification**: Creates single timeline with all variables

### Data Quality Indicators

The CLI provides comprehensive data quality assessment:

**Quality Score (0-1):**
- **> 0.8**: Excellent quality
- **0.6-0.8**: Good quality  
- **0.4-0.6**: Fair quality
- **< 0.4**: Poor quality

**Quality Factors:**
- Missing data percentage
- Timestamp validity
- Numeric data consistency
- Outlier detection
- Data completeness

### Viewing Data Status

**Interactive Mode:**
```
> /data
```

**Expected Output:**
```
Data Summary:
Total datasets: 5
Total records: 334,797
Unified dataset: 262,028 rows, 6 columns
Numeric variables for correlation: 4

STOP:
  Records: 47,508
  Variables: 5
  Missing data: 0.2%
  Time range: 2025-01-02 16:19:42 to 2025-07-03 15:26:42
  Valid timestamps: 47506/47508

SPEED:
  Records: 78,449
  Variables: 4
  Missing data: 0.0%
  Time range: 2025-01-02 16:19:42 to 2025-07-03 15:26:42
  Valid timestamps: 78449/78449
```

### Data Troubleshooting

**Common Issues:**

1. **Missing Files**
   ```
   Error: No data files found in test-data
   Solution: Ensure CSV files are in the correct directory
   ```

2. **Timestamp Errors**
   ```
   Warning: Created 47506/47508 valid timestamps
   Solution: Check date/time format consistency
   ```

3. **Low Data Quality**
   ```
   Data Quality Score: 0.45
   Solution: Review missing data and outliers
   ```

4. **No Correlations Found**
   ```
   Significant Correlations: 0
   Solution: Lower correlation threshold or check data variability
   ```

---

## Settings Configuration

### Default Settings

The CLI uses intelligent defaults optimized for manufacturing data:

```
Significance Threshold: 0.05    # p-value for statistical significance
Minimum Correlation: 0.3        # Minimum correlation to report
Correlation Method: pearson     # Statistical method
Time Window: 60 minutes         # Default analysis window
```

### Environment Variable Configuration

Configure defaults in `.env` file:

```env
# Analysis Settings
DEFAULT_SIGNIFICANCE_THRESHOLD=0.05
DEFAULT_MIN_CORRELATION=0.01    # Lowered to capture small correlations
DEFAULT_CORRELATION_METHOD=pearson
DEFAULT_TIME_WINDOW=60

# Multi-Method Settings
ENABLE_MULTI_METHOD_ANALYSIS=true
DEFAULT_CONVERGENCE_THRESHOLD=0.8
BOOTSTRAP_SAMPLES=100

# Forecasting Settings (NEW)
ENABLE_FORECASTING=true
DEFAULT_FORECAST_HORIZON=60
MODEL_TRAINING_EPOCHS=10
FORECASTING_CONFIDENCE_LEVEL=0.95
```

### Interactive Configuration

In interactive mode, use `/settings` command:

```
> /settings

Current Analysis Settings:
┏━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Setting               ┃ Current Value ┃ Description                                                         ┃
┡━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ significance_threshold │ 0.05          │ P-value threshold for statistical significance                     │
│ min_correlation        │ 0.3           │ Minimum correlation coefficient to report                          │
│ correlation_method     │ pearson       │ Correlation method (pearson, spearman, kendall)                   │
│ time_window           │ 60            │ Default time window for analysis (minutes)                         │
└───────────────────────┴───────────────┴─────────────────────────────────────────────────────────────────────┘

Do you want to modify any settings? [y/N]: y
Enter new value for significance_threshold (current: 0.05): 0.01
Enter new value for min_correlation (current: 0.3): 0.5
Enter new value for correlation_method (current: pearson): spearman
Enter new value for time_window (current: 60): 30
Settings updated!
```

### Setting Descriptions

#### Significance Threshold
- **Range:** 0.001 - 0.1
- **Default:** 0.05
- **Purpose:** P-value cutoff for statistical significance
- **Lower values:** More stringent significance requirements
- **Higher values:** More lenient significance requirements

**Recommended Values:**
- **0.01**: High confidence analysis
- **0.05**: Standard analysis (default)
- **0.1**: Exploratory analysis

#### Minimum Correlation
- **Range:** 0.1 - 0.9
- **Default:** 0.3
- **Purpose:** Minimum correlation coefficient to report
- **Lower values:** Report weak correlations
- **Higher values:** Focus on strong correlations only

**Recommended Values:**
- **0.1**: Detect weak relationships
- **0.3**: Moderate relationships (default)
- **0.5**: Strong relationships only
- **0.7**: Very strong relationships only

#### Correlation Method
- **Options:** pearson, spearman, kendall, multi_method, forecasting
- **Default:** pearson
- **Purpose:** Statistical correlation calculation method

**Method Selection:**
- **Pearson**: Linear relationships, normally distributed data
- **Spearman**: Monotonic relationships, non-parametric
- **Kendall**: Small sample sizes, ordinal data
- **Multi_method**: Compare all three methods with convergence analysis
- **Forecasting**: **NEW:** Advanced time series forecasting with PatchTST

#### Time Window
- **Range:** 1 - 1440 minutes
- **Default:** 60
- **Purpose:** Default time window for lag analysis and forecasting
- **Lower values:** Fine-grained temporal analysis
- **Higher values:** Long-term pattern analysis

#### Forecasting Horizon 🆕
- **Range:** 1 - 1440 minutes
- **Default:** 60
- **Purpose:** Default forecast horizon for predictive analysis
- **Common values:** 15 (quarter-hour), 60 (hour), 240 (4-hour), 1440 (day)

---

## Output & Export

### Console Output Format

The CLI provides rich, structured output with color coding and formatting:

#### Analysis Results Header
```
=== CORRELATION ANALYSIS RESULTS ===
Data Quality Score: 0.981
```

#### Correlation Table
```
Significant Correlations Found: 2
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Variable 1        │ Variable 2           │ Correlation │ P-value │ Significance ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ speed_Speed       │ fm_stack_Sheet Qty   │ 0.478      │ 0.0001  │ high         │
│ speed_Speed       │ fm_stack_Accepted    │ 0.396      │ 0.0012  │ high         │
└───────────────────┴──────────────────────┴─────────────┴─────────┴──────────────┘
```

#### Key Insights
```
Key Insights:
• HIGH Strong positive correlation between accepted and rejected sheets indicates process instability
• MEDIUM Speed shows moderate correlation with production volume
• HIGH Time-lagged analysis reveals 40-minute optimal delay
```

#### Recommendations
```
Recommendations:
1. Investigate quality control calibration between acceptance/rejection systems
2. Optimize speed settings for maximum throughput without quality impact
3. Implement 40-minute predictive quality monitoring
```

### JSON Export Format

Exported JSON files contain structured analysis results:

```json
{
  "query": "What correlations exist between speed and thickness?",
  "analysis_type": "general",
  "timestamp": "2025-07-04T16:24:28.624879",
  "results": {
    "dataset_summary": {
      "total_records": 262028,
      "time_range": {
        "start": "2025-01-02T16:19:42",
        "end": "2025-07-03T15:26:42"
      },
      "variables_analyzed": [
        "speed_Speed",
        "fm_stack_Sheet Quantity",
        "fm_stack_Sheet Accepted",
        "fm_stack_Total Sheet Rejected"
      ]
    },
    "significant_correlations": [
      {
        "variable_1": "speed_Speed",
        "variable_2": "fm_stack_Sheet Quantity",
        "correlation_coefficient": 0.478,
        "p_value": 0.0001,
        "significance_level": "high",
        "confidence_interval": [0.412, 0.539],
        "sample_size": 45678,
        "method": "pearson"
      }
    ],
    "insights": [
      {
        "insight_type": "correlation_strength",
        "description": "Strong positive correlation between accepted and rejected sheets",
        "supporting_evidence": {
          "correlation": 0.478,
          "p_value": 0.0001,
          "sample_size": 45678
        },
        "confidence_level": "high",
        "actionable_recommendation": "Investigate quality control calibration"
      }
    ],
    "recommendations": [
      "Investigate quality control calibration between acceptance/rejection systems",
      "Optimize speed settings for maximum throughput without quality impact"
    ],
    "data_quality_score": 0.981,
    "analysis_metadata": {
      "analysis_timestamp": "2025-07-04T16:24:28.624879",
      "data_timeframe": "2025-01-02T16:19:42 to 2025-07-03T15:26:42",
      "methods_used": ["pearson_correlation", "significance_testing"],
      "settings": {
        "significance_threshold": 0.05,
        "min_correlation": 0.3,
        "correlation_method": "pearson"
      }
    },
    "forecasting_results": {
      "forecast_enabled": true,
      "target_variable": "thickness_avg",
      "forecast_horizon": 60,
      "forecast_values": [12.5, 12.3, 12.7, 12.8],
      "confidence_intervals": {
        "lower": [12.1, 11.9, 12.3, 12.4],
        "upper": [12.9, 12.7, 13.1, 13.2]
      },
      "model_performance": {
        "mse": 0.045,
        "mae": 0.032,
        "mape": 2.1
      },
      "manufacturing_insights": [
        "Thickness trend is stable within acceptable range",
        "No quality alerts predicted for next hour"
      ]
    }
  }
}
```

### Export Options

#### Interactive Mode Export
```
> /export
> /export custom_filename.json
```

#### Analysis Mode Export
```bash
python -m src.cli analyze -q "Your question" -o results.json
```

#### Conversation History Export (Interactive Only)

Interactive mode exports include full conversation history:

```json
{
  "export_timestamp": "2025-07-04T16:30:00",
  "settings": {
    "significance_threshold": 0.05,
    "min_correlation": 0.3,
    "correlation_method": "pearson",
    "time_window": 60
  },
  "data_summary": {
    "datasets_loaded": ["stop", "speed", "thickness", "fm_stack", "sm_stack"],
    "unified_data_shape": [262028, 6]
  },
  "analysis_history": [
    {
      "timestamp": "2025-07-04T16:25:00",
      "query": "What correlations exist between speed and thickness?",
      "analysis_type": "general",
      "results": { /* Full analysis results */ }
    }
  ]
}
```

---

## Example Workflows

### Workflow 1: Daily Quality Monitoring with Forecasting

**Objective:** Monitor daily quality correlations and predict future trends for process control

```bash
#!/bin/bash
# daily_quality_check.sh

DATE=$(date +%Y%m%d)
OUTPUT_DIR="quality_reports"
mkdir -p $OUTPUT_DIR

# Quality-focused analysis
python -m src.cli analyze \
  -q "What process variables are correlated with quality issues today?" \
  -t quality \
  -o "${OUTPUT_DIR}/quality_analysis_${DATE}.json"

# NEW: Predictive quality forecasting
python -m src.cli analyze \
  -q "Generate 4-hour quality forecasts for predictive maintenance" \
  -t forecasting \
  -o "${OUTPUT_DIR}/quality_forecast_${DATE}.json"

# Process optimization check
python -m src.cli analyze \
  -q "Identify optimization opportunities in current process settings" \
  -t optimization \
  -o "${OUTPUT_DIR}/optimization_${DATE}.json"

echo "Daily quality analysis with forecasting completed: ${OUTPUT_DIR}/"
```

### Workflow 2: Root Cause Investigation

**Objective:** Investigate specific quality issues

```bash
# Interactive investigation session
python -m src.cli interactive

# Session commands:
# > What causes high scrap rates in the forming machine?
# > /analyze rca
# > What correlations exist 30 minutes before quality issues?
# > /settings
# > (change time_window to 30)
# > /analyze lag
# > /export rca_investigation.json
```

### Workflow 3: Process Optimization Campaign with Predictive Analysis

**Objective:** Systematic optimization analysis with forecasting insights

```bash
# Multi-analysis optimization study with forecasting
QUERIES=(
  "What speed settings optimize throughput without affecting quality?"
  "How do stoppage patterns correlate with subsequent production efficiency?"
  "What process variables show the strongest correlations with productivity?"
  "Identify bottlenecks in the production process"
)

# NEW: Forecasting queries for optimization
FORECAST_QUERIES=(
  "Predict optimal production speeds for next 24 hours"
  "Forecast potential quality issues in next 4 hours"
  "Generate multi-horizon forecasts for efficiency optimization"
)

for i in "${!QUERIES[@]}"; do
  python -m src.cli analyze \
    -q "${QUERIES[$i]}" \
    -t optimization \
    -o "optimization_study_$((i+1)).json"
done

# NEW: Add forecasting analysis
for i in "${!FORECAST_QUERIES[@]}"; do
  python -m src.cli analyze \
    -q "${FORECAST_QUERIES[$i]}" \
    -t forecasting \
    -o "forecast_optimization_$((i+1)).json"
done

# Combine results for comprehensive review
echo "Optimization study with forecasting complete. Review files: optimization_study_*.json and forecast_optimization_*.json"
```

### Workflow 4: Shift Handover Analysis

**Objective:** Automated shift report generation

```bash
#!/bin/bash
# shift_handover.sh

SHIFT=$(date +%H | awk '{if($1>=6 && $1<14) print "day"; else if($1>=14 && $1<22) print "evening"; else print "night"}')
TIMESTAMP=$(date +%Y%m%d_%H%M)

python -m src.cli analyze \
  -q "Summarize key correlations and process status for shift handover" \
  -t general \
  -o "shift_reports/handover_${SHIFT}_${TIMESTAMP}.json"

# Extract key metrics for dashboard
python -c "
import json
with open('shift_reports/handover_${SHIFT}_${TIMESTAMP}.json', 'r') as f:
    data = json.load(f)
print(f'Data Quality: {data[\"results\"][\"data_quality_score\"]:.3f}')
print(f'Significant Correlations: {len(data[\"results\"][\"significant_correlations\"])}')
print(f'Key Insights: {len(data[\"results\"][\"insights\"])}')
"
```

### Workflow 5: Lag Analysis for Predictive Maintenance

**Objective:** Identify predictive correlations for maintenance planning

```bash
# Interactive lag analysis session
python -m src.cli interactive <<EOF
/settings
(modify time_window to 120 for 2-hour analysis window)
What correlations exist between early process indicators and later quality issues?
/analyze lag
How do vibration patterns correlate with subsequent mechanical issues?
/analyze lag
What process variables can predict maintenance needs 4 hours in advance?
/export predictive_maintenance_analysis.json
/quit
EOF
```

### Workflow 6: Automated Monitoring Dashboard with Predictive Alerts

**Objective:** Continuous monitoring with predictive alert thresholds

```bash
#!/bin/bash
# monitoring_dashboard.sh

# Run analysis every hour
while true; do
  TIMESTAMP=$(date +%Y%m%d_%H%M)
  
  # Quick quality check
  python -m src.cli analyze \
    -q "Current process correlations and quality indicators" \
    -t quality \
    -o "monitoring/quality_${TIMESTAMP}.json"
  
  # NEW: Predictive forecasting for early warning
  python -m src.cli analyze \
    -q "Predict quality issues and process deviations for next 2 hours" \
    -t forecasting \
    -o "monitoring/forecast_${TIMESTAMP}.json"
  
  # Check for current anomalies
  QUALITY_SCORE=$(python -c "
import json
with open('monitoring/quality_${TIMESTAMP}.json', 'r') as f:
    data = json.load(f)
print(data['results']['data_quality_score'])
")
  
  # NEW: Check forecasting alerts
  FORECAST_ALERTS=$(python -c "
import json
with open('monitoring/forecast_${TIMESTAMP}.json', 'r') as f:
    data = json.load(f)
insights = data.get('results', {}).get('forecasting_results', {}).get('manufacturing_insights', [])
alerts = [insight for insight in insights if 'alert' in insight.lower() or 'warning' in insight.lower()]
print(len(alerts))
")
  
  # Alert if quality score drops
  if (( $(echo "$QUALITY_SCORE < 0.7" | bc -l) )); then
    echo "ALERT: Quality score dropped to $QUALITY_SCORE at $(date)"
    # Send notification (email, Slack, etc.)
  fi
  
  # NEW: Alert for predicted issues
  if [ "$FORECAST_ALERTS" -gt 0 ]; then
    echo "PREDICTIVE ALERT: $FORECAST_ALERTS potential issues forecasted at $(date)"
    # Send predictive maintenance notification
  fi
  
  # Wait 1 hour
  sleep 3600
done
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Import Errors

**Error:**
```
ImportError: attempted relative import with no known parent package
```

**Solution:**
```bash
# Use module format instead of direct execution
python -m src.cli --help

# Not: python src/cli.py --help
```

#### 2. Environment Variable Issues

**Error:**
```
ValueError: invalid literal for int() with base 10: '60  # minutes'
```

**Solution:**
Remove inline comments from `.env` file:
```env
# Wrong:
DEFAULT_TIME_WINDOW=60  # minutes

# Correct:
DEFAULT_TIME_WINDOW=60
```

#### 3. API Configuration Issues

**Error:**
```
ValueError: ANTHROPIC_API_KEY not found in environment. Please set this variable to use Anthropic models.
ValueError: ANTHROPIC_MODEL not found in environment. Please set this variable (e.g., 'claude-sonnet-4-20250514').
```

**Solution:**
```bash
# Add both required variables to .env file:
echo "ANTHROPIC_API_KEY=your-key-here" >> .env
echo "ANTHROPIC_MODEL=claude-sonnet-4-20250514" >> .env

# Verify configuration:
grep "ANTHROPIC_" .env

# For Vertex AI users:
echo "GEMINI_MODEL=gemini-2.5-pro" >> .env
grep "GEMINI_MODEL" .env
```

#### 4. Data Loading Failures

**Error:**
```
No data files found in test-data
```

**Solutions:**
```bash
# Check data directory exists
ls -la test-data/

# Verify CSV files present
ls -la test-data/*.csv

# Use custom data directory
python -m src.cli analyze -q "test" --data-dir /path/to/data
```

#### 5. Low Data Quality Scores

**Issue:** Data quality score < 0.6

**Diagnostic Commands:**
```bash
# Interactive mode investigation
python -m src.cli interactive
> /data
> /load
```

**Common Causes:**
- High percentage of missing values
- Invalid timestamp formats
- Non-numeric data in numeric columns
- Excessive outliers

**Solutions:**
- Review CSV file formats
- Check timestamp column consistency
- Validate numeric data types
- Clean outliers in source data

#### 6. No Correlations Found

**Issue:** "Significant Correlations: 0"

**Solutions:**
```bash
# Lower correlation threshold
python -m src.cli interactive
> /settings
> (set min_correlation to 0.1)

# Change correlation method
> (set correlation_method to spearman)

# Check data variability
> /data
```

#### 7. Timeout Issues

**Issue:** Analysis takes too long or times out

**Solutions:**
- Check network connectivity for API calls
- Reduce data size for testing
- Use local caching for large datasets
- Increase timeout in production environments

#### 8. Memory Issues

**Issue:** Out of memory with large datasets

**Solutions:**
```bash
# Check dataset size
python -m src.cli interactive
> /data

# Use data sampling for large datasets
# Optimize data loading in production
```

### Performance Optimization

#### Data Loading
- **Cache unified datasets** for repeated analysis
- **Validate CSV formats** before processing
- **Use appropriate data types** for memory efficiency

#### Analysis Speed
- **Start with general analysis** before specialized types
- **Use appropriate correlation methods** for data characteristics
- **Batch multiple queries** in interactive mode

#### API Usage
- **Reuse interactive sessions** to avoid reloading data
- **Cache results** for similar queries
- **Monitor API rate limits**

### Debug Mode

Enable detailed logging for troubleshooting:

```bash
# Set environment variable for debug logging
export PYTHONPATH="."
export DEBUG=1

# Run with verbose output
python -m src.cli analyze -q "debug test" -t general
```

### Getting Help

**Documentation:**
- CLI User Guide (this document)
- Test documentation: `tests/README.md`
- Project documentation: `CLAUDE.md`

**Interactive Help:**
```bash
python -m src.cli --help
python -m src.cli analyze --help
python -m src.cli interactive
> /help
```

**Log Analysis:**
- Check console output for warnings and errors
- Review exported JSON files for analysis details
- Monitor data quality scores for input validation

---

## Advanced Usage

### Automation and Integration

#### CI/CD Integration

```yaml
# .github/workflows/manufacturing-analysis.yml
name: Manufacturing Data Analysis

on:
  schedule:
    - cron: '0 */6 * * *'  # Every 6 hours
  workflow_dispatch:

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v3
        with:
          python-version: '3.9'
          
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          
      - name: Run Analysis
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          ANTHROPIC_MODEL: ${{ secrets.ANTHROPIC_MODEL }}
        run: |
          python -m src.cli analyze \
            -q "Automated quality and efficiency analysis" \
            -t general \
            -o analysis_results.json
            
      - name: Upload Results
        uses: actions/upload-artifact@v3
        with:
          name: analysis-results
          path: analysis_results.json
```

#### Docker Integration

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV DATA_PATH=/data

# Create data volume
VOLUME /data

# Default command
CMD ["python", "-m", "src.cli", "interactive"]
```

```bash
# Build and run
docker build -t manufacturing-cli .
docker run -it -v ./data:/data manufacturing-cli

# With environment file
docker run -it --env-file .env -v ./data:/data manufacturing-cli
```

#### API Server Integration

Create a web API wrapper for the CLI:

```python
# api_server.py
from fastapi import FastAPI, BackgroundTasks
from pydantic import BaseModel
import subprocess
import json
import uuid

app = FastAPI()

class AnalysisRequest(BaseModel):
    query: str
    analysis_type: str = "general"
    data_dir: str = "test-data"

@app.post("/analyze")
async def analyze_data(request: AnalysisRequest, background_tasks: BackgroundTasks):
    # Generate unique filename
    output_file = f"analysis_{uuid.uuid4().hex}.json"
    
    # Run CLI analysis
    result = subprocess.run([
        "python", "-m", "src.cli", "analyze",
        "-q", request.query,
        "-t", request.analysis_type,
        "-o", output_file,
        "--data-dir", request.data_dir
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        # Load and return results
        with open(output_file, 'r') as f:
            analysis_results = json.load(f)
        
        # Schedule cleanup
        background_tasks.add_task(cleanup_file, output_file)
        
        return {"status": "success", "results": analysis_results}
    else:
        return {"status": "error", "message": result.stderr}

def cleanup_file(filename: str):
    import os
    try:
        os.remove(filename)
    except FileNotFoundError:
        pass
```

### Custom Analysis Pipelines

#### Multi-Stage Analysis Pipeline

```python
# pipeline.py
import subprocess
import json
import pandas as pd
from datetime import datetime

class ManufacturingAnalysisPipeline:
    def __init__(self, data_dir="test-data"):
        self.data_dir = data_dir
        self.results = []
    
    def run_analysis(self, query, analysis_type="general"):
        """Run single analysis and store results"""
        output_file = f"temp_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        result = subprocess.run([
            "python", "-m", "src.cli", "analyze",
            "-q", query,
            "-t", analysis_type,
            "-o", output_file,
            "--data-dir", self.data_dir
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            with open(output_file, 'r') as f:
                analysis_result = json.load(f)
            self.results.append(analysis_result)
            os.remove(output_file)  # Cleanup
            return analysis_result
        else:
            raise Exception(f"Analysis failed: {result.stderr}")
    
    def comprehensive_analysis(self):
        """Run comprehensive multi-type analysis"""
        analyses = [
            ("What are the strongest correlations in the manufacturing data?", "general"),
            ("What process variables predict quality issues?", "quality"),
            ("What time-delayed correlations exist in the process?", "lag"),
            ("What optimization opportunities exist?", "optimization")
        ]
        
        results = {}
        for query, analysis_type in analyses:
            print(f"Running {analysis_type} analysis...")
            results[analysis_type] = self.run_analysis(query, analysis_type)
        
        return results
    
    def generate_report(self, results):
        """Generate comprehensive analysis report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_analyses": len(results),
                "average_data_quality": sum(r["results"]["data_quality_score"] for r in results.values()) / len(results),
                "total_correlations": sum(len(r["results"]["significant_correlations"]) for r in results.values()),
                "total_insights": sum(len(r["results"]["insights"]) for r in results.values())
            },
            "analyses": results
        }
        
        return report

# Usage
if __name__ == "__main__":
    pipeline = ManufacturingAnalysisPipeline()
    results = pipeline.comprehensive_analysis()
    report = pipeline.generate_report(results)
    
    with open("comprehensive_analysis_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("Comprehensive analysis complete!")
```

### Performance Monitoring

#### Analysis Performance Tracker

```python
# performance_monitor.py
import time
import json
import subprocess
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.metrics = []
    
    def timed_analysis(self, query, analysis_type="general"):
        """Run analysis with performance monitoring"""
        start_time = time.time()
        
        result = subprocess.run([
            "python", "-m", "src.cli", "analyze",
            "-q", query,
            "-t", analysis_type,
            "-o", "temp_perf_analysis.json"
        ], capture_output=True, text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Load results for additional metrics
        if result.returncode == 0:
            with open("temp_perf_analysis.json", 'r') as f:
                analysis_result = json.load(f)
            
            data_quality = analysis_result["results"]["data_quality_score"]
            correlation_count = len(analysis_result["results"]["significant_correlations"])
            insight_count = len(analysis_result["results"]["insights"])
        else:
            data_quality = 0
            correlation_count = 0
            insight_count = 0
        
        # Store performance metrics
        self.metrics.append({
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "analysis_type": analysis_type,
            "duration_seconds": duration,
            "data_quality_score": data_quality,
            "correlations_found": correlation_count,
            "insights_generated": insight_count,
            "success": result.returncode == 0
        })
        
        return duration, result.returncode == 0
    
    def benchmark_suite(self):
        """Run standard benchmark queries"""
        benchmarks = [
            ("What are the key correlations?", "general"),
            ("Quality correlation analysis", "quality"),
            ("Time-lag correlation analysis", "lag"),
            ("Process optimization analysis", "optimization")
        ]
        
        results = {}
        for query, analysis_type in benchmarks:
            print(f"Benchmarking {analysis_type}...")
            duration, success = self.timed_analysis(query, analysis_type)
            results[analysis_type] = {"duration": duration, "success": success}
        
        return results
    
    def generate_performance_report(self):
        """Generate performance analysis report"""
        if not self.metrics:
            return {"error": "No performance data available"}
        
        successful_runs = [m for m in self.metrics if m["success"]]
        
        report = {
            "summary": {
                "total_runs": len(self.metrics),
                "successful_runs": len(successful_runs),
                "success_rate": len(successful_runs) / len(self.metrics),
                "average_duration": sum(m["duration_seconds"] for m in successful_runs) / len(successful_runs) if successful_runs else 0,
                "average_data_quality": sum(m["data_quality_score"] for m in successful_runs) / len(successful_runs) if successful_runs else 0
            },
            "by_analysis_type": {},
            "detailed_metrics": self.metrics
        }
        
        # Group by analysis type
        for metric in successful_runs:
            analysis_type = metric["analysis_type"]
            if analysis_type not in report["by_analysis_type"]:
                report["by_analysis_type"][analysis_type] = []
            report["by_analysis_type"][analysis_type].append(metric)
        
        return report

# Usage
monitor = PerformanceMonitor()
benchmark_results = monitor.benchmark_suite()
performance_report = monitor.generate_performance_report()

with open("performance_report.json", "w") as f:
    json.dump(performance_report, f, indent=2)

print("Performance analysis complete!")
```

### Custom Configuration Management

#### Advanced Configuration System

```python
# config_manager.py
import os
import json
from pathlib import Path
from typing import Dict, Any

class ConfigManager:
    """Advanced configuration management for manufacturing analysis CLI"""
    
    def __init__(self, config_file="analysis_config.json"):
        self.config_file = Path(config_file)
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                return json.load(f)
        else:
            return self.create_default_config()
    
    def create_default_config(self) -> Dict[str, Any]:
        """Create default configuration"""
        config = {
            "analysis_settings": {
                "significance_threshold": 0.05,
                "min_correlation": 0.3,
                "correlation_method": "pearson",
                "time_window": 60
            },
            "data_settings": {
                "data_directory": "test-data",
                "cache_directory": ".cache",
                "required_files": ["stop.csv", "speed.csv", "thickness.csv", "fm_stack.csv", "sm_stack.csv"]
            },
            "analysis_profiles": {
                "quick": {
                    "significance_threshold": 0.1,
                    "min_correlation": 0.5,
                    "analysis_types": ["general"]
                },
                "detailed": {
                    "significance_threshold": 0.01,
                    "min_correlation": 0.2,
                    "analysis_types": ["general", "quality", "lag", "optimization"]
                },
                "quality_focus": {
                    "significance_threshold": 0.05,
                    "min_correlation": 0.3,
                    "analysis_types": ["quality", "rca"]
                }
            },
            "export_settings": {
                "default_format": "json",
                "include_metadata": True,
                "auto_timestamp": True
            }
        }
        
        self.save_config(config)
        return config
    
    def save_config(self, config: Dict[str, Any] = None):
        """Save configuration to file"""
        if config is None:
            config = self.config
        
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
    
    def get_profile_settings(self, profile: str) -> Dict[str, Any]:
        """Get settings for specific analysis profile"""
        if profile in self.config["analysis_profiles"]:
            base_settings = self.config["analysis_settings"].copy()
            profile_settings = self.config["analysis_profiles"][profile]
            base_settings.update(profile_settings)
            return base_settings
        else:
            raise ValueError(f"Unknown profile: {profile}")
    
    def run_profile_analysis(self, profile: str, query: str):
        """Run analysis with specific profile settings"""
        settings = self.get_profile_settings(profile)
        
        # Export settings to environment variables temporarily
        env_updates = {
            "DEFAULT_SIGNIFICANCE_THRESHOLD": str(settings["significance_threshold"]),
            "DEFAULT_MIN_CORRELATION": str(settings["min_correlation"]),
            "DEFAULT_CORRELATION_METHOD": settings["correlation_method"],
            "DEFAULT_TIME_WINDOW": str(settings["time_window"])
        }
        
        # Update environment
        original_env = {}
        for key, value in env_updates.items():
            original_env[key] = os.environ.get(key)
            os.environ[key] = value
        
        try:
            # Run analysis with profile settings
            for analysis_type in settings.get("analysis_types", ["general"]):
                output_file = f"{profile}_{analysis_type}_analysis.json"
                
                result = subprocess.run([
                    "python", "-m", "src.cli", "analyze",
                    "-q", query,
                    "-t", analysis_type,
                    "-o", output_file
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"✓ {profile} {analysis_type} analysis completed: {output_file}")
                else:
                    print(f"✗ {profile} {analysis_type} analysis failed: {result.stderr}")
        
        finally:
            # Restore environment
            for key, value in original_env.items():
                if value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = value

# Usage
config_manager = ConfigManager()

# Run different analysis profiles
config_manager.run_profile_analysis("quick", "What are the main correlations?")
config_manager.run_profile_analysis("detailed", "Comprehensive manufacturing analysis")
config_manager.run_profile_analysis("quality_focus", "Quality issues and root causes")
```

This comprehensive user guide provides everything needed to effectively use the Manufacturing Correlation Analysis CLI, from basic usage to advanced automation and integration scenarios.