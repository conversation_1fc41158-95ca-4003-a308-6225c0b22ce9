#!/usr/bin/env python3
"""
Basic Multi-Method Correlation Analysis Example

This script demonstrates the basic usage of the multi-method correlation analysis
system for manufacturing data analysis.

Usage:
    python examples/multi_method_analysis_basic.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.multi_correlations import MultiMethodCorrelationAnalyzer
from src.data.loader import ManufacturingDataLoader
import pandas as pd


def main():
    """Run basic multi-method correlation analysis example"""
    
    print("🔬 Multi-Method Correlation Analysis - Basic Example")
    print("=" * 60)
    
    # Step 1: Load manufacturing data
    print("\n📁 Step 1: Loading Manufacturing Data")
    print("-" * 40)
    
    try:
        loader = ManufacturingDataLoader('test-data')
        datasets = loader.load_all_manufacturing_data()
        unified_data = loader.create_unified_dataset()
        
        print(f"✅ Loaded {len(datasets)} datasets")
        print(f"✅ Created unified dataset: {unified_data.shape[0]:,} rows, {unified_data.shape[1]} columns")
        
        # Display available numeric variables
        numeric_vars = unified_data.select_dtypes(include=['float64', 'int64']).columns.tolist()
        print(f"📊 Numeric variables available: {len(numeric_vars)}")
        for var in numeric_vars:
            print(f"   • {var}")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # Step 2: Initialize multi-method analyzer
    print("\n🧮 Step 2: Initializing Multi-Method Analyzer")
    print("-" * 50)
    
    analyzer = MultiMethodCorrelationAnalyzer(
        significance_level=0.05
    )
    print("✅ Multi-method analyzer initialized")
    
    # Step 3: Calculate multi-method correlations
    print("\n🔍 Step 3: Calculating Multi-Method Correlations")
    print("-" * 55)
    
    try:
        # Select key manufacturing variables for analysis
        variables_to_analyze = [var for var in numeric_vars if any(
            keyword in var.lower() for keyword in ['speed', 'thickness', 'quantity', 'accepted']
        )][:4]  # Limit to 4 variables for demo
        
        print(f"🎯 Analyzing variables: {variables_to_analyze}")
        
        results = analyzer.calculate_multi_method_correlations(
            unified_data,
            variables=variables_to_analyze,
            min_periods=50
        )
        
        print(f"✅ Calculated correlations for {len(results)} variable pairs")
        
    except Exception as e:
        print(f"❌ Error calculating correlations: {e}")
        return
    
    # Step 4: Display results
    print("\n📊 Step 4: Multi-Method Correlation Results")
    print("-" * 45)
    
    for pair_key, result in results.items():
        print(f"\n🔗 {result.variable_1} ↔ {result.variable_2}")
        print(f"   Sample size: {result.sample_size:,}")
        
        # Display all three correlation methods
        print(f"   📈 Pearson:  r = {result.pearson_correlation:8.6f}, p = {result.pearson_p_value:.6f}")
        print(f"   📈 Spearman: r = {result.spearman_correlation:8.6f}, p = {result.spearman_p_value:.6f}")
        print(f"   📈 Kendall:  r = {result.kendall_correlation:8.6f}, p = {result.kendall_p_value:.6f}")
        
        # Method convergence and recommendation
        print(f"   🎯 Convergence: {result.method_convergence_score:.6f}")
        print(f"   💡 Recommended: {result.recommended_method.title()}")
        
        # Interpretation summary
        if result.interpretation:
            pearson_strength = result.interpretation.get('pearson', 'Unknown correlation')
            print(f"   📝 Interpretation: {pearson_strength}")
    
    # Step 5: Method convergence analysis
    print("\n🔄 Step 5: Method Convergence Analysis")
    print("-" * 40)
    
    try:
        convergence_analysis = analyzer.analyze_method_convergence(results)
        
        print(f"📊 Overall convergence score: {convergence_analysis['overall_convergence_score']:.6f}")
        
        # Method stability
        if 'method_stability' in convergence_analysis:
            stability = convergence_analysis['method_stability']
            print("🏗️  Method stability:")
            for method, score in stability.items():
                print(f"   • {method.title()}: {score:.6f}")
        
        # Cross-method correlations
        if 'cross_method_correlations' in convergence_analysis:
            cross_corr = convergence_analysis['cross_method_correlations']
            print("🔗 Cross-method agreement:")
            for pair, agreement in cross_corr.items():
                method1, method2 = pair.split('_')
                print(f"   • {method1.title()} ↔ {method2.title()}: {agreement:.6f}")
        
        # Convergence distribution
        if 'convergence_distribution' in convergence_analysis:
            dist = convergence_analysis['convergence_distribution']
            print("📈 Convergence distribution:")
            print(f"   • High convergence pairs: {dist.get('high_convergence_pairs', 0)}")
            print(f"   • Medium convergence pairs: {dist.get('medium_convergence_pairs', 0)}")
            print(f"   • Low convergence pairs: {dist.get('low_convergence_pairs', 0)}")
            print(f"   • Overall convergence rate: {dist.get('convergence_rate', 0):.1%}")
        
    except Exception as e:
        print(f"❌ Error in convergence analysis: {e}")
    
    # Step 6: Robustness analysis (optional)
    print("\n🛡️  Step 6: Bootstrap Robustness Analysis")
    print("-" * 45)
    
    try:
        print("🔄 Running bootstrap analysis (this may take a moment)...")
        
        robustness_metrics = analyzer.calculate_robustness_metrics(
            unified_data,
            results,
            bootstrap_samples=50  # Reduced for demo speed
        )
        
        print(f"✅ Bootstrap analysis completed")
        
        # Display robustness results
        for pair_key, pair_metrics in robustness_metrics.items():
            result = results[pair_key]
            print(f"\n🔗 {result.variable_1} ↔ {result.variable_2}")
            
            for method in ['pearson', 'spearman', 'kendall']:
                if method in pair_metrics:
                    stability = pair_metrics[method]['stability_score']
                    print(f"   🛡️  {method.title()} stability: {stability:.6f}")
        
    except Exception as e:
        print(f"⚠️  Robustness analysis skipped: {e}")
    
    # Summary
    print("\n🎉 Analysis Complete!")
    print("=" * 30)
    print(f"✅ Analyzed {len(results)} correlation pairs")
    print(f"✅ Compared 3 correlation methods (Pearson, Spearman, Kendall)")
    print(f"✅ Assessed method convergence and robustness")
    print("\n💡 Key Takeaways:")
    print("   • Use multi-method analysis for robust correlation discovery")
    print("   • Check convergence scores to validate relationship strength")
    print("   • Follow method recommendations based on data characteristics")
    print("   • Consider bootstrap robustness for critical decisions")


if __name__ == "__main__":
    main()