"""
Time Series Processing for Manufacturing Data

This module provides specialized functions for processing time series data
from manufacturing systems, including resampling, filtering, event detection,
and temporal feature engineering.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from scipy import signal
from scipy.stats import zscore
import warnings
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TimeSeriesProcessor:
    """
    Comprehensive time series processing for manufacturing data.
    """
    
    def __init__(self):
        """Initialize the time series processor."""
        self.processed_data = {}
        self.processing_metadata = {}
    
    def resample_time_series(self, 
                           df: pd.DataFrame, 
                           target_frequency: str = '1min',
                           time_column: str = 'timestamp',
                           aggregation_methods: Dict[str, str] = None) -> pd.DataFrame:
        """
        Resample time series data to a target frequency.
        
        Args:
            df: DataFrame with time series data
            target_frequency: Target frequency (e.g., '1min', '5min', '1h')
            time_column: Name of the timestamp column
            aggregation_methods: Dictionary mapping column names to aggregation methods
            
        Returns:
            Resampled DataFrame
        """
        # Default aggregation methods
        if aggregation_methods is None:
            aggregation_methods = {
                'speed': 'mean',
                'thickness': 'mean',
                'temperature': 'mean',
                'pressure': 'mean',
                'flow_rate': 'mean',
                'count': 'sum',
                'duration': 'sum'
            }
        
        # Ensure timestamp is datetime and set as index
        df_copy = df.copy()
        if time_column in df_copy.columns:
            df_copy[time_column] = pd.to_datetime(df_copy[time_column])
            df_copy = df_copy.set_index(time_column)
        
        # Prepare aggregation dictionary
        agg_dict = {}
        for col in df_copy.columns:
            if col in aggregation_methods:
                agg_dict[col] = aggregation_methods[col]
            else:
                # Default aggregation based on data type
                if df_copy[col].dtype in ['int64', 'float64']:
                    agg_dict[col] = 'mean'
                else:
                    agg_dict[col] = 'first'
        
        # Perform resampling
        resampled = df_copy.resample(target_frequency).agg(agg_dict)
        
        # Reset index to make timestamp a column again
        resampled = resampled.reset_index()
        
        logger.info(f"Resampled from {len(df)} to {len(resampled)} records at {target_frequency} frequency")
        
        return resampled
    
    def detect_anomalies(self, 
                        df: pd.DataFrame, 
                        columns: List[str] = None,
                        method: str = 'zscore',
                        threshold: float = 3.0) -> pd.DataFrame:
        """
        Detect anomalies in time series data.
        
        Args:
            df: DataFrame with time series data
            columns: List of columns to check for anomalies
            method: Anomaly detection method ('zscore', 'iqr', 'isolation_forest')
            threshold: Threshold for anomaly detection
            
        Returns:
            DataFrame with anomaly flags
        """
        df_copy = df.copy()
        
        # Select columns to analyze
        if columns is None:
            columns = df_copy.select_dtypes(include=[np.number]).columns.tolist()
        
        # Apply anomaly detection method
        for col in columns:
            if col in df_copy.columns:
                anomaly_col = f'{col}_anomaly'
                
                if method == 'zscore':
                    # Z-score method
                    z_scores = np.abs(zscore(df_copy[col].dropna()))
                    df_copy[anomaly_col] = False
                    df_copy.loc[df_copy[col].notna(), anomaly_col] = z_scores > threshold
                
                elif method == 'iqr':
                    # Interquartile Range method
                    q1 = df_copy[col].quantile(0.25)
                    q3 = df_copy[col].quantile(0.75)
                    iqr = q3 - q1
                    lower_bound = q1 - threshold * iqr
                    upper_bound = q3 + threshold * iqr
                    
                    df_copy[anomaly_col] = (df_copy[col] < lower_bound) | (df_copy[col] > upper_bound)
                
                elif method == 'isolation_forest':
                    # Isolation Forest method
                    try:
                        from sklearn.ensemble import IsolationForest
                        
                        # Prepare data
                        data = df_copy[col].dropna().values.reshape(-1, 1)
                        
                        # Fit isolation forest
                        iso_forest = IsolationForest(contamination=0.1, random_state=42)
                        anomaly_labels = iso_forest.fit_predict(data)
                        
                        # Map results back to DataFrame
                        df_copy[anomaly_col] = False
                        df_copy.loc[df_copy[col].notna(), anomaly_col] = anomaly_labels == -1
                        
                    except ImportError:
                        logger.warning("scikit-learn not available, falling back to z-score method")
                        # Fallback to z-score
                        z_scores = np.abs(zscore(df_copy[col].dropna()))
                        df_copy[anomaly_col] = False
                        df_copy.loc[df_copy[col].notna(), anomaly_col] = z_scores > threshold
        
        return df_copy
    
    def smooth_time_series(self, 
                          df: pd.DataFrame, 
                          columns: List[str] = None,
                          method: str = 'moving_average',
                          window_size: int = 5) -> pd.DataFrame:
        """
        Apply smoothing to time series data.
        
        Args:
            df: DataFrame with time series data
            columns: List of columns to smooth
            method: Smoothing method ('moving_average', 'exponential', 'savgol')
            window_size: Size of the smoothing window
            
        Returns:
            DataFrame with smoothed data
        """
        df_copy = df.copy()
        
        # Select columns to smooth
        if columns is None:
            columns = df_copy.select_dtypes(include=[np.number]).columns.tolist()
        
        for col in columns:
            if col in df_copy.columns:
                smoothed_col = f'{col}_smoothed'
                
                if method == 'moving_average':
                    # Simple moving average
                    df_copy[smoothed_col] = df_copy[col].rolling(window=window_size, center=True).mean()
                
                elif method == 'exponential':
                    # Exponential moving average
                    df_copy[smoothed_col] = df_copy[col].ewm(span=window_size).mean()
                
                elif method == 'savgol':
                    # Savitzky-Golay filter
                    try:
                        # Ensure odd window size
                        if window_size % 2 == 0:
                            window_size += 1
                        
                        # Apply Savitzky-Golay filter
                        smoothed_values = signal.savgol_filter(
                            df_copy[col].dropna().values, 
                            window_size, 
                            polyorder=3
                        )
                        
                        # Map back to DataFrame
                        df_copy[smoothed_col] = np.nan
                        df_copy.loc[df_copy[col].notna(), smoothed_col] = smoothed_values
                        
                    except Exception as e:
                        logger.warning(f"Savitzky-Golay filter failed for {col}: {e}")
                        # Fallback to moving average
                        df_copy[smoothed_col] = df_copy[col].rolling(window=window_size, center=True).mean()
        
        return df_copy
    
    def detect_events(self, 
                     df: pd.DataFrame, 
                     column: str,
                     event_type: str = 'threshold',
                     threshold: float = None,
                     min_duration: int = 5) -> pd.DataFrame:
        """
        Detect events in time series data.
        
        Args:
            df: DataFrame with time series data
            column: Column to analyze for events
            event_type: Type of event detection ('threshold', 'change_point', 'outlier')
            threshold: Threshold value for event detection
            min_duration: Minimum duration for event to be considered valid
            
        Returns:
            DataFrame with detected events
        """
        if column not in df.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        
        df_copy = df.copy()
        events = []
        
        if event_type == 'threshold':
            if threshold is None:
                # Use mean + 2*std as threshold
                threshold = df_copy[column].mean() + 2 * df_copy[column].std()
            
            # Find threshold crossings
            above_threshold = df_copy[column] > threshold
            
            # Find start and end of events
            event_starts = above_threshold & ~above_threshold.shift(1, fill_value=False)
            event_ends = ~above_threshold & above_threshold.shift(1, fill_value=False)
            
            start_indices = df_copy[event_starts].index.tolist()
            end_indices = df_copy[event_ends].index.tolist()
            
            # Match starts and ends
            for start_idx in start_indices:
                # Find corresponding end
                end_idx = None
                for end in end_indices:
                    if end > start_idx:
                        end_idx = end
                        break
                
                if end_idx is None:
                    end_idx = df_copy.index[-1]  # Event continues to end of data
                
                duration = end_idx - start_idx
                
                if duration >= min_duration:
                    events.append({
                        'start_index': start_idx,
                        'end_index': end_idx,
                        'duration': duration,
                        'max_value': df_copy.loc[start_idx:end_idx, column].max(),
                        'event_type': 'threshold_exceedance'
                    })
        
        elif event_type == 'change_point':
            # Simple change point detection using rolling statistics
            window = 20
            
            # Calculate rolling mean and std
            rolling_mean = df_copy[column].rolling(window=window).mean()
            rolling_std = df_copy[column].rolling(window=window).std()
            
            # Detect significant changes
            mean_change = rolling_mean.diff().abs()
            std_change = rolling_std.diff().abs()
            
            # Combine changes
            change_score = mean_change + std_change
            
            # Find change points
            change_threshold = change_score.quantile(0.95)
            change_points = df_copy[change_score > change_threshold]
            
            for idx in change_points.index:
                events.append({
                    'start_index': idx,
                    'end_index': idx,
                    'duration': 1,
                    'change_magnitude': change_score.loc[idx],
                    'event_type': 'change_point'
                })
        
        elif event_type == 'outlier':
            # Outlier detection using IQR method
            q1 = df_copy[column].quantile(0.25)
            q3 = df_copy[column].quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = df_copy[(df_copy[column] < lower_bound) | (df_copy[column] > upper_bound)]
            
            for idx in outliers.index:
                events.append({
                    'start_index': idx,
                    'end_index': idx,
                    'duration': 1,
                    'value': df_copy.loc[idx, column],
                    'event_type': 'outlier'
                })
        
        # Convert events to DataFrame
        if events:
            events_df = pd.DataFrame(events)
            
            # Add timestamp information if available
            if 'timestamp' in df_copy.columns:
                events_df['start_time'] = df_copy.loc[events_df['start_index'], 'timestamp'].values
                events_df['end_time'] = df_copy.loc[events_df['end_index'], 'timestamp'].values
            
            return events_df
        else:
            return pd.DataFrame()
    
    def calculate_rolling_statistics(self, 
                                   df: pd.DataFrame, 
                                   columns: List[str] = None,
                                   window_sizes: List[int] = [5, 15, 30, 60]) -> pd.DataFrame:
        """
        Calculate rolling statistics for time series data.
        
        Args:
            df: DataFrame with time series data
            columns: List of columns to calculate statistics for
            window_sizes: List of window sizes (in time periods)
            
        Returns:
            DataFrame with rolling statistics
        """
        df_copy = df.copy()
        
        # Select columns to analyze
        if columns is None:
            columns = df_copy.select_dtypes(include=[np.number]).columns.tolist()
        
        for col in columns:
            if col in df_copy.columns:
                for window in window_sizes:
                    # Rolling mean
                    df_copy[f'{col}_mean_{window}'] = df_copy[col].rolling(window=window).mean()
                    
                    # Rolling standard deviation
                    df_copy[f'{col}_std_{window}'] = df_copy[col].rolling(window=window).std()
                    
                    # Rolling min/max
                    df_copy[f'{col}_min_{window}'] = df_copy[col].rolling(window=window).min()
                    df_copy[f'{col}_max_{window}'] = df_copy[col].rolling(window=window).max()
                    
                    # Rolling coefficient of variation
                    df_copy[f'{col}_cv_{window}'] = (
                        df_copy[f'{col}_std_{window}'] / df_copy[f'{col}_mean_{window}']
                    )
        
        return df_copy
    
    def create_time_features(self, 
                           df: pd.DataFrame, 
                           time_column: str = 'timestamp') -> pd.DataFrame:
        """
        Create time-based features from timestamp column.
        
        Args:
            df: DataFrame with timestamp column
            time_column: Name of the timestamp column
            
        Returns:
            DataFrame with time features
        """
        df_copy = df.copy()
        
        if time_column not in df_copy.columns:
            raise ValueError(f"Time column '{time_column}' not found in DataFrame")
        
        # Ensure timestamp is datetime
        df_copy[time_column] = pd.to_datetime(df_copy[time_column])
        
        # Extract time components
        df_copy['hour'] = df_copy[time_column].dt.hour
        df_copy['day_of_week'] = df_copy[time_column].dt.dayofweek
        df_copy['day_of_month'] = df_copy[time_column].dt.day
        df_copy['month'] = df_copy[time_column].dt.month
        df_copy['quarter'] = df_copy[time_column].dt.quarter
        df_copy['year'] = df_copy[time_column].dt.year
        
        # Cyclical features
        df_copy['hour_sin'] = np.sin(2 * np.pi * df_copy['hour'] / 24)
        df_copy['hour_cos'] = np.cos(2 * np.pi * df_copy['hour'] / 24)
        df_copy['day_sin'] = np.sin(2 * np.pi * df_copy['day_of_week'] / 7)
        df_copy['day_cos'] = np.cos(2 * np.pi * df_copy['day_of_week'] / 7)
        df_copy['month_sin'] = np.sin(2 * np.pi * df_copy['month'] / 12)
        df_copy['month_cos'] = np.cos(2 * np.pi * df_copy['month'] / 12)
        
        # Shift patterns (assuming 8-hour shifts)
        df_copy['shift'] = ((df_copy['hour'] // 8) + 1).astype(int)
        df_copy['is_weekend'] = (df_copy['day_of_week'] >= 5).astype(int)
        
        # Time since start of data
        df_copy['time_since_start'] = (
            df_copy[time_column] - df_copy[time_column].min()
        ).dt.total_seconds() / 3600  # Hours
        
        return df_copy
    
    def align_mixed_frequency_data(self, 
                                  high_freq_df: pd.DataFrame, 
                                  low_freq_df: pd.DataFrame,
                                  time_column: str = 'timestamp',
                                  method: str = 'forward_fill') -> pd.DataFrame:
        """
        Align mixed-frequency data (e.g., high-frequency sensor data with low-frequency quality data).
        
        Args:
            high_freq_df: High-frequency DataFrame
            low_freq_df: Low-frequency DataFrame
            time_column: Name of the timestamp column
            method: Alignment method ('forward_fill', 'backward_fill', 'interpolate')
            
        Returns:
            Aligned DataFrame
        """
        # Ensure both DataFrames have datetime timestamps
        high_freq_df[time_column] = pd.to_datetime(high_freq_df[time_column])
        low_freq_df[time_column] = pd.to_datetime(low_freq_df[time_column])
        
        # Sort by timestamp
        high_freq_df = high_freq_df.sort_values(time_column)
        low_freq_df = low_freq_df.sort_values(time_column)
        
        # Merge using merge_asof for time-based alignment
        aligned_df = pd.merge_asof(
            high_freq_df,
            low_freq_df,
            on=time_column,
            direction='backward',  # Use the most recent low-frequency value
            suffixes=('', '_low_freq')
        )
        
        # Apply specified method for missing values
        low_freq_columns = [col for col in aligned_df.columns if col.endswith('_low_freq')]
        
        if method == 'forward_fill':
            aligned_df[low_freq_columns] = aligned_df[low_freq_columns].fillna(method='ffill')
        elif method == 'backward_fill':
            aligned_df[low_freq_columns] = aligned_df[low_freq_columns].fillna(method='bfill')
        elif method == 'interpolate':
            aligned_df[low_freq_columns] = aligned_df[low_freq_columns].interpolate()
        
        return aligned_df

# Example usage
def main():
    """Example usage of time series processing functions"""
    
    # Create sample manufacturing time series data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate timestamps
    timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='30s')
    
    # Generate process variables with some patterns
    base_signal = np.sin(2 * np.pi * np.arange(n_samples) / 100)  # Base cycle
    
    # Speed with some noise and events
    speed = 150 + 10 * base_signal + np.random.normal(0, 2, n_samples)
    # Add some anomalies
    speed[200:220] = 200  # Speed spike
    speed[500:510] = 100  # Speed drop
    
    # Temperature with slower variation
    temperature = 80 + 5 * np.sin(2 * np.pi * np.arange(n_samples) / 200) + np.random.normal(0, 1, n_samples)
    
    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'speed': speed,
        'temperature': temperature,
        'thickness': 12.5 + 0.01 * speed + np.random.normal(0, 0.1, n_samples)
    })
    
    print("Original data shape:", df.shape)
    print("Time range:", df['timestamp'].min(), "to", df['timestamp'].max())
    
    # Initialize processor
    processor = TimeSeriesProcessor()
    
    # Test resampling
    print("\n=== Testing Resampling ===")
    resampled = processor.resample_time_series(df, target_frequency='5min')
    print("Resampled shape:", resampled.shape)
    
    # Test anomaly detection
    print("\n=== Testing Anomaly Detection ===")
    anomaly_results = processor.detect_anomalies(df, columns=['speed'], method='zscore')
    anomaly_count = anomaly_results['speed_anomaly'].sum()
    print(f"Detected {anomaly_count} anomalies in speed data")
    
    # Test smoothing
    print("\n=== Testing Smoothing ===")
    smoothed = processor.smooth_time_series(df, columns=['speed'], method='moving_average', window_size=10)
    print("Added smoothed columns:", [col for col in smoothed.columns if 'smoothed' in col])
    
    # Test event detection
    print("\n=== Testing Event Detection ===")
    events = processor.detect_events(df, 'speed', event_type='threshold', threshold=160)
    print(f"Detected {len(events)} threshold events")
    if not events.empty:
        print("Event details:")
        print(events[['start_index', 'end_index', 'duration', 'max_value']].head())
    
    # Test rolling statistics
    print("\n=== Testing Rolling Statistics ===")
    rolling_stats = processor.calculate_rolling_statistics(df, columns=['speed'], window_sizes=[10, 30])
    rolling_columns = [col for col in rolling_stats.columns if any(stat in col for stat in ['mean', 'std', 'cv'])]
    print("Added rolling statistics columns:", rolling_columns)
    
    # Test time features
    print("\n=== Testing Time Features ===")
    time_features = processor.create_time_features(df)
    time_columns = [col for col in time_features.columns if col in ['hour', 'day_of_week', 'shift', 'hour_sin', 'hour_cos']]
    print("Added time features:", time_columns)
    
    # Test mixed frequency alignment
    print("\n=== Testing Mixed Frequency Alignment ===")
    # Create low-frequency quality data
    quality_timestamps = pd.date_range('2024-01-01', periods=50, freq='10min')
    quality_df = pd.DataFrame({
        'timestamp': quality_timestamps,
        'quality_score': np.random.normal(95, 3, 50)
    })
    
    aligned = processor.align_mixed_frequency_data(df, quality_df)
    print("Aligned data shape:", aligned.shape)
    print("Quality data coverage:", aligned['quality_score'].notna().sum(), "out of", len(aligned))

if __name__ == "__main__":
    main()