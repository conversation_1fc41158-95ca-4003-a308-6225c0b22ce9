"""
CSV Data Loader for Industrial Manufacturing Data

This module provides utilities for loading and validating CSV data files
commonly found in manufacturing environments, with special handling for
time-series data, missing values, and data quality checks.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging
from datetime import datetime, timedelta
import warnings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataValidationResult:
    """Results of data validation"""
    def __init__(self):
        self.is_valid = True
        self.errors = []
        self.warnings = []
        self.quality_score = 0.0
        self.recommendations = []

class ManufacturingDataLoader:
    """
    Specialized data loader for manufacturing CSV files with validation
    and preprocessing capabilities.
    """
    
    def __init__(self, data_dir: str = "test-data"):
        """
        Initialize the data loader.
        
        Args:
            data_dir: Directory containing CSV files
        """
        self.data_dir = Path(data_dir)
        self.loaded_data = {}
        self.validation_results = {}
        
        # Expected data schemas
        self.expected_schemas = {
            'thickness': ['timestamp', 'thickness_value', 'sensor_id'],
            'speed': ['timestamp', 'speed_value', 'line_id'],
            'stop': ['timestamp', 'duration', 'reason', 'line_id'],
            'fm_stack': ['timestamp', 'stack_id', 'scrap_type', 'quantity'],
            'sm_stack': ['timestamp', 'stack_id', 'scrap_type', 'quantity']
        }
        
        # Data type mappings
        self.dtype_mappings = {
            'thickness': {
                'thickness_value': 'float64',
                'sensor_id': 'string'
            },
            'speed': {
                'speed_value': 'float64',
                'line_id': 'string'
            },
            'stop': {
                'duration': 'float64',
                'reason': 'string',
                'line_id': 'string'
            },
            'fm_stack': {
                'stack_id': 'string',
                'scrap_type': 'string',
                'quantity': 'float64'
            },
            'sm_stack': {
                'stack_id': 'string',
                'scrap_type': 'string',
                'quantity': 'float64'
            }
        }
    
    def load_csv_file(self, filename: str, **kwargs) -> pd.DataFrame:
        """
        Load a CSV file with robust error handling and type inference.
        
        Args:
            filename: Name of the CSV file
            **kwargs: Additional arguments for pd.read_csv
            
        Returns:
            Loaded DataFrame
        """
        filepath = self.data_dir / filename
        
        if not filepath.exists():
            raise FileNotFoundError(f"File not found: {filepath}")
        
        try:
            # Default parameters for manufacturing data
            default_params = {
                'parse_dates': ['timestamp'] if 'timestamp' in kwargs.get('usecols', []) else [],
                'na_values': ['', 'NULL', 'null', 'N/A', 'n/a', '#N/A', 'NaN'],
                'low_memory': False,
                'encoding': 'utf-8'
            }
            
            # Update with user parameters
            params = {**default_params, **kwargs}
            
            # Load the data
            df = pd.read_csv(filepath, **params)
            
            # Basic validation
            if df.empty:
                logger.warning(f"Loaded empty DataFrame from {filename}")
            else:
                logger.info(f"Successfully loaded {len(df)} rows from {filename}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading {filename}: {str(e)}")
            raise
    
    def load_all_manufacturing_data(self) -> Dict[str, pd.DataFrame]:
        """
        Load all standard manufacturing data files.
        
        Returns:
            Dictionary of DataFrames keyed by data type
        """
        data_files = {
            'thickness': 'thickness.csv',
            'speed': 'speed.csv',
            'stop': 'stop.csv',
            'fm_stack': 'fm_stack.csv',
            'sm_stack': 'sm_stack.csv'
        }
        
        loaded_data = {}
        
        for data_type, filename in data_files.items():
            try:
                df = self.load_csv_file(filename)
                
                # Apply data type mappings if available
                if data_type in self.dtype_mappings:
                    for col, dtype in self.dtype_mappings[data_type].items():
                        if col in df.columns:
                            try:
                                df[col] = df[col].astype(dtype)
                            except Exception as e:
                                logger.warning(f"Could not convert {col} to {dtype}: {e}")
                
                loaded_data[data_type] = df
                
            except FileNotFoundError:
                logger.warning(f"File not found: {filename}")
                continue
            except Exception as e:
                logger.error(f"Error loading {filename}: {e}")
                continue
        
        self.loaded_data = loaded_data
        return loaded_data
    
    def validate_data_quality(self, df: pd.DataFrame, data_type: str) -> DataValidationResult:
        """
        Validate data quality for a specific dataset.
        
        Args:
            df: DataFrame to validate
            data_type: Type of data (thickness, speed, etc.)
            
        Returns:
            DataValidationResult with validation outcomes
        """
        result = DataValidationResult()
        
        # Check for empty DataFrame
        if df.empty:
            result.is_valid = False
            result.errors.append("DataFrame is empty")
            return result
        
        # Check expected columns
        expected_cols = self.expected_schemas.get(data_type, [])
        missing_cols = [col for col in expected_cols if col not in df.columns]
        
        if missing_cols:
            result.warnings.append(f"Missing expected columns: {missing_cols}")
        
        # Check for timestamp column
        if 'timestamp' in df.columns:
            # Validate timestamp format
            try:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
                # Check for duplicate timestamps
                duplicate_timestamps = df['timestamp'].duplicated().sum()
                if duplicate_timestamps > 0:
                    result.warnings.append(f"Found {duplicate_timestamps} duplicate timestamps")
                
                # Check for timestamp gaps
                if len(df) > 1:
                    time_diff = df['timestamp'].diff().dropna()
                    median_interval = time_diff.median()
                    large_gaps = time_diff[time_diff > median_interval * 5]
                    
                    if len(large_gaps) > 0:
                        result.warnings.append(f"Found {len(large_gaps)} large time gaps")
                
            except Exception as e:
                result.errors.append(f"Invalid timestamp format: {e}")
        
        # Check for missing values
        missing_data = df.isnull().sum()
        total_missing = missing_data.sum()
        
        if total_missing > 0:
            missing_percentage = (total_missing / (len(df) * len(df.columns))) * 100
            result.warnings.append(f"Missing data: {missing_percentage:.1f}% of total values")
            
            # Critical if more than 50% missing
            if missing_percentage > 50:
                result.errors.append("Excessive missing data (>50%)")
        
        # Check numeric columns for outliers
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            if col in df.columns:
                # IQR method for outlier detection
                q1 = df[col].quantile(0.25)
                q3 = df[col].quantile(0.75)
                iqr = q3 - q1
                
                if iqr > 0:
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    
                    outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                    outlier_percentage = (outliers / len(df)) * 100
                    
                    if outlier_percentage > 5:
                        result.warnings.append(f"Column {col}: {outlier_percentage:.1f}% outliers")
        
        # Data type specific validations
        if data_type == 'thickness':
            if 'thickness_value' in df.columns:
                # Thickness should be positive and within reasonable range
                invalid_thickness = ((df['thickness_value'] <= 0) | 
                                   (df['thickness_value'] > 50)).sum()
                if invalid_thickness > 0:
                    result.warnings.append(f"Invalid thickness values: {invalid_thickness}")
        
        elif data_type == 'speed':
            if 'speed_value' in df.columns:
                # Speed should be non-negative
                negative_speed = (df['speed_value'] < 0).sum()
                if negative_speed > 0:
                    result.errors.append(f"Negative speed values: {negative_speed}")
        
        elif data_type == 'stop':
            if 'duration' in df.columns:
                # Duration should be positive
                invalid_duration = (df['duration'] <= 0).sum()
                if invalid_duration > 0:
                    result.warnings.append(f"Invalid duration values: {invalid_duration}")
        
        # Calculate quality score
        error_weight = 0.5
        warning_weight = 0.1
        
        error_penalty = len(result.errors) * error_weight
        warning_penalty = len(result.warnings) * warning_weight
        
        completeness_score = 1 - (total_missing / (len(df) * len(df.columns)))
        
        result.quality_score = max(0, completeness_score - error_penalty - warning_penalty)
        
        # Generate recommendations
        if result.quality_score < 0.7:
            result.recommendations.append("Data quality is below acceptable threshold")
        
        if missing_data.sum() > 0:
            result.recommendations.append("Consider data imputation for missing values")
        
        if len(result.warnings) > 0:
            result.recommendations.append("Review data collection processes")
        
        return result
    
    def preprocess_manufacturing_data(self, df: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """
        Preprocess manufacturing data with standardization and cleaning.
        
        Args:
            df: DataFrame to preprocess
            data_type: Type of manufacturing data
            
        Returns:
            Preprocessed DataFrame
        """
        df_processed = df.copy()
        
        # Ensure timestamp is datetime
        if 'timestamp' in df_processed.columns:
            df_processed['timestamp'] = pd.to_datetime(df_processed['timestamp'])
            df_processed = df_processed.sort_values('timestamp')
        
        # Remove duplicate rows
        initial_len = len(df_processed)
        df_processed = df_processed.drop_duplicates()
        
        if len(df_processed) < initial_len:
            logger.info(f"Removed {initial_len - len(df_processed)} duplicate rows")
        
        # Data type specific preprocessing
        if data_type == 'thickness':
            # Remove physically impossible thickness values
            if 'thickness_value' in df_processed.columns:
                df_processed = df_processed[
                    (df_processed['thickness_value'] > 0) & 
                    (df_processed['thickness_value'] < 100)
                ]
        
        elif data_type == 'speed':
            # Remove negative speeds
            if 'speed_value' in df_processed.columns:
                df_processed = df_processed[df_processed['speed_value'] >= 0]
        
        elif data_type == 'stop':
            # Remove zero or negative durations
            if 'duration' in df_processed.columns:
                df_processed = df_processed[df_processed['duration'] > 0]
        
        # Handle missing values
        numeric_cols = df_processed.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            if df_processed[col].isnull().sum() > 0:
                # Use forward fill for time series data
                df_processed[col] = df_processed[col].fillna(method='ffill')
                
                # If still missing, use median
                if df_processed[col].isnull().sum() > 0:
                    df_processed[col] = df_processed[col].fillna(df_processed[col].median())
        
        return df_processed
    
    def align_time_series_data(self, 
                              primary_df: pd.DataFrame, 
                              secondary_df: pd.DataFrame,
                              time_column: str = 'timestamp',
                              method: str = 'nearest') -> pd.DataFrame:
        """
        Align two time series datasets with different frequencies.
        
        Args:
            primary_df: Primary (typically higher frequency) DataFrame
            secondary_df: Secondary DataFrame to align
            time_column: Name of the timestamp column
            method: Merge method ('nearest', 'left', 'right')
            
        Returns:
            Aligned DataFrame
        """
        # Ensure both DataFrames have datetime index
        for df in [primary_df, secondary_df]:
            if time_column in df.columns:
                df[time_column] = pd.to_datetime(df[time_column])
        
        # Sort both DataFrames by timestamp
        primary_sorted = primary_df.sort_values(time_column)
        secondary_sorted = secondary_df.sort_values(time_column)
        
        # Perform merge_asof for time-based alignment
        if method == 'nearest':
            # Use merge_asof for nearest timestamp matching
            aligned_df = pd.merge_asof(
                primary_sorted,
                secondary_sorted,
                on=time_column,
                direction='nearest',
                suffixes=('', '_secondary')
            )
        else:
            # Regular merge
            aligned_df = pd.merge(
                primary_sorted,
                secondary_sorted,
                on=time_column,
                how=method,
                suffixes=('', '_secondary')
            )
        
        return aligned_df

# Example usage and testing
def main():
    """Example usage of the manufacturing data loader"""
    
    # Initialize loader
    loader = ManufacturingDataLoader()
    
    try:
        # Load all manufacturing data
        print("Loading manufacturing data...")
        all_data = loader.load_all_manufacturing_data()
        
        print(f"Loaded {len(all_data)} datasets:")
        for data_type, df in all_data.items():
            print(f"  {data_type}: {df.shape}")
        
        # Validate each dataset
        print("\nValidating data quality...")
        for data_type, df in all_data.items():
            result = loader.validate_data_quality(df, data_type)
            
            print(f"\n{data_type.upper()} Data Quality:")
            print(f"  Quality Score: {result.quality_score:.2f}")
            print(f"  Errors: {len(result.errors)}")
            print(f"  Warnings: {len(result.warnings)}")
            
            if result.errors:
                print("  Errors:")
                for error in result.errors:
                    print(f"    - {error}")
            
            if result.warnings:
                print("  Warnings:")
                for warning in result.warnings:
                    print(f"    - {warning}")
        
        # Example of data preprocessing
        if 'thickness' in all_data:
            print("\nPreprocessing thickness data...")
            processed_thickness = loader.preprocess_manufacturing_data(
                all_data['thickness'], 'thickness'
            )
            print(f"Original shape: {all_data['thickness'].shape}")
            print(f"Processed shape: {processed_thickness.shape}")
        
        # Example of time series alignment
        if 'thickness' in all_data and 'speed' in all_data:
            print("\nAligning thickness and speed data...")
            aligned_data = loader.align_time_series_data(
                all_data['thickness'], 
                all_data['speed']
            )
            print(f"Aligned data shape: {aligned_data.shape}")
            print(f"Columns: {list(aligned_data.columns)}")
            
    except Exception as e:
        print(f"Error in main: {e}")

if __name__ == "__main__":
    main()