"""
Correlation Analysis Module for Manufacturing Data

This module provides comprehensive correlation analysis capabilities
specifically designed for manufacturing time series data, including
lag-based correlations, statistical significance testing, and
manufacturing-specific correlation patterns.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from scipy import stats
from scipy.stats import pearsonr, spearmanr, kendalltau
import logging
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CorrelationResult:
    """Data class for correlation analysis results"""
    variable_1: str
    variable_2: str
    correlation_coefficient: float
    p_value: float
    confidence_interval: Tuple[float, float]
    sample_size: int
    method: str
    significance_level: str
    interpretation: str

@dataclass
class LagCorrelationResult:
    """Data class for lag correlation analysis results"""
    variable_1: str
    variable_2: str
    optimal_lag: int
    max_correlation: float
    p_value: float
    lag_range: range
    correlation_by_lag: Dict[int, float]
    interpretation: str

class ManufacturingCorrelationAnalyzer:
    """
    Specialized correlation analyzer for manufacturing data with
    focus on time-lagged relationships and process-specific patterns.
    """
    
    def __init__(self, significance_level: float = 0.05):
        """
        Initialize the correlation analyzer.
        
        Args:
            significance_level: Statistical significance threshold
        """
        self.significance_level = significance_level
        self.correlation_cache = {}
        
        # Manufacturing-specific variable relationships
        self.process_relationships = {
            'thickness': {'related_to': ['speed', 'pressure', 'temperature'], 'typical_lags': [5, 15, 30]},
            'speed': {'related_to': ['thickness', 'quality_score'], 'typical_lags': [0, 10, 20]},
            'temperature': {'related_to': ['thickness', 'pressure'], 'typical_lags': [15, 30, 60]},
            'pressure': {'related_to': ['thickness', 'temperature'], 'typical_lags': [5, 15, 30]},
            'quality_score': {'related_to': ['thickness', 'speed', 'temperature'], 'typical_lags': [30, 60, 120]}
        }
    
    def calculate_correlation_matrix(self, 
                                   df: pd.DataFrame, 
                                   method: str = 'pearson',
                                   min_periods: int = 30) -> pd.DataFrame:
        """
        Calculate correlation matrix with statistical validation.
        
        Args:
            df: DataFrame with numeric data
            method: Correlation method ('pearson', 'spearman', 'kendall')
            min_periods: Minimum number of observations required
            
        Returns:
            Correlation matrix DataFrame
        """
        # Select only numeric columns
        numeric_df = df.select_dtypes(include=[np.number])
        
        if numeric_df.empty:
            logger.warning("No numeric columns found for correlation analysis")
            return pd.DataFrame()
        
        # Calculate correlation matrix
        if method == 'pearson':
            corr_matrix = numeric_df.corr(method='pearson', min_periods=min_periods)
        elif method == 'spearman':
            corr_matrix = numeric_df.corr(method='spearman', min_periods=min_periods)
        elif method == 'kendall':
            corr_matrix = numeric_df.corr(method='kendall', min_periods=min_periods)
        else:
            raise ValueError(f"Unknown correlation method: {method}")
        
        return corr_matrix
    
    def find_significant_correlations(self, 
                                    df: pd.DataFrame, 
                                    method: str = 'pearson',
                                    min_correlation: float = 0.3,
                                    return_all: bool = False) -> List[CorrelationResult]:
        """
        Find statistically significant correlations above threshold.
        
        Args:
            df: DataFrame with numeric data
            method: Correlation method
            min_correlation: Minimum correlation coefficient threshold
            return_all: If True, return all correlations (not just significant ones)
            
        Returns:
            List of CorrelationResult objects
        """
        numeric_df = df.select_dtypes(include=[np.number])
        columns = numeric_df.columns.tolist()
        
        significant_correlations = []
        
        for i, col1 in enumerate(columns):
            for col2 in columns[i+1:]:
                # Clean data (remove NaN values)
                clean_data = numeric_df[[col1, col2]].dropna()
                
                if len(clean_data) < 10:  # Need minimum samples
                    continue
                
                # Calculate correlation and p-value
                if method == 'pearson':
                    corr_coef, p_value = pearsonr(clean_data[col1], clean_data[col2])
                elif method == 'spearman':
                    corr_coef, p_value = spearmanr(clean_data[col1], clean_data[col2])
                elif method == 'kendall':
                    corr_coef, p_value = kendalltau(clean_data[col1], clean_data[col2])
                else:
                    continue
                
                # Calculate confidence interval
                conf_int = self._calculate_confidence_interval(corr_coef, len(clean_data))
                
                # Determine significance level
                if p_value < 0.001:
                    significance = 'highly_significant'
                elif p_value < 0.01:
                    significance = 'very_significant'
                elif p_value < self.significance_level:
                    significance = 'significant'
                else:
                    significance = 'not_significant'
                
                # Generate interpretation
                direction = 'positive' if corr_coef > 0 else 'negative'
                
                if abs(corr_coef) > 0.8:
                    strength = 'very strong'
                elif abs(corr_coef) > 0.6:
                    strength = 'strong'
                elif abs(corr_coef) > 0.4:
                    strength = 'moderate'
                elif abs(corr_coef) > 0.2:
                    strength = 'weak'
                else:
                    strength = 'very weak'
                
                interpretation = f"{strength.title()} {direction} correlation"
                
                # Create result object
                result = CorrelationResult(
                    variable_1=col1,
                    variable_2=col2,
                    correlation_coefficient=corr_coef,
                    p_value=p_value,
                    confidence_interval=conf_int,
                    sample_size=len(clean_data),
                    method=method,
                    significance_level=significance,
                    interpretation=interpretation
                )
                
                # Filter based on criteria
                if return_all or (abs(corr_coef) >= min_correlation and p_value < self.significance_level):
                    significant_correlations.append(result)
        
        # Sort by absolute correlation coefficient (descending)
        significant_correlations.sort(key=lambda x: abs(x.correlation_coefficient), reverse=True)
        
        return significant_correlations
    
    def analyze_lag_correlations(self, 
                               df: pd.DataFrame, 
                               variable_1: str, 
                               variable_2: str,
                               max_lag: int = 60,
                               time_column: str = 'timestamp') -> LagCorrelationResult:
        """
        Analyze time-lagged correlations between two variables.
        
        Args:
            df: DataFrame with time series data
            variable_1: First variable (typically the leading indicator)
            variable_2: Second variable (typically the response)
            max_lag: Maximum lag to test (in time periods)
            time_column: Name of the timestamp column
            
        Returns:
            LagCorrelationResult with optimal lag and correlation values
        """
        if variable_1 not in df.columns or variable_2 not in df.columns:
            raise ValueError(f"Variables {variable_1} or {variable_2} not found in DataFrame")
        
        # Sort by time if time column exists
        if time_column in df.columns:
            df_sorted = df.sort_values(time_column).copy()
        else:
            df_sorted = df.copy()
        
        correlation_by_lag = {}
        p_values_by_lag = {}
        
        for lag in range(0, max_lag + 1):
            # Create lagged version
            if lag == 0:
                # No lag
                clean_data = df_sorted[[variable_1, variable_2]].dropna()
                if len(clean_data) < 10:
                    continue
                corr_coef, p_value = pearsonr(clean_data[variable_1], clean_data[variable_2])
            else:
                # Create lagged DataFrame
                lagged_df = df_sorted.copy()
                lagged_df[f'{variable_1}_lag_{lag}'] = lagged_df[variable_1].shift(lag)
                
                clean_data = lagged_df[[f'{variable_1}_lag_{lag}', variable_2]].dropna()
                if len(clean_data) < 10:
                    continue
                
                corr_coef, p_value = pearsonr(clean_data[f'{variable_1}_lag_{lag}'], clean_data[variable_2])
            
            if not np.isnan(corr_coef):
                correlation_by_lag[lag] = corr_coef
                p_values_by_lag[lag] = p_value
        
        if not correlation_by_lag:
            raise ValueError("No valid correlations found across lag range")
        
        # Find optimal lag (maximum absolute correlation)
        optimal_lag = max(correlation_by_lag.keys(), key=lambda k: abs(correlation_by_lag[k]))
        max_correlation = correlation_by_lag[optimal_lag]
        optimal_p_value = p_values_by_lag[optimal_lag]
        
        # Generate interpretation
        if optimal_lag == 0:
            lag_interpretation = "Immediate correlation (no lag)"
        else:
            lag_interpretation = f"Optimal lag of {optimal_lag} time periods"
        
        direction = "positive" if max_correlation > 0 else "negative"
        strength = "strong" if abs(max_correlation) > 0.7 else "moderate" if abs(max_correlation) > 0.3 else "weak"
        
        interpretation = f"{strength.title()} {direction} correlation with {lag_interpretation}"
        
        return LagCorrelationResult(
            variable_1=variable_1,
            variable_2=variable_2,
            optimal_lag=optimal_lag,
            max_correlation=max_correlation,
            p_value=optimal_p_value,
            lag_range=range(0, max_lag + 1),
            correlation_by_lag=correlation_by_lag,
            interpretation=interpretation
        )
    
    def analyze_process_correlations(self, 
                                   df: pd.DataFrame, 
                                   target_variable: str,
                                   process_variables: List[str] = None) -> Dict[str, LagCorrelationResult]:
        """
        Analyze correlations between a target variable and process variables
        using manufacturing-specific lag windows.
        
        Args:
            df: DataFrame with manufacturing data
            target_variable: Target variable (e.g., 'quality_score', 'thickness')
            process_variables: List of process variables to analyze
            
        Returns:
            Dictionary of LagCorrelationResult objects
        """
        if target_variable not in df.columns:
            raise ValueError(f"Target variable '{target_variable}' not found in DataFrame")
        
        # Use predefined process variables if not specified
        if process_variables is None:
            if target_variable in self.process_relationships:
                process_variables = self.process_relationships[target_variable]['related_to']
            else:
                # Use all numeric columns except target
                numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
                process_variables = [col for col in numeric_cols if col != target_variable]
        
        results = {}
        
        for process_var in process_variables:
            if process_var in df.columns:
                try:
                    # Determine appropriate lag range based on variable type
                    if target_variable in self.process_relationships:
                        typical_lags = self.process_relationships[target_variable]['typical_lags']
                        max_lag = max(typical_lags) * 2  # Extended range
                    else:
                        max_lag = 60  # Default
                    
                    # Analyze lag correlations
                    lag_result = self.analyze_lag_correlations(
                        df, process_var, target_variable, max_lag=max_lag
                    )
                    
                    results[process_var] = lag_result
                    
                except Exception as e:
                    logger.warning(f"Failed to analyze correlation between {process_var} and {target_variable}: {e}")
                    continue
        
        return results
    
    def calculate_partial_correlations(self, 
                                     df: pd.DataFrame, 
                                     target_var: str, 
                                     control_vars: List[str]) -> Dict[str, float]:
        """
        Calculate partial correlations controlling for specified variables.
        
        Args:
            df: DataFrame with data
            target_var: Target variable
            control_vars: Variables to control for
            
        Returns:
            Dictionary of partial correlation coefficients
        """
        from scipy.stats import pearsonr
        
        # Select relevant columns
        all_vars = [target_var] + control_vars
        clean_data = df[all_vars].dropna()
        
        if len(clean_data) < len(all_vars) + 10:  # Need sufficient samples
            logger.warning("Insufficient data for partial correlation analysis")
            return {}
        
        partial_correlations = {}
        
        # Calculate partial correlations for each control variable
        for i, control_var in enumerate(control_vars):
            try:
                # Remove the effect of other control variables
                other_controls = [var for var in control_vars if var != control_var]
                
                if other_controls:
                    # Multiple regression to remove effects of other variables
                    from sklearn.linear_model import LinearRegression
                    
                    # Fit models
                    lr_target = LinearRegression()
                    lr_control = LinearRegression()
                    
                    X_other = clean_data[other_controls]
                    
                    # Get residuals
                    lr_target.fit(X_other, clean_data[target_var])
                    target_residuals = clean_data[target_var] - lr_target.predict(X_other)
                    
                    lr_control.fit(X_other, clean_data[control_var])
                    control_residuals = clean_data[control_var] - lr_control.predict(X_other)
                    
                    # Calculate correlation of residuals
                    partial_corr, _ = pearsonr(target_residuals, control_residuals)
                else:
                    # Simple correlation if no other controls
                    partial_corr, _ = pearsonr(clean_data[target_var], clean_data[control_var])
                
                partial_correlations[control_var] = partial_corr
                
            except Exception as e:
                logger.warning(f"Failed to calculate partial correlation for {control_var}: {e}")
                continue
        
        return partial_correlations
    
    def detect_correlation_patterns(self, 
                                  correlation_matrix: pd.DataFrame, 
                                  threshold: float = 0.7) -> Dict[str, List[str]]:
        """
        Detect patterns in correlation matrix (e.g., clusters, chains).
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            threshold: Threshold for strong correlations
            
        Returns:
            Dictionary with detected patterns
        """
        patterns = {
            'strong_positive_clusters': [],
            'strong_negative_clusters': [],
            'isolated_variables': [],
            'correlation_chains': []
        }
        
        # Find strongly correlated variable pairs
        strong_correlations = []
        
        for i, col1 in enumerate(correlation_matrix.columns):
            for col2 in correlation_matrix.columns[i+1:]:
                corr_val = correlation_matrix.loc[col1, col2]
                
                if abs(corr_val) >= threshold:
                    strong_correlations.append({
                        'var1': col1,
                        'var2': col2,
                        'correlation': corr_val
                    })
        
        # Group into clusters
        positive_pairs = [pair for pair in strong_correlations if pair['correlation'] > 0]
        negative_pairs = [pair for pair in strong_correlations if pair['correlation'] < 0]
        
        # Find connected components (clusters)
        def find_clusters(pairs):
            clusters = []
            used_vars = set()
            
            for pair in pairs:
                var1, var2 = pair['var1'], pair['var2']
                
                if var1 not in used_vars and var2 not in used_vars:
                    # Start new cluster
                    cluster = {var1, var2}
                    used_vars.update([var1, var2])
                    
                    # Extend cluster
                    extended = True
                    while extended:
                        extended = False
                        for other_pair in pairs:
                            ov1, ov2 = other_pair['var1'], other_pair['var2']
                            if (ov1 in cluster or ov2 in cluster) and ov1 not in used_vars:
                                cluster.add(ov1)
                                used_vars.add(ov1)
                                extended = True
                            elif (ov1 in cluster or ov2 in cluster) and ov2 not in used_vars:
                                cluster.add(ov2)
                                used_vars.add(ov2)
                                extended = True
                    
                    if len(cluster) > 2:
                        clusters.append(list(cluster))
            
            return clusters
        
        patterns['strong_positive_clusters'] = find_clusters(positive_pairs)
        patterns['strong_negative_clusters'] = find_clusters(negative_pairs)
        
        # Find isolated variables
        all_connected_vars = set()
        for cluster in patterns['strong_positive_clusters'] + patterns['strong_negative_clusters']:
            all_connected_vars.update(cluster)
        
        patterns['isolated_variables'] = [
            var for var in correlation_matrix.columns 
            if var not in all_connected_vars
        ]
        
        return patterns
    
    def _calculate_confidence_interval(self, r: float, n: int, confidence: float = 0.95) -> Tuple[float, float]:
        """
        Calculate confidence interval for correlation coefficient.
        
        Args:
            r: Correlation coefficient
            n: Sample size
            confidence: Confidence level
            
        Returns:
            Tuple of (lower_bound, upper_bound)
        """
        if n < 4:
            return (np.nan, np.nan)
        
        # Fisher z-transformation
        z = 0.5 * np.log((1 + r) / (1 - r))
        
        # Standard error
        se = 1 / np.sqrt(n - 3)
        
        # Critical value
        alpha = 1 - confidence
        z_critical = stats.norm.ppf(1 - alpha/2)
        
        # Confidence interval in z-space
        z_lower = z - z_critical * se
        z_upper = z + z_critical * se
        
        # Transform back to correlation space
        r_lower = (np.exp(2 * z_lower) - 1) / (np.exp(2 * z_lower) + 1)
        r_upper = (np.exp(2 * z_upper) - 1) / (np.exp(2 * z_upper) + 1)
        
        return (r_lower, r_upper)

# Example usage
def main():
    """Example usage of correlation analysis"""
    
    # Create sample manufacturing data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate correlated manufacturing variables
    timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='1min')
    
    # Base process variables
    speed = np.random.normal(150, 10, n_samples)
    temperature = 80 + 0.1 * speed + np.random.normal(0, 2, n_samples)
    
    # Thickness with lag relationship to speed
    thickness = np.zeros(n_samples)
    thickness[0] = 12.5
    for i in range(1, n_samples):
        # Thickness responds to speed with a 15-minute lag
        lag_idx = max(0, i - 15)
        thickness[i] = 12.5 + 0.01 * speed[lag_idx] + 0.005 * temperature[i] + np.random.normal(0, 0.1)
    
    # Quality score inversely related to process variation
    speed_variation = pd.Series(speed).rolling(30).std().fillna(0)
    quality_score = 100 - 0.5 * speed_variation - 0.1 * np.abs(temperature - 80) + np.random.normal(0, 1, n_samples)
    
    # Pressure correlated with temperature
    pressure = 50 + 0.2 * temperature + np.random.normal(0, 1, n_samples)
    
    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'speed': speed,
        'temperature': temperature,
        'thickness': thickness,
        'quality_score': quality_score,
        'pressure': pressure
    })
    
    print("Generated manufacturing data:")
    print(f"Shape: {df.shape}")
    print(f"Variables: {list(df.select_dtypes(include=[np.number]).columns)}")
    
    # Initialize analyzer
    analyzer = ManufacturingCorrelationAnalyzer()
    
    # Test correlation matrix
    print("\n=== Correlation Matrix ===")
    corr_matrix = analyzer.calculate_correlation_matrix(df, method='pearson')
    print(corr_matrix.round(3))
    
    # Test significant correlations
    print("\n=== Significant Correlations ===")
    significant_corrs = analyzer.find_significant_correlations(df, min_correlation=0.3)
    
    for corr in significant_corrs[:5]:  # Show top 5
        print(f"{corr.variable_1} ↔ {corr.variable_2}: "
              f"r={corr.correlation_coefficient:.3f}, "
              f"p={corr.p_value:.4f}, "
              f"{corr.interpretation}")
    
    # Test lag correlation analysis
    print("\n=== Lag Correlation Analysis ===")
    lag_result = analyzer.analyze_lag_correlations(df, 'speed', 'thickness', max_lag=30)
    
    print(f"Speed → Thickness:")
    print(f"  Optimal lag: {lag_result.optimal_lag} periods")
    print(f"  Max correlation: {lag_result.max_correlation:.3f}")
    print(f"  P-value: {lag_result.p_value:.4f}")
    print(f"  Interpretation: {lag_result.interpretation}")
    
    # Test process correlations
    print("\n=== Process Correlations for Quality Score ===")
    process_results = analyzer.analyze_process_correlations(df, 'quality_score')
    
    for var, result in process_results.items():
        print(f"{var} → quality_score:")
        print(f"  Lag: {result.optimal_lag}, Correlation: {result.max_correlation:.3f}")
    
    # Test pattern detection
    print("\n=== Correlation Patterns ===")
    patterns = analyzer.detect_correlation_patterns(corr_matrix, threshold=0.5)
    
    print(f"Strong positive clusters: {patterns['strong_positive_clusters']}")
    print(f"Strong negative clusters: {patterns['strong_negative_clusters']}")
    print(f"Isolated variables: {patterns['isolated_variables']}")

if __name__ == "__main__":
    main()