"""
Data Processing Module for Manufacturing Analysis

This module provides utilities for loading, processing, and analyzing
manufacturing data from various sources including CSV files, databases,
and real-time streams.
"""

from .csv_loader import ManufacturingDataLoader, DataValidationResult
from .correlation_analyzer import ManufacturingCorrelationAnalyzer, CorrelationResult, LagCorrelationResult
from .time_series_processor import TimeSeriesProcessor
from .mixed_frequency_aligner import MixedFrequencyAligner, DataFrequency, AlignmentResult

__all__ = [
    'ManufacturingDataLoader',
    'DataValidationResult',
    'ManufacturingCorrelationAnalyzer',
    'CorrelationResult',
    'LagCorrelationResult',
    'TimeSeriesProcessor',
    'MixedFrequencyAligner',
    'DataFrequency',
    'AlignmentResult'
]

__version__ = '1.0.0'