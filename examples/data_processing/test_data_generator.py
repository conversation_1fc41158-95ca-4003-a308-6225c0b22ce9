"""
Test Data Generator for Manufacturing Examples

This module generates sample CSV files that match the structure described
in INITIAL_1.md for testing and development purposes.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestDataGenerator:
    """
    Generate realistic test data for manufacturing analysis examples.
    
    Creates CSV files matching the structure described in INITIAL_1.md:
    - stop.csv - Machine stoppage events
    - speed.csv - Continuous speed measurements
    - thickness.csv - Thickness measurements from QC sensors
    - fm_stack.csv - Forming machine scrap data
    - sm_stack.csv - Sheet machine scrap data
    """
    
    def __init__(self, output_dir: str = "test-data", random_seed: int = 42):
        """
        Initialize the test data generator.
        
        Args:
            output_dir: Directory to save generated CSV files
            random_seed: Random seed for reproducible data
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        np.random.seed(random_seed)
        
        # Simulation parameters
        self.start_date = datetime(2024, 1, 1, 6, 0, 0)  # Start at 6 AM
        self.end_date = datetime(2024, 1, 8, 6, 0, 0)    # One week of data
        self.total_hours = int((self.end_date - self.start_date).total_seconds() / 3600)
        
        # Production parameters
        self.nominal_speed = 150  # m/min
        self.nominal_thickness = 12.5  # mm
        self.nominal_temperature = 80  # °C
        self.nominal_pressure = 50  # bar
        
        # Process variation parameters
        self.speed_std = 5
        self.thickness_std = 0.2
        self.temperature_std = 2
        self.pressure_std = 1
    
    def generate_all_test_data(self) -> Dict[str, pd.DataFrame]:
        """
        Generate all test data files.
        
        Returns:
            Dictionary of generated DataFrames
        """
        logger.info("Generating comprehensive test data...")
        
        # Generate each dataset
        datasets = {}
        
        # 1. Generate stoppage events first (affects other variables)
        datasets['stop'] = self.generate_stoppage_data()
        
        # 2. Generate continuous speed data (affected by stoppages)
        datasets['speed'] = self.generate_speed_data(datasets['stop'])
        
        # 3. Generate thickness data (correlated with speed)
        datasets['thickness'] = self.generate_thickness_data(datasets['speed'])
        
        # 4. Generate scrap data (correlated with process instability)
        datasets['fm_stack'] = self.generate_scrap_data('FM', datasets['speed'], datasets['thickness'])
        datasets['sm_stack'] = self.generate_scrap_data('SM', datasets['speed'], datasets['thickness'])
        
        # Save all datasets
        for name, df in datasets.items():
            output_file = self.output_dir / f"{name}.csv"
            df.to_csv(output_file, index=False)
            logger.info(f"Saved {name}.csv: {len(df)} records")
        
        return datasets
    
    def generate_stoppage_data(self) -> pd.DataFrame:
        """
        Generate machine stoppage events.
        
        Returns:
            DataFrame with stoppage events
        """
        # Generate stoppage events (roughly 1-2 per day)
        n_stoppages = np.random.poisson(self.total_hours / 12)  # ~2 stoppages per day
        
        stoppage_data = []
        
        for i in range(n_stoppages):
            # Random time during the week
            hours_offset = np.random.uniform(0, self.total_hours)
            stoppage_time = self.start_date + timedelta(hours=hours_offset)
            
            # Stoppage duration (5-120 minutes, exponential distribution)
            duration = max(5, np.random.exponential(20))  # Average 20 min, min 5 min
            duration = min(duration, 120)  # Cap at 2 hours
            
            # Stoppage reasons with realistic probabilities
            reasons = [
                'mechanical_failure', 'electrical_issue', 'material_shortage',
                'quality_issue', 'maintenance', 'changeover', 'operator_break'
            ]
            reason_weights = [0.25, 0.15, 0.15, 0.10, 0.10, 0.15, 0.10]
            reason = np.random.choice(reasons, p=reason_weights)
            
            # Line ID (multiple production lines)
            line_id = f"LINE_{np.random.choice(['A', 'B', 'C'])}"
            
            stoppage_data.append({
                'timestamp': stoppage_time,
                'duration': duration,
                'reason': reason,
                'line_id': line_id,
                'severity': np.random.choice(['low', 'medium', 'high'], p=[0.6, 0.3, 0.1])
            })
        
        # Sort by timestamp
        df = pd.DataFrame(stoppage_data)
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        logger.info(f"Generated {len(df)} stoppage events")
        return df
    
    def generate_speed_data(self, stoppage_df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate continuous speed measurements affected by stoppages.
        
        Args:
            stoppage_df: DataFrame with stoppage events
            
        Returns:
            DataFrame with speed measurements
        """
        # Generate timestamps every 30 seconds
        timestamps = []
        current_time = self.start_date
        while current_time < self.end_date:
            timestamps.append(current_time)
            current_time += timedelta(seconds=30)
        
        speed_data = []
        
        for ts in timestamps:
            # Base speed with some process variation
            base_speed = self.nominal_speed + np.random.normal(0, self.speed_std)
            
            # Check if this time is during a stoppage
            is_stopped = False
            for _, stoppage in stoppage_df.iterrows():
                stop_start = stoppage['timestamp']
                stop_end = stop_start + timedelta(minutes=stoppage['duration'])
                
                if stop_start <= ts <= stop_end:
                    is_stopped = True
                    break
            
            # Speed during stoppage
            if is_stopped:
                speed = 0  # Complete stop
            else:
                # Check if recently restarted (gradual ramp-up)
                recent_restart = False
                for _, stoppage in stoppage_df.iterrows():
                    stop_end = stoppage['timestamp'] + timedelta(minutes=stoppage['duration'])
                    rampup_end = stop_end + timedelta(minutes=15)  # 15 min ramp-up
                    
                    if stop_end <= ts <= rampup_end:
                        # Gradual speed increase
                        rampup_progress = (ts - stop_end).total_seconds() / (15 * 60)
                        speed = base_speed * min(1.0, rampup_progress)
                        recent_restart = True
                        break
                
                if not recent_restart:
                    speed = base_speed
                    
                    # Add some process cycles (shift patterns, etc.)
                    hour = ts.hour
                    if 22 <= hour or hour <= 6:  # Night shift - slightly lower speed
                        speed *= 0.95
                    
                    # Weekly pattern - Monday startup slower
                    if ts.weekday() == 0 and ts.hour < 10:  # Monday morning
                        speed *= 0.90
            
            # Ensure non-negative speed
            speed = max(0, speed)
            
            # Line assignment (consistent with stoppage data)
            line_id = f"LINE_{np.random.choice(['A', 'B', 'C'])}"
            
            speed_data.append({
                'timestamp': ts,
                'speed_value': speed,
                'line_id': line_id,
                'target_speed': self.nominal_speed
            })
        
        df = pd.DataFrame(speed_data)
        logger.info(f"Generated {len(df)} speed measurements")
        return df
    
    def generate_thickness_data(self, speed_df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate thickness measurements correlated with speed.
        
        Args:
            speed_df: DataFrame with speed data
            
        Returns:
            DataFrame with thickness measurements
        """
        # Sample thickness measurements every 2 minutes (less frequent than speed)
        speed_df['timestamp'] = pd.to_datetime(speed_df['timestamp'])
        
        # Resample to 2-minute intervals
        speed_resampled = speed_df.set_index('timestamp').resample('2min').agg({
            'speed_value': 'mean',
            'line_id': 'first'
        }).reset_index()
        
        thickness_data = []
        
        for _, row in speed_resampled.iterrows():
            # Thickness is correlated with speed (higher speed = slight thickness variation)
            speed_effect = (row['speed_value'] - self.nominal_speed) * 0.001  # Small effect
            
            # Base thickness with measurement noise
            thickness = (self.nominal_thickness + 
                        speed_effect + 
                        np.random.normal(0, self.thickness_std))
            
            # Quality control - occasionally flag out-of-spec measurements
            spec_min = 11.5
            spec_max = 13.5
            is_in_spec = spec_min <= thickness <= spec_max
            
            # Sensor information
            sensor_id = f"QC_{np.random.choice(['01', '02', '03'])}"
            
            thickness_data.append({
                'timestamp': row['timestamp'],
                'thickness_value': thickness,
                'sensor_id': sensor_id,
                'line_id': row['line_id'],
                'spec_min': spec_min,
                'spec_max': spec_max,
                'in_spec': is_in_spec
            })
        
        df = pd.DataFrame(thickness_data)
        logger.info(f"Generated {len(df)} thickness measurements")
        return df
    
    def generate_scrap_data(self, stack_type: str, speed_df: pd.DataFrame, thickness_df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate scrap event data correlated with process instability.
        
        Args:
            stack_type: 'FM' (forming machine) or 'SM' (sheet machine)
            speed_df: DataFrame with speed data
            thickness_df: DataFrame with thickness data
            
        Returns:
            DataFrame with scrap events
        """
        # Merge speed and thickness data for correlation analysis
        speed_df['timestamp'] = pd.to_datetime(speed_df['timestamp'])
        thickness_df['timestamp'] = pd.to_datetime(thickness_df['timestamp'])
        
        # Resample to hourly for scrap analysis
        speed_hourly = speed_df.set_index('timestamp').resample('1h').agg({
            'speed_value': ['mean', 'std']
        }).reset_index()
        speed_hourly.columns = ['timestamp', 'speed_mean', 'speed_std']
        
        thickness_hourly = thickness_df.set_index('timestamp').resample('1h').agg({
            'thickness_value': ['mean', 'std'],
            'in_spec': 'mean'
        }).reset_index()
        thickness_hourly.columns = ['timestamp', 'thickness_mean', 'thickness_std', 'in_spec_rate']
        
        # Merge datasets
        process_data = pd.merge_asof(
            speed_hourly.sort_values('timestamp'),
            thickness_hourly.sort_values('timestamp'),
            on='timestamp',
            direction='nearest'
        )
        
        scrap_data = []
        
        for _, row in process_data.iterrows():
            # Calculate process instability score
            speed_instability = row['speed_std'] / self.speed_std if pd.notna(row['speed_std']) else 0
            thickness_instability = row['thickness_std'] / self.thickness_std if pd.notna(row['thickness_std']) else 0
            spec_deviation = 1 - row['in_spec_rate'] if pd.notna(row['in_spec_rate']) else 0
            
            instability_score = (speed_instability + thickness_instability + spec_deviation * 2) / 4
            
            # Scrap probability based on instability
            base_scrap_rate = 0.02 if stack_type == 'FM' else 0.015  # FM slightly higher scrap
            scrap_probability = base_scrap_rate * (1 + instability_score * 3)
            
            # Generate scrap events (Poisson process)
            n_scrap_events = np.random.poisson(scrap_probability)
            
            for i in range(n_scrap_events):
                # Random time within the hour
                event_time = row['timestamp'] + timedelta(minutes=np.random.uniform(0, 60))
                
                # Scrap types with probabilities
                if stack_type == 'FM':
                    scrap_types = ['thickness_deviation', 'surface_defect', 'dimension_error', 'material_contamination']
                    type_weights = [0.4, 0.3, 0.2, 0.1]
                else:  # SM
                    scrap_types = ['cutting_error', 'surface_defect', 'edge_damage', 'thickness_deviation']
                    type_weights = [0.35, 0.25, 0.25, 0.15]
                
                scrap_type = np.random.choice(scrap_types, p=type_weights)
                
                # Scrap quantity (related to severity)
                if instability_score > 1.5:  # High instability
                    quantity = np.random.exponential(5)  # More scrap
                else:
                    quantity = np.random.exponential(2)  # Normal scrap
                
                quantity = max(0.1, min(quantity, 20))  # Reasonable bounds
                
                # Stack ID
                stack_id = f"{stack_type}_{np.random.choice(['001', '002', '003'])}"
                
                scrap_data.append({
                    'timestamp': event_time,
                    'stack_id': stack_id,
                    'scrap_type': scrap_type,
                    'quantity': quantity,
                    'severity': 'high' if instability_score > 1.5 else 'medium' if instability_score > 0.5 else 'low',
                    'shift': 'day' if 6 <= event_time.hour < 18 else 'night'
                })
        
        df = pd.DataFrame(scrap_data)
        if not df.empty:
            df = df.sort_values('timestamp').reset_index(drop=True)
        
        logger.info(f"Generated {len(df)} {stack_type} scrap events")
        return df
    
    def generate_summary_report(self, datasets: Dict[str, pd.DataFrame]) -> str:
        """
        Generate a summary report of the test data.
        
        Args:
            datasets: Dictionary of generated datasets
            
        Returns:
            Summary report as string
        """
        report_lines = [
            "TEST DATA GENERATION SUMMARY",
            "=" * 40,
            f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Data period: {self.start_date} to {self.end_date}",
            f"Total duration: {self.total_hours} hours",
            ""
        ]
        
        for name, df in datasets.items():
            report_lines.append(f"{name.upper()} DATASET:")
            report_lines.append(f"  Records: {len(df)}")
            report_lines.append(f"  Columns: {list(df.columns)}")
            
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                start_time = df['timestamp'].min()
                end_time = df['timestamp'].max()
                duration = end_time - start_time
                report_lines.append(f"  Time range: {start_time} to {end_time}")
                report_lines.append(f"  Duration: {duration}")
            
            # Dataset-specific statistics
            if name == 'stop':
                avg_duration = df['duration'].mean()
                total_downtime = df['duration'].sum()
                report_lines.append(f"  Average stoppage duration: {avg_duration:.1f} minutes")
                report_lines.append(f"  Total downtime: {total_downtime:.1f} minutes")
                
            elif name == 'speed':
                avg_speed = df['speed_value'].mean()
                speed_efficiency = (avg_speed / self.nominal_speed) * 100
                report_lines.append(f"  Average speed: {avg_speed:.1f} m/min")
                report_lines.append(f"  Speed efficiency: {speed_efficiency:.1f}%")
                
            elif name == 'thickness':
                avg_thickness = df['thickness_value'].mean()
                in_spec_rate = df['in_spec'].mean() * 100 if 'in_spec' in df.columns else 0
                report_lines.append(f"  Average thickness: {avg_thickness:.2f} mm")
                report_lines.append(f"  In-spec rate: {in_spec_rate:.1f}%")
                
            elif name in ['fm_stack', 'sm_stack']:
                total_scrap = df['quantity'].sum() if 'quantity' in df.columns else 0
                avg_scrap_per_event = df['quantity'].mean() if 'quantity' in df.columns and len(df) > 0 else 0
                report_lines.append(f"  Total scrap quantity: {total_scrap:.1f} units")
                report_lines.append(f"  Average scrap per event: {avg_scrap_per_event:.1f} units")
            
            report_lines.append("")
        
        return "\n".join(report_lines)

def main():
    """Generate test data and save summary report"""
    
    # Initialize generator
    generator = TestDataGenerator("test-data")
    
    # Generate all datasets
    datasets = generator.generate_all_test_data()
    
    # Generate and save summary report
    summary = generator.generate_summary_report(datasets)
    
    # Save summary to file
    summary_file = generator.output_dir / "test_data_summary.txt"
    with open(summary_file, 'w') as f:
        f.write(summary)
    
    print("Test data generation complete!")
    print(f"Files saved in: {generator.output_dir}")
    print(f"Summary report: {summary_file}")
    print("\nGenerated files:")
    for file in generator.output_dir.glob("*.csv"):
        print(f"  - {file.name}")

if __name__ == "__main__":
    main()