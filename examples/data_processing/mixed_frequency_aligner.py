"""
Mixed-Frequency Data Alignment for Manufacturing Systems

This module provides specialized tools for aligning and processing data streams
with different frequencies commonly found in manufacturing environments.
Handles event-based data, continuous sensors, and laboratory measurements.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta
import logging
from enum import Enum
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataFrequency(Enum):
    """Enumeration of common data frequencies in manufacturing"""
    HIGH_FREQUENCY = "high_freq"        # Seconds (sensor data)
    MEDIUM_FREQUENCY = "medium_freq"    # Minutes (process variables)
    LOW_FREQUENCY = "low_freq"          # Hours/days (quality measurements)
    EVENT_BASED = "event_based"         # Irregular (stoppages, maintenance)

@dataclass
class DataStreamInfo:
    """Information about a data stream"""
    name: str
    frequency: DataFrequency
    typical_interval: Optional[str]
    key_variables: List[str]
    alignment_priority: int  # Higher number = higher priority for alignment

@dataclass
class AlignmentResult:
    """Result of data alignment operation"""
    aligned_data: pd.DataFrame
    alignment_method: str
    source_streams: List[str]
    coverage_stats: Dict[str, float]
    quality_metrics: Dict[str, Any]

class MixedFrequencyAligner:
    """
    Advanced data alignment system for mixed-frequency manufacturing data.
    """
    
    def __init__(self, primary_time_column: str = 'timestamp'):
        """
        Initialize the mixed-frequency aligner.
        
        Args:
            primary_time_column: Name of the primary timestamp column
        """
        self.primary_time_column = primary_time_column
        self.data_streams = {}
        self.alignment_strategies = {
            'nearest_neighbor': self._align_nearest_neighbor,
            'forward_fill': self._align_forward_fill,
            'interpolation': self._align_interpolation,
            'event_window': self._align_event_window,
            'hierarchical': self._align_hierarchical
        }
    
    def register_data_stream(self, 
                           name: str, 
                           data: pd.DataFrame, 
                           frequency: DataFrequency,
                           key_variables: List[str] = None,
                           alignment_priority: int = 1) -> None:
        """
        Register a data stream for alignment.
        
        Args:
            name: Name of the data stream
            data: DataFrame containing the data
            frequency: Frequency classification of the data
            key_variables: List of key variables in this stream
            alignment_priority: Priority for alignment (higher = more important)
        """
        # Validate timestamp column
        if self.primary_time_column not in data.columns:
            raise ValueError(f"Timestamp column '{self.primary_time_column}' not found in {name}")
        
        # Ensure timestamp is datetime
        data = data.copy()
        data[self.primary_time_column] = pd.to_datetime(data[self.primary_time_column])
        data = data.sort_values(self.primary_time_column)
        
        # Auto-detect key variables if not provided
        if key_variables is None:
            key_variables = [col for col in data.columns if col != self.primary_time_column]
        
        # Calculate typical interval
        if len(data) > 1:
            time_diffs = data[self.primary_time_column].diff().dropna()
            typical_interval = time_diffs.median()
        else:
            typical_interval = None
        
        # Store stream information
        stream_info = DataStreamInfo(
            name=name,
            frequency=frequency,
            typical_interval=str(typical_interval) if typical_interval else None,
            key_variables=key_variables,
            alignment_priority=alignment_priority
        )
        
        self.data_streams[name] = {
            'data': data,
            'info': stream_info
        }
        
        logger.info(f"Registered data stream '{name}' with {len(data)} records")
    
    def align_all_streams(self, 
                         base_stream: str = None,
                         target_frequency: str = '1min',
                         alignment_method: str = 'hierarchical') -> AlignmentResult:
        """
        Align all registered data streams to a common timeline.
        
        Args:
            base_stream: Name of base stream (highest priority if None)
            target_frequency: Target frequency for alignment
            alignment_method: Method to use for alignment
            
        Returns:
            AlignmentResult with aligned data
        """
        if not self.data_streams:
            raise ValueError("No data streams registered")
        
        # Determine base stream
        if base_stream is None:
            # Use stream with highest priority
            base_stream = max(
                self.data_streams.keys(),
                key=lambda k: self.data_streams[k]['info'].alignment_priority
            )
        
        if base_stream not in self.data_streams:
            raise ValueError(f"Base stream '{base_stream}' not found")
        
        # Apply alignment strategy
        if alignment_method not in self.alignment_strategies:
            raise ValueError(f"Unknown alignment method: {alignment_method}")
        
        aligned_data = self.alignment_strategies[alignment_method](
            base_stream, target_frequency
        )
        
        # Calculate coverage statistics
        coverage_stats = self._calculate_coverage_stats(aligned_data)
        
        # Calculate quality metrics
        quality_metrics = self._calculate_quality_metrics(aligned_data)
        
        return AlignmentResult(
            aligned_data=aligned_data,
            alignment_method=alignment_method,
            source_streams=list(self.data_streams.keys()),
            coverage_stats=coverage_stats,
            quality_metrics=quality_metrics
        )
    
    def align_event_with_continuous(self, 
                                  event_stream: str,
                                  continuous_stream: str,
                                  pre_event_window: timedelta = timedelta(minutes=30),
                                  post_event_window: timedelta = timedelta(minutes=60)) -> pd.DataFrame:
        """
        Align event data with continuous data using event windows.
        
        Args:
            event_stream: Name of event data stream
            continuous_stream: Name of continuous data stream
            pre_event_window: Time window before events
            post_event_window: Time window after events
            
        Returns:
            DataFrame with event-aligned continuous data
        """
        if event_stream not in self.data_streams or continuous_stream not in self.data_streams:
            raise ValueError("Event or continuous stream not found")
        
        event_data = self.data_streams[event_stream]['data']
        continuous_data = self.data_streams[continuous_stream]['data']
        
        aligned_records = []
        
        for idx, event in event_data.iterrows():
            event_time = event[self.primary_time_column]
            
            # Define time windows
            start_time = event_time - pre_event_window
            end_time = event_time + post_event_window
            
            # Extract continuous data in window
            window_data = continuous_data[
                (continuous_data[self.primary_time_column] >= start_time) &
                (continuous_data[self.primary_time_column] <= end_time)
            ].copy()
            
            if not window_data.empty:
                # Add event information to each continuous record
                for event_col in event_data.columns:
                    if event_col != self.primary_time_column:
                        window_data[f'event_{event_col}'] = event[event_col]
                
                # Add time relative to event
                window_data['time_relative_to_event'] = (
                    window_data[self.primary_time_column] - event_time
                ).dt.total_seconds() / 60  # Minutes
                
                # Add event identifier
                window_data['event_id'] = idx
                
                aligned_records.append(window_data)
        
        if aligned_records:
            return pd.concat(aligned_records, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def create_synchronized_timeline(self, 
                                   frequency: str = '1min',
                                   start_time: datetime = None,
                                   end_time: datetime = None) -> pd.DataFrame:
        """
        Create a synchronized timeline for all data streams.
        
        Args:
            frequency: Target frequency for the timeline
            start_time: Start time (auto-detected if None)
            end_time: End time (auto-detected if None)
            
        Returns:
            DataFrame with synchronized timeline
        """
        if not self.data_streams:
            raise ValueError("No data streams registered")
        
        # Determine time range
        if start_time is None:
            start_time = min(
                stream['data'][self.primary_time_column].min()
                for stream in self.data_streams.values()
            )
        
        if end_time is None:
            end_time = max(
                stream['data'][self.primary_time_column].max()
                for stream in self.data_streams.values()
            )
        
        # Create synchronized timeline
        timeline = pd.date_range(start=start_time, end=end_time, freq=frequency)
        
        return pd.DataFrame({self.primary_time_column: timeline})
    
    def _align_nearest_neighbor(self, base_stream: str, target_frequency: str) -> pd.DataFrame:
        """Align using nearest neighbor matching"""
        base_data = self.data_streams[base_stream]['data']
        
        # Create target timeline
        timeline = self.create_synchronized_timeline(target_frequency)
        aligned_data = timeline.copy()
        
        # Merge base stream
        aligned_data = pd.merge_asof(
            aligned_data.sort_values(self.primary_time_column),
            base_data.sort_values(self.primary_time_column),
            on=self.primary_time_column,
            direction='nearest',
            suffixes=('', f'_{base_stream}')
        )
        
        # Merge other streams
        for stream_name, stream_data in self.data_streams.items():
            if stream_name != base_stream:
                aligned_data = pd.merge_asof(
                    aligned_data.sort_values(self.primary_time_column),
                    stream_data['data'].sort_values(self.primary_time_column),
                    on=self.primary_time_column,
                    direction='nearest',
                    suffixes=('', f'_{stream_name}')
                )
        
        return aligned_data
    
    def _align_forward_fill(self, base_stream: str, target_frequency: str) -> pd.DataFrame:
        """Align using forward fill method"""
        base_data = self.data_streams[base_stream]['data']
        
        # Create target timeline
        timeline = self.create_synchronized_timeline(target_frequency)
        aligned_data = timeline.copy()
        
        # Merge with forward fill
        for stream_name, stream_data in self.data_streams.items():
            merged = pd.merge_asof(
                aligned_data.sort_values(self.primary_time_column),
                stream_data['data'].sort_values(self.primary_time_column),
                on=self.primary_time_column,
                direction='backward',  # Forward fill
                suffixes=('', f'_{stream_name}')
            )
            aligned_data = merged
        
        return aligned_data
    
    def _align_interpolation(self, base_stream: str, target_frequency: str) -> pd.DataFrame:
        """Align using interpolation method"""
        # Start with forward fill alignment
        aligned_data = self._align_forward_fill(base_stream, target_frequency)
        
        # Apply interpolation to numeric columns
        numeric_cols = aligned_data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            aligned_data[col] = aligned_data[col].interpolate(method='time')
        
        return aligned_data
    
    def _align_event_window(self, base_stream: str, target_frequency: str) -> pd.DataFrame:
        """Align using event window method"""
        # Identify event streams
        event_streams = [
            name for name, stream in self.data_streams.items()
            if stream['info'].frequency == DataFrequency.EVENT_BASED
        ]
        
        # Identify continuous streams
        continuous_streams = [
            name for name, stream in self.data_streams.items()
            if stream['info'].frequency != DataFrequency.EVENT_BASED
        ]
        
        if not event_streams or not continuous_streams:
            # Fall back to nearest neighbor if no events
            return self._align_nearest_neighbor(base_stream, target_frequency)
        
        # Use event-driven alignment
        aligned_records = []
        
        for event_stream in event_streams:
            for continuous_stream in continuous_streams:
                event_aligned = self.align_event_with_continuous(
                    event_stream, continuous_stream
                )
                if not event_aligned.empty:
                    aligned_records.append(event_aligned)
        
        if aligned_records:
            return pd.concat(aligned_records, ignore_index=True)
        else:
            # Fall back to nearest neighbor
            return self._align_nearest_neighbor(base_stream, target_frequency)
    
    def _align_hierarchical(self, base_stream: str, target_frequency: str) -> pd.DataFrame:
        """Align using hierarchical frequency approach"""
        # Sort streams by frequency and priority
        sorted_streams = sorted(
            self.data_streams.items(),
            key=lambda x: (
                self._frequency_order(x[1]['info'].frequency),
                -x[1]['info'].alignment_priority
            )
        )
        
        # Start with highest frequency stream
        aligned_data = None
        
        for stream_name, stream_data in sorted_streams:
            current_data = stream_data['data'].copy()
            
            if aligned_data is None:
                # First stream - resample to target frequency
                aligned_data = self._resample_to_frequency(current_data, target_frequency)
            else:
                # Merge with existing aligned data
                aligned_data = pd.merge_asof(
                    aligned_data.sort_values(self.primary_time_column),
                    current_data.sort_values(self.primary_time_column),
                    on=self.primary_time_column,
                    direction='nearest',
                    suffixes=('', f'_{stream_name}')
                )
        
        return aligned_data
    
    def _resample_to_frequency(self, data: pd.DataFrame, target_frequency: str) -> pd.DataFrame:
        """Resample data to target frequency"""
        # Set timestamp as index
        data_indexed = data.set_index(self.primary_time_column)
        
        # Determine aggregation methods
        agg_methods = {}
        for col in data_indexed.columns:
            if data_indexed[col].dtype in ['int64', 'float64']:
                agg_methods[col] = 'mean'
            else:
                agg_methods[col] = 'first'
        
        # Resample
        resampled = data_indexed.resample(target_frequency).agg(agg_methods)
        
        # Reset index
        return resampled.reset_index()
    
    def _frequency_order(self, frequency: DataFrequency) -> int:
        """Return numerical order for frequency (lower = higher frequency)"""
        order_map = {
            DataFrequency.HIGH_FREQUENCY: 1,
            DataFrequency.MEDIUM_FREQUENCY: 2,
            DataFrequency.LOW_FREQUENCY: 3,
            DataFrequency.EVENT_BASED: 4
        }
        return order_map.get(frequency, 5)
    
    def _calculate_coverage_stats(self, data: pd.DataFrame) -> Dict[str, float]:
        """Calculate data coverage statistics"""
        total_records = len(data)
        coverage_stats = {}
        
        for col in data.columns:
            if col != self.primary_time_column:
                non_null_count = data[col].count()
                coverage_stats[col] = non_null_count / total_records if total_records > 0 else 0
        
        return coverage_stats
    
    def _calculate_quality_metrics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate data quality metrics after alignment"""
        metrics = {
            'total_records': len(data),
            'time_span_hours': 0,
            'average_coverage': 0,
            'interpolation_quality': {}
        }
        
        if len(data) > 1:
            # Calculate time span
            time_span = data[self.primary_time_column].max() - data[self.primary_time_column].min()
            metrics['time_span_hours'] = time_span.total_seconds() / 3600
            
            # Calculate average coverage
            coverage_stats = self._calculate_coverage_stats(data)
            metrics['average_coverage'] = np.mean(list(coverage_stats.values()))
            
            # Assess interpolation quality for numeric columns
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col in data.columns:
                    # Calculate variance before and after gaps
                    col_data = data[col].dropna()
                    if len(col_data) > 10:
                        variance = col_data.var()
                        metrics['interpolation_quality'][col] = {
                            'variance': variance,
                            'smoothness_score': 1 / (1 + variance) if variance > 0 else 1
                        }
        
        return metrics

# Manufacturing-specific alignment utilities
class ManufacturingAlignment:
    """Specialized alignment utilities for manufacturing data"""
    
    @staticmethod
    def align_production_shifts(data: pd.DataFrame, 
                              shift_duration: timedelta = timedelta(hours=8),
                              shift_start_hour: int = 6) -> pd.DataFrame:
        """
        Align data to production shift boundaries.
        
        Args:
            data: DataFrame with timestamp data
            shift_duration: Duration of each shift
            shift_start_hour: Hour when first shift starts
            
        Returns:
            DataFrame with shift alignment
        """
        data = data.copy()
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        
        # Calculate shift number for each record
        start_of_day = data['timestamp'].dt.normalize() + timedelta(hours=shift_start_hour)
        hours_from_shift_start = (data['timestamp'] - start_of_day).dt.total_seconds() / 3600
        
        # Handle negative hours (before shift start)
        hours_from_shift_start = hours_from_shift_start % 24
        
        shift_number = (hours_from_shift_start // (shift_duration.total_seconds() / 3600)).astype(int)
        data['shift_number'] = shift_number
        
        # Calculate shift start time
        shift_start_times = start_of_day + pd.to_timedelta(
            shift_number * shift_duration.total_seconds() / 3600, unit='h'
        )
        data['shift_start'] = shift_start_times
        
        return data
    
    @staticmethod
    def create_process_windows(events_df: pd.DataFrame,
                             continuous_df: pd.DataFrame,
                             window_config: Dict[str, Dict[str, int]]) -> pd.DataFrame:
        """
        Create process windows around manufacturing events.
        
        Args:
            events_df: DataFrame with event data
            continuous_df: DataFrame with continuous process data
            window_config: Configuration for window sizes by event type
            
        Returns:
            DataFrame with process windows
        """
        window_data = []
        
        for idx, event in events_df.iterrows():
            event_type = event.get('event_type', 'default')
            event_time = pd.to_datetime(event['timestamp'])
            
            # Get window configuration
            config = window_config.get(event_type, {'pre': 30, 'post': 60})
            pre_minutes = config['pre']
            post_minutes = config['post']
            
            # Extract window data
            window_start = event_time - timedelta(minutes=pre_minutes)
            window_end = event_time + timedelta(minutes=post_minutes)
            
            window_records = continuous_df[
                (continuous_df['timestamp'] >= window_start) &
                (continuous_df['timestamp'] <= window_end)
            ].copy()
            
            if not window_records.empty:
                # Add event metadata
                window_records['event_id'] = idx
                window_records['event_type'] = event_type
                window_records['event_time'] = event_time
                window_records['minutes_from_event'] = (
                    window_records['timestamp'] - event_time
                ).dt.total_seconds() / 60
                
                window_data.append(window_records)
        
        if window_data:
            return pd.concat(window_data, ignore_index=True)
        else:
            return pd.DataFrame()

# Example usage
def main():
    """Example usage of mixed-frequency data alignment"""
    
    # Create sample data streams with different frequencies
    np.random.seed(42)
    
    # High-frequency sensor data (every 30 seconds)
    hf_timestamps = pd.date_range('2024-01-01', periods=2000, freq='30s')
    hf_data = pd.DataFrame({
        'timestamp': hf_timestamps,
        'temperature': 80 + np.random.normal(0, 2, 2000),
        'pressure': 50 + np.random.normal(0, 1, 2000),
        'flow_rate': 100 + np.random.normal(0, 5, 2000)
    })
    
    # Medium-frequency process data (every 5 minutes)
    mf_timestamps = pd.date_range('2024-01-01', periods=200, freq='5min')
    mf_data = pd.DataFrame({
        'timestamp': mf_timestamps,
        'speed': 150 + np.random.normal(0, 10, 200),
        'thickness': 12.5 + np.random.normal(0, 0.2, 200)
    })
    
    # Low-frequency quality data (every hour)
    lf_timestamps = pd.date_range('2024-01-01', periods=17, freq='1h')
    lf_data = pd.DataFrame({
        'timestamp': lf_timestamps,
        'quality_score': 95 + np.random.normal(0, 3, 17),
        'defect_rate': np.random.exponential(0.5, 17)
    })
    
    # Event-based data (irregular stoppages)
    event_timestamps = [
        '2024-01-01 02:30:00',
        '2024-01-01 06:15:00',
        '2024-01-01 10:45:00',
        '2024-01-01 14:20:00'
    ]
    event_data = pd.DataFrame({
        'timestamp': pd.to_datetime(event_timestamps),
        'event_type': ['maintenance', 'stoppage', 'changeover', 'stoppage'],
        'duration': [45, 15, 30, 20],
        'reason': ['scheduled', 'malfunction', 'product_change', 'material_shortage']
    })
    
    print("Created sample data streams:")
    print(f"High-frequency (30s): {len(hf_data)} records")
    print(f"Medium-frequency (5min): {len(mf_data)} records")
    print(f"Low-frequency (1h): {len(lf_data)} records")
    print(f"Event-based: {len(event_data)} records")
    
    # Initialize aligner
    aligner = MixedFrequencyAligner()
    
    # Register data streams
    aligner.register_data_stream(
        'sensors', hf_data, DataFrequency.HIGH_FREQUENCY, 
        alignment_priority=3
    )
    aligner.register_data_stream(
        'process', mf_data, DataFrequency.MEDIUM_FREQUENCY, 
        alignment_priority=4
    )
    aligner.register_data_stream(
        'quality', lf_data, DataFrequency.LOW_FREQUENCY, 
        alignment_priority=2
    )
    aligner.register_data_stream(
        'events', event_data, DataFrequency.EVENT_BASED, 
        alignment_priority=1
    )
    
    # Test different alignment methods
    print("\n=== Testing Alignment Methods ===")
    
    methods = ['nearest_neighbor', 'forward_fill', 'interpolation', 'hierarchical']
    
    for method in methods:
        print(f"\n{method.upper()} Method:")
        try:
            result = aligner.align_all_streams(
                target_frequency='2min',
                alignment_method=method
            )
            
            print(f"  Aligned data shape: {result.aligned_data.shape}")
            print(f"  Average coverage: {result.quality_metrics['average_coverage']:.2f}")
            print(f"  Time span: {result.quality_metrics['time_span_hours']:.1f} hours")
            
            # Show coverage by variable
            print("  Coverage by variable:")
            for var, coverage in result.coverage_stats.items():
                print(f"    {var}: {coverage:.2f}")
                
        except Exception as e:
            print(f"  Error: {e}")
    
    # Test event-window alignment
    print("\n=== Event-Window Alignment ===")
    event_aligned = aligner.align_event_with_continuous(
        'events', 'sensors',
        pre_event_window=timedelta(minutes=15),
        post_event_window=timedelta(minutes=30)
    )
    
    print(f"Event-aligned data shape: {event_aligned.shape}")
    if not event_aligned.empty:
        print("Event types found:", event_aligned['event_event_type'].unique())
        print("Time range relative to events:")
        print(f"  Min: {event_aligned['time_relative_to_event'].min():.1f} minutes")
        print(f"  Max: {event_aligned['time_relative_to_event'].max():.1f} minutes")
    
    # Test manufacturing-specific alignment
    print("\n=== Manufacturing Shift Alignment ===")
    shift_aligned = ManufacturingAlignment.align_production_shifts(
        mf_data, shift_start_hour=6
    )
    print(f"Shift-aligned data shape: {shift_aligned.shape}")
    print("Shifts found:", sorted(shift_aligned['shift_number'].unique()))

if __name__ == "__main__":
    main()