"""
Basic Pydantic AI Agent Example

This module demonstrates the fundamental structure of a Pydantic AI agent
with proper type hints, system prompts, and message handling.
"""

import asyncio
from typing import Optional, Dict, Any
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext

# Response models for structured outputs
class AnalysisResult(BaseModel):
    """Structured output for analysis results"""
    summary: str
    insights: list[str]
    recommendations: list[str]
    confidence: float

class DataSummary(BaseModel):
    """Data summary statistics"""
    total_records: int
    missing_values: int
    data_quality_score: float
    key_metrics: Dict[str, float]

# Define the agent with system prompt
data_analysis_agent = Agent(
    'openai:gpt-4o',
    system_prompt="""You are an expert data analysis assistant specializing in industrial processes.
    
    Your role is to:
    1. Analyze manufacturing and sensor data
    2. Identify patterns and correlations
    3. Provide actionable insights
    4. Recommend process improvements
    
    Always provide structured, evidence-based responses with confidence levels.
    Focus on practical insights that can guide engineering decisions.""",
    result_type=AnalysisResult,
)

# Tool function example
@data_analysis_agent.tool
def calculate_basic_stats(ctx: RunContext[None], data: list[float]) -> DataSummary:
    """
    Calculate basic statistics for a dataset.
    
    Args:
        ctx: Run context from Pydantic AI
        data: List of numeric values
        
    Returns:
        DataSummary with basic statistics
    """
    if not data:
        return DataSummary(
            total_records=0,
            missing_values=0,
            data_quality_score=0.0,
            key_metrics={}
        )
    
    total_records = len(data)
    missing_values = sum(1 for x in data if x is None)
    valid_data = [x for x in data if x is not None]
    
    if not valid_data:
        return DataSummary(
            total_records=total_records,
            missing_values=missing_values,
            data_quality_score=0.0,
            key_metrics={}
        )
    
    # Calculate statistics
    mean_val = sum(valid_data) / len(valid_data)
    sorted_data = sorted(valid_data)
    median_val = sorted_data[len(sorted_data) // 2]
    min_val = min(valid_data)
    max_val = max(valid_data)
    
    # Data quality score based on completeness
    data_quality_score = (total_records - missing_values) / total_records
    
    return DataSummary(
        total_records=total_records,
        missing_values=missing_values,
        data_quality_score=data_quality_score,
        key_metrics={
            'mean': mean_val,
            'median': median_val,
            'min': min_val,
            'max': max_val,
            'range': max_val - min_val
        }
    )

async def run_basic_analysis(data: list[float], query: str) -> AnalysisResult:
    """
    Run basic data analysis with the agent.
    
    Args:
        data: List of numeric data points
        query: User's analysis question
        
    Returns:
        AnalysisResult with insights and recommendations
    """
    result = await data_analysis_agent.run(
        f"Analyze this data: {data[:10]}... (showing first 10 values). "
        f"User question: {query}",
        deps=None
    )
    
    return result.data

# Example usage
async def main():
    """Example usage of the basic Pydantic AI agent"""
    
    # Sample manufacturing data (e.g., thickness measurements)
    sample_data = [
        12.5, 12.3, 12.7, 12.4, 12.6, 12.8, 12.2, 12.9, 12.1, 12.7,
        12.4, 12.5, 12.6, 12.3, 12.8, 12.7, 12.2, 12.9, 12.5, 12.4
    ]
    
    # Run analysis
    print("Running basic data analysis...")
    
    result = await run_basic_analysis(
        sample_data,
        "What patterns do you see in this thickness data? Are there any quality concerns?"
    )
    
    print("\nAnalysis Results:")
    print(f"Summary: {result.summary}")
    print(f"Confidence: {result.confidence:.2f}")
    
    print("\nInsights:")
    for insight in result.insights:
        print(f"- {insight}")
    
    print("\nRecommendations:")
    for rec in result.recommendations:
        print(f"- {rec}")

if __name__ == "__main__":
    asyncio.run(main())