"""
Correlation Analysis Agent Example

Specialized Pydantic AI agent for discovering correlations in manufacturing data.
Includes tools for statistical analysis, correlation discovery, and insights generation.
"""

import asyncio
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pathlib import Path
import json

# Data models for structured outputs
class CorrelationResult(BaseModel):
    """Individual correlation result"""
    variable_1: str
    variable_2: str
    correlation_coefficient: float
    p_value: float
    significance_level: str
    interpretation: str

class CorrelationAnalysis(BaseModel):
    """Complete correlation analysis results"""
    dataset_summary: Dict[str, Any]
    significant_correlations: List[CorrelationResult]
    correlation_matrix: Dict[str, Dict[str, float]]
    insights: List[str]
    recommendations: List[str]
    data_quality_score: float

class TimeSeriesCorrelation(BaseModel):
    """Time-based correlation analysis"""
    time_period: str
    lag_analysis: Dict[str, float]
    peak_correlation: float
    optimal_lag: int
    temporal_insights: List[str]

# Dependencies for the agent
class CorrelationDependencies(BaseModel):
    """Dependencies passed to the correlation agent"""
    data: pd.DataFrame
    time_column: Optional[str] = None
    target_variables: Optional[List[str]] = None
    significance_threshold: float = 0.05

# Create the correlation analysis agent
correlation_agent = Agent(
    'claude-3-5-sonnet-20241022',
    system_prompt="""You are an expert statistical analyst specializing in manufacturing data correlation analysis.

    Your expertise includes:
    - Pearson, Spearman, and Kendall correlation analysis
    - Time-lagged correlation discovery
    - Statistical significance testing
    - Industrial process correlation interpretation
    - Root cause analysis through correlation patterns

    When analyzing correlations:
    1. Always consider the physical meaning of relationships
    2. Distinguish between correlation and causation
    3. Account for time lags in manufacturing processes
    4. Provide confidence levels and statistical significance
    5. Offer practical engineering interpretations

    Focus on actionable insights that can guide process improvements.""",
    result_type=CorrelationAnalysis,
    deps_type=CorrelationDependencies,
)

@correlation_agent.tool
def calculate_correlation_matrix(ctx: RunContext[CorrelationDependencies], method: str = 'pearson') -> Dict[str, Dict[str, float]]:
    """
    Calculate correlation matrix for numeric variables.
    
    Args:
        ctx: Run context with data dependencies
        method: Correlation method ('pearson', 'spearman', 'kendall')
        
    Returns:
        Dictionary representing correlation matrix
    """
    df = ctx.deps.data
    
    # Select only numeric columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    if not numeric_cols:
        return {}
    
    # Calculate correlation matrix
    if method == 'pearson':
        corr_matrix = df[numeric_cols].corr(method='pearson')
    elif method == 'spearman':
        corr_matrix = df[numeric_cols].corr(method='spearman')
    elif method == 'kendall':
        corr_matrix = df[numeric_cols].corr(method='kendall')
    else:
        corr_matrix = df[numeric_cols].corr()
    
    # Convert to dictionary format
    return corr_matrix.to_dict()

@correlation_agent.tool
def find_significant_correlations(ctx: RunContext[CorrelationDependencies], min_correlation: float = 0.3) -> List[Dict[str, Any]]:
    """
    Find statistically significant correlations above threshold.
    
    Args:
        ctx: Run context with data dependencies
        min_correlation: Minimum correlation coefficient threshold
        
    Returns:
        List of significant correlation results
    """
    from scipy.stats import pearsonr, spearmanr
    
    df = ctx.deps.data
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    significant_correlations = []
    
    for i, col1 in enumerate(numeric_cols):
        for col2 in numeric_cols[i+1:]:
            # Skip if same column
            if col1 == col2:
                continue
            
            # Remove NaN values
            clean_data = df[[col1, col2]].dropna()
            
            if len(clean_data) < 10:  # Need minimum samples
                continue
            
            # Calculate Pearson correlation
            corr_coef, p_value = pearsonr(clean_data[col1], clean_data[col2])
            
            # Check significance
            if abs(corr_coef) >= min_correlation and p_value < ctx.deps.significance_threshold:
                
                # Determine significance level
                if p_value < 0.001:
                    significance = 'highly_significant'
                elif p_value < 0.01:
                    significance = 'significant'
                else:
                    significance = 'marginally_significant'
                
                # Generate interpretation
                direction = 'positive' if corr_coef > 0 else 'negative'
                strength = 'strong' if abs(corr_coef) > 0.7 else 'moderate' if abs(corr_coef) > 0.3 else 'weak'
                
                interpretation = f"{strength.title()} {direction} correlation"
                
                significant_correlations.append({
                    'variable_1': col1,
                    'variable_2': col2,
                    'correlation_coefficient': corr_coef,
                    'p_value': p_value,
                    'significance_level': significance,
                    'interpretation': interpretation
                })
    
    return significant_correlations

@correlation_agent.tool
def analyze_time_lagged_correlations(ctx: RunContext[CorrelationDependencies], 
                                   var1: str, var2: str, max_lag: int = 60) -> Dict[str, Any]:
    """
    Analyze time-lagged correlations between two variables.
    
    Args:
        ctx: Run context with data dependencies
        var1: First variable name
        var2: Second variable name
        max_lag: Maximum lag to test (in time units)
        
    Returns:
        Dictionary with lag analysis results
    """
    df = ctx.deps.data
    
    if var1 not in df.columns or var2 not in df.columns:
        return {'error': f'Variables {var1} or {var2} not found in data'}
    
    # Ensure data is sorted by time if time column exists
    if ctx.deps.time_column and ctx.deps.time_column in df.columns:
        df = df.sort_values(ctx.deps.time_column)
    
    lag_correlations = {}
    
    for lag in range(0, max_lag + 1):
        if lag == 0:
            # No lag
            clean_data = df[[var1, var2]].dropna()
        else:
            # Create lagged version
            lagged_df = df.copy()
            lagged_df[f'{var2}_lag_{lag}'] = lagged_df[var2].shift(lag)
            clean_data = lagged_df[[var1, f'{var2}_lag_{lag}']].dropna()
        
        if len(clean_data) < 10:
            continue
        
        # Calculate correlation
        if lag == 0:
            corr_coef = clean_data[var1].corr(clean_data[var2])
        else:
            corr_coef = clean_data[var1].corr(clean_data[f'{var2}_lag_{lag}'])
        
        if not np.isnan(corr_coef):
            lag_correlations[lag] = corr_coef
    
    if not lag_correlations:
        return {'error': 'No valid correlations found'}
    
    # Find optimal lag
    optimal_lag = max(lag_correlations.keys(), key=lambda k: abs(lag_correlations[k]))
    peak_correlation = lag_correlations[optimal_lag]
    
    return {
        'lag_correlations': lag_correlations,
        'optimal_lag': optimal_lag,
        'peak_correlation': peak_correlation,
        'analysis_summary': f'Peak correlation of {peak_correlation:.3f} at lag {optimal_lag}'
    }

@correlation_agent.tool
def calculate_data_quality_score(ctx: RunContext[CorrelationDependencies]) -> float:
    """
    Calculate overall data quality score.
    
    Args:
        ctx: Run context with data dependencies
        
    Returns:
        Data quality score (0.0 to 1.0)
    """
    df = ctx.deps.data
    
    # Completeness score
    total_cells = df.shape[0] * df.shape[1]
    non_null_cells = df.count().sum()
    completeness = non_null_cells / total_cells if total_cells > 0 else 0
    
    # Consistency score (based on duplicate rows)
    duplicate_rows = df.duplicated().sum()
    consistency = 1 - (duplicate_rows / len(df)) if len(df) > 0 else 0
    
    # Validity score (based on numeric columns having reasonable values)
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    validity_scores = []
    
    for col in numeric_cols:
        col_data = df[col].dropna()
        if len(col_data) > 0:
            # Check for outliers using IQR method
            q1 = col_data.quantile(0.25)
            q3 = col_data.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = ((col_data < lower_bound) | (col_data > upper_bound)).sum()
            validity = 1 - (outliers / len(col_data))
            validity_scores.append(validity)
    
    validity = np.mean(validity_scores) if validity_scores else 1.0
    
    # Overall quality score (weighted average)
    quality_score = (completeness * 0.4 + consistency * 0.3 + validity * 0.3)
    
    return quality_score

async def analyze_correlations(data: pd.DataFrame, 
                             time_column: Optional[str] = None,
                             target_variables: Optional[List[str]] = None,
                             min_correlation: float = 0.3) -> CorrelationAnalysis:
    """
    Perform comprehensive correlation analysis on manufacturing data.
    
    Args:
        data: Pandas DataFrame with manufacturing data
        time_column: Name of the time column
        target_variables: List of target variables for focused analysis
        min_correlation: Minimum correlation threshold
        
    Returns:
        CorrelationAnalysis with complete results
    """
    
    # Create dependencies
    deps = CorrelationDependencies(
        data=data,
        time_column=time_column,
        target_variables=target_variables,
        significance_threshold=0.05
    )
    
    # Create analysis prompt
    prompt = f"""
    Analyze the correlations in this manufacturing dataset:
    
    Dataset info:
    - Shape: {data.shape}
    - Columns: {list(data.columns)}
    - Numeric columns: {list(data.select_dtypes(include=[np.number]).columns)}
    - Time column: {time_column}
    - Target variables: {target_variables}
    
    Focus on:
    1. Identifying significant correlations (min threshold: {min_correlation})
    2. Understanding process relationships
    3. Detecting potential quality issues
    4. Providing actionable insights
    
    Please provide a comprehensive correlation analysis.
    """
    
    # Run the agent
    result = await correlation_agent.run(prompt, deps=deps)
    
    return result.data

# Example usage
async def main():
    """Example usage of correlation analysis agent"""
    
    # Create sample manufacturing data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate correlated manufacturing data
    time_index = pd.date_range('2024-01-01', periods=n_samples, freq='1min')
    
    # Base variables
    speed = np.random.normal(150, 10, n_samples)  # Production speed
    temperature = np.random.normal(80, 5, n_samples)  # Process temperature
    
    # Create correlated variables
    thickness = 12.5 + 0.01 * speed + 0.005 * temperature + np.random.normal(0, 0.2, n_samples)
    pressure = 50 + 0.1 * speed + 0.05 * temperature + np.random.normal(0, 2, n_samples)
    
    # Quality indicator (inversely related to process variation)
    quality_score = 100 - 0.1 * np.abs(speed - 150) - 0.2 * np.abs(temperature - 80) + np.random.normal(0, 1, n_samples)
    
    # Create DataFrame
    data = pd.DataFrame({
        'timestamp': time_index,
        'speed': speed,
        'temperature': temperature,
        'thickness': thickness,
        'pressure': pressure,
        'quality_score': quality_score
    })
    
    print("Running correlation analysis...")
    print(f"Dataset shape: {data.shape}")
    print(f"Columns: {list(data.columns)}")
    
    # Run correlation analysis
    results = await analyze_correlations(
        data=data,
        time_column='timestamp',
        target_variables=['quality_score'],
        min_correlation=0.3
    )
    
    print("\n=== CORRELATION ANALYSIS RESULTS ===")
    print(f"Data Quality Score: {results.data_quality_score:.3f}")
    
    print(f"\nSignificant Correlations Found: {len(results.significant_correlations)}")
    for corr in results.significant_correlations:
        print(f"  {corr.variable_1} ↔ {corr.variable_2}: {corr.correlation_coefficient:.3f} "
              f"(p={corr.p_value:.4f}, {corr.significance_level})")
    
    print(f"\nKey Insights:")
    for insight in results.insights:
        print(f"  • {insight}")
    
    print(f"\nRecommendations:")
    for rec in results.recommendations:
        print(f"  • {rec}")

if __name__ == "__main__":
    asyncio.run(main())