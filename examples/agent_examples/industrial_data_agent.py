"""
Industrial Data Analysis Agent Example

Specialized agent for analyzing manufacturing sensor data with focus on:
- Mixed-frequency data alignment
- Event-driven analysis
- Process optimization insights
- Quality control recommendations
"""

import asyncio
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Any, Tuple
from pydantic import BaseModel, <PERSON>
from pydantic_ai import Agent, RunContext
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Data models for industrial analysis
class EventAnalysis(BaseModel):
    """Analysis of manufacturing events"""
    event_type: str
    frequency: int
    avg_duration: float
    impact_score: float
    correlation_with_quality: float
    recommendations: List[str]

class ProcessMetrics(BaseModel):
    """Process performance metrics"""
    overall_efficiency: float
    quality_score: float
    stability_index: float
    critical_issues: List[str]
    improvement_opportunities: List[str]

class IndustrialAnalysis(BaseModel):
    """Complete industrial data analysis"""
    time_period: str
    process_summary: ProcessMetrics
    event_analysis: List[EventAnalysis]
    correlation_insights: List[str]
    quality_recommendations: List[str]
    operational_insights: List[str]
    data_quality_assessment: Dict[str, float]

# Dependencies for industrial analysis
class IndustrialDependencies(BaseModel):
    """Dependencies for industrial data analysis"""
    continuous_data: pd.DataFrame  # High-frequency sensor data
    event_data: Optional[pd.DataFrame] = None  # Event-based data (stoppages, etc.)
    quality_data: Optional[pd.DataFrame] = None  # Quality measurements
    time_column: str = 'timestamp'
    target_quality_metrics: List[str] = Field(default_factory=list)

# Create the industrial analysis agent
industrial_agent = Agent(
    'claude-3-5-sonnet-20241022',
    system_prompt="""You are an expert industrial process analyst with deep knowledge of manufacturing systems.

    Your specialties include:
    - Manufacturing process optimization
    - Quality control and defect analysis
    - Equipment performance monitoring
    - Root cause analysis for production issues
    - Statistical process control (SPC)
    - Predictive maintenance insights

    When analyzing industrial data:
    1. Focus on process physics and engineering principles
    2. Consider equipment constraints and operating windows
    3. Identify leading indicators of quality issues
    4. Provide actionable recommendations for operators
    5. Prioritize safety and process stability
    6. Account for production economics in recommendations

    Your analysis should be practical, implementable, and focused on continuous improvement.""",
    result_type=IndustrialAnalysis,
    deps_type=IndustrialDependencies,
)

@industrial_agent.tool
def analyze_process_stability(ctx: RunContext[IndustrialDependencies], 
                            window_size: int = 60) -> Dict[str, float]:
    """
    Analyze process stability using statistical process control metrics.
    
    Args:
        ctx: Run context with industrial data
        window_size: Window size for rolling statistics (minutes)
        
    Returns:
        Dictionary with stability metrics
    """
    df = ctx.deps.continuous_data
    
    # Get numeric columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    stability_metrics = {}
    
    for col in numeric_cols:
        if col in df.columns:
            # Calculate rolling statistics
            rolling_mean = df[col].rolling(window=window_size).mean()
            rolling_std = df[col].rolling(window=window_size).std()
            
            # Coefficient of variation (CV)
            cv = rolling_std / rolling_mean
            avg_cv = cv.mean()
            
            # Process capability approximation
            if rolling_std.mean() > 0:
                # Assuming process target is the mean with ±3σ limits
                process_mean = df[col].mean()
                process_std = df[col].std()
                
                # Simple Cp calculation (assuming spec limits are ±3σ from mean)
                cp = (6 * process_std) / (6 * process_std)  # Simplified
                
                # Stability index (inverse of CV)
                stability_index = 1 / (1 + avg_cv) if avg_cv > 0 else 1.0
            else:
                stability_index = 1.0
            
            stability_metrics[col] = {
                'coefficient_of_variation': avg_cv,
                'stability_index': stability_index,
                'process_std': process_std,
                'out_of_control_points': len(df[np.abs((df[col] - process_mean) / process_std) > 3])
            }
    
    return stability_metrics

@industrial_agent.tool
def analyze_event_impact(ctx: RunContext[IndustrialDependencies], 
                        event_type: str = 'stoppage',
                        pre_event_window: int = 30,
                        post_event_window: int = 60) -> Dict[str, Any]:
    """
    Analyze the impact of events on continuous process variables.
    
    Args:
        ctx: Run context with industrial data
        event_type: Type of event to analyze
        pre_event_window: Minutes before event to analyze
        post_event_window: Minutes after event to analyze
        
    Returns:
        Dictionary with event impact analysis
    """
    continuous_df = ctx.deps.continuous_data
    event_df = ctx.deps.event_data
    
    if event_df is None or event_df.empty:
        return {'error': 'No event data available'}
    
    # Filter events by type if specified
    if event_type and 'event_type' in event_df.columns:
        events = event_df[event_df['event_type'] == event_type]
    else:
        events = event_df
    
    if events.empty:
        return {'error': f'No {event_type} events found'}
    
    # Ensure timestamp columns are datetime
    if ctx.deps.time_column in continuous_df.columns:
        continuous_df[ctx.deps.time_column] = pd.to_datetime(continuous_df[ctx.deps.time_column])
    
    impact_analysis = []
    
    for idx, event in events.iterrows():
        event_time = pd.to_datetime(event[ctx.deps.time_column])
        
        # Extract pre-event and post-event data
        pre_start = event_time - timedelta(minutes=pre_event_window)
        post_end = event_time + timedelta(minutes=post_event_window)
        
        pre_event_data = continuous_df[
            (continuous_df[ctx.deps.time_column] >= pre_start) & 
            (continuous_df[ctx.deps.time_column] < event_time)
        ]
        
        post_event_data = continuous_df[
            (continuous_df[ctx.deps.time_column] >= event_time) & 
            (continuous_df[ctx.deps.time_column] <= post_end)
        ]
        
        if len(pre_event_data) > 0 and len(post_event_data) > 0:
            # Calculate impact metrics
            numeric_cols = continuous_df.select_dtypes(include=[np.number]).columns
            
            event_impact = {
                'event_id': idx,
                'event_time': event_time,
                'duration': event.get('duration', 0),
                'variable_impacts': {}
            }
            
            for col in numeric_cols:
                if col in continuous_df.columns:
                    pre_mean = pre_event_data[col].mean()
                    post_mean = post_event_data[col].mean()
                    pre_std = pre_event_data[col].std()
                    post_std = post_event_data[col].std()
                    
                    # Calculate impact score
                    mean_change = abs(post_mean - pre_mean) / pre_mean if pre_mean != 0 else 0
                    std_change = abs(post_std - pre_std) / pre_std if pre_std != 0 else 0
                    
                    impact_score = (mean_change + std_change) / 2
                    
                    event_impact['variable_impacts'][col] = {
                        'mean_change_pct': mean_change * 100,
                        'std_change_pct': std_change * 100,
                        'impact_score': impact_score
                    }
            
            impact_analysis.append(event_impact)
    
    return {
        'total_events': len(events),
        'analyzed_events': len(impact_analysis),
        'event_impacts': impact_analysis,
        'average_impact_by_variable': {
            col: np.mean([event['variable_impacts'].get(col, {}).get('impact_score', 0) 
                         for event in impact_analysis])
            for col in continuous_df.select_dtypes(include=[np.number]).columns
        }
    }

@industrial_agent.tool
def calculate_quality_correlations(ctx: RunContext[IndustrialDependencies], 
                                 lag_minutes: List[int] = [0, 15, 30, 60]) -> Dict[str, Any]:
    """
    Calculate correlations between process variables and quality metrics.
    
    Args:
        ctx: Run context with industrial data
        lag_minutes: List of lag times to test
        
    Returns:
        Dictionary with quality correlations
    """
    continuous_df = ctx.deps.continuous_data
    quality_df = ctx.deps.quality_data
    
    if quality_df is None or quality_df.empty:
        return {'error': 'No quality data available'}
    
    # Merge data on timestamp
    if ctx.deps.time_column in continuous_df.columns and ctx.deps.time_column in quality_df.columns:
        # Ensure datetime types
        continuous_df[ctx.deps.time_column] = pd.to_datetime(continuous_df[ctx.deps.time_column])
        quality_df[ctx.deps.time_column] = pd.to_datetime(quality_df[ctx.deps.time_column])
        
        # Get quality metrics
        quality_metrics = ctx.deps.target_quality_metrics
        if not quality_metrics:
            quality_metrics = quality_df.select_dtypes(include=[np.number]).columns.tolist()
        
        correlation_results = {}
        
        for quality_metric in quality_metrics:
            if quality_metric in quality_df.columns:
                
                correlation_results[quality_metric] = {}
                
                # Test different lag times
                for lag in lag_minutes:
                    # Create lagged continuous data
                    lagged_continuous = continuous_df.copy()
                    lagged_continuous[ctx.deps.time_column] = lagged_continuous[ctx.deps.time_column] + timedelta(minutes=lag)
                    
                    # Merge with quality data
                    merged_data = pd.merge_asof(
                        quality_df[[ctx.deps.time_column, quality_metric]].sort_values(ctx.deps.time_column),
                        lagged_continuous.sort_values(ctx.deps.time_column),
                        on=ctx.deps.time_column,
                        direction='nearest'
                    )
                    
                    # Calculate correlations
                    numeric_cols = continuous_df.select_dtypes(include=[np.number]).columns
                    lag_correlations = {}
                    
                    for col in numeric_cols:
                        if col in merged_data.columns:
                            corr = merged_data[quality_metric].corr(merged_data[col])
                            if not np.isnan(corr):
                                lag_correlations[col] = corr
                    
                    correlation_results[quality_metric][f'lag_{lag}min'] = lag_correlations
        
        return correlation_results
    
    return {'error': 'Timestamp columns not found or not properly formatted'}

@industrial_agent.tool
def generate_process_recommendations(ctx: RunContext[IndustrialDependencies]) -> List[str]:
    """
    Generate process improvement recommendations based on data analysis.
    
    Args:
        ctx: Run context with industrial data
        
    Returns:
        List of actionable recommendations
    """
    continuous_df = ctx.deps.continuous_data
    event_df = ctx.deps.event_data
    
    recommendations = []
    
    # Analyze process variability
    numeric_cols = continuous_df.select_dtypes(include=[np.number]).columns
    
    for col in numeric_cols:
        if col in continuous_df.columns:
            cv = continuous_df[col].std() / continuous_df[col].mean()
            
            if cv > 0.1:  # High variability
                recommendations.append(
                    f"High variability detected in {col} (CV: {cv:.2f}). "
                    f"Consider implementing tighter process control or investigating root causes."
                )
            
            # Check for outliers
            q1 = continuous_df[col].quantile(0.25)
            q3 = continuous_df[col].quantile(0.75)
            iqr = q3 - q1
            outliers = continuous_df[(continuous_df[col] < q1 - 1.5 * iqr) | 
                                   (continuous_df[col] > q3 + 1.5 * iqr)]
            
            if len(outliers) > len(continuous_df) * 0.05:  # More than 5% outliers
                recommendations.append(
                    f"Excessive outliers in {col} ({len(outliers)} points, "
                    f"{len(outliers)/len(continuous_df)*100:.1f}%). "
                    f"Review sensor calibration and process conditions."
                )
    
    # Analyze event frequency
    if event_df is not None and not event_df.empty:
        if 'event_type' in event_df.columns:
            event_counts = event_df['event_type'].value_counts()
            
            for event_type, count in event_counts.items():
                if count > 10:  # Frequent events
                    recommendations.append(
                        f"Frequent {event_type} events detected ({count} occurrences). "
                        f"Consider implementing preventive measures or maintenance."
                    )
    
    # General recommendations
    if not recommendations:
        recommendations.append("Process appears stable. Continue monitoring key metrics.")
    
    return recommendations

async def analyze_industrial_data(continuous_data: pd.DataFrame,
                                event_data: Optional[pd.DataFrame] = None,
                                quality_data: Optional[pd.DataFrame] = None,
                                time_column: str = 'timestamp',
                                target_quality_metrics: List[str] = None) -> IndustrialAnalysis:
    """
    Perform comprehensive industrial data analysis.
    
    Args:
        continuous_data: High-frequency sensor data
        event_data: Event-based data (stoppages, maintenance, etc.)
        quality_data: Quality measurements
        time_column: Name of the timestamp column
        target_quality_metrics: List of quality metrics to focus on
        
    Returns:
        IndustrialAnalysis with complete results
    """
    
    # Create dependencies
    deps = IndustrialDependencies(
        continuous_data=continuous_data,
        event_data=event_data,
        quality_data=quality_data,
        time_column=time_column,
        target_quality_metrics=target_quality_metrics or []
    )
    
    # Determine time period
    if time_column in continuous_data.columns:
        start_time = continuous_data[time_column].min()
        end_time = continuous_data[time_column].max()
        time_period = f"{start_time} to {end_time}"
    else:
        time_period = "Unknown time period"
    
    # Create analysis prompt
    prompt = f"""
    Perform comprehensive industrial data analysis on this manufacturing dataset:
    
    Dataset Overview:
    - Time period: {time_period}
    - Continuous data shape: {continuous_data.shape}
    - Event data: {'Available' if event_data is not None else 'Not available'}
    - Quality data: {'Available' if quality_data is not None else 'Not available'}
    - Variables: {list(continuous_data.columns)}
    
    Please provide:
    1. Process performance assessment
    2. Quality correlation analysis
    3. Event impact analysis
    4. Operational recommendations
    5. Data quality assessment
    
    Focus on actionable insights for process improvement.
    """
    
    # Run the agent
    result = await industrial_agent.run(prompt, deps=deps)
    
    return result.data

# Example usage
async def main():
    """Example usage of industrial data analysis agent"""
    
    # Generate sample industrial data
    np.random.seed(42)
    n_samples = 2000
    
    # Create timestamp index
    timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='30s')
    
    # Generate correlated process variables
    base_temp = 80 + np.random.normal(0, 2, n_samples)
    base_speed = 150 + np.random.normal(0, 5, n_samples)
    base_pressure = 50 + 0.1 * base_speed + np.random.normal(0, 1, n_samples)
    
    # Create continuous data
    continuous_data = pd.DataFrame({
        'timestamp': timestamps,
        'temperature': base_temp,
        'speed': base_speed,
        'pressure': base_pressure,
        'flow_rate': 100 + 0.05 * base_speed + np.random.normal(0, 2, n_samples),
        'thickness': 12.5 + 0.01 * base_speed + np.random.normal(0, 0.1, n_samples)
    })
    
    # Generate event data
    event_times = pd.date_range('2024-01-01', '2024-01-01 16:00:00', freq='2h')
    event_data = pd.DataFrame({
        'timestamp': event_times,
        'event_type': ['stoppage', 'maintenance', 'quality_check'] * (len(event_times) // 3 + 1),
        'duration': np.random.randint(5, 30, len(event_times))
    })[:len(event_times)]
    
    # Generate quality data
    quality_times = pd.date_range('2024-01-01', '2024-01-01 16:00:00', freq='1h')
    quality_data = pd.DataFrame({
        'timestamp': quality_times,
        'quality_score': 95 + np.random.normal(0, 2, len(quality_times)),
        'defect_rate': np.random.exponential(0.5, len(quality_times))
    })
    
    print("Running industrial data analysis...")
    print(f"Continuous data: {continuous_data.shape}")
    print(f"Event data: {event_data.shape}")
    print(f"Quality data: {quality_data.shape}")
    
    # Run analysis
    results = await analyze_industrial_data(
        continuous_data=continuous_data,
        event_data=event_data,
        quality_data=quality_data,
        time_column='timestamp',
        target_quality_metrics=['quality_score', 'defect_rate']
    )
    
    print("\n=== INDUSTRIAL DATA ANALYSIS RESULTS ===")
    print(f"Time Period: {results.time_period}")
    print(f"Overall Efficiency: {results.process_summary.overall_efficiency:.2f}")
    print(f"Quality Score: {results.process_summary.quality_score:.2f}")
    print(f"Stability Index: {results.process_summary.stability_index:.2f}")
    
    print(f"\nEvent Analysis:")
    for event in results.event_analysis:
        print(f"  {event.event_type}: {event.frequency} occurrences, "
              f"avg duration: {event.avg_duration:.1f}min, "
              f"impact score: {event.impact_score:.3f}")
    
    print(f"\nCorrelation Insights:")
    for insight in results.correlation_insights:
        print(f"  • {insight}")
    
    print(f"\nQuality Recommendations:")
    for rec in results.quality_recommendations:
        print(f"  • {rec}")

if __name__ == "__main__":
    asyncio.run(main())