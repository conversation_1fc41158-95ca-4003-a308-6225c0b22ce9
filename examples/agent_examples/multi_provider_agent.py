"""
Multi-Provider Pydantic AI Agent Example

Demonstrates how to configure an agent with multiple LLM providers
(Anthropic Claude and Google Vertex AI) with fallback mechanisms.
"""

import asyncio
import os
from typing import Optional, Union
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models import KnownModelName
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ProviderConfig(BaseModel):
    """Configuration for LLM providers"""
    primary_provider: str
    fallback_provider: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    vertex_project_id: Optional[str] = None
    vertex_location: Optional[str] = None

class AnalysisResponse(BaseModel):
    """Structured response from the agent"""
    analysis: str
    confidence: float
    data_source: str
    provider_used: str
    processing_time: float

def create_multi_provider_agent(config: ProviderConfig) -> Agent:
    """
    Create an agent with multiple provider support.
    
    Args:
        config: Provider configuration
        
    Returns:
        Configured Pydantic AI agent
    """
    
    # Determine primary model
    if config.primary_provider.lower() == 'anthropic':
        primary_model = 'claude-3-5-sonnet-20241022'
    elif config.primary_provider.lower() == 'vertex_ai':
        primary_model = 'gemini-1.5-pro'
    else:
        primary_model = 'openai:gpt-4o'
    
    # Create agent with fallback
    agent = Agent(
        model=primary_model,
        system_prompt="""You are an expert industrial data analyst specializing in manufacturing processes.
        
        Your expertise includes:
        - Time series analysis of sensor data
        - Correlation analysis between process variables
        - Quality control and defect detection
        - Process optimization recommendations
        
        Always provide:
        1. Clear, actionable insights
        2. Confidence levels for your analysis
        3. Specific recommendations with rationale
        4. Data quality assessments
        
        Respond in a structured format suitable for engineering decision-making.""",
        result_type=AnalysisResponse,
    )
    
    return agent

def setup_environment_variables():
    """Setup and validate environment variables for different providers"""
    
    config = ProviderConfig(
        primary_provider=os.getenv('LLM_PROVIDER', 'anthropic'),
        anthropic_api_key=os.getenv('ANTHROPIC_API_KEY'),
        vertex_project_id=os.getenv('VERTEX_AI_PROJECT'),
        vertex_location=os.getenv('VERTEX_AI_LOCATION', 'us-central1')
    )
    
    # Validate configuration
    if config.primary_provider.lower() == 'anthropic':
        if not config.anthropic_api_key:
            raise ValueError("ANTHROPIC_API_KEY is required for Anthropic provider")
    elif config.primary_provider.lower() == 'vertex_ai':
        if not config.vertex_project_id:
            raise ValueError("VERTEX_AI_PROJECT is required for Vertex AI provider")
    
    return config

class MultiProviderAnalyzer:
    """
    Analyzer class that handles multiple LLM providers with fallback
    """
    
    def __init__(self, config: ProviderConfig):
        self.config = config
        self.agent = create_multi_provider_agent(config)
        self.primary_provider = config.primary_provider
    
    async def analyze_data(self, data_description: str, query: str) -> AnalysisResponse:
        """
        Analyze data using the configured agent with fallback handling.
        
        Args:
            data_description: Description of the data being analyzed
            query: Specific analysis question
            
        Returns:
            AnalysisResponse with results
        """
        import time
        start_time = time.time()
        
        try:
            # Primary provider attempt
            result = await self.agent.run(
                f"Data: {data_description}\nQuery: {query}",
                deps=None
            )
            
            processing_time = time.time() - start_time
            
            # Add metadata to response
            response = result.data
            response.provider_used = self.primary_provider
            response.processing_time = processing_time
            
            return response
            
        except Exception as e:
            # Fallback logic could be implemented here
            print(f"Error with primary provider {self.primary_provider}: {e}")
            
            # For now, re-raise the exception
            # In a production system, you would switch to fallback provider
            raise

# Tool functions for the agent
@create_multi_provider_agent(ProviderConfig(primary_provider='anthropic')).tool
def validate_sensor_data(ctx: RunContext[None], data_points: list[float], sensor_type: str) -> dict:
    """
    Validate sensor data quality and detect anomalies.
    
    Args:
        ctx: Run context
        data_points: List of sensor readings
        sensor_type: Type of sensor (e.g., 'thickness', 'speed', 'pressure')
        
    Returns:
        Dictionary with validation results
    """
    if not data_points:
        return {
            'valid': False,
            'error': 'No data points provided',
            'anomalies': [],
            'quality_score': 0.0
        }
    
    # Basic validation rules by sensor type
    validation_rules = {
        'thickness': {'min': 0, 'max': 50, 'typical_range': (10, 15)},
        'speed': {'min': 0, 'max': 1000, 'typical_range': (50, 200)},
        'pressure': {'min': 0, 'max': 100, 'typical_range': (20, 80)},
        'temperature': {'min': -50, 'max': 200, 'typical_range': (20, 80)}
    }
    
    rules = validation_rules.get(sensor_type, {'min': 0, 'max': float('inf'), 'typical_range': (0, float('inf'))})
    
    # Check for anomalies
    anomalies = []
    valid_count = 0
    
    for i, value in enumerate(data_points):
        if value is None:
            anomalies.append(f"Missing value at index {i}")
        elif value < rules['min'] or value > rules['max']:
            anomalies.append(f"Out of range value {value} at index {i}")
        elif value < rules['typical_range'][0] or value > rules['typical_range'][1]:
            anomalies.append(f"Atypical value {value} at index {i}")
        else:
            valid_count += 1
    
    quality_score = valid_count / len(data_points) if data_points else 0
    
    return {
        'valid': len(anomalies) == 0,
        'anomalies': anomalies,
        'quality_score': quality_score,
        'total_points': len(data_points),
        'valid_points': valid_count,
        'sensor_type': sensor_type
    }

# Example usage
async def main():
    """Example usage of multi-provider agent"""
    
    try:
        # Setup configuration
        config = setup_environment_variables()
        print(f"Using primary provider: {config.primary_provider}")
        
        # Create analyzer
        analyzer = MultiProviderAnalyzer(config)
        
        # Sample industrial data
        thickness_data = [12.5, 12.3, 12.7, 12.4, 12.6, 12.8, 12.2, 12.9, 12.1, 12.7]
        speed_data = [150, 148, 155, 152, 149, 156, 147, 158, 145, 153]
        
        # Analyze thickness data
        print("\nAnalyzing thickness data...")
        result = await analyzer.analyze_data(
            f"Thickness measurements (mm): {thickness_data}",
            "What patterns do you see in this thickness data? Are there quality concerns?"
        )
        
        print(f"Analysis: {result.analysis}")
        print(f"Confidence: {result.confidence:.2f}")
        print(f"Provider used: {result.provider_used}")
        print(f"Processing time: {result.processing_time:.2f}s")
        
        # Analyze speed data
        print("\nAnalyzing speed data...")
        result = await analyzer.analyze_data(
            f"Production speed (m/min): {speed_data}",
            "How does speed variation correlate with potential quality issues?"
        )
        
        print(f"Analysis: {result.analysis}")
        print(f"Confidence: {result.confidence:.2f}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure to set up your environment variables in .env file")

if __name__ == "__main__":
    asyncio.run(main())