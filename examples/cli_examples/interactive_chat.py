"""
Interactive Chat CLI with Pydantic AI Agent

Interactive command-line interface that provides a chat-like experience
for manufacturing data analysis using Pydantic AI agents.
"""

import asyncio
import click
import pandas as pd
from pathlib import Path
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.markdown import Markdown
from rich.live import Live
from rich.spinner import Spinner
import sys
import os
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from agent_examples.correlation_agent import correlation_agent, CorrelationDependencies
from agent_examples.industrial_data_agent import industrial_agent, IndustrialDependencies
from data_processing.csv_loader import ManufacturingDataLoader

console = Console()

class InteractiveManufacturingChat:
    """
    Interactive chat interface for manufacturing data analysis
    """
    
    def __init__(self, data_dir: str = "test-data"):
        """
        Initialize the interactive chat system.
        
        Args:
            data_dir: Directory containing manufacturing data files
        """
        self.data_dir = Path(data_dir)
        self.loader = ManufacturingDataLoader(str(data_dir))
        self.loaded_data = {}
        self.conversation_history = []
        self.current_agent = 'correlation'  # Default agent
        self.running = True
        
        # Available agents
        self.agents = {
            'correlation': {
                'agent': correlation_agent,
                'description': 'Correlation analysis and statistical insights',
                'deps_type': CorrelationDependencies
            },
            'industrial': {
                'agent': industrial_agent,
                'description': 'Industrial process analysis and optimization',
                'deps_type': IndustrialDependencies
            }
        }
    
    async def start(self):
        """Start the interactive chat session"""
        
        # Welcome message
        welcome_text = """
# 🏭 Manufacturing Data Analysis Chat

Welcome to the interactive manufacturing data analysis system!

**Available Commands:**
- `/help` - Show this help message
- `/load` - Load manufacturing data files
- `/agent <name>` - Switch to a different agent
- `/data` - Show loaded data summary
- `/clear` - Clear conversation history
- `/quit` - Exit the chat

**Available Agents:**
- `correlation` - Statistical correlation analysis
- `industrial` - Industrial process optimization

Type your questions about manufacturing data, correlations, or process optimization.
        """
        
        console.print(Panel(Markdown(welcome_text), title="Welcome", border_style="blue"))
        
        # Auto-load data if available
        if self.data_dir.exists():
            await self._auto_load_data()
        
        # Main chat loop
        while self.running:
            try:
                # Get user input
                user_input = Prompt.ask(
                    f"[bold cyan]You[/bold cyan] ({self.current_agent})",
                    default=""
                )
                
                if not user_input.strip():
                    continue
                
                # Handle commands
                if user_input.startswith('/'):
                    await self._handle_command(user_input)
                else:
                    # Process with AI agent
                    await self._process_with_agent(user_input)
                    
            except KeyboardInterrupt:
                if Confirm.ask("\nDo you want to exit?"):
                    break
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
        
        console.print("\n[bold green]Thank you for using Manufacturing Data Analysis Chat![/bold green]")
    
    async def _auto_load_data(self):
        """Automatically load available data files"""
        try:
            with Live(Spinner('dots', text='Loading data...'), console=console):
                self.loaded_data = self.loader.load_all_manufacturing_data()
            
            if self.loaded_data:
                console.print(f"[green]✓ Automatically loaded {len(self.loaded_data)} datasets[/green]")
                
                # Show quick summary
                for data_type, df in self.loaded_data.items():
                    console.print(f"  {data_type}: {len(df)} records, {len(df.columns)} columns")
            else:
                console.print("[yellow]No data files found in the specified directory[/yellow]")
                
        except Exception as e:
            console.print(f"[red]Error loading data: {e}[/red]")
    
    async def _handle_command(self, command: str):
        """Handle special commands"""
        parts = command[1:].split()  # Remove '/' and split
        cmd = parts[0].lower()
        
        if cmd == 'help':
            await self._show_help()
        
        elif cmd == 'load':
            await self._load_data()
        
        elif cmd == 'agent':
            if len(parts) > 1:
                await self._switch_agent(parts[1])
            else:
                await self._show_agents()
        
        elif cmd == 'data':
            await self._show_data_summary()
        
        elif cmd == 'clear':
            self.conversation_history = []
            console.print("[yellow]Conversation history cleared[/yellow]")
        
        elif cmd == 'quit' or cmd == 'exit':
            self.running = False
        
        else:
            console.print(f"[red]Unknown command: {command}[/red]")
            console.print("Type `/help` for available commands")
    
    async def _show_help(self):
        """Show help information"""
        help_text = """
# 📚 Help - Manufacturing Data Analysis Chat

## Commands:
- `/help` - Show this help message
- `/load` - (Re)load manufacturing data files
- `/agent <name>` - Switch to correlation or industrial agent
- `/data` - Show summary of loaded datasets
- `/clear` - Clear conversation history
- `/quit` - Exit the chat

## Agents:
- **correlation** - Analyze correlations between variables, lag analysis, statistical significance
- **industrial** - Process optimization, event analysis, quality recommendations

## Example Questions:
- "What correlations exist between speed and thickness?"
- "Analyze the impact of stoppages on quality metrics"
- "Show me lag correlations between temperature and thickness"
- "What patterns do you see in the thickness data?"
- "How do process variables relate to scrap rates?"

## Data Format:
The system expects CSV files in the data directory:
- `thickness.csv` - Product thickness measurements
- `speed.csv` - Production line speed data
- `stop.csv` - Machine stoppage events
- `fm_stack.csv`, `sm_stack.csv` - Scrap/stack data
        """
        
        console.print(Panel(Markdown(help_text), title="Help", border_style="green"))
    
    async def _load_data(self):
        """Load or reload manufacturing data"""
        try:
            with Live(Spinner('dots', text='Loading data...'), console=console):
                self.loaded_data = self.loader.load_all_manufacturing_data()
            
            if self.loaded_data:
                console.print(f"[green]✓ Loaded {len(self.loaded_data)} datasets[/green]")
                
                # Create summary table
                from rich.table import Table
                table = Table(title="Loaded Datasets")
                table.add_column("Dataset", style="cyan")
                table.add_column("Records", justify="right", style="green")
                table.add_column("Variables", justify="right", style="yellow")
                
                for data_type, df in self.loaded_data.items():
                    table.add_row(data_type, str(len(df)), str(len(df.columns)))
                
                console.print(table)
            else:
                console.print("[yellow]No data files found[/yellow]")
                
        except Exception as e:
            console.print(f"[red]Error loading data: {e}[/red]")
    
    async def _switch_agent(self, agent_name: str):
        """Switch to a different agent"""
        if agent_name in self.agents:
            self.current_agent = agent_name
            agent_info = self.agents[agent_name]
            console.print(f"[green]✓ Switched to {agent_name} agent[/green]")
            console.print(f"[dim]{agent_info['description']}[/dim]")
        else:
            console.print(f"[red]Unknown agent: {agent_name}[/red]")
            await self._show_agents()
    
    async def _show_agents(self):
        """Show available agents"""
        console.print("[bold]Available Agents:[/bold]")
        for name, info in self.agents.items():
            current = " (current)" if name == self.current_agent else ""
            console.print(f"  [cyan]{name}[/cyan]{current}: {info['description']}")
    
    async def _show_data_summary(self):
        """Show summary of loaded data"""
        if not self.loaded_data:
            console.print("[yellow]No data loaded. Use `/load` to load data.[/yellow]")
            return
        
        console.print("[bold]Data Summary:[/bold]")
        
        for data_type, df in self.loaded_data.items():
            console.print(f"\n[cyan]{data_type.upper()}:[/cyan]")
            console.print(f"  Records: {len(df)}")
            console.print(f"  Variables: {list(df.columns)}")
            
            # Show time range if timestamp column exists
            if 'timestamp' in df.columns:
                try:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    start_time = df['timestamp'].min()
                    end_time = df['timestamp'].max()
                    console.print(f"  Time range: {start_time} to {end_time}")
                except:
                    console.print("  Time range: Invalid timestamps")
            
            # Show missing data
            missing = df.isnull().sum().sum()
            if missing > 0:
                console.print(f"  Missing values: {missing}")
    
    async def _process_with_agent(self, user_input: str):
        """Process user input with the current AI agent"""
        if not self.loaded_data:
            console.print("[yellow]Please load data first using `/load` command[/yellow]")
            return
        
        try:
            agent_info = self.agents[self.current_agent]
            agent = agent_info['agent']
            deps_type = agent_info['deps_type']
            
            # Prepare dependencies based on agent type
            if self.current_agent == 'correlation':
                # Use the largest dataset for correlation analysis
                largest_dataset = max(self.loaded_data.items(), key=lambda x: len(x[1]))
                data_name, data_df = largest_dataset
                
                deps = CorrelationDependencies(
                    data=data_df,
                    time_column='timestamp' if 'timestamp' in data_df.columns else None,
                    target_variables=None,
                    significance_threshold=0.05
                )
                
                prompt = f"""
                Analyze this manufacturing dataset: {data_name}
                
                Dataset info:
                - Shape: {data_df.shape}
                - Columns: {list(data_df.columns)}
                
                User question: {user_input}
                
                Please provide correlation analysis focusing on the user's specific question.
                """
            
            elif self.current_agent == 'industrial':
                # Prepare industrial dependencies
                continuous_data = None
                event_data = None
                quality_data = None
                
                # Identify data types
                for data_type, df in self.loaded_data.items():
                    if data_type in ['thickness', 'speed']:
                        continuous_data = df
                    elif data_type in ['stop']:
                        event_data = df
                    elif 'quality' in df.columns:
                        quality_data = df
                
                if continuous_data is None:
                    # Use the largest dataset as continuous data
                    continuous_data = max(self.loaded_data.values(), key=len)
                
                deps = IndustrialDependencies(
                    continuous_data=continuous_data,
                    event_data=event_data,
                    quality_data=quality_data,
                    time_column='timestamp',
                    target_quality_metrics=[]
                )
                
                prompt = f"""
                Analyze this industrial manufacturing data:
                
                Available datasets: {list(self.loaded_data.keys())}
                Continuous data shape: {continuous_data.shape if continuous_data is not None else 'None'}
                Event data: {'Available' if event_data is not None else 'Not available'}
                Quality data: {'Available' if quality_data is not None else 'Not available'}
                
                User question: {user_input}
                
                Please provide industrial process analysis focusing on the user's question.
                """
            
            # Show thinking indicator
            with Live(Spinner('dots', text='Analyzing...'), console=console, transient=True):
                result = await agent.run(prompt, deps=deps)
            
            # Display result
            if hasattr(result.data, 'insights') and result.data.insights:
                # Structured response
                console.print(f"[bold green]Agent ({self.current_agent}):[/bold green]")
                
                if hasattr(result.data, 'dataset_summary'):
                    console.print(f"\n[bold]Data Quality Score:[/bold] {result.data.data_quality_score:.2f}")
                
                if hasattr(result.data, 'significant_correlations') and result.data.significant_correlations:
                    console.print(f"\n[bold]Significant Correlations Found:[/bold] {len(result.data.significant_correlations)}")
                    for i, corr in enumerate(result.data.significant_correlations[:3], 1):
                        console.print(f"{i}. {corr.variable_1} ↔ {corr.variable_2}: {corr.correlation_coefficient:.3f}")
                
                console.print(f"\n[bold]Key Insights:[/bold]")
                for insight in result.data.insights:
                    console.print(f"• {insight}")
                
                if hasattr(result.data, 'recommendations') and result.data.recommendations:
                    console.print(f"\n[bold]Recommendations:[/bold]")
                    for rec in result.data.recommendations:
                        console.print(f"• {rec}")
            
            else:
                # Simple response
                console.print(f"[bold green]Agent ({self.current_agent}):[/bold green] {result.data}")
            
            # Add to conversation history
            self.conversation_history.append({
                'timestamp': datetime.now(),
                'user_input': user_input,
                'agent_response': str(result.data),
                'agent_type': self.current_agent
            })
            
        except Exception as e:
            console.print(f"[red]Error processing with agent: {e}[/red]")
            console.print("[dim]Try rephrasing your question or check if data is loaded properly.[/dim]")

@click.command()
@click.option('--data-dir', '-d', default='test-data', help='Data directory path')
@click.option('--agent', '-a', default='correlation', help='Starting agent (correlation/industrial)')
def main(data_dir, agent):
    """Start interactive manufacturing data analysis chat"""
    
    # Validate data directory
    data_path = Path(data_dir)
    if not data_path.exists():
        console.print(f"[red]Data directory not found: {data_dir}[/red]")
        console.print("Please specify a valid data directory with --data-dir")
        return
    
    # Initialize and start chat
    chat = InteractiveManufacturingChat(data_dir)
    
    # Set starting agent
    if agent in chat.agents:
        chat.current_agent = agent
    
    # Run the chat
    try:
        asyncio.run(chat.start())
    except KeyboardInterrupt:
        console.print("\n[yellow]Chat interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error starting chat: {e}[/red]")

if __name__ == '__main__':
    main()