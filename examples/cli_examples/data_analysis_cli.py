"""
Advanced Data Analysis CLI

Comprehensive command-line interface for manufacturing data analysis
with support for multiple analysis types, export options, and advanced features.
"""

import click
import pandas as pd
import numpy as np
import asyncio
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.panel import Panel
import json
import yaml
from datetime import datetime, timedelta
import sys
import os

# Add parent directories to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from data_processing.csv_loader import ManufacturingDataLoader
from data_processing.correlation_analyzer import ManufacturingCorrelationAnalyzer
from data_processing.time_series_processor import TimeSeriesProcessor
from data_processing.mixed_frequency_aligner import MixedFrequencyAligner, DataFrequency

console = Console()

class AnalysisConfig:
    """Configuration for analysis parameters"""
    def __init__(self):
        self.correlation_threshold = 0.3
        self.significance_level = 0.05
        self.max_lag = 60
        self.output_format = 'json'
        self.include_plots = True
        self.time_column = 'timestamp'

@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file (JSON/YAML)')
@click.option('--data-dir', '-d', default='test-data', help='Data directory')
@click.option('--output-dir', '-o', default='output', help='Output directory')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, config, data_dir, output_dir, verbose):
    """Advanced Manufacturing Data Analysis CLI"""
    ctx.ensure_object(dict)
    
    # Load configuration
    analysis_config = AnalysisConfig()
    if config:
        config_data = load_config(config)
        for key, value in config_data.items():
            if hasattr(analysis_config, key):
                setattr(analysis_config, key, value)
    
    ctx.obj['config'] = analysis_config
    ctx.obj['data_dir'] = Path(data_dir)
    ctx.obj['output_dir'] = Path(output_dir)
    ctx.obj['verbose'] = verbose
    
    # Create output directory
    ctx.obj['output_dir'].mkdir(exist_ok=True)
    
    if verbose:
        console.print(f"[dim]Data directory: {data_dir}[/dim]")
        console.print(f"[dim]Output directory: {output_dir}[/dim]")

def load_config(config_path):
    """Load configuration from JSON or YAML file"""
    path = Path(config_path)
    
    try:
        with open(path, 'r') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                return yaml.safe_load(f)
            else:
                return json.load(f)
    except Exception as e:
        console.print(f"[red]Error loading config: {e}[/red]")
        return {}

@cli.command()
@click.option('--start-date', type=click.DateTime(), help='Start date for analysis')
@click.option('--end-date', type=click.DateTime(), help='End date for analysis')
@click.option('--datasets', multiple=True, help='Specific datasets to analyze')
@click.pass_context
def analyze(ctx, start_date, end_date, datasets):
    """Perform comprehensive data analysis"""
    config = ctx.obj['config']
    data_dir = ctx.obj['data_dir']
    output_dir = ctx.obj['output_dir']
    verbose = ctx.obj['verbose']
    
    console.print("[bold blue]Starting comprehensive analysis...[/bold blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        console=console,
    ) as progress:
        
        # Load data
        load_task = progress.add_task("Loading data...", total=100)
        loader = ManufacturingDataLoader(str(data_dir))
        all_data = loader.load_all_manufacturing_data()
        progress.update(load_task, completed=100)
        
        if not all_data:
            console.print("[red]No data found to analyze[/red]")
            return
        
        # Filter datasets if specified
        if datasets:
            all_data = {k: v for k, v in all_data.items() if k in datasets}
        
        # Filter by date range if specified
        if start_date or end_date:
            filtered_data = {}
            for data_type, df in all_data.items():
                if config.time_column in df.columns:
                    df[config.time_column] = pd.to_datetime(df[config.time_column])
                    
                    if start_date:
                        df = df[df[config.time_column] >= start_date]
                    if end_date:
                        df = df[df[config.time_column] <= end_date]
                    
                    filtered_data[data_type] = df
                else:
                    filtered_data[data_type] = df
            
            all_data = filtered_data
        
        # Analysis tasks
        analysis_task = progress.add_task("Running analysis...", total=len(all_data) * 4)
        
        results = {
            'metadata': {
                'analysis_timestamp': datetime.now().isoformat(),
                'datasets_analyzed': list(all_data.keys()),
                'configuration': vars(config)
            },
            'data_quality': {},
            'correlations': {},
            'time_series_analysis': {},
            'patterns': {}
        }
        
        # Initialize analyzers
        correlation_analyzer = ManufacturingCorrelationAnalyzer(config.significance_level)
        ts_processor = TimeSeriesProcessor()
        
        for data_type, df in all_data.items():
            if verbose:
                console.print(f"Analyzing {data_type}...")
            
            # Data quality assessment
            quality_result = loader.validate_data_quality(df, data_type)
            results['data_quality'][data_type] = {
                'quality_score': quality_result.quality_score,
                'errors': quality_result.errors,
                'warnings': quality_result.warnings,
                'recommendations': quality_result.recommendations
            }
            progress.advance(analysis_task)
            
            # Correlation analysis
            correlations = correlation_analyzer.find_significant_correlations(
                df, min_correlation=config.correlation_threshold
            )
            
            correlation_matrix = correlation_analyzer.calculate_correlation_matrix(df)
            
            results['correlations'][data_type] = {
                'significant_correlations': [
                    {
                        'variable_1': corr.variable_1,
                        'variable_2': corr.variable_2,
                        'correlation': corr.correlation_coefficient,
                        'p_value': corr.p_value,
                        'interpretation': corr.interpretation
                    }
                    for corr in correlations
                ],
                'correlation_matrix': correlation_matrix.to_dict() if not correlation_matrix.empty else {}
            }
            progress.advance(analysis_task)
            
            # Time series analysis
            if config.time_column in df.columns:
                # Detect anomalies
                anomaly_df = ts_processor.detect_anomalies(df)
                anomaly_counts = {
                    col: int(anomaly_df[f'{col}_anomaly'].sum())
                    for col in df.select_dtypes(include=[np.number]).columns
                    if f'{col}_anomaly' in anomaly_df.columns
                }
                
                # Calculate rolling statistics
                rolling_stats = ts_processor.calculate_rolling_statistics(df)
                
                results['time_series_analysis'][data_type] = {
                    'anomaly_counts': anomaly_counts,
                    'time_range': {
                        'start': df[config.time_column].min().isoformat() if df[config.time_column].notna().any() else None,
                        'end': df[config.time_column].max().isoformat() if df[config.time_column].notna().any() else None,
                        'duration_hours': (df[config.time_column].max() - df[config.time_column].min()).total_seconds() / 3600 if df[config.time_column].notna().any() else 0
                    }
                }
            progress.advance(analysis_task)
            
            # Pattern detection
            if not correlation_matrix.empty:
                patterns = correlation_analyzer.detect_correlation_patterns(
                    correlation_matrix, threshold=0.7
                )
                results['patterns'][data_type] = patterns
            progress.advance(analysis_task)
        
        # Save results
        save_task = progress.add_task("Saving results...", total=100)
        
        # Save main results
        output_file = output_dir / f"analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        progress.update(save_task, completed=100)
    
    console.print(f"[green]✓ Analysis complete! Results saved to {output_file}[/green]")
    
    # Display summary
    display_analysis_summary(results)

@cli.command()
@click.option('--variable1', '-v1', required=True, help='First variable')
@click.option('--variable2', '-v2', required=True, help='Second variable')
@click.option('--dataset', '-ds', help='Dataset containing variables')
@click.option('--export', '-e', is_flag=True, help='Export results to file')
@click.pass_context
def lag_correlation(ctx, variable1, variable2, dataset, export):
    """Analyze time-lagged correlations between variables"""
    config = ctx.obj['config']
    data_dir = ctx.obj['data_dir']
    output_dir = ctx.obj['output_dir']
    
    console.print(f"[bold blue]Lag Correlation Analysis: {variable1} → {variable2}[/bold blue]")
    
    # Load data
    loader = ManufacturingDataLoader(str(data_dir))
    all_data = loader.load_all_manufacturing_data()
    
    # Find target dataset
    target_df = None
    target_dataset = None
    
    if dataset and dataset in all_data:
        if variable1 in all_data[dataset].columns and variable2 in all_data[dataset].columns:
            target_df = all_data[dataset]
            target_dataset = dataset
    else:
        for data_type, df in all_data.items():
            if variable1 in df.columns and variable2 in df.columns:
                target_df = df
                target_dataset = data_type
                break
    
    if target_df is None:
        console.print(f"[red]Variables not found in any dataset[/red]")
        return
    
    # Perform analysis
    analyzer = ManufacturingCorrelationAnalyzer()
    
    with console.status("Calculating lag correlations..."):
        lag_result = analyzer.analyze_lag_correlations(
            target_df, variable1, variable2, max_lag=config.max_lag
        )
    
    # Display results
    console.print(f"\n[bold]Results for {target_dataset} dataset:[/bold]")
    console.print(f"Optimal lag: [green]{lag_result.optimal_lag}[/green] time periods")
    console.print(f"Max correlation: [green]{lag_result.max_correlation:.4f}[/green]")
    console.print(f"P-value: [yellow]{lag_result.p_value:.6f}[/yellow]")
    console.print(f"Interpretation: {lag_result.interpretation}")
    
    # Show correlation by lag table
    if lag_result.correlation_by_lag:
        table = Table(title="Correlation by Lag")
        table.add_column("Lag", justify="right", style="cyan")
        table.add_column("Correlation", justify="right", style="green")
        table.add_column("Abs. Correlation", justify="right", style="yellow")
        
        sorted_lags = sorted(lag_result.correlation_by_lag.items())
        for lag, corr in sorted_lags:
            table.add_row(str(lag), f"{corr:.4f}", f"{abs(corr):.4f}")
        
        console.print(table)
    
    # Export results if requested
    if export:
        export_data = {
            'analysis_type': 'lag_correlation',
            'timestamp': datetime.now().isoformat(),
            'variables': {'variable_1': variable1, 'variable_2': variable2},
            'dataset': target_dataset,
            'results': {
                'optimal_lag': lag_result.optimal_lag,
                'max_correlation': lag_result.max_correlation,
                'p_value': lag_result.p_value,
                'interpretation': lag_result.interpretation,
                'correlation_by_lag': lag_result.correlation_by_lag
            }
        }
        
        export_file = output_dir / f"lag_correlation_{variable1}_{variable2}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(export_file, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        console.print(f"[green]Results exported to {export_file}[/green]")

@cli.command()
@click.option('--target-frequency', '-tf', default='1min', help='Target frequency for alignment')
@click.option('--method', '-m', default='hierarchical', help='Alignment method')
@click.option('--export', '-e', is_flag=True, help='Export aligned data')
@click.pass_context
def align_data(ctx, target_frequency, method, export):
    """Align mixed-frequency data streams"""
    data_dir = ctx.obj['data_dir']
    output_dir = ctx.obj['output_dir']
    
    console.print("[bold blue]Aligning mixed-frequency data...[/bold blue]")
    
    # Load data
    loader = ManufacturingDataLoader(str(data_dir))
    all_data = loader.load_all_manufacturing_data()
    
    if len(all_data) < 2:
        console.print("[red]Need at least 2 datasets for alignment[/red]")
        return
    
    # Initialize aligner
    aligner = MixedFrequencyAligner()
    
    # Register data streams
    frequency_mapping = {
        'thickness': DataFrequency.MEDIUM_FREQUENCY,
        'speed': DataFrequency.MEDIUM_FREQUENCY,
        'stop': DataFrequency.EVENT_BASED,
        'fm_stack': DataFrequency.LOW_FREQUENCY,
        'sm_stack': DataFrequency.LOW_FREQUENCY
    }
    
    with console.status("Registering data streams..."):
        for data_type, df in all_data.items():
            frequency = frequency_mapping.get(data_type, DataFrequency.MEDIUM_FREQUENCY)
            priority = 4 if data_type in ['thickness', 'speed'] else 2
            
            aligner.register_data_stream(
                data_type, df, frequency, alignment_priority=priority
            )
    
    # Perform alignment
    with console.status(f"Aligning data using {method} method..."):
        result = aligner.align_all_streams(
            target_frequency=target_frequency,
            alignment_method=method
        )
    
    # Display results
    console.print(f"[green]✓ Alignment complete![/green]")
    console.print(f"Aligned data shape: [yellow]{result.aligned_data.shape}[/yellow]")
    console.print(f"Method used: [cyan]{result.alignment_method}[/cyan]")
    console.print(f"Time span: [magenta]{result.quality_metrics['time_span_hours']:.1f} hours[/magenta]")
    
    # Coverage table
    table = Table(title="Data Coverage by Variable")
    table.add_column("Variable", style="cyan")
    table.add_column("Coverage", justify="right", style="green")
    table.add_column("Records", justify="right", style="yellow")
    
    for var, coverage in result.coverage_stats.items():
        records = int(coverage * len(result.aligned_data))
        table.add_row(var, f"{coverage:.1%}", str(records))
    
    console.print(table)
    
    # Export if requested
    if export:
        export_file = output_dir / f"aligned_data_{target_frequency}_{method}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        result.aligned_data.to_csv(export_file, index=False)
        console.print(f"[green]Aligned data exported to {export_file}[/green]")

@cli.command()
@click.option('--format', '-f', type=click.Choice(['json', 'excel', 'csv']), default='json', help='Output format')
@click.option('--include-plots', is_flag=True, help='Include visualization plots')
@click.pass_context
def export_report(ctx, format, include_plots):
    """Generate and export comprehensive analysis report"""
    data_dir = ctx.obj['data_dir']
    output_dir = ctx.obj['output_dir']
    config = ctx.obj['config']
    
    console.print("[bold blue]Generating comprehensive report...[/bold blue]")
    
    # Load and analyze data
    loader = ManufacturingDataLoader(str(data_dir))
    all_data = loader.load_all_manufacturing_data()
    
    if not all_data:
        console.print("[red]No data found for report generation[/red]")
        return
    
    analyzer = ManufacturingCorrelationAnalyzer()
    report_data = {}
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        console=console,
    ) as progress:
        
        task = progress.add_task("Generating report...", total=len(all_data))
        
        for data_type, df in all_data.items():
            # Data summary
            summary = {
                'shape': df.shape,
                'columns': list(df.columns),
                'missing_values': df.isnull().sum().to_dict(),
                'data_types': df.dtypes.astype(str).to_dict()
            }
            
            # Statistical summary for numeric columns
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                summary['statistics'] = df[numeric_cols].describe().to_dict()
            
            # Correlation analysis
            correlations = analyzer.find_significant_correlations(df)
            correlation_matrix = analyzer.calculate_correlation_matrix(df)
            
            report_data[data_type] = {
                'summary': summary,
                'correlations': {
                    'significant_count': len(correlations),
                    'correlations': [
                        {
                            'var1': c.variable_1,
                            'var2': c.variable_2,
                            'correlation': c.correlation_coefficient,
                            'p_value': c.p_value
                        }
                        for c in correlations[:10]  # Top 10
                    ],
                    'matrix': correlation_matrix.to_dict() if not correlation_matrix.empty else {}
                }
            }
            
            progress.advance(task)
    
    # Export based on format
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    if format == 'json':
        output_file = output_dir / f"manufacturing_report_{timestamp}.json"
        with open(output_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
    
    elif format == 'excel':
        output_file = output_dir / f"manufacturing_report_{timestamp}.xlsx"
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            for data_type, data in report_data.items():
                # Summary sheet
                summary_df = pd.DataFrame.from_dict(data['summary'], orient='index')
                summary_df.to_excel(writer, sheet_name=f'{data_type}_summary')
                
                # Correlations sheet
                if data['correlations']['correlations']:
                    corr_df = pd.DataFrame(data['correlations']['correlations'])
                    corr_df.to_excel(writer, sheet_name=f'{data_type}_correlations', index=False)
    
    elif format == 'csv':
        # Create separate CSV files for each dataset
        for data_type, data in report_data.items():
            if data['correlations']['correlations']:
                corr_df = pd.DataFrame(data['correlations']['correlations'])
                corr_file = output_dir / f"{data_type}_correlations_{timestamp}.csv"
                corr_df.to_csv(corr_file, index=False)
        
        output_file = output_dir / f"report_summary_{timestamp}.txt"
        with open(output_file, 'w') as f:
            f.write("Manufacturing Data Analysis Report\n")
            f.write("=" * 40 + "\n\n")
            
            for data_type, data in report_data.items():
                f.write(f"\n{data_type.upper()} Dataset:\n")
                f.write(f"  Shape: {data['summary']['shape']}\n")
                f.write(f"  Significant correlations: {data['correlations']['significant_count']}\n")
    
    console.print(f"[green]✓ Report generated: {output_file}[/green]")

def display_analysis_summary(results):
    """Display a summary of analysis results"""
    console.print("\n[bold]Analysis Summary[/bold]")
    
    # Data quality summary
    console.print("\n[bold cyan]Data Quality:[/bold cyan]")
    quality_table = Table()
    quality_table.add_column("Dataset", style="cyan")
    quality_table.add_column("Quality Score", justify="right", style="green")
    quality_table.add_column("Issues", justify="right", style="red")
    
    for dataset, quality in results['data_quality'].items():
        issues = len(quality['errors']) + len(quality['warnings'])
        quality_table.add_row(
            dataset,
            f"{quality['quality_score']:.2f}",
            str(issues)
        )
    
    console.print(quality_table)
    
    # Correlation summary
    console.print("\n[bold cyan]Correlations Found:[/bold cyan]")
    for dataset, corr_data in results['correlations'].items():
        count = len(corr_data['significant_correlations'])
        console.print(f"  {dataset}: {count} significant correlations")

if __name__ == '__main__':
    cli()