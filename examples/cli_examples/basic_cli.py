"""
Basic CLI Example for Manufacturing Data Analysis

Simple command-line interface demonstrating core functionality
for loading, analyzing, and visualizing manufacturing data.
"""

import click
import pandas as pd
import asyncio
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.progress import track
import sys
import os

# Add parent directories to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from data_processing.csv_loader import ManufacturingDataLoader
from data_processing.correlation_analyzer import ManufacturingCorrelationAnalyzer

console = Console()

@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--data-dir', '-d', default='test-data', help='Data directory path')
@click.pass_context
def cli(ctx, verbose, data_dir):
    """Manufacturing Data Analysis CLI Tool"""
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    ctx.obj['data_dir'] = data_dir
    
    if verbose:
        console.print(f"[dim]Using data directory: {data_dir}[/dim]")

@cli.command()
@click.pass_context
def load(ctx):
    """Load and validate all manufacturing data files"""
    data_dir = ctx.obj['data_dir']
    verbose = ctx.obj['verbose']
    
    console.print("[bold blue]Loading manufacturing data...[/bold blue]")
    
    try:
        # Initialize loader
        loader = ManufacturingDataLoader(data_dir)
        
        # Load all data
        all_data = loader.load_all_manufacturing_data()
        
        if not all_data:
            console.print("[red]No data files found![/red]")
            return
        
        # Create summary table
        table = Table(title="Loaded Data Summary")
        table.add_column("Dataset", style="cyan")
        table.add_column("Records", justify="right", style="green")
        table.add_column("Columns", justify="right", style="yellow")
        table.add_column("Time Range", style="magenta")
        
        for data_type, df in all_data.items():
            # Calculate time range if timestamp column exists
            time_range = "N/A"
            if 'timestamp' in df.columns:
                try:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    start_time = df['timestamp'].min().strftime('%Y-%m-%d %H:%M')
                    end_time = df['timestamp'].max().strftime('%Y-%m-%d %H:%M')
                    time_range = f"{start_time} to {end_time}"
                except:
                    time_range = "Invalid timestamps"
            
            table.add_row(
                data_type,
                str(len(df)),
                str(len(df.columns)),
                time_range
            )
        
        console.print(table)
        
        # Validation summary
        if verbose:
            console.print("\n[bold]Data Validation:[/bold]")
            for data_type, df in all_data.items():
                result = loader.validate_data_quality(df, data_type)
                
                status = "✓" if result.quality_score > 0.8 else "⚠" if result.quality_score > 0.6 else "✗"
                console.print(f"{status} {data_type}: Quality score {result.quality_score:.2f}")
                
                if result.errors:
                    for error in result.errors:
                        console.print(f"   [red]Error: {error}[/red]")
                
                if result.warnings and verbose:
                    for warning in result.warnings[:3]:  # Show first 3 warnings
                        console.print(f"   [yellow]Warning: {warning}[/yellow]")
        
    except Exception as e:
        console.print(f"[red]Error loading data: {e}[/red]")

@cli.command()
@click.option('--dataset', '-ds', help='Specific dataset to analyze')
@click.option('--min-correlation', '-mc', default=0.3, help='Minimum correlation threshold')
@click.pass_context
def correlate(ctx, dataset, min_correlation):
    """Analyze correlations in manufacturing data"""
    data_dir = ctx.obj['data_dir']
    
    console.print("[bold blue]Analyzing correlations...[/bold blue]")
    
    try:
        # Load data
        loader = ManufacturingDataLoader(data_dir)
        all_data = loader.load_all_manufacturing_data()
        
        if not all_data:
            console.print("[red]No data to analyze![/red]")
            return
        
        # Select dataset to analyze
        if dataset and dataset in all_data:
            datasets_to_analyze = {dataset: all_data[dataset]}
        else:
            datasets_to_analyze = all_data
        
        # Initialize analyzer
        analyzer = ManufacturingCorrelationAnalyzer()
        
        for data_type, df in datasets_to_analyze.items():
            console.print(f"\n[bold]Analyzing {data_type} data:[/bold]")
            
            # Find significant correlations
            correlations = analyzer.find_significant_correlations(
                df, min_correlation=min_correlation
            )
            
            if correlations:
                # Create correlation table
                table = Table(title=f"Significant Correlations in {data_type}")
                table.add_column("Variable 1", style="cyan")
                table.add_column("Variable 2", style="cyan")
                table.add_column("Correlation", justify="right", style="green")
                table.add_column("P-value", justify="right", style="yellow")
                table.add_column("Interpretation", style="magenta")
                
                for corr in correlations[:10]:  # Show top 10
                    table.add_row(
                        corr.variable_1,
                        corr.variable_2,
                        f"{corr.correlation_coefficient:.3f}",
                        f"{corr.p_value:.4f}",
                        corr.interpretation
                    )
                
                console.print(table)
            else:
                console.print(f"[yellow]No significant correlations found in {data_type}[/yellow]")
    
    except Exception as e:
        console.print(f"[red]Error analyzing correlations: {e}[/red]")

@cli.command()
@click.option('--variable1', '-v1', required=True, help='First variable for lag analysis')
@click.option('--variable2', '-v2', required=True, help='Second variable for lag analysis')
@click.option('--dataset', '-ds', help='Dataset containing the variables')
@click.option('--max-lag', '-ml', default=60, help='Maximum lag to test')
@click.pass_context
def lag_analysis(ctx, variable1, variable2, dataset, max_lag):
    """Perform lag correlation analysis between two variables"""
    data_dir = ctx.obj['data_dir']
    
    console.print(f"[bold blue]Analyzing lag correlation: {variable1} → {variable2}[/bold blue]")
    
    try:
        # Load data
        loader = ManufacturingDataLoader(data_dir)
        all_data = loader.load_all_manufacturing_data()
        
        # Find dataset containing both variables
        target_df = None
        target_dataset = None
        
        if dataset and dataset in all_data:
            if variable1 in all_data[dataset].columns and variable2 in all_data[dataset].columns:
                target_df = all_data[dataset]
                target_dataset = dataset
        else:
            # Search for dataset containing both variables
            for data_type, df in all_data.items():
                if variable1 in df.columns and variable2 in df.columns:
                    target_df = df
                    target_dataset = data_type
                    break
        
        if target_df is None:
            console.print(f"[red]Variables {variable1} and {variable2} not found in any dataset[/red]")
            return
        
        console.print(f"[dim]Using dataset: {target_dataset}[/dim]")
        
        # Perform lag analysis
        analyzer = ManufacturingCorrelationAnalyzer()
        
        with console.status("Calculating lag correlations..."):
            lag_result = analyzer.analyze_lag_correlations(
                target_df, variable1, variable2, max_lag=max_lag
            )
        
        # Display results
        console.print(f"\n[bold]Lag Analysis Results:[/bold]")
        console.print(f"Optimal lag: [green]{lag_result.optimal_lag}[/green] periods")
        console.print(f"Maximum correlation: [green]{lag_result.max_correlation:.3f}[/green]")
        console.print(f"P-value: [yellow]{lag_result.p_value:.4f}[/yellow]")
        console.print(f"Interpretation: {lag_result.interpretation}")
        
        # Show correlation by lag (top results)
        if lag_result.correlation_by_lag:
            console.print(f"\n[bold]Correlation by Lag (top 10):[/bold]")
            
            # Sort by absolute correlation
            sorted_lags = sorted(
                lag_result.correlation_by_lag.items(),
                key=lambda x: abs(x[1]),
                reverse=True
            )
            
            table = Table()
            table.add_column("Lag", justify="right", style="cyan")
            table.add_column("Correlation", justify="right", style="green")
            
            for lag, corr in sorted_lags[:10]:
                table.add_row(str(lag), f"{corr:.3f}")
            
            console.print(table)
    
    except Exception as e:
        console.print(f"[red]Error in lag analysis: {e}[/red]")

@cli.command()
@click.pass_context
def summary(ctx):
    """Show summary statistics for all datasets"""
    data_dir = ctx.obj['data_dir']
    
    console.print("[bold blue]Manufacturing Data Summary[/bold blue]")
    
    try:
        # Load data
        loader = ManufacturingDataLoader(data_dir)
        all_data = loader.load_all_manufacturing_data()
        
        if not all_data:
            console.print("[red]No data files found![/red]")
            return
        
        for data_type, df in all_data.items():
            console.print(f"\n[bold]{data_type.upper()} Dataset:[/bold]")
            
            # Basic info
            console.print(f"Shape: {df.shape}")
            console.print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
            
            # Numeric columns summary
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                console.print(f"\nNumeric columns: {len(numeric_cols)}")
                
                table = Table(title=f"Statistics for {data_type}")
                table.add_column("Variable", style="cyan")
                table.add_column("Count", justify="right", style="green")
                table.add_column("Mean", justify="right", style="yellow")
                table.add_column("Std", justify="right", style="yellow")
                table.add_column("Min", justify="right", style="magenta")
                table.add_column("Max", justify="right", style="magenta")
                
                for col in numeric_cols[:10]:  # Show first 10 numeric columns
                    stats = df[col].describe()
                    table.add_row(
                        col,
                        f"{stats['count']:.0f}",
                        f"{stats['mean']:.2f}",
                        f"{stats['std']:.2f}",
                        f"{stats['min']:.2f}",
                        f"{stats['max']:.2f}"
                    )
                
                console.print(table)
            
            # Missing data summary
            missing_data = df.isnull().sum()
            if missing_data.sum() > 0:
                console.print(f"\n[yellow]Missing data:[/yellow]")
                for col, missing_count in missing_data[missing_data > 0].items():
                    percentage = (missing_count / len(df)) * 100
                    console.print(f"  {col}: {missing_count} ({percentage:.1f}%)")
    
    except Exception as e:
        console.print(f"[red]Error generating summary: {e}[/red]")

@cli.command()
@click.option('--output', '-o', default='correlation_report.txt', help='Output file for report')
@click.pass_context
def report(ctx, output):
    """Generate comprehensive correlation analysis report"""
    data_dir = ctx.obj['data_dir']
    
    console.print(f"[bold blue]Generating correlation report...[/bold blue]")
    
    try:
        # Load data
        loader = ManufacturingDataLoader(data_dir)
        all_data = loader.load_all_manufacturing_data()
        
        if not all_data:
            console.print("[red]No data to analyze![/red]")
            return
        
        # Initialize analyzer
        analyzer = ManufacturingCorrelationAnalyzer()
        
        # Generate report content
        report_lines = []
        report_lines.append("MANUFACTURING DATA CORRELATION ANALYSIS REPORT")
        report_lines.append("=" * 50)
        report_lines.append(f"Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        for data_type, df in track(all_data.items(), description="Analyzing datasets"):
            report_lines.append(f"\n{data_type.upper()} DATASET ANALYSIS")
            report_lines.append("-" * 30)
            report_lines.append(f"Records: {len(df)}")
            report_lines.append(f"Variables: {len(df.columns)}")
            
            # Data quality
            validation = loader.validate_data_quality(df, data_type)
            report_lines.append(f"Quality Score: {validation.quality_score:.2f}")
            
            # Correlations
            correlations = analyzer.find_significant_correlations(df, min_correlation=0.3)
            report_lines.append(f"Significant correlations found: {len(correlations)}")
            
            if correlations:
                report_lines.append("\nTop Correlations:")
                for i, corr in enumerate(correlations[:5], 1):
                    report_lines.append(
                        f"{i}. {corr.variable_1} ↔ {corr.variable_2}: "
                        f"r={corr.correlation_coefficient:.3f}, p={corr.p_value:.4f}"
                    )
            
            # Correlation matrix
            corr_matrix = analyzer.calculate_correlation_matrix(df)
            if not corr_matrix.empty:
                report_lines.append(f"\nCorrelation Matrix ({corr_matrix.shape[0]}x{corr_matrix.shape[1]}):")
                report_lines.append(corr_matrix.round(3).to_string())
        
        # Write report
        output_path = Path(output)
        with open(output_path, 'w') as f:
            f.write('\n'.join(report_lines))
        
        console.print(f"[green]Report saved to: {output_path}[/green]")
        console.print(f"Report size: {output_path.stat().st_size / 1024:.1f} KB")
    
    except Exception as e:
        console.print(f"[red]Error generating report: {e}[/red]")

if __name__ == '__main__':
    cli()