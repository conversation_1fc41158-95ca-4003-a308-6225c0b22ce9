"""
Industrial Sensor Data CLI

Specialized command-line interface for industrial sensor data analysis
with real-time monitoring capabilities, alert systems, and process optimization.
"""

import click
import pandas as pd
import numpy as np
import asyncio
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import track
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from datetime import datetime, timedelta
import time
import sys
import os
import json

# Add parent directories to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from data_processing.csv_loader import ManufacturingDataLoader
from data_processing.time_series_processor import TimeSeriesProcessor
from data_processing.correlation_analyzer import ManufacturingCorrelationAnalyzer

console = Console()

class SensorMonitor:
    """Real-time sensor monitoring system"""
    
    def __init__(self, data_dir: str):
        self.data_dir = Path(data_dir)
        self.loader = ManufacturingDataLoader(str(data_dir))
        self.processor = TimeSeriesProcessor()
        self.analyzer = ManufacturingCorrelationAnalyzer()
        self.monitoring = False
        self.alert_thresholds = {
            'thickness': {'min': 10.0, 'max': 15.0},
            'speed': {'min': 100, 'max': 200},
            'temperature': {'min': 70, 'max': 90},
            'pressure': {'min': 40, 'max': 60}
        }
        self.alert_history = []
    
    def check_alerts(self, data: pd.DataFrame) -> list:
        """Check for alert conditions in current data"""
        alerts = []
        
        for variable, thresholds in self.alert_thresholds.items():
            if variable in data.columns:
                current_value = data[variable].iloc[-1] if len(data) > 0 else None
                
                if current_value is not None:
                    if current_value < thresholds['min']:
                        alerts.append({
                            'type': 'LOW_VALUE',
                            'variable': variable,
                            'value': current_value,
                            'threshold': thresholds['min'],
                            'severity': 'HIGH',
                            'timestamp': datetime.now()
                        })
                    elif current_value > thresholds['max']:
                        alerts.append({
                            'type': 'HIGH_VALUE',
                            'variable': variable,
                            'value': current_value,
                            'threshold': thresholds['max'],
                            'severity': 'HIGH',
                            'timestamp': datetime.now()
                        })
        
        return alerts
    
    def generate_dashboard_layout(self, data: dict) -> Layout:
        """Generate real-time dashboard layout"""
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=8)
        )
        
        layout["main"].split_row(
            Layout(name="sensors"),
            Layout(name="alerts")
        )
        
        # Header
        header_text = Text("🏭 Industrial Sensor Monitoring Dashboard", style="bold blue")
        header_text.append(f" | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="dim")
        layout["header"].update(Panel(header_text, title="Status: ACTIVE"))
        
        # Sensor data
        if data:
            sensor_table = Table(title="Current Sensor Readings")
            sensor_table.add_column("Sensor", style="cyan")
            sensor_table.add_column("Value", justify="right", style="green")
            sensor_table.add_column("Status", style="yellow")
            sensor_table.add_column("Trend", style="magenta")
            
            for sensor_name, df in data.items():
                if not df.empty and len(df) > 0:
                    latest_value = df.iloc[-1]
                    
                    for col in df.select_dtypes(include=[np.number]).columns:
                        if col != 'timestamp':
                            value = latest_value[col] if col in latest_value else 'N/A'
                            
                            # Determine status
                            status = "NORMAL"
                            if col in self.alert_thresholds:
                                thresholds = self.alert_thresholds[col]
                                if value < thresholds['min'] or value > thresholds['max']:
                                    status = "ALERT"
                            
                            # Calculate trend (simplified)
                            trend = "STABLE"
                            if len(df) > 5:
                                recent_values = df[col].tail(5)
                                if recent_values.iloc[-1] > recent_values.iloc[0]:
                                    trend = "RISING"
                                elif recent_values.iloc[-1] < recent_values.iloc[0]:
                                    trend = "FALLING"
                            
                            sensor_table.add_row(
                                f"{sensor_name}_{col}",
                                f"{value:.2f}" if isinstance(value, (int, float)) else str(value),
                                status,
                                trend
                            )
            
            layout["sensors"].update(sensor_table)
        else:
            layout["sensors"].update(Panel("No sensor data available", title="Sensors"))
        
        # Alerts
        if self.alert_history:
            alert_table = Table(title="Recent Alerts")
            alert_table.add_column("Time", style="cyan")
            alert_table.add_column("Type", style="red")
            alert_table.add_column("Variable", style="yellow")
            alert_table.add_column("Value", justify="right", style="green")
            
            for alert in self.alert_history[-10:]:  # Show last 10 alerts
                alert_table.add_row(
                    alert['timestamp'].strftime('%H:%M:%S'),
                    alert['type'],
                    alert['variable'],
                    f"{alert['value']:.2f}"
                )
            
            layout["alerts"].update(alert_table)
        else:
            layout["alerts"].update(Panel("No alerts", title="Alerts", border_style="green"))
        
        # Footer with statistics
        footer_text = f"Total Sensors: {len(data)} | "
        footer_text += f"Active Alerts: {len([a for a in self.alert_history if (datetime.now() - a['timestamp']).seconds < 300])} | "
        footer_text += f"Data Points: {sum(len(df) for df in data.values()) if data else 0}"
        
        layout["footer"].update(Panel(footer_text, title="Statistics"))
        
        return layout

@click.group()
@click.option('--data-dir', '-d', default='test-data', help='Sensor data directory')
@click.option('--output-dir', '-o', default='sensor_output', help='Output directory')
@click.pass_context
def cli(ctx, data_dir, output_dir):
    """Industrial Sensor Data Analysis CLI"""
    ctx.ensure_object(dict)
    ctx.obj['data_dir'] = Path(data_dir)
    ctx.obj['output_dir'] = Path(output_dir)
    ctx.obj['output_dir'].mkdir(exist_ok=True)

@cli.command()
@click.option('--interval', '-i', default=5, help='Monitoring interval in seconds')
@click.option('--duration', '-dur', default=60, help='Monitoring duration in seconds')
@click.pass_context
def monitor(ctx, interval, duration):
    """Start real-time sensor monitoring"""
    data_dir = ctx.obj['data_dir']
    
    console.print("[bold blue]Starting real-time sensor monitoring...[/bold blue]")
    console.print(f"Monitoring interval: {interval}s, Duration: {duration}s")
    console.print("Press Ctrl+C to stop monitoring")
    
    monitor = SensorMonitor(str(data_dir))
    start_time = time.time()
    
    try:
        with Live(console=console, refresh_per_second=1) as live:
            while time.time() - start_time < duration:
                # Load current data (simulating real-time updates)
                current_data = monitor.loader.load_all_manufacturing_data()
                
                # Check for alerts
                for data_type, df in current_data.items():
                    if not df.empty:
                        alerts = monitor.check_alerts(df)
                        monitor.alert_history.extend(alerts)
                
                # Update dashboard
                layout = monitor.generate_dashboard_layout(current_data)
                live.update(layout)
                
                time.sleep(interval)
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped by user[/yellow]")
    
    # Summary
    console.print(f"\n[bold]Monitoring Summary:[/bold]")
    console.print(f"Total alerts: {len(monitor.alert_history)}")
    console.print(f"Duration: {time.time() - start_time:.1f} seconds")

@cli.command()
@click.option('--sensor', '-s', help='Specific sensor to analyze')
@click.option('--threshold', '-t', default=3.0, help='Anomaly detection threshold')
@click.option('--window', '-w', default=60, help='Analysis window size')
@click.pass_context
def detect_anomalies(ctx, sensor, threshold, window):
    """Detect anomalies in sensor data"""
    data_dir = ctx.obj['data_dir']
    output_dir = ctx.obj['output_dir']
    
    console.print("[bold blue]Detecting sensor anomalies...[/bold blue]")
    
    # Load data
    loader = ManufacturingDataLoader(str(data_dir))
    all_data = loader.load_all_manufacturing_data()
    
    if not all_data:
        console.print("[red]No sensor data found[/red]")
        return
    
    processor = TimeSeriesProcessor()
    anomaly_results = {}
    
    for data_type, df in all_data.items():
        if sensor and sensor not in data_type:
            continue
        
        console.print(f"\nAnalyzing {data_type} data...")
        
        # Detect anomalies
        anomaly_df = processor.detect_anomalies(
            df, 
            method='zscore', 
            threshold=threshold
        )
        
        # Count anomalies by variable
        anomaly_counts = {}
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            anomaly_col = f'{col}_anomaly'
            if anomaly_col in anomaly_df.columns:
                count = anomaly_df[anomaly_col].sum()
                percentage = (count / len(anomaly_df)) * 100
                anomaly_counts[col] = {
                    'count': int(count),
                    'percentage': percentage
                }
        
        anomaly_results[data_type] = anomaly_counts
        
        # Display results
        if anomaly_counts:
            table = Table(title=f"Anomalies in {data_type}")
            table.add_column("Variable", style="cyan")
            table.add_column("Anomalies", justify="right", style="red")
            table.add_column("Percentage", justify="right", style="yellow")
            table.add_column("Severity", style="magenta")
            
            for var, stats in anomaly_counts.items():
                severity = "HIGH" if stats['percentage'] > 5 else "MEDIUM" if stats['percentage'] > 1 else "LOW"
                table.add_row(
                    var,
                    str(stats['count']),
                    f"{stats['percentage']:.1f}%",
                    severity
                )
            
            console.print(table)
    
    # Export results
    output_file = output_dir / f"anomaly_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(anomaly_results, f, indent=2)
    
    console.print(f"\n[green]Results saved to {output_file}[/green]")

@cli.command()
@click.option('--variable', '-v', required=True, help='Variable to analyze for events')
@click.option('--event-type', '-et', default='threshold', help='Event detection type')
@click.option('--threshold-value', '-tv', type=float, help='Threshold value for detection')
@click.pass_context
def detect_events(ctx, variable, event_type, threshold_value):
    """Detect process events in sensor data"""
    data_dir = ctx.obj['data_dir']
    output_dir = ctx.obj['output_dir']
    
    console.print(f"[bold blue]Detecting {event_type} events in {variable}...[/bold blue]")
    
    # Load data
    loader = ManufacturingDataLoader(str(data_dir))
    all_data = loader.load_all_manufacturing_data()
    
    # Find dataset containing the variable
    target_df = None
    target_dataset = None
    
    for data_type, df in all_data.items():
        if variable in df.columns:
            target_df = df
            target_dataset = data_type
            break
    
    if target_df is None:
        console.print(f"[red]Variable '{variable}' not found in any dataset[/red]")
        return
    
    processor = TimeSeriesProcessor()
    
    # Detect events
    events = processor.detect_events(
        target_df, 
        variable, 
        event_type=event_type,
        threshold=threshold_value
    )
    
    if events.empty:
        console.print(f"[yellow]No {event_type} events detected for {variable}[/yellow]")
        return
    
    # Display results
    console.print(f"\n[bold]Found {len(events)} events in {target_dataset} dataset:[/bold]")
    
    table = Table(title=f"{event_type.title()} Events for {variable}")
    table.add_column("Event #", justify="right", style="cyan")
    table.add_column("Start Index", justify="right", style="green")
    table.add_column("Duration", justify="right", style="yellow")
    
    if 'max_value' in events.columns:
        table.add_column("Max Value", justify="right", style="magenta")
    if 'start_time' in events.columns:
        table.add_column("Start Time", style="blue")
    
    for idx, event in events.iterrows():
        row_data = [str(idx + 1), str(event['start_index']), str(event['duration'])]
        
        if 'max_value' in events.columns:
            row_data.append(f"{event['max_value']:.2f}")
        if 'start_time' in events.columns:
            row_data.append(str(event['start_time']))
        
        table.add_row(*row_data)
    
    console.print(table)
    
    # Event statistics
    console.print(f"\n[bold]Event Statistics:[/bold]")
    console.print(f"Total events: {len(events)}")
    console.print(f"Average duration: {events['duration'].mean():.1f}")
    console.print(f"Max duration: {events['duration'].max()}")
    console.print(f"Min duration: {events['duration'].min()}")
    
    if 'max_value' in events.columns:
        console.print(f"Average max value: {events['max_value'].mean():.2f}")
    
    # Export results
    output_file = output_dir / f"events_{variable}_{event_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    events.to_csv(output_file, index=False)
    
    console.print(f"\n[green]Events exported to {output_file}[/green]")

@cli.command()
@click.option('--window-size', '-ws', default=60, help='Rolling statistics window size')
@click.option('--export', '-e', is_flag=True, help='Export processed data')
@click.pass_context
def process_sensors(ctx, window_size, export):
    """Process sensor data with rolling statistics and smoothing"""
    data_dir = ctx.obj['data_dir']
    output_dir = ctx.obj['output_dir']
    
    console.print("[bold blue]Processing sensor data...[/bold blue]")
    
    # Load data
    loader = ManufacturingDataLoader(str(data_dir))
    all_data = loader.load_all_manufacturing_data()
    
    if not all_data:
        console.print("[red]No sensor data found[/red]")
        return
    
    processor = TimeSeriesProcessor()
    processed_data = {}
    
    for data_type, df in track(all_data.items(), description="Processing datasets"):
        console.print(f"\nProcessing {data_type}...")
        
        # Calculate rolling statistics
        rolling_df = processor.calculate_rolling_statistics(
            df, window_sizes=[window_size // 4, window_size // 2, window_size]
        )
        
        # Apply smoothing
        smoothed_df = processor.smooth_time_series(
            rolling_df, method='moving_average', window_size=window_size // 4
        )
        
        # Create time features if timestamp exists
        if 'timestamp' in smoothed_df.columns:
            final_df = processor.create_time_features(smoothed_df)
        else:
            final_df = smoothed_df
        
        processed_data[data_type] = final_df
        
        # Display processing summary
        original_cols = len(df.columns)
        processed_cols = len(final_df.columns)
        
        console.print(f"  Original columns: {original_cols}")
        console.print(f"  Processed columns: {processed_cols}")
        console.print(f"  Added features: {processed_cols - original_cols}")
    
    # Export if requested
    if export:
        for data_type, df in processed_data.items():
            output_file = output_dir / f"processed_{data_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(output_file, index=False)
            console.print(f"Exported {data_type} to {output_file}")
    
    # Summary statistics
    console.print(f"\n[bold]Processing Summary:[/bold]")
    console.print(f"Datasets processed: {len(processed_data)}")
    console.print(f"Window size used: {window_size}")
    
    total_features = sum(len(df.columns) for df in processed_data.values())
    console.print(f"Total features generated: {total_features}")

@cli.command()
@click.option('--config-file', '-cf', help='Alert configuration file (JSON)')
@click.pass_context
def configure_alerts(ctx, config_file):
    """Configure alert thresholds for sensor monitoring"""
    output_dir = ctx.obj['output_dir']
    
    if config_file and Path(config_file).exists():
        # Load existing configuration
        with open(config_file, 'r') as f:
            config = json.load(f)
        console.print(f"[green]Loaded configuration from {config_file}[/green]")
    else:
        # Create default configuration
        config = {
            'alert_thresholds': {
                'thickness': {'min': 10.0, 'max': 15.0, 'units': 'mm'},
                'speed': {'min': 100, 'max': 200, 'units': 'm/min'},
                'temperature': {'min': 70, 'max': 90, 'units': '°C'},
                'pressure': {'min': 40, 'max': 60, 'units': 'bar'}
            },
            'alert_settings': {
                'check_interval': 5,
                'alert_cooldown': 60,
                'severity_levels': ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
            }
        }
    
    # Display current configuration
    console.print("[bold]Current Alert Configuration:[/bold]")
    
    table = Table(title="Alert Thresholds")
    table.add_column("Variable", style="cyan")
    table.add_column("Min Threshold", justify="right", style="green")
    table.add_column("Max Threshold", justify="right", style="red")
    table.add_column("Units", style="yellow")
    
    for var, settings in config['alert_thresholds'].items():
        table.add_row(
            var,
            str(settings['min']),
            str(settings['max']),
            settings.get('units', 'N/A')
        )
    
    console.print(table)
    
    # Save configuration
    config_output = output_dir / f"alert_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(config_output, 'w') as f:
        json.dump(config, f, indent=2)
    
    console.print(f"\n[green]Configuration saved to {config_output}[/green]")
    console.print("Use this file with the monitor command: --config-file")

if __name__ == '__main__':
    cli()