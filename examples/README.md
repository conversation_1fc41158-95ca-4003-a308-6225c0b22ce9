# Manufacturing Data Analysis Examples

This directory contains comprehensive code examples for building the Phase 1.5 Multi-Method Correlation Analysis Agent and related manufacturing data analysis tools. All examples are production-ready and follow the patterns specified in the project requirements.

## 📁 Folder Structure

```
examples/
├── agent_examples/                    # Pydantic AI agent implementations
├── data_processing/                   # Data loading and analysis tools
├── cli_examples/                     # Command-line interfaces
├── visualization/                    # Plotting and dashboard tools
├── multi_method_analysis_basic.py    # 🆕 Basic multi-method correlation example
├── multi_method_analysis_advanced.py # 🆕 Advanced multi-method analysis example
├── multi_method_agent_example.py     # 🆕 AI agent multi-method interaction example
├── multi_method_visualization_example.py # 🆕 Multi-method visualization example
└── README.md                         # This file
```

## 🤖 Agent Examples (`agent_examples/`)

Complete Pydantic AI agent implementations for manufacturing data analysis:

### `basic_pydantic_agent.py`
- **Purpose**: Demonstrates fundamental Pydantic AI agent structure
- **Features**: Basic agent setup, tool functions, structured outputs
- **Use Case**: Learning Pydantic AI basics, simple data analysis
- **Key Components**:
  - `AnalysisResult` model for structured responses
  - `calculate_basic_stats` tool function
  - Error handling and validation

### `multi_provider_agent.py`
- **Purpose**: Multi-LLM provider support with fallback mechanisms
- **Features**: Anthropic Claude + Google Vertex AI support, configuration management
- **Use Case**: Production deployments requiring provider redundancy
- **Key Components**:
  - `ProviderConfig` for multi-provider setup
  - Environment variable management
  - Fallback logic implementation

### `correlation_agent.py` ⭐
- **Purpose**: **Core correlation analysis agent for Phase 1**
- **Features**: Statistical correlation discovery, significance testing, lag analysis
- **Use Case**: Primary agent for manufacturing correlation analysis
- **Key Components**:
  - `CorrelationAnalysis` structured output model
  - `calculate_correlation_matrix` tool
  - `find_significant_correlations` tool
  - `analyze_time_lagged_correlations` tool
  - Manufacturing-specific correlation interpretation

### `industrial_data_agent.py`
- **Purpose**: Advanced industrial process analysis and optimization
- **Features**: Process stability analysis, event impact assessment, quality recommendations
- **Use Case**: Comprehensive manufacturing process optimization
- **Key Components**:
  - `IndustrialAnalysis` output model
  - `analyze_process_stability` tool
  - `analyze_event_impact` tool
  - `calculate_quality_correlations` tool

## 📊 Data Processing (`data_processing/`)

Robust data loading and processing utilities:

### `csv_loader.py`
- **Purpose**: Manufacturing-specific CSV data loading and validation
- **Features**: Data quality assessment, preprocessing, schema validation
- **Use Case**: Loading test-data CSV files with proper validation
- **Key Components**:
  - `ManufacturingDataLoader` class
  - `DataValidationResult` for quality metrics
  - Automatic data type inference
  - Missing data handling

### `time_series_processor.py`
- **Purpose**: Advanced time series processing for manufacturing data
- **Features**: Resampling, anomaly detection, smoothing, event detection
- **Use Case**: Preprocessing time series data before analysis
- **Key Components**:
  - `TimeSeriesProcessor` class
  - `detect_anomalies` with multiple methods
  - `smooth_time_series` with various algorithms
  - `create_time_features` for temporal analysis

### `correlation_analyzer.py` ⭐
- **Purpose**: **Core correlation analysis engine for Phase 1**
- **Features**: Comprehensive correlation analysis with statistical validation
- **Use Case**: Backend engine for correlation agent
- **Key Components**:
  - `ManufacturingCorrelationAnalyzer` class
  - `CorrelationResult` and `LagCorrelationResult` models
  - Statistical significance testing
  - Manufacturing-specific lag windows
  - Partial correlation analysis

### `mixed_frequency_aligner.py`
- **Purpose**: Align data streams with different sampling frequencies
- **Features**: Event-driven alignment, hierarchical frequency handling
- **Use Case**: Combining high-frequency sensor data with low-frequency lab results
- **Key Components**:
  - `MixedFrequencyAligner` class
  - `DataFrequency` enumeration
  - Multiple alignment strategies
  - Manufacturing shift alignment utilities

### `test_data_generator.py` ⭐
- **Purpose**: **Generate realistic test data for development and testing**
- **Features**: Creates CSV files matching INITIAL_1.md specifications with realistic correlations
- **Use Case**: Generate sample data when test-data/ folder is not available
- **Key Components**:
  - `TestDataGenerator` class
  - Correlated data generation (stoppages affect speed, speed affects thickness, etc.)
  - Realistic manufacturing patterns (shift effects, weekly cycles)
  - Summary report generation

## 💻 CLI Examples (`cli_examples/`)

Complete command-line interfaces for different use cases:

### `basic_cli.py`
- **Purpose**: Simple CLI for basic data analysis operations
- **Features**: Data loading, correlation analysis, summary statistics
- **Use Case**: Quick data exploration and validation
- **Commands**:
  - `load` - Load and validate data files
  - `correlate` - Find significant correlations
  - `lag-analysis` - Analyze time-lagged relationships
  - `summary` - Generate data summaries

### `interactive_chat.py` ⭐
- **Purpose**: **Interactive chat interface with Pydantic AI agents**
- **Features**: Real-time agent interaction, conversation history, agent switching
- **Use Case**: Primary interface for Phase 1 correlation analysis
- **Commands**:
  - `/load` - Load manufacturing data
  - `/agent <name>` - Switch between agents
  - `/data` - Show data summary
  - Natural language queries for correlation analysis

### `data_analysis_cli.py`
- **Purpose**: Advanced CLI with comprehensive analysis capabilities
- **Features**: Multi-format export, configuration files, batch processing
- **Use Case**: Production data analysis workflows
- **Commands**:
  - `analyze` - Comprehensive analysis with date filtering
  - `lag-correlation` - Advanced lag analysis with export
  - `align-data` - Mixed-frequency data alignment
  - `export-report` - Generate analysis reports

### `industrial_sensor_cli.py`
- **Purpose**: Real-time sensor monitoring and analysis
- **Features**: Live monitoring, anomaly detection, alert systems
- **Use Case**: Production monitoring and quality control
- **Commands**:
  - `monitor` - Real-time sensor monitoring
  - `detect-anomalies` - Anomaly detection analysis
  - `detect-events` - Process event identification
  - `configure-alerts` - Set up alert thresholds

## 📈 Visualization (`visualization/`)

Comprehensive plotting and dashboard tools:

### `correlation_plots.py` ⭐
- **Purpose**: **Correlation visualization for Phase 1 agent**
- **Features**: Correlation matrices, scatter plots, lag analysis plots, network graphs
- **Use Case**: Visual output for correlation analysis agent
- **Key Functions**:
  - `plot_correlation_matrix` - Interactive/static heatmaps
  - `plot_scatter_with_correlation` - Correlation scatter plots
  - `plot_lag_correlation_analysis` - Time-lag visualizations
  - `plot_correlation_network` - Network view of relationships

### `time_series_plots.py`
- **Purpose**: Time series visualization and anomaly highlighting
- **Features**: Multi-variable plots, anomaly overlays, rolling statistics
- **Use Case**: Time-based analysis and process monitoring
- **Key Functions**:
  - `plot_time_series` - Basic time series plots
  - `plot_anomaly_detection` - Anomaly highlighting
  - `plot_event_analysis` - Event overlay on time series
  - `plot_rolling_statistics` - Rolling window analysis

### `industrial_dashboard.py`
- **Purpose**: Comprehensive manufacturing dashboard with KPIs
- **Features**: Real-time monitoring, alerts, process metrics
- **Use Case**: Production monitoring and management dashboards
- **Key Components**:
  - `IndustrialDashboard` class with Dash app
  - Real-time KPI monitoring
  - Alert system integration
  - Static dashboard generation

## 🆕 Multi-Method Analysis Examples (Phase 1.5)

New comprehensive examples demonstrating the multi-method correlation analysis system with Pearson, Spearman, and Kendall methods:

### `multi_method_analysis_basic.py` ⭐
- **Purpose**: **Comprehensive basic multi-method correlation analysis**
- **Features**: Compare all three correlation methods, convergence analysis, method recommendations
- **Use Case**: Introduction to multi-method analysis for manufacturing data
- **Key Components**:
  - `MultiMethodCorrelationAnalyzer` usage
  - Method convergence analysis
  - Bootstrap robustness testing
  - Manufacturing-specific insights

### `multi_method_analysis_advanced.py` ⭐
- **Purpose**: **Advanced multi-method analysis with domain-specific insights**
- **Features**: Manufacturing process analysis, method selection patterns, data quality assessment
- **Use Case**: Production-ready multi-method analysis for process optimization
- **Key Components**:
  - Domain-specific variable grouping (Production, Quality, Operations)
  - Method recommendation analysis
  - Data quality assessment impact
  - Manufacturing insights generation

### `multi_method_agent_example.py` ⭐
- **Purpose**: **AI agent interaction with multi-method capabilities**
- **Features**: Natural language queries, direct tool usage, agent-based multi-method analysis
- **Use Case**: Interactive multi-method correlation analysis through AI agent
- **Key Components**:
  - Natural language multi-method queries
  - `calculate_multi_method_correlations_tool` usage
  - `analyze_method_convergence_tool` usage
  - `recommend_correlation_method_tool` usage
  - `calculate_robustness_metrics_tool` usage

### `multi_method_visualization_example.py` ⭐
- **Purpose**: **Comprehensive multi-method visualization suite**
- **Features**: Side-by-side heatmaps, convergence plots, comparison matrices, interactive dashboards
- **Use Case**: Visual validation and communication of multi-method analysis results
- **Key Components**:
  - `MultiMethodCorrelationPlotter` usage
  - Side-by-side method comparison heatmaps
  - Interactive convergence analysis plots
  - Method comparison matrices
  - Comprehensive multi-method dashboards
  - Batch visualization generation

## 🏭 Test Data Generation

Before running the examples, you'll need sample data. Use the test data generator to create realistic manufacturing datasets:

### Generate Test Data
```python
# Generate all test data files
from data_processing.test_data_generator import TestDataGenerator

generator = TestDataGenerator("test-data")
datasets = generator.generate_all_test_data()

# This creates:
# - stop.csv - Machine stoppage events with realistic frequencies
# - speed.csv - Continuous speed measurements (every 30 seconds)
# - thickness.csv - Quality measurements (every 2 minutes) 
# - fm_stack.csv - Forming machine scrap data
# - sm_stack.csv - Sheet machine scrap data
```

### Command Line Generation
```bash
# Generate test data from command line
python examples/data_processing/test_data_generator.py

# Output:
# test-data/stop.csv
# test-data/speed.csv  
# test-data/thickness.csv
# test-data/fm_stack.csv
# test-data/sm_stack.csv
# test-data/test_data_summary.txt
```

### Data Relationships Built-In
The generated data includes realistic manufacturing correlations:

- **Stoppages → Speed**: Speed drops to zero during stoppages, gradual ramp-up after restart
- **Speed → Thickness**: Higher speeds cause slight thickness variations
- **Process Instability → Scrap**: More scrap events during periods of high variation
- **Shift Patterns**: Night shift runs slightly slower, Monday morning startup effects
- **Weekly Cycles**: Realistic production patterns over a full week

## 🚀 Quick Start Guide

### 1. Basic Correlation Analysis
```python
# Load the correlation agent
from agent_examples.correlation_agent import correlation_agent, CorrelationDependencies
import pandas as pd

# Generate or load your data
from data_processing.test_data_generator import TestDataGenerator

# Option 1: Generate test data
generator = TestDataGenerator("test-data")
datasets = generator.generate_all_test_data()
data = datasets['thickness']

# Option 2: Load existing data
# data = pd.read_csv('test-data/thickness.csv')

# Create dependencies
deps = CorrelationDependencies(
    data=data,
    time_column='timestamp',
    significance_threshold=0.05
)

# Run analysis
result = await correlation_agent.run(
    "What correlations exist between process variables?",
    deps=deps
)

print(result.data.insights)
```

### 2. Interactive Chat Session
```bash
# First generate test data
python examples/data_processing/test_data_generator.py

# Start interactive chat
python examples/cli_examples/interactive_chat.py --data-dir test-data

# In chat:
# > What is the correlation between speed and thickness?
# > Show me lag correlations for temperature and quality
# > /agent industrial
# > Analyze the impact of stoppages on process variables
```

### 3. Data Loading and Validation
```python
# Load and validate manufacturing data
from data_processing.csv_loader import ManufacturingDataLoader

loader = ManufacturingDataLoader('test-data')
all_data = loader.load_all_manufacturing_data()

# Validate data quality
for data_type, df in all_data.items():
    result = loader.validate_data_quality(df, data_type)
    print(f"{data_type}: Quality score {result.quality_score:.2f}")
```

### 4. Correlation Analysis
```python
# Comprehensive correlation analysis
from data_processing.correlation_analyzer import ManufacturingCorrelationAnalyzer

analyzer = ManufacturingCorrelationAnalyzer()

# Find significant correlations
correlations = analyzer.find_significant_correlations(
    data, min_correlation=0.3
)

# Analyze lag correlations
lag_result = analyzer.analyze_lag_correlations(
    data, 'speed', 'thickness', max_lag=60
)

print(f"Optimal lag: {lag_result.optimal_lag}")
print(f"Max correlation: {lag_result.max_correlation:.3f}")
```

### 6. Multi-Method Analysis (Phase 1.5) 🆕
```python
# Basic multi-method correlation analysis
from src.data.multi_correlations import MultiMethodCorrelationAnalyzer

analyzer = MultiMethodCorrelationAnalyzer()

# Calculate correlations using all three methods
results = analyzer.calculate_multi_method_correlations(
    data, 
    variables=['speed', 'thickness', 'quality_score'],
    min_periods=50
)

# Display results for each method
for pair_key, result in results.items():
    print(f"{result.variable_1} ↔ {result.variable_2}:")
    print(f"  Pearson:  {result.pearson_correlation:.6f} (p={result.pearson_p_value:.6f})")
    print(f"  Spearman: {result.spearman_correlation:.6f} (p={result.spearman_p_value:.6f})")
    print(f"  Kendall:  {result.kendall_correlation:.6f} (p={result.kendall_p_value:.6f})")
    print(f"  Convergence: {result.method_convergence_score:.6f}")
    print(f"  Recommended: {result.recommended_method}")

# Method convergence analysis
convergence_analysis = analyzer.analyze_method_convergence(results)
print(f"Overall convergence: {convergence_analysis['overall_convergence_score']:.6f}")
```

### 7. Multi-Method Visualization 🆕
```python
# Multi-method visualization
from src.visualization.multi_plots import MultiMethodCorrelationPlotter

plotter = MultiMethodCorrelationPlotter()

# Prepare visualization data
viz_results = {
    'multi_method_results': {
        pair_key: {
            'variable_1': result.variable_1,
            'variable_2': result.variable_2,
            'pearson_correlation': result.pearson_correlation,
            'spearman_correlation': result.spearman_correlation,
            'kendall_correlation': result.kendall_correlation,
            'method_convergence_score': result.method_convergence_score
        } for pair_key, result in results.items()
    }
}

# Create side-by-side heatmaps
fig = plotter.create_multi_method_heatmaps(
    viz_results,
    title="Multi-Method Correlation Analysis"
)

# Create convergence analysis plot
convergence_fig = plotter.plot_method_convergence(viz_results)

# Create comprehensive dashboard
dashboard = plotter.create_multi_method_dashboard(viz_results)
```

### 8. Traditional Visualization
```python
# Create correlation visualizations
from visualization.correlation_plots import CorrelationVisualizer

visualizer = CorrelationVisualizer()

# Correlation matrix
correlation_matrix = data.corr()
visualizer.plot_correlation_matrix(correlation_matrix, interactive=True)

# Scatter plot with correlation
visualizer.plot_scatter_with_correlation(
    data, 'speed', 'thickness', interactive=True
)
```

## 🔧 Configuration Examples

### Environment Variables (.env)
```bash
# LLM Provider Configuration
LLM_PROVIDER=ANTHROPIC
ANTHROPIC_API_KEY=your_claude_api_key_here

# Alternative: Google Vertex AI
# LLM_PROVIDER=VERTEX_AI
# GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json
# VERTEX_AI_PROJECT=your-project-id
# VERTEX_AI_LOCATION=us-central1

# Data Configuration
DATA_DIRECTORY=test-data
TIME_COLUMN=timestamp
CORRELATION_THRESHOLD=0.3
SIGNIFICANCE_LEVEL=0.05
```

### Analysis Configuration (JSON)
```json
{
  "correlation_threshold": 0.3,
  "significance_level": 0.05,
  "max_lag": 60,
  "output_format": "json",
  "include_plots": true,
  "time_column": "timestamp",
  "manufacturing_variables": {
    "thickness": {
      "unit": "mm",
      "valid_range": [10, 15],
      "typical_lags": [5, 15, 30]
    },
    "speed": {
      "unit": "m/min",
      "valid_range": [100, 200],
      "typical_lags": [0, 10, 20]
    }
  }
}
```

## 📋 Requirements

All examples require the following Python packages:

### Core Dependencies
```
pandas>=1.5.0
numpy>=1.24.0
pydantic>=2.0.0
pydantic-ai>=0.0.12
```

### Data Analysis
```
scipy>=1.10.0
scikit-learn>=1.3.0
python-dotenv>=1.0.0
```

### Visualization
```
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.15.0
```

### CLI Tools
```
click>=8.1.0
rich>=13.0.0
```

### Dashboard (Optional)
```
dash>=2.14.0
dash-bootstrap-components>=1.5.0
```

### Install All Dependencies
```bash
pip install pandas numpy pydantic pydantic-ai scipy scikit-learn python-dotenv matplotlib seaborn plotly click rich dash dash-bootstrap-components
```

## 🎯 Phase 1.5 Implementation Priority

For Phase 1.5 Multi-Method Correlation Analysis Agent, focus on these examples in order:

### Phase 1.5 Multi-Method Examples (NEW)
1. **`multi_method_analysis_basic.py`** - Basic multi-method correlation analysis
2. **`multi_method_analysis_advanced.py`** - Advanced multi-method with manufacturing insights
3. **`multi_method_agent_example.py`** - AI agent multi-method interaction
4. **`multi_method_visualization_example.py`** - Comprehensive multi-method visualization

### Core Phase 1 Foundation
5. **`correlation_agent.py`** - Core agent implementation (enhanced with multi-method tools)
6. **`correlation_analyzer.py`** - Backend analysis engine 
7. **`csv_loader.py`** - Data loading utilities
8. **`interactive_chat.py`** - User interface
9. **`correlation_plots.py`** - Traditional visualization output
10. **`basic_cli.py`** - Alternative interface

These examples provide everything needed to implement the Phase 1.5 multi-method correlation analysis system with comprehensive method comparison, convergence analysis, and intelligent method selection.

## 🛠️ Integration with Project Structure

These examples integrate with the main project structure:

```
test_1/
├── src/
│   ├── agents/           # Use agent_examples/ as reference
│   ├── data/            # Use data_processing/ as reference
│   └── cli/             # Use cli_examples/ as reference
├── examples/            # This directory
├── test-data/          # CSV files for testing
└── tests/              # Unit tests (mirror examples structure)
```

## 🧪 Testing

Each example includes test functions and can be run independently:

```bash
# First generate test data
python examples/data_processing/test_data_generator.py

# Test individual modules
python examples/agent_examples/correlation_agent.py
python examples/data_processing/correlation_analyzer.py
python examples/visualization/correlation_plots.py

# Run CLI examples
python examples/cli_examples/basic_cli.py --help
python examples/cli_examples/interactive_chat.py --data-dir test-data

# Run multi-method examples (NEW)
python examples/multi_method_analysis_basic.py
python examples/multi_method_analysis_advanced.py
python examples/multi_method_agent_example.py
python examples/multi_method_visualization_example.py
```

## 📖 Documentation References

- **Pydantic AI**: https://ai.pydantic.dev/
- **Anthropic Claude API**: https://docs.anthropic.com/claude/reference/getting-started-with-the-api
- **Pandas Correlation**: https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.corr.html
- **Manufacturing Data Analysis**: See `docs/` folder for domain-specific guidance

## 🤝 Contributing

When extending these examples:

1. Follow the established patterns and naming conventions
2. Include comprehensive docstrings using Google style
3. Add error handling and validation
4. Create corresponding test functions
5. Update this README with new examples

## 🔍 Next Steps

After reviewing these examples:

### Phase 1.5 Multi-Method Implementation
1. **Start with Multi-Method Basics**: Run `multi_method_analysis_basic.py` to understand core concepts
2. **Advanced Analysis**: Use `multi_method_analysis_advanced.py` for production-ready patterns
3. **AI Agent Integration**: Implement `multi_method_agent_example.py` for natural language interface
4. **Visualization Suite**: Deploy `multi_method_visualization_example.py` for stakeholder communication

### Foundation Implementation 
5. **Enhance Existing Agent**: Integrate multi-method tools into `correlation_agent.py`
6. **Update CLI**: Extend `interactive_chat.py` with multi-method commands
7. **Comprehensive Visualization**: Combine traditional and multi-method visualization capabilities
8. **Test with Real Data**: Validate multi-method analysis with actual manufacturing datasets

### Production Deployment
9. **Method Selection Automation**: Implement intelligent method selection for different data types
10. **Convergence Monitoring**: Set up automated convergence analysis for process control
11. **Bootstrap Validation**: Deploy robustness testing for critical manufacturing decisions
12. **Plan Phase 2**: Review advanced causal inference and multi-agent capabilities

These examples provide a complete foundation for building a production-ready Phase 1.5 Multi-Method Correlation Analysis Agent with robust statistical validation, intelligent method selection, and comprehensive visualization capabilities.