#!/usr/bin/env python3
"""
Multi-Method AI Agent Example

This script demonstrates how to use the multi-method correlation analysis
capabilities through the AI agent interface for manufacturing data analysis.

Usage:
    python examples/multi_method_agent_example.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.correlation_agent import create_correlation_agent
from src.data.loader import ManufacturingDataLoader
from src.agents.multi_tools import (
    calculate_multi_method_correlations_tool,
    analyze_method_convergence_tool,
    recommend_correlation_method_tool,
    calculate_robustness_metrics_tool
)
import asyncio
import json
from datetime import datetime


async def demonstrate_multi_method_queries():
    """Demonstrate various multi-method correlation queries through the AI agent"""
    
    print("🤖 Multi-Method AI Agent Demonstration")
    print("=" * 50)
    print("This example shows how to interact with the AI agent for multi-method correlation analysis.")
    
    # Step 1: Load data and create agent
    print("\n📁 Step 1: Setting up AI Agent and Data")
    print("-" * 45)
    
    try:
        # Load manufacturing data
        loader = ManufacturingDataLoader('test-data')
        datasets = loader.load_all_manufacturing_data()
        unified_data = loader.create_unified_dataset()
        
        print(f"✅ Loaded manufacturing data: {unified_data.shape[0]:,} rows")
        
        # Create correlation agent
        agent = create_correlation_agent()
        print("✅ AI correlation agent initialized")
        
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return
    
    # Step 2: Basic multi-method analysis query
    print("\n🔍 Step 2: Basic Multi-Method Analysis Query")
    print("-" * 50)
    
    query1 = """
    Compare Pearson, Spearman, and Kendall correlations for all manufacturing variables. 
    Show me the correlation strengths, p-values, and convergence scores for each method.
    Focus on relationships involving speed, thickness, and quality metrics.
    """
    
    try:
        print(f"🗣️  Query: {query1.strip()}")
        print("\n🤖 Agent Response:")
        
        # Create proper dependencies object
        from src.agents.correlation_agent import ManufacturingDataDependencies
        deps = ManufacturingDataDependencies(
            data=unified_data,
            time_column='timestamp',
            significance_threshold=0.05,
            min_correlation=0.01,
            analysis_type='general'
        )
        
        response1 = await agent.run(query1, deps=deps)
        
        print(f"📄 {response1.output}")
        
    except Exception as e:
        print(f"❌ Query error: {e}")
    
    # Step 3: Method selection query
    print("\n🎯 Step 3: Method Selection Guidance Query")
    print("-" * 50)
    
    query2 = """
    Which correlation method should I use for analyzing the relationship between 
    production speed and thickness measurements? Consider the data distribution 
    characteristics and provide specific recommendations.
    """
    
    try:
        print(f"🗣️  Query: {query2.strip()}")
        print("\n🤖 Agent Response:")
        
        response2 = await agent.run(query2, deps=deps)
        
        print(f"📄 {response2.output}")
        
    except Exception as e:
        print(f"❌ Query error: {e}")
    
    # Step 4: Convergence analysis query
    print("\n🔄 Step 4: Method Convergence Analysis Query")
    print("-" * 50)
    
    query3 = """
    Analyze how well the three correlation methods (Pearson, Spearman, Kendall) agree 
    with each other for manufacturing variables. Show me convergence scores and identify 
    which relationships are robust across all methods.
    """
    
    try:
        print(f"🗣️  Query: {query3.strip()}")
        print("\n🤖 Agent Response:")
        
        response3 = await agent.run(query3, deps=deps)
        
        print(f"📄 {response3.output}")
        
    except Exception as e:
        print(f"❌ Query error: {e}")
    
    # Step 5: Manufacturing-specific multi-method query
    print("\n🏭 Step 5: Manufacturing-Specific Multi-Method Query")
    print("-" * 55)
    
    query4 = """
    For fiber cement manufacturing optimization, analyze correlations between production 
    variables using all three correlation methods. Focus on:
    1. Speed vs quality relationships 
    2. Thickness uniformity patterns
    3. Stoppage impact on subsequent production
    
    Recommend the optimal correlation method for each relationship type and explain why.
    """
    
    try:
        print(f"🗣️  Query: {query4.strip()}")
        print("\n🤖 Agent Response:")
        
        response4 = await agent.run(query4, deps=deps)
        
        print(f"📄 {response4.output}")
        
    except Exception as e:
        print(f"❌ Query error: {e}")
    
    # Step 6: Robustness analysis query
    print("\n🛡️  Step 6: Robustness Analysis Query")
    print("-" * 40)
    
    query5 = """
    Calculate bootstrap robustness metrics for the most important correlations 
    in the manufacturing data. Show me which correlation methods provide the 
    most stable results and identify any correlations that may be unreliable.
    """
    
    try:
        print(f"🗣️  Query: {query5.strip()}")
        print("\n🤖 Agent Response:")
        
        response5 = await agent.run(query5, deps=deps)
        
        print(f"📄 {response5.output}")
        
    except Exception as e:
        print(f"❌ Query error: {e}")


async def demonstrate_direct_tool_usage():
    """Demonstrate direct usage of multi-method correlation tools"""
    
    print("\n\n🔧 Direct Tool Usage Demonstration")
    print("=" * 45)
    print("This section shows how to use multi-method tools directly.")
    
    # Load data
    try:
        loader = ManufacturingDataLoader('test-data')
        datasets = loader.load_all_manufacturing_data()
        unified_data = loader.create_unified_dataset()
        
        # Get numeric variables for analysis
        numeric_vars = unified_data.select_dtypes(include=['float64', 'int64']).columns.tolist()
        analysis_vars = numeric_vars[:4]  # Use first 4 for demo
        
        print(f"📊 Analyzing variables: {analysis_vars}")
        
        # Create context for tools
        from src.agents.correlation_agent import ManufacturingDataDependencies
        deps = ManufacturingDataDependencies(
            data=unified_data,
            time_column='timestamp',
            significance_threshold=0.05,
            min_correlation=0.01,
            analysis_type='general'
        )
        
        # Create a mock context for direct tool usage
        class MockContext:
            def __init__(self, deps):
                self.deps = deps
        
        ctx = MockContext(deps)
        
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        return
    
    # Tool 1: Multi-method correlations
    print("\n🔍 Tool 1: Multi-Method Correlations")
    print("-" * 40)
    
    try:
        result1 = calculate_multi_method_correlations_tool(
            ctx, 
            variables=analysis_vars,
            min_periods=30
        )
        
        print(f"✅ Multi-method analysis result type: {type(result1)}")
        if 'multi_method_results' in result1 and result1['multi_method_results']:
            print(f"📊 Found {len(result1['multi_method_results'])} correlation pairs")
            
            # Display first result as example
            if result1['multi_method_results']:
                first_pair = next(iter(result1['multi_method_results'].values()))
                print(f"📈 Example: {first_pair['variable_1']} ↔ {first_pair['variable_2']}")
                print(f"   Pearson: {first_pair['pearson_correlation']:.6f}")
                print(f"   Spearman: {first_pair['spearman_correlation']:.6f}")
                print(f"   Kendall: {first_pair['kendall_correlation']:.6f}")
                print(f"   Convergence: {first_pair['method_convergence_score']:.6f}")
                print(f"   Recommended: {first_pair['recommended_method']}")
        
    except Exception as e:
        print(f"❌ Multi-method correlation error: {e}")
    
    # Tool 2: Method convergence analysis
    print("\n🔄 Tool 2: Method Convergence Analysis")
    print("-" * 45)
    
    try:
        # First get correlations, then analyze convergence
        correlations = calculate_multi_method_correlations_tool(
            ctx, 
            variables=analysis_vars,
            min_periods=30
        )
        
        if 'multi_method_results' in correlations and correlations['multi_method_results']:
            convergence_result = analyze_method_convergence_tool(ctx, correlations)
            
            print(f"✅ Convergence analysis completed")
            print(f"📊 Overall convergence score: {convergence_result['overall_convergence_score']:.6f}")
            
            if 'method_stability' in convergence_result:
                print("🏗️  Method stability scores:")
                for method, stability in convergence_result['method_stability'].items():
                    print(f"   • {method}: {stability:.6f}")
            
            if 'cross_method_correlations' in convergence_result:
                print("🔗 Cross-method agreements:")
                for pair, agreement in convergence_result['cross_method_correlations'].items():
                    print(f"   • {pair}: {agreement:.6f}")
        
    except Exception as e:
        print(f"❌ Convergence analysis error: {e}")
    
    # Tool 3: Method recommendation
    print("\n🎯 Tool 3: Method Recommendation")
    print("-" * 35)
    
    try:
        # Select two variables for method recommendation
        if len(analysis_vars) >= 2:
            var1, var2 = analysis_vars[0], analysis_vars[1]
            
            recommendation = recommend_correlation_method_tool(
                ctx, 
                variable_1=var1,
                variable_2=var2
            )
            
            print(f"✅ Method recommendation completed")
            print(f"📊 Variables: {var1} ↔ {var2}")
            print(f"🎯 Recommended method: {recommendation['recommended_method']}")
            print(f"📈 Confidence level: {recommendation['recommendation_confidence']}")
            
            if 'reasoning' in recommendation:
                print(f"💭 Reasoning: {recommendation['reasoning']}")
        
    except Exception as e:
        print(f"❌ Method recommendation error: {e}")
    
    # Tool 4: Robustness metrics (optional - may be slow)
    print("\n🛡️  Tool 4: Robustness Metrics (Quick Demo)")
    print("-" * 50)
    
    try:
        # Get correlations first
        correlations = calculate_multi_method_correlations_tool(
            ctx, 
            variables=analysis_vars[:2],  # Use only 2 variables for speed
            min_periods=30
        )
        
        if 'multi_method_results' in correlations and correlations['multi_method_results']:
            print("🔄 Running bootstrap robustness analysis (reduced samples for demo)...")
            
            robustness_result = calculate_robustness_metrics_tool(
                ctx,
                correlation_results=correlations,
                bootstrap_samples=25  # Reduced for demo speed
            )
            
            print(f"✅ Robustness analysis completed")
            
            if 'robustness_metrics' in robustness_result:
                print("🛡️  Robustness summary:")
                for pair_key, metrics in robustness_result['robustness_metrics'].items():
                    print(f"   📊 {pair_key}:")
                    for method, method_metrics in metrics.items():
                        if isinstance(method_metrics, dict) and 'stability_score' in method_metrics:
                            stability = method_metrics['stability_score']
                            print(f"      • {method}: {stability:.6f} stability")
        
    except Exception as e:
        print(f"⚠️  Robustness analysis skipped: {e}")


def save_example_results():
    """Save example results for reference"""
    
    print("\n💾 Saving Example Results")
    print("-" * 30)
    
    # Create example results structure
    example_results = {
        "timestamp": datetime.now().isoformat(),
        "example_type": "multi_method_agent_demonstration",
        "description": "Example results from multi-method AI agent analysis",
        "capabilities_demonstrated": [
            "Multi-method correlation comparison",
            "Method selection guidance", 
            "Convergence analysis",
            "Manufacturing-specific insights",
            "Bootstrap robustness testing",
            "Direct tool usage"
        ],
        "tools_used": [
            "calculate_multi_method_correlations_tool",
            "analyze_method_convergence_tool", 
            "recommend_correlation_method_tool",
            "calculate_robustness_metrics_tool"
        ],
        "sample_queries": [
            "Compare Pearson, Spearman, and Kendall correlations for all manufacturing variables",
            "Which correlation method should I use for speed vs thickness analysis?",
            "Analyze method convergence for manufacturing variables",
            "Manufacturing-specific multi-method optimization analysis",
            "Calculate bootstrap robustness metrics for key correlations"
        ],
        "benefits": [
            "Natural language interface for complex statistical analysis",
            "Manufacturing domain expertise built into AI responses",
            "Automatic method selection based on data characteristics",
            "Convergence analysis for robust correlation discovery",
            "Bootstrap validation for critical manufacturing decisions"
        ]
    }
    
    # Save results
    try:
        output_file = "examples/multi_method_agent_example_results.json"
        with open(output_file, 'w') as f:
            json.dump(example_results, f, indent=2)
        
        print(f"✅ Example results saved to {output_file}")
        
    except Exception as e:
        print(f"❌ Error saving results: {e}")


async def main():
    """Run the complete multi-method AI agent demonstration"""
    
    print("🚀 Multi-Method AI Agent Complete Demonstration")
    print("=" * 60)
    print("This comprehensive example demonstrates the full capabilities of")
    print("the multi-method correlation analysis system through AI agent interaction.")
    
    # Run the agent query demonstrations
    await demonstrate_multi_method_queries()
    
    # Run the direct tool usage demonstrations
    await demonstrate_direct_tool_usage()
    
    # Save example results
    save_example_results()
    
    # Final summary
    print("\n🎉 Multi-Method AI Agent Demonstration Complete!")
    print("=" * 55)
    
    print("\n💡 Key Capabilities Demonstrated:")
    print("   ✅ Natural language queries for multi-method analysis")
    print("   ✅ Intelligent method selection based on data characteristics")
    print("   ✅ Method convergence analysis for robustness validation")
    print("   ✅ Manufacturing-specific insights and recommendations")
    print("   ✅ Bootstrap robustness testing for critical decisions")
    print("   ✅ Direct tool usage for programmatic integration")
    
    print("\n🎯 Use Cases:")
    print("   • Interactive correlation analysis with domain expertise")
    print("   • Automated method selection for different data types")
    print("   • Robustness validation for manufacturing process control")
    print("   • Integration into larger manufacturing analytics pipelines")
    print("   • Quality assurance through multi-method validation")
    
    print("\n📚 Next Steps:")
    print("   • Integrate with real-time manufacturing data streams")
    print("   • Customize analysis parameters for specific processes")
    print("   • Extend with additional correlation methods")
    print("   • Build automated reporting and alerting systems")


if __name__ == "__main__":
    asyncio.run(main())