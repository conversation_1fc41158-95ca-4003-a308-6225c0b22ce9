#!/usr/bin/env python3
"""
Multi-Method Visualization Example

This script demonstrates the visualization capabilities of the multi-method
correlation analysis system, including side-by-side heatmaps, convergence
plots, and comprehensive dashboards.

Usage:
    python examples/multi_method_visualization_example.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.multi_correlations import MultiMethodCorrelationAnalyzer
from src.data.loader import ManufacturingDataLoader
from src.visualization.multi_plots import (
    MultiMethodCorrelationPlotter,
    quick_multi_method_heatmaps,
    quick_method_convergence_plot
)
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt


def create_output_directory():
    """Create output directory for visualization files"""
    output_dir = Path('examples/visualization_output')
    output_dir.mkdir(exist_ok=True)
    return output_dir


def prepare_sample_data():
    """Load and prepare manufacturing data for visualization"""
    
    print("📁 Loading Manufacturing Data")
    print("-" * 35)
    
    try:
        loader = ManufacturingDataLoader('test-data')
        datasets = loader.load_all_manufacturing_data()
        unified_data = loader.create_unified_dataset()
        
        print(f"✅ Loaded {len(datasets)} datasets")
        print(f"✅ Unified dataset: {unified_data.shape[0]:,} rows, {unified_data.shape[1]} columns")
        
        # Get numeric variables for analysis
        numeric_vars = unified_data.select_dtypes(include=[np.number]).columns.tolist()
        print(f"📊 Available numeric variables: {len(numeric_vars)}")
        
        return unified_data, numeric_vars
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None


def generate_multi_method_analysis(unified_data, numeric_vars):
    """Generate multi-method correlation analysis results"""
    
    print("\n🔍 Generating Multi-Method Correlation Analysis")
    print("-" * 55)
    
    try:
        # Initialize analyzer
        analyzer = MultiMethodCorrelationAnalyzer(
            significance_level=0.05
        )
        
        # Select variables for analysis (limit for visualization clarity)
        analysis_vars = numeric_vars[:6] if len(numeric_vars) >= 6 else numeric_vars
        print(f"🎯 Analyzing variables: {analysis_vars}")
        
        # Calculate multi-method correlations
        correlation_results = analyzer.calculate_multi_method_correlations(
            unified_data,
            variables=analysis_vars,
            min_periods=30
        )
        
        print(f"✅ Generated {len(correlation_results)} correlation pairs")
        
        # Calculate convergence analysis
        convergence_analysis = analyzer.analyze_method_convergence(correlation_results)
        print("✅ Convergence analysis completed")
        
        return correlation_results, convergence_analysis
        
    except Exception as e:
        print(f"❌ Error in multi-method analysis: {e}")
        return None, None


def format_results_for_visualization(correlation_results, convergence_analysis):
    """Format results for visualization functions"""
    
    visualization_results = {
        'multi_method_results': {},
        'convergence_analysis': convergence_analysis
    }
    
    # Convert correlation results to visualization format
    for pair_key, result in correlation_results.items():
        visualization_results['multi_method_results'][pair_key] = {
            'variable_1': result.variable_1,
            'variable_2': result.variable_2,
            'pearson_correlation': result.pearson_correlation,
            'spearman_correlation': result.spearman_correlation,
            'kendall_correlation': result.kendall_correlation,
            'pearson_p_value': result.pearson_p_value,
            'spearman_p_value': result.spearman_p_value,
            'kendall_p_value': result.kendall_p_value,
            'sample_size': result.sample_size,
            'method_convergence_score': result.method_convergence_score,
            'recommended_method': result.recommended_method,
            'data_distribution_assessment': result.data_distribution_assessment,
            'pearson_confidence_interval': list(result.pearson_confidence_interval),
            'spearman_confidence_interval': list(result.spearman_confidence_interval),
            'kendall_confidence_interval': list(result.kendall_confidence_interval),
            'interpretation': result.interpretation
        }
    
    return visualization_results


def demonstrate_multi_method_heatmaps(plotter, visualization_results, output_dir):
    """Demonstrate multi-method heatmap visualization"""
    
    print("\n🔥 Multi-Method Heatmaps Demonstration")
    print("-" * 45)
    
    try:
        # Basic multi-method heatmaps
        print("📊 Creating basic multi-method heatmaps...")
        fig1 = plotter.create_multi_method_heatmaps(
            visualization_results,
            title="Manufacturing Process Multi-Method Correlations",
            save_path=str(output_dir / 'basic_multi_method_heatmaps.png')
        )
        print("✅ Basic heatmaps saved")
        plt.close(fig1)
        
        # Heatmaps with convergence information
        print("📊 Creating heatmaps with convergence analysis...")
        fig2 = plotter.create_multi_method_heatmaps(
            visualization_results,
            title="Multi-Method Correlations with Convergence Analysis",
            show_convergence=True,
            figsize=(18, 6),
            save_path=str(output_dir / 'convergence_heatmaps.png')
        )
        print("✅ Convergence heatmaps saved")
        plt.close(fig2)
        
        # Custom styled heatmaps
        print("📊 Creating custom styled heatmaps...")
        fig3 = plotter.create_multi_method_heatmaps(
            visualization_results,
            title="Custom Multi-Method Correlation Analysis",
            figsize=(20, 8),
            save_path=str(output_dir / 'custom_heatmaps.png')
        )
        print("✅ Custom heatmaps saved")
        plt.close(fig3)
        
    except Exception as e:
        print(f"❌ Error creating heatmaps: {e}")


def demonstrate_convergence_plots(plotter, visualization_results, output_dir):
    """Demonstrate method convergence visualization"""
    
    print("\n🔄 Method Convergence Plots Demonstration")
    print("-" * 50)
    
    try:
        # Interactive convergence analysis plot
        print("📈 Creating interactive convergence analysis plot...")
        fig1 = plotter.plot_method_convergence(
            visualization_results,
            save_path=str(output_dir / 'interactive_convergence_analysis.html')
        )
        print("✅ Interactive convergence plot saved (HTML)")
        
        # Detailed convergence analysis
        print("📈 Creating detailed convergence analysis...")
        fig2 = plotter.plot_method_convergence(
            visualization_results,
            title="Detailed Method Convergence Analysis",
            save_path=str(output_dir / 'detailed_convergence_analysis.html')
        )
        print("✅ Detailed convergence plot saved (HTML)")
        
    except Exception as e:
        print(f"❌ Error creating convergence plots: {e}")


def demonstrate_comparison_matrices(plotter, visualization_results, output_dir):
    """Demonstrate method comparison matrix visualization"""
    
    print("\n📊 Method Comparison Matrices Demonstration")
    print("-" * 55)
    
    try:
        # Pearson vs Spearman comparison
        print("📈 Creating Pearson vs Spearman comparison matrix...")
        fig1 = plotter.plot_method_comparison_matrix(
            visualization_results,
            method_pair=('pearson', 'spearman'),
            title="Pearson vs Spearman Correlation Comparison",
            save_path=str(output_dir / 'pearson_vs_spearman_comparison.png')
        )
        print("✅ Pearson vs Spearman comparison saved")
        plt.close(fig1)
        
        # Pearson vs Kendall comparison
        print("📈 Creating Pearson vs Kendall comparison matrix...")
        fig2 = plotter.plot_method_comparison_matrix(
            visualization_results,
            method_pair=('pearson', 'kendall'),
            title="Pearson vs Kendall Correlation Comparison",
            save_path=str(output_dir / 'pearson_vs_kendall_comparison.png')
        )
        print("✅ Pearson vs Kendall comparison saved")
        plt.close(fig2)
        
        # Spearman vs Kendall comparison
        print("📈 Creating Spearman vs Kendall comparison matrix...")
        fig3 = plotter.plot_method_comparison_matrix(
            visualization_results,
            method_pair=('spearman', 'kendall'),
            title="Spearman vs Kendall Correlation Comparison",
            save_path=str(output_dir / 'spearman_vs_kendall_comparison.png')
        )
        print("✅ Spearman vs Kendall comparison saved")
        plt.close(fig3)
        
    except Exception as e:
        print(f"❌ Error creating comparison matrices: {e}")


def demonstrate_comprehensive_dashboard(plotter, visualization_results, output_dir):
    """Demonstrate comprehensive multi-method dashboard"""
    
    print("\n📊 Comprehensive Dashboard Demonstration")
    print("-" * 45)
    
    try:
        # Create comprehensive dashboard
        print("🎛️  Creating comprehensive multi-method dashboard...")
        fig = plotter.create_multi_method_dashboard(
            visualization_results,
            title="Manufacturing Multi-Method Correlation Dashboard",
            save_path=str(output_dir / 'comprehensive_dashboard.html')
        )
        print("✅ Comprehensive dashboard saved (HTML)")
        
        # Create manufacturing-focused dashboard
        print("🏭 Creating manufacturing-focused dashboard...")
        fig2 = plotter.create_multi_method_dashboard(
            visualization_results,
            title="Fiber Cement Manufacturing Multi-Method Analysis Dashboard",
            save_path=str(output_dir / 'manufacturing_dashboard.html')
        )
        print("✅ Manufacturing dashboard saved (HTML)")
        
    except Exception as e:
        print(f"❌ Error creating dashboard: {e}")


def demonstrate_convenience_functions(visualization_results, output_dir):
    """Demonstrate quick convenience functions"""
    
    print("\n⚡ Quick Convenience Functions Demonstration")
    print("-" * 55)
    
    try:
        # Quick multi-method heatmaps
        print("🔥 Creating quick multi-method heatmaps...")
        fig1 = quick_multi_method_heatmaps(
            visualization_results,
            title="Quick Multi-Method Heatmaps",
            save_path=str(output_dir / 'quick_heatmaps.png')
        )
        print("✅ Quick heatmaps created")
        plt.close(fig1)
        
        # Quick convergence plot
        print("📈 Creating quick convergence plot...")
        fig2 = quick_method_convergence_plot(
            visualization_results,
            title="Quick Method Convergence Analysis",
            save_path=str(output_dir / 'quick_convergence.html')
        )
        print("✅ Quick convergence plot created")
        
    except Exception as e:
        print(f"❌ Error with convenience functions: {e}")


def demonstrate_batch_visualization(plotter, visualization_results, output_dir):
    """Demonstrate batch visualization generation"""
    
    print("\n📦 Batch Visualization Generation")
    print("-" * 40)
    
    try:
        # Generate all visualizations at once
        print("🎨 Generating complete visualization suite...")
        
        plotter.save_all_multi_method_plots(
            visualization_results,
            output_dir=str(output_dir / 'batch_output'),
            prefix="manufacturing_"
        )
        
        print("✅ Complete visualization suite generated")
        
        # List generated files
        batch_output_dir = output_dir / 'batch_output'
        if batch_output_dir.exists():
            generated_files = list(batch_output_dir.glob('*'))
            print(f"📁 Generated {len(generated_files)} visualization files:")
            for file_path in generated_files:
                print(f"   • {file_path.name}")
        
    except Exception as e:
        print(f"❌ Error in batch visualization: {e}")


def create_visualization_summary(output_dir):
    """Create a summary of all generated visualizations"""
    
    print("\n📋 Creating Visualization Summary")
    print("-" * 40)
    
    summary = {
        "visualization_types": [
            "Multi-method correlation heatmaps",
            "Method convergence analysis plots",
            "Method comparison matrices", 
            "Comprehensive interactive dashboards",
            "Quick convenience visualizations",
            "Batch-generated visualization suites"
        ],
        "file_formats": [
            "PNG (static images)",
            "HTML (interactive plots)",
            "Multiple formats in batch mode"
        ],
        "features": [
            "Side-by-side method comparison",
            "Convergence score visualization",
            "Interactive exploration",
            "Method recommendation highlighting",
            "Manufacturing-specific styling",
            "Customizable layouts and colors"
        ],
        "use_cases": [
            "Manufacturing process optimization reports",
            "Quality assurance analysis presentations", 
            "Statistical method validation studies",
            "Multi-stakeholder communication",
            "Process control dashboards",
            "Research and development analysis"
        ]
    }
    
    try:
        import json
        summary_file = output_dir / 'visualization_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"✅ Visualization summary saved to {summary_file}")
        
        # Also create a markdown summary
        md_summary = f"""# Multi-Method Visualization Examples Summary

## Generated Visualizations

### Static Images (PNG)
- Basic multi-method heatmaps
- Convergence-enhanced heatmaps  
- Custom styled heatmaps
- Method comparison matrices (Pearson vs Spearman, Pearson vs Kendall, Spearman vs Kendall)
- Quick convenience heatmaps

### Interactive Plots (HTML)
- Interactive convergence analysis plots
- Detailed convergence analysis
- Comprehensive multi-method dashboards
- Manufacturing-focused dashboards
- Quick convergence plots

### Batch Output
- Complete visualization suite with all plot types
- Systematically generated with consistent naming

## Key Features Demonstrated

1. **Multi-Method Comparison**: Side-by-side visualization of Pearson, Spearman, and Kendall correlations
2. **Convergence Analysis**: Visual assessment of method agreement and stability
3. **Interactive Exploration**: HTML-based plots for detailed data exploration
4. **Manufacturing Focus**: Domain-specific styling and interpretation
5. **Scalable Generation**: Batch processing for multiple visualizations
6. **Customization**: Flexible layouts, colors, and formatting options

## Applications

- Process optimization reports
- Quality assurance presentations
- Statistical validation studies
- Stakeholder communication
- Real-time monitoring dashboards
- Research documentation

Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        md_file = output_dir / 'visualization_summary.md'
        with open(md_file, 'w') as f:
            f.write(md_summary)
        
        print(f"✅ Markdown summary saved to {md_file}")
        
    except Exception as e:
        print(f"❌ Error creating summary: {e}")


def main():
    """Run the complete multi-method visualization demonstration"""
    
    print("🎨 Multi-Method Visualization Complete Demonstration")
    print("=" * 65)
    print("This example demonstrates all visualization capabilities of the")
    print("multi-method correlation analysis system for manufacturing data.")
    
    # Step 1: Setup
    output_dir = create_output_directory()
    print(f"📁 Output directory: {output_dir}")
    
    # Step 2: Prepare data
    unified_data, numeric_vars = prepare_sample_data()
    if unified_data is None:
        print("❌ Cannot proceed without data")
        return
    
    # Step 3: Generate analysis
    correlation_results, convergence_analysis = generate_multi_method_analysis(unified_data, numeric_vars)
    if correlation_results is None:
        print("❌ Cannot proceed without correlation results")
        return
    
    # Step 4: Format for visualization
    visualization_results = format_results_for_visualization(correlation_results, convergence_analysis)
    
    # Step 5: Initialize plotter
    print("\n🎨 Initializing Multi-Method Correlation Plotter")
    print("-" * 55)
    plotter = MultiMethodCorrelationPlotter()
    print("✅ Plotter initialized with custom color schemes")
    
    # Step 6: Demonstrate different visualization types
    demonstrate_multi_method_heatmaps(plotter, visualization_results, output_dir)
    demonstrate_convergence_plots(plotter, visualization_results, output_dir)
    demonstrate_comparison_matrices(plotter, visualization_results, output_dir)
    demonstrate_comprehensive_dashboard(plotter, visualization_results, output_dir)
    demonstrate_convenience_functions(visualization_results, output_dir)
    demonstrate_batch_visualization(plotter, visualization_results, output_dir)
    
    # Step 7: Create summary
    create_visualization_summary(output_dir)
    
    # Final summary
    print("\n🎉 Multi-Method Visualization Demonstration Complete!")
    print("=" * 60)
    
    print(f"\n📁 All visualizations saved to: {output_dir}")
    print("\n📊 Generated Visualization Types:")
    print("   ✅ Multi-method correlation heatmaps (side-by-side comparison)")
    print("   ✅ Method convergence analysis plots (interactive)")
    print("   ✅ Method comparison matrices (pairwise method analysis)")
    print("   ✅ Comprehensive dashboards (all-in-one analysis)")
    print("   ✅ Quick convenience plots (rapid visualization)")
    print("   ✅ Batch visualization suite (systematic generation)")
    
    print("\n🎯 Visualization Applications:")
    print("   • Manufacturing process optimization reports")
    print("   • Quality assurance analysis presentations") 
    print("   • Statistical method validation studies")
    print("   • Multi-stakeholder communication dashboards")
    print("   • Real-time process monitoring visualizations")
    print("   • Research and development documentation")
    
    print("\n💡 Key Benefits:")
    print("   • Visual validation of correlation robustness across methods")
    print("   • Interactive exploration of manufacturing relationships")
    print("   • Professional-quality output for stakeholder communication")
    print("   • Scalable generation for operational reporting")
    print("   • Manufacturing domain-specific styling and interpretation")
    
    # Display file count
    generated_files = list(output_dir.rglob('*'))
    file_count = len([f for f in generated_files if f.is_file()])
    print(f"\n📈 Total files generated: {file_count}")


if __name__ == "__main__":
    main()