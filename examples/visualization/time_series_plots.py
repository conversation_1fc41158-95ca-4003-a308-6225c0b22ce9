"""
Time Series Visualization Module

Comprehensive visualization tools for time series analysis in manufacturing data.
Includes trend plots, anomaly detection visualization, event analysis, and multi-variable comparisons.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class TimeSeriesVisualizer:
    """
    Comprehensive visualization toolkit for time series analysis
    """
    
    def __init__(self, figsize: Tuple[int, int] = (15, 8), dpi: int = 300):
        """
        Initialize the time series visualizer.
        
        Args:
            figsize: Default figure size for matplotlib plots
            dpi: Resolution for saved plots
        """
        self.figsize = figsize
        self.dpi = dpi
        self.colors = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e',
            'anomaly': '#d62728',
            'event': '#2ca02c',
            'threshold': '#9467bd'
        }
    
    def plot_time_series(self,
                        data: pd.DataFrame,
                        variables: List[str],
                        time_column: str = 'timestamp',
                        title: str = "Time Series Plot",
                        save_path: Optional[str] = None,
                        interactive: bool = False) -> Optional[go.Figure]:
        """
        Create basic time series plot for multiple variables.
        
        Args:
            data: DataFrame with time series data
            variables: List of variable names to plot
            time_column: Name of timestamp column
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        if time_column not in data.columns:
            print(f"Time column '{time_column}' not found")
            return None
        
        # Ensure timestamp is datetime
        data = data.copy()
        data[time_column] = pd.to_datetime(data[time_column])
        data = data.sort_values(time_column)
        
        if interactive:
            # Interactive plotly plot
            fig = go.Figure()
            
            for i, var in enumerate(variables):
                if var in data.columns:
                    fig.add_trace(go.Scatter(
                        x=data[time_column],
                        y=data[var],
                        mode='lines',
                        name=var,
                        line=dict(width=2)
                    ))
            
            fig.update_layout(
                title=title,
                xaxis_title="Time",
                yaxis_title="Value",
                width=1200,
                height=600,
                hovermode='x unified'
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib plot
            fig, axes = plt.subplots(len(variables), 1, figsize=self.figsize, 
                                   dpi=self.dpi, sharex=True)
            
            if len(variables) == 1:
                axes = [axes]
            
            for i, var in enumerate(variables):
                if var in data.columns:
                    axes[i].plot(data[time_column], data[var], 
                               linewidth=2, label=var)
                    axes[i].set_ylabel(var, fontsize=11)
                    axes[i].grid(True, alpha=0.3)
                    axes[i].legend()
            
            # Format x-axis
            axes[-1].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
            axes[-1].xaxis.set_major_locator(mdates.HourLocator(interval=6))
            plt.xticks(rotation=45)
            
            plt.suptitle(title, fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def plot_anomaly_detection(self,
                             data: pd.DataFrame,
                             variable: str,
                             anomaly_column: str,
                             time_column: str = 'timestamp',
                             title: Optional[str] = None,
                             save_path: Optional[str] = None,
                             interactive: bool = False) -> Optional[go.Figure]:
        """
        Plot time series with anomaly highlights.
        
        Args:
            data: DataFrame with time series data
            variable: Variable name to plot
            anomaly_column: Column indicating anomalies (boolean)
            time_column: Name of timestamp column
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        if any(col not in data.columns for col in [variable, anomaly_column, time_column]):
            print("Required columns not found in data")
            return None
        
        data = data.copy()
        data[time_column] = pd.to_datetime(data[time_column])
        data = data.sort_values(time_column)
        
        if title is None:
            title = f"Anomaly Detection: {variable}"
        
        if interactive:
            # Interactive plotly plot
            fig = go.Figure()
            
            # Normal data points
            normal_data = data[~data[anomaly_column]]
            fig.add_trace(go.Scatter(
                x=normal_data[time_column],
                y=normal_data[variable],
                mode='lines',
                name='Normal',
                line=dict(color=self.colors['primary'], width=2)
            ))
            
            # Anomaly points
            anomaly_data = data[data[anomaly_column]]
            if not anomaly_data.empty:
                fig.add_trace(go.Scatter(
                    x=anomaly_data[time_column],
                    y=anomaly_data[variable],
                    mode='markers',
                    name='Anomalies',
                    marker=dict(
                        color=self.colors['anomaly'],
                        size=8,
                        symbol='x'
                    )
                ))
            
            fig.update_layout(
                title=title,
                xaxis_title="Time",
                yaxis_title=variable,
                width=1200,
                height=600,
                hovermode='x unified'
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib plot
            plt.figure(figsize=self.figsize, dpi=self.dpi)
            
            # Plot normal data
            normal_data = data[~data[anomaly_column]]
            plt.plot(normal_data[time_column], normal_data[variable], 
                    color=self.colors['primary'], linewidth=2, label='Normal')
            
            # Plot anomalies
            anomaly_data = data[data[anomaly_column]]
            if not anomaly_data.empty:
                plt.scatter(anomaly_data[time_column], anomaly_data[variable],
                          color=self.colors['anomaly'], s=50, marker='x', 
                          label=f'Anomalies ({len(anomaly_data)})', zorder=5)
            
            plt.xlabel('Time', fontsize=12)
            plt.ylabel(variable, fontsize=12)
            plt.title(title, fontsize=16, fontweight='bold')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # Format x-axis
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
            plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=6))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def plot_event_analysis(self,
                          continuous_data: pd.DataFrame,
                          events_data: pd.DataFrame,
                          variable: str,
                          time_column: str = 'timestamp',
                          event_type_column: str = 'event_type',
                          title: Optional[str] = None,
                          save_path: Optional[str] = None,
                          interactive: bool = False) -> Optional[go.Figure]:
        """
        Plot time series with event overlays.
        
        Args:
            continuous_data: DataFrame with continuous time series
            events_data: DataFrame with event data
            variable: Variable name to plot
            time_column: Name of timestamp column
            event_type_column: Column indicating event type
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        # Prepare data
        continuous_data = continuous_data.copy()
        events_data = events_data.copy()
        
        continuous_data[time_column] = pd.to_datetime(continuous_data[time_column])
        events_data[time_column] = pd.to_datetime(events_data[time_column])
        
        continuous_data = continuous_data.sort_values(time_column)
        events_data = events_data.sort_values(time_column)
        
        if title is None:
            title = f"Event Analysis: {variable}"
        
        if interactive:
            # Interactive plotly plot
            fig = go.Figure()
            
            # Plot continuous data
            fig.add_trace(go.Scatter(
                x=continuous_data[time_column],
                y=continuous_data[variable],
                mode='lines',
                name=variable,
                line=dict(color=self.colors['primary'], width=2)
            ))
            
            # Add event markers
            event_types = events_data[event_type_column].unique()
            colors = ['red', 'orange', 'green', 'purple', 'brown']
            
            for i, event_type in enumerate(event_types):
                event_subset = events_data[events_data[event_type_column] == event_type]
                color = colors[i % len(colors)]
                
                # Add vertical lines for events
                for _, event in event_subset.iterrows():
                    fig.add_vline(
                        x=event[time_column],
                        line_dash="dash",
                        line_color=color,
                        annotation_text=event_type
                    )
            
            fig.update_layout(
                title=title,
                xaxis_title="Time",
                yaxis_title=variable,
                width=1200,
                height=600,
                hovermode='x unified'
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib plot
            plt.figure(figsize=self.figsize, dpi=self.dpi)
            
            # Plot continuous data
            plt.plot(continuous_data[time_column], continuous_data[variable],
                    color=self.colors['primary'], linewidth=2, label=variable)
            
            # Add event markers
            event_types = events_data[event_type_column].unique()
            colors = ['red', 'orange', 'green', 'purple', 'brown']
            
            for i, event_type in enumerate(event_types):
                event_subset = events_data[events_data[event_type_column] == event_type]
                color = colors[i % len(colors)]
                
                for _, event in event_subset.iterrows():
                    plt.axvline(x=event[time_column], color=color, 
                               linestyle='--', alpha=0.7, linewidth=1)
                
                # Add legend entry
                plt.axvline(x=[], color=color, linestyle='--', 
                           label=f'{event_type} ({len(event_subset)})')
            
            plt.xlabel('Time', fontsize=12)
            plt.ylabel(variable, fontsize=12)
            plt.title(title, fontsize=16, fontweight='bold')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # Format x-axis
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
            plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=6))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def plot_rolling_statistics(self,
                              data: pd.DataFrame,
                              variable: str,
                              window_sizes: List[int] = [10, 30, 60],
                              time_column: str = 'timestamp',
                              title: Optional[str] = None,
                              save_path: Optional[str] = None,
                              interactive: bool = False) -> Optional[go.Figure]:
        """
        Plot rolling statistics (mean, std) for a variable.
        
        Args:
            data: DataFrame with time series data
            variable: Variable name to analyze
            window_sizes: List of window sizes for rolling statistics
            time_column: Name of timestamp column
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        data = data.copy()
        data[time_column] = pd.to_datetime(data[time_column])
        data = data.sort_values(time_column)
        
        if title is None:
            title = f"Rolling Statistics: {variable}"
        
        if interactive:
            # Interactive plotly subplot
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                subplot_titles=(f'{variable} with Rolling Means', 'Rolling Standard Deviation'),
                vertical_spacing=0.1
            )
            
            # Original data
            fig.add_trace(
                go.Scatter(x=data[time_column], y=data[variable],
                          mode='lines', name='Original', opacity=0.7),
                row=1, col=1
            )
            
            # Rolling means
            colors = ['red', 'green', 'purple']
            for i, window in enumerate(window_sizes):
                rolling_mean = data[variable].rolling(window=window).mean()
                rolling_std = data[variable].rolling(window=window).std()
                
                fig.add_trace(
                    go.Scatter(x=data[time_column], y=rolling_mean,
                              mode='lines', name=f'Mean ({window})',
                              line=dict(color=colors[i % len(colors)])),
                    row=1, col=1
                )
                
                fig.add_trace(
                    go.Scatter(x=data[time_column], y=rolling_std,
                              mode='lines', name=f'Std ({window})',
                              line=dict(color=colors[i % len(colors)])),
                    row=2, col=1
                )
            
            fig.update_layout(
                title=title,
                height=800,
                width=1200,
                hovermode='x unified'
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib subplots
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, 
                                          dpi=self.dpi, sharex=True)
            
            # Original data
            ax1.plot(data[time_column], data[variable], 
                    color='blue', alpha=0.7, linewidth=1, label='Original')
            
            # Rolling statistics
            colors = ['red', 'green', 'purple']
            for i, window in enumerate(window_sizes):
                rolling_mean = data[variable].rolling(window=window).mean()
                rolling_std = data[variable].rolling(window=window).std()
                
                color = colors[i % len(colors)]
                
                ax1.plot(data[time_column], rolling_mean, 
                        color=color, linewidth=2, label=f'Mean ({window})')
                
                ax2.plot(data[time_column], rolling_std, 
                        color=color, linewidth=2, label=f'Std ({window})')
            
            ax1.set_ylabel(f'{variable} Value', fontsize=11)
            ax1.set_title(f'{variable} with Rolling Means', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            ax2.set_ylabel('Rolling Std', fontsize=11)
            ax2.set_xlabel('Time', fontsize=11)
            ax2.set_title('Rolling Standard Deviation', fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # Format x-axis
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
            ax2.xaxis.set_major_locator(mdates.HourLocator(interval=6))
            plt.xticks(rotation=45)
            
            plt.suptitle(title, fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def plot_process_windows(self,
                           window_data: pd.DataFrame,
                           variable: str,
                           event_column: str = 'event_type',
                           time_relative_column: str = 'time_relative_to_event',
                           title: Optional[str] = None,
                           save_path: Optional[str] = None,
                           interactive: bool = False) -> Optional[go.Figure]:
        """
        Plot process behavior around events using time windows.
        
        Args:
            window_data: DataFrame with event window data
            variable: Variable to analyze
            event_column: Column indicating event type
            time_relative_column: Column with time relative to event
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        if title is None:
            title = f"Process Windows Analysis: {variable}"
        
        event_types = window_data[event_column].unique()
        
        if interactive:
            # Interactive plotly plot
            fig = go.Figure()
            
            colors = ['red', 'blue', 'green', 'orange', 'purple']
            
            for i, event_type in enumerate(event_types):
                event_data = window_data[window_data[event_column] == event_type]
                
                # Group by event and calculate mean trajectory
                event_ids = event_data['event_id'].unique()
                
                for event_id in event_ids:
                    single_event = event_data[event_data['event_id'] == event_id]
                    
                    fig.add_trace(go.Scatter(
                        x=single_event[time_relative_column],
                        y=single_event[variable],
                        mode='lines',
                        name=f'{event_type} (ID: {event_id})',
                        line=dict(color=colors[i % len(colors)], width=1),
                        opacity=0.3,
                        showlegend=(event_id == event_ids[0])  # Show legend for first trace only
                    ))
                
                # Calculate and plot mean trajectory
                mean_trajectory = event_data.groupby(time_relative_column)[variable].mean().reset_index()
                
                fig.add_trace(go.Scatter(
                    x=mean_trajectory[time_relative_column],
                    y=mean_trajectory[variable],
                    mode='lines',
                    name=f'{event_type} (Mean)',
                    line=dict(color=colors[i % len(colors)], width=3)
                ))
            
            # Add vertical line at event time
            fig.add_vline(x=0, line_dash="dash", line_color="black", 
                         annotation_text="Event Occurs")
            
            fig.update_layout(
                title=title,
                xaxis_title="Time Relative to Event (minutes)",
                yaxis_title=variable,
                width=1200,
                height=600,
                hovermode='x unified'
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib plot
            plt.figure(figsize=self.figsize, dpi=self.dpi)
            
            colors = ['red', 'blue', 'green', 'orange', 'purple']
            
            for i, event_type in enumerate(event_types):
                event_data = window_data[window_data[event_column] == event_type]
                color = colors[i % len(colors)]
                
                # Plot individual event trajectories (faded)
                event_ids = event_data['event_id'].unique()
                for event_id in event_ids:
                    single_event = event_data[event_data['event_id'] == event_id]
                    plt.plot(single_event[time_relative_column], single_event[variable],
                            color=color, alpha=0.3, linewidth=1)
                
                # Calculate and plot mean trajectory
                mean_trajectory = event_data.groupby(time_relative_column)[variable].mean()
                plt.plot(mean_trajectory.index, mean_trajectory.values,
                        color=color, linewidth=3, label=f'{event_type} (Mean)')
            
            # Add vertical line at event time
            plt.axvline(x=0, color='black', linestyle='--', alpha=0.7, 
                       label='Event Occurs')
            
            plt.xlabel('Time Relative to Event (minutes)', fontsize=12)
            plt.ylabel(variable, fontsize=12)
            plt.title(title, fontsize=16, fontweight='bold')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def create_time_series_dashboard(self,
                                   data: pd.DataFrame,
                                   variables: List[str],
                                   time_column: str = 'timestamp',
                                   save_path: Optional[str] = None) -> go.Figure:
        """
        Create comprehensive time series dashboard.
        
        Args:
            data: DataFrame with time series data
            variables: List of variables to include
            time_column: Name of timestamp column
            save_path: Path to save the dashboard
            
        Returns:
            Plotly figure with dashboard
        """
        # Prepare data
        data = data.copy()
        data[time_column] = pd.to_datetime(data[time_column])
        data = data.sort_values(time_column)
        
        # Create subplots
        n_vars = len(variables)
        fig = make_subplots(
            rows=n_vars, cols=1,
            shared_xaxes=True,
            subplot_titles=variables,
            vertical_spacing=0.02
        )
        
        # Plot each variable
        for i, var in enumerate(variables, 1):
            if var in data.columns:
                fig.add_trace(
                    go.Scatter(
                        x=data[time_column],
                        y=data[var],
                        mode='lines',
                        name=var,
                        line=dict(width=2)
                    ),
                    row=i, col=1
                )
        
        # Update layout
        fig.update_layout(
            title="Manufacturing Time Series Dashboard",
            height=200 * n_vars + 100,
            width=1400,
            hovermode='x unified',
            showlegend=False
        )
        
        # Update x-axis for bottom subplot only
        fig.update_xaxes(title_text="Time", row=n_vars, col=1)
        
        if save_path:
            fig.write_html(save_path)
        
        return fig

# Utility functions
def quick_time_series_plots(data: pd.DataFrame,
                           time_column: str = 'timestamp',
                           save_dir: Optional[str] = None,
                           interactive: bool = True) -> Dict[str, Any]:
    """
    Quickly generate all time series plots for a dataset.
    
    Args:
        data: DataFrame with time series data
        time_column: Name of timestamp column
        save_dir: Directory to save plots
        interactive: Whether to create interactive plots
        
    Returns:
        Dictionary with plot results
    """
    from pathlib import Path
    
    visualizer = TimeSeriesVisualizer()
    results = {}
    
    if save_dir:
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
    
    # Get numeric variables
    numeric_vars = data.select_dtypes(include=[np.number]).columns.tolist()
    
    if time_column in numeric_vars:
        numeric_vars.remove(time_column)
    
    if len(numeric_vars) == 0:
        print("No numeric variables found for time series analysis")
        return results
    
    # 1. Basic time series plot
    ts_path = str(save_path / "time_series_overview.html") if save_dir else None
    ts_fig = visualizer.plot_time_series(
        data, numeric_vars[:5],  # Limit to first 5 variables
        time_column=time_column,
        save_path=ts_path,
        interactive=interactive
    )
    results['time_series'] = ts_fig
    
    # 2. Rolling statistics for first variable
    if len(numeric_vars) > 0:
        rolling_path = str(save_path / f"rolling_stats_{numeric_vars[0]}.html") if save_dir else None
        rolling_fig = visualizer.plot_rolling_statistics(
            data, numeric_vars[0],
            time_column=time_column,
            save_path=rolling_path,
            interactive=interactive
        )
        results['rolling_stats'] = rolling_fig
    
    # 3. Dashboard
    if interactive:
        dashboard_path = str(save_path / "time_series_dashboard.html") if save_dir else None
        dashboard_fig = visualizer.create_time_series_dashboard(
            data, numeric_vars[:4],  # Limit to 4 variables for readability
            time_column=time_column,
            save_path=dashboard_path
        )
        results['dashboard'] = dashboard_fig
    
    return results

# Example usage
def main():
    """Example usage of time series visualization tools"""
    
    # Create sample manufacturing time series data
    np.random.seed(42)
    n_samples = 2000
    
    # Generate timestamps
    timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='30s')
    
    # Generate process variables with patterns
    base_signal = np.sin(2 * np.pi * np.arange(n_samples) / 200)  # Base cycle
    
    speed = 150 + 10 * base_signal + np.random.normal(0, 2, n_samples)
    temperature = 80 + 5 * np.sin(2 * np.pi * np.arange(n_samples) / 300) + np.random.normal(0, 1, n_samples)
    thickness = 12.5 + 0.01 * speed + np.random.normal(0, 0.1, n_samples)
    
    # Add some anomalies
    speed[500:520] = 200  # Speed spike
    temperature[1000:1020] = 95  # Temperature spike
    
    # Create DataFrame
    data = pd.DataFrame({
        'timestamp': timestamps,
        'speed': speed,
        'temperature': temperature,
        'thickness': thickness
    })
    
    print("Sample manufacturing time series data created")
    print(f"Shape: {data.shape}")
    print(f"Time range: {data['timestamp'].min()} to {data['timestamp'].max()}")
    
    # Initialize visualizer
    visualizer = TimeSeriesVisualizer()
    
    # Test visualizations
    print("\n=== Testing Time Series Visualizations ===")
    
    # 1. Basic time series plot
    print("Creating basic time series plot...")
    visualizer.plot_time_series(
        data, ['speed', 'temperature', 'thickness'], 
        interactive=False
    )
    
    # 2. Simulate anomaly detection
    print("Creating anomaly detection plot...")
    # Simple anomaly detection (z-score > 2)
    data['speed_anomaly'] = np.abs((data['speed'] - data['speed'].mean()) / data['speed'].std()) > 2
    
    visualizer.plot_anomaly_detection(
        data, 'speed', 'speed_anomaly', interactive=False
    )
    
    # 3. Rolling statistics
    print("Creating rolling statistics plot...")
    visualizer.plot_rolling_statistics(
        data, 'temperature', window_sizes=[20, 50, 100], interactive=False
    )
    
    # 4. Quick analysis
    print("Running quick time series analysis...")
    results = quick_time_series_plots(data, interactive=False)
    
    print(f"Generated {len(results)} visualizations")

if __name__ == "__main__":
    main()