"""
Industrial Manufacturing Dashboard

Comprehensive dashboard for real-time manufacturing data visualization
with KPIs, process monitoring, quality metrics, and alert systems.
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import dash
from dash import dcc, html, Input, Output, callback
import dash_bootstrap_components as dbc
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
import warnings
warnings.filterwarnings('ignore')

class IndustrialDashboard:
    """
    Comprehensive manufacturing dashboard with real-time monitoring capabilities
    """
    
    def __init__(self, title: str = "Manufacturing Control Dashboard"):
        """
        Initialize the industrial dashboard.
        
        Args:
            title: Dashboard title
        """
        self.title = title
        self.app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
        self.data_cache = {}
        self.kpi_thresholds = {
            'efficiency': {'target': 85, 'warning': 80, 'critical': 75},
            'quality_score': {'target': 95, 'warning': 90, 'critical': 85},
            'availability': {'target': 95, 'warning': 90, 'critical': 85},
            'throughput': {'target': 100, 'warning': 85, 'critical': 70}
        }
        
        self._setup_layout()
        self._setup_callbacks()
    
    def _setup_layout(self):
        """Setup the dashboard layout"""
        
        # Header
        header = dbc.Row([
            dbc.Col([
                html.H1(self.title, className="text-center text-primary mb-4"),
                html.H4(f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 
                        className="text-center text-muted", id="last-updated")
            ])
        ])
        
        # KPI Cards
        kpi_row = dbc.Row([
            dbc.Col([
                self._create_kpi_card("Overall Efficiency", "efficiency", "%", "success")
            ], width=3),
            dbc.Col([
                self._create_kpi_card("Quality Score", "quality_score", "%", "info")
            ], width=3),
            dbc.Col([
                self._create_kpi_card("Equipment Availability", "availability", "%", "warning")
            ], width=3),
            dbc.Col([
                self._create_kpi_card("Throughput", "throughput", "units/hr", "danger")
            ], width=3)
        ], className="mb-4")
        
        # Main content area
        main_content = dbc.Row([
            # Process monitoring
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Process Variables"),
                    dbc.CardBody([
                        dcc.Graph(id="process-variables-chart")
                    ])
                ])
            ], width=8),
            
            # Alerts and status
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("System Status"),
                    dbc.CardBody([
                        html.Div(id="system-status")
                    ])
                ], className="mb-3"),
                
                dbc.Card([
                    dbc.CardHeader("Recent Alerts"),
                    dbc.CardBody([
                        html.Div(id="alerts-list")
                    ])
                ])
            ], width=4)
        ], className="mb-4")
        
        # Secondary charts
        secondary_row = dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Quality Metrics"),
                    dbc.CardBody([
                        dcc.Graph(id="quality-chart")
                    ])
                ])
            ], width=6),
            
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Production Statistics"),
                    dbc.CardBody([
                        dcc.Graph(id="production-stats")
                    ])
                ])
            ], width=6)
        ])
        
        # Layout assembly
        self.app.layout = dbc.Container([
            header,
            kpi_row,
            main_content,
            secondary_row,
            
            # Refresh interval
            dcc.Interval(
                id='interval-component',
                interval=5*1000,  # Update every 5 seconds
                n_intervals=0
            )
        ], fluid=True)
    
    def _create_kpi_card(self, title: str, metric_id: str, unit: str, color: str) -> dbc.Card:
        """Create a KPI card component"""
        return dbc.Card([
            dbc.CardBody([
                html.H4(title, className="card-title text-center"),
                html.H2("--", id=f"kpi-{metric_id}", className=f"text-{color} text-center"),
                html.P(f"Target: -- {unit}", id=f"target-{metric_id}", className="text-muted text-center small"),
                dbc.Progress(id=f"progress-{metric_id}", value=0, color=color, className="mb-2")
            ])
        ], color=color, outline=True)
    
    def _setup_callbacks(self):
        """Setup dashboard callbacks for interactivity"""
        
        @self.app.callback(
            [Output('kpi-efficiency', 'children'),
             Output('kpi-quality_score', 'children'),
             Output('kpi-availability', 'children'),
             Output('kpi-throughput', 'children'),
             Output('progress-efficiency', 'value'),
             Output('progress-quality_score', 'value'),
             Output('progress-availability', 'value'),
             Output('progress-throughput', 'value'),
             Output('process-variables-chart', 'figure'),
             Output('quality-chart', 'figure'),
             Output('production-stats', 'figure'),
             Output('system-status', 'children'),
             Output('alerts-list', 'children'),
             Output('last-updated', 'children')],
            [Input('interval-component', 'n_intervals')]
        )
        def update_dashboard(n):
            """Update all dashboard components"""
            
            # Simulate data updates (in real implementation, load from database/API)
            current_data = self._generate_sample_data()
            
            # Calculate KPIs
            kpis = self._calculate_kpis(current_data)
            
            # Update KPI values and progress bars
            kpi_values = []
            progress_values = []
            
            for metric in ['efficiency', 'quality_score', 'availability', 'throughput']:
                value = kpis.get(metric, 0)
                target = self.kpi_thresholds[metric]['target']
                
                kpi_values.append(f"{value:.1f}%")
                progress_values.append(min(100, (value / target) * 100))
            
            # Create charts
            process_chart = self._create_process_chart(current_data)
            quality_chart = self._create_quality_chart(current_data)
            production_chart = self._create_production_chart(current_data)
            
            # System status
            status_components = self._create_status_components(current_data, kpis)
            
            # Alerts
            alerts_components = self._create_alerts_components(current_data, kpis)
            
            # Last updated timestamp
            last_updated = f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            return (
                *kpi_values, *progress_values,
                process_chart, quality_chart, production_chart,
                status_components, alerts_components, last_updated
            )
    
    def _generate_sample_data(self) -> Dict[str, Any]:
        """Generate sample manufacturing data"""
        now = datetime.now()
        timestamps = [now - timedelta(minutes=i) for i in range(60, 0, -1)]
        
        # Process variables
        base_temp = 80
        base_speed = 150
        base_pressure = 50
        
        data = {
            'timestamps': timestamps,
            'temperature': [base_temp + np.random.normal(0, 2) for _ in timestamps],
            'speed': [base_speed + np.random.normal(0, 5) for _ in timestamps],
            'pressure': [base_pressure + np.random.normal(0, 1) for _ in timestamps],
            'thickness': [12.5 + np.random.normal(0, 0.2) for _ in timestamps],
            'quality_scores': [95 + np.random.normal(0, 3) for _ in timestamps],
            'production_rate': [100 + np.random.normal(0, 10) for _ in timestamps],
            'defect_rate': [np.random.exponential(0.5) for _ in timestamps],
            'downtime_events': np.random.choice([0, 1], size=len(timestamps), p=[0.95, 0.05])
        }
        
        return data
    
    def _calculate_kpis(self, data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate key performance indicators"""
        
        # Overall efficiency (based on speed and availability)
        avg_speed = np.mean(data['speed'])
        target_speed = 150
        speed_efficiency = (avg_speed / target_speed) * 100
        
        availability = (1 - np.mean(data['downtime_events'])) * 100
        efficiency = speed_efficiency * (availability / 100)
        
        # Quality score
        quality_score = np.mean(data['quality_scores'])
        
        # Throughput (production rate relative to target)
        throughput = (np.mean(data['production_rate']) / 100) * 100
        
        return {
            'efficiency': efficiency,
            'quality_score': quality_score,
            'availability': availability,
            'throughput': throughput
        }
    
    def _create_process_chart(self, data: Dict[str, Any]) -> go.Figure:
        """Create process variables chart"""
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Temperature (°C)', 'Speed (m/min)', 'Pressure (bar)', 'Thickness (mm)'),
            vertical_spacing=0.1,
            horizontal_spacing=0.1
        )
        
        # Temperature
        fig.add_trace(
            go.Scatter(x=data['timestamps'], y=data['temperature'],
                      mode='lines', name='Temperature', line=dict(color='red')),
            row=1, col=1
        )
        
        # Speed
        fig.add_trace(
            go.Scatter(x=data['timestamps'], y=data['speed'],
                      mode='lines', name='Speed', line=dict(color='blue')),
            row=1, col=2
        )
        
        # Pressure
        fig.add_trace(
            go.Scatter(x=data['timestamps'], y=data['pressure'],
                      mode='lines', name='Pressure', line=dict(color='green')),
            row=2, col=1
        )
        
        # Thickness
        fig.add_trace(
            go.Scatter(x=data['timestamps'], y=data['thickness'],
                      mode='lines', name='Thickness', line=dict(color='purple')),
            row=2, col=2
        )
        
        fig.update_layout(
            height=400,
            showlegend=False,
            title_text="Real-time Process Variables"
        )
        
        return fig
    
    def _create_quality_chart(self, data: Dict[str, Any]) -> go.Figure:
        """Create quality metrics chart"""
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('Quality Score', 'Defect Rate'),
            vertical_spacing=0.3
        )
        
        # Quality score
        fig.add_trace(
            go.Scatter(x=data['timestamps'], y=data['quality_scores'],
                      mode='lines+markers', name='Quality Score',
                      line=dict(color='green')),
            row=1, col=1
        )
        
        # Add quality target line
        fig.add_hline(y=95, line_dash="dash", line_color="green", 
                     annotation_text="Target", row=1, col=1)
        
        # Defect rate
        fig.add_trace(
            go.Scatter(x=data['timestamps'], y=data['defect_rate'],
                      mode='lines+markers', name='Defect Rate',
                      line=dict(color='red')),
            row=2, col=1
        )
        
        fig.update_layout(
            height=400,
            showlegend=False
        )
        
        return fig
    
    def _create_production_chart(self, data: Dict[str, Any]) -> go.Figure:
        """Create production statistics chart"""
        
        # Calculate hourly production rate
        recent_rate = np.mean(data['production_rate'][-12:])  # Last 12 data points
        target_rate = 100
        
        # Create gauge chart
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = recent_rate,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Production Rate (units/hr)"},
            delta = {'reference': target_rate},
            gauge = {
                'axis': {'range': [None, 150]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 70], 'color': "lightgray"},
                    {'range': [70, 85], 'color': "yellow"},
                    {'range': [85, 150], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': target_rate
                }
            }
        ))
        
        fig.update_layout(height=400)
        
        return fig
    
    def _create_status_components(self, data: Dict[str, Any], kpis: Dict[str, float]) -> List[html.Div]:
        """Create system status components"""
        
        components = []
        
        # Overall system status
        overall_status = "NORMAL"
        status_color = "success"
        
        if any(kpis[metric] < self.kpi_thresholds[metric]['critical'] for metric in kpis):
            overall_status = "CRITICAL"
            status_color = "danger"
        elif any(kpis[metric] < self.kpi_thresholds[metric]['warning'] for metric in kpis):
            overall_status = "WARNING"
            status_color = "warning"
        
        components.append(
            dbc.Alert([
                html.H5("System Status", className="alert-heading"),
                html.Hr(),
                html.P(f"Overall Status: {overall_status}")
            ], color=status_color)
        )
        
        # Equipment status
        equipment_status = [
            {"name": "Line 1", "status": "RUNNING", "efficiency": 92},
            {"name": "Line 2", "status": "RUNNING", "efficiency": 88},
            {"name": "QC Station", "status": "RUNNING", "efficiency": 95},
            {"name": "Packaging", "status": "MAINTENANCE", "efficiency": 0}
        ]
        
        for equipment in equipment_status:
            color = "success" if equipment["status"] == "RUNNING" else "warning"
            components.append(
                dbc.ListGroupItem([
                    html.Div([
                        html.Strong(equipment["name"]),
                        dbc.Badge(equipment["status"], color=color, className="ms-2"),
                        html.Br(),
                        html.Small(f"Efficiency: {equipment['efficiency']}%")
                    ])
                ])
            )
        
        return components
    
    def _create_alerts_components(self, data: Dict[str, Any], kpis: Dict[str, float]) -> List[html.Div]:
        """Create alerts components"""
        
        alerts = []
        current_time = datetime.now()
        
        # Check for KPI-based alerts
        for metric, value in kpis.items():
            thresholds = self.kpi_thresholds[metric]
            
            if value < thresholds['critical']:
                alerts.append({
                    'time': current_time,
                    'level': 'CRITICAL',
                    'message': f'{metric.replace("_", " ").title()} below critical threshold: {value:.1f}%',
                    'color': 'danger'
                })
            elif value < thresholds['warning']:
                alerts.append({
                    'time': current_time,
                    'level': 'WARNING',
                    'message': f'{metric.replace("_", " ").title()} below warning threshold: {value:.1f}%',
                    'color': 'warning'
                })
        
        # Check for process variable alerts
        latest_temp = data['temperature'][-1]
        if latest_temp > 85:
            alerts.append({
                'time': current_time,
                'level': 'WARNING',
                'message': f'High temperature detected: {latest_temp:.1f}°C',
                'color': 'warning'
            })
        
        latest_pressure = data['pressure'][-1]
        if latest_pressure > 55:
            alerts.append({
                'time': current_time,
                'level': 'WARNING',
                'message': f'High pressure detected: {latest_pressure:.1f} bar',
                'color': 'warning'
            })
        
        # Create alert components
        components = []
        
        if not alerts:
            components.append(
                dbc.Alert("No active alerts", color="success")
            )
        else:
            for alert in alerts[-5:]:  # Show last 5 alerts
                components.append(
                    dbc.Alert([
                        html.Strong(f"{alert['level']}: "),
                        alert['message'],
                        html.Br(),
                        html.Small(alert['time'].strftime('%H:%M:%S'))
                    ], color=alert['color'])
                )
        
        return components
    
    def run(self, host: str = '127.0.0.1', port: int = 8050, debug: bool = False):
        """Run the dashboard application"""
        print(f"Starting Industrial Dashboard at http://{host}:{port}")
        self.app.run_server(host=host, port=port, debug=debug)

def create_static_dashboard(data: pd.DataFrame, 
                          save_path: str = "manufacturing_dashboard.html") -> go.Figure:
    """
    Create a static version of the manufacturing dashboard.
    
    Args:
        data: DataFrame with manufacturing data
        save_path: Path to save the dashboard
        
    Returns:
        Plotly figure with dashboard
    """
    
    # Create comprehensive dashboard with subplots
    fig = make_subplots(
        rows=3, cols=3,
        subplot_titles=(
            'Temperature Trend', 'Speed vs Target', 'Quality Score',
            'Pressure Distribution', 'Thickness Control', 'Production Rate',
            'Efficiency Gauge', 'Defect Rate', 'Correlation Matrix'
        ),
        specs=[
            [{"type": "xy"}, {"type": "xy"}, {"type": "xy"}],
            [{"type": "xy"}, {"type": "xy"}, {"type": "xy"}],
            [{"type": "indicator"}, {"type": "xy"}, {"type": "xy"}]
        ],
        vertical_spacing=0.08,
        horizontal_spacing=0.08
    )
    
    # Ensure we have required columns
    required_cols = ['temperature', 'speed', 'quality_score', 'pressure', 'thickness']
    available_cols = [col for col in required_cols if col in data.columns]
    
    if 'timestamp' in data.columns:
        data['timestamp'] = pd.to_datetime(data['timestamp'])
    else:
        data['timestamp'] = pd.date_range(start='2024-01-01', periods=len(data), freq='1min')
    
    # Row 1: Process variables
    if 'temperature' in available_cols:
        fig.add_trace(
            go.Scatter(x=data['timestamp'], y=data['temperature'],
                      mode='lines', name='Temperature'),
            row=1, col=1
        )
    
    if 'speed' in available_cols:
        fig.add_trace(
            go.Scatter(x=data['timestamp'], y=data['speed'],
                      mode='lines', name='Actual Speed'),
            row=1, col=2
        )
        fig.add_hline(y=150, line_dash="dash", line_color="red", 
                     annotation_text="Target", row=1, col=2)
    
    if 'quality_score' in available_cols:
        fig.add_trace(
            go.Scatter(x=data['timestamp'], y=data['quality_score'],
                      mode='lines+markers', name='Quality Score'),
            row=1, col=3
        )
    
    # Row 2: Statistical views
    if 'pressure' in available_cols:
        fig.add_trace(
            go.Histogram(x=data['pressure'], name='Pressure'),
            row=2, col=1
        )
    
    if 'thickness' in available_cols:
        fig.add_trace(
            go.Scatter(x=data['timestamp'], y=data['thickness'],
                      mode='lines', name='Thickness'),
            row=2, col=2
        )
        
        # Add control limits
        mean_thickness = data['thickness'].mean()
        std_thickness = data['thickness'].std()
        fig.add_hline(y=mean_thickness + 3*std_thickness, line_dash="dash", 
                     line_color="red", row=2, col=2)
        fig.add_hline(y=mean_thickness - 3*std_thickness, line_dash="dash", 
                     line_color="red", row=2, col=2)
    
    # Production rate (synthetic)
    production_rate = np.random.normal(100, 10, len(data))
    fig.add_trace(
        go.Bar(x=data['timestamp'][::10], y=production_rate[::10], 
               name='Production Rate'),
        row=2, col=3
    )
    
    # Row 3: Advanced visualizations
    
    # Efficiency gauge
    if 'speed' in available_cols:
        efficiency = (data['speed'].mean() / 150) * 100
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=efficiency,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Efficiency %"},
                gauge={'axis': {'range': [None, 120]},
                       'bar': {'color': "darkblue"},
                       'steps': [{'range': [0, 80], 'color': "lightgray"},
                               {'range': [80, 100], 'color': "yellow"},
                               {'range': [100, 120], 'color': "green"}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 90}}
            ),
            row=3, col=1
        )
    
    # Defect rate trend (synthetic)
    defect_rate = np.random.exponential(0.5, len(data))
    fig.add_trace(
        go.Scatter(x=data['timestamp'], y=defect_rate,
                  mode='lines', name='Defect Rate'),
        row=3, col=2
    )
    
    # Correlation heatmap
    if len(available_cols) > 1:
        corr_matrix = data[available_cols].corr()
        fig.add_trace(
            go.Heatmap(z=corr_matrix.values,
                      x=corr_matrix.columns,
                      y=corr_matrix.index,
                      colorscale='RdBu_r',
                      zmid=0),
            row=3, col=3
        )
    
    # Update layout
    fig.update_layout(
        height=1200,
        title_text="Manufacturing Dashboard - Comprehensive View",
        showlegend=False
    )
    
    # Save dashboard
    fig.write_html(save_path)
    
    return fig

# Example usage
def main():
    """Example usage of industrial dashboard"""
    
    # Generate sample data
    np.random.seed(42)
    n_samples = 500
    
    timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='1min')
    
    data = pd.DataFrame({
        'timestamp': timestamps,
        'temperature': 80 + np.random.normal(0, 2, n_samples),
        'speed': 150 + np.random.normal(0, 5, n_samples),
        'pressure': 50 + np.random.normal(0, 1, n_samples),
        'thickness': 12.5 + np.random.normal(0, 0.2, n_samples),
        'quality_score': 95 + np.random.normal(0, 3, n_samples)
    })
    
    print("Sample manufacturing data created")
    print(f"Shape: {data.shape}")
    
    # Create static dashboard
    print("Creating static dashboard...")
    static_fig = create_static_dashboard(data, "sample_dashboard.html")
    print("Static dashboard saved to 'sample_dashboard.html'")
    
    # Create interactive dashboard (uncomment to run)
    # print("Creating interactive dashboard...")
    # dashboard = IndustrialDashboard("Sample Manufacturing Dashboard")
    # dashboard.run(debug=True)  # Run with debug=True for development

if __name__ == "__main__":
    main()