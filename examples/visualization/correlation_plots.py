"""
Correlation Visualization Module

Comprehensive visualization tools for correlation analysis in manufacturing data.
Includes correlation matrices, heatmaps, scatter plots, and interactive visualizations.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set style for matplotlib
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class CorrelationVisualizer:
    """
    Comprehensive visualization toolkit for correlation analysis
    """
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8), dpi: int = 300):
        """
        Initialize the correlation visualizer.
        
        Args:
            figsize: Default figure size for matplotlib plots
            dpi: Resolution for saved plots
        """
        self.figsize = figsize
        self.dpi = dpi
        self.color_palettes = {
            'correlation': ['#d73027', '#f46d43', '#fdae61', '#fee08b', 
                           '#e6f598', '#abdda4', '#66c2a5', '#3288bd'],
            'manufacturing': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', 
                             '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
        }
    
    def plot_correlation_matrix(self, 
                              correlation_matrix: pd.DataFrame,
                              title: str = "Correlation Matrix",
                              save_path: Optional[str] = None,
                              interactive: bool = False) -> Optional[go.Figure]:
        """
        Create a correlation matrix heatmap.
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        if interactive:
            # Interactive plotly heatmap
            fig = go.Figure(data=go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.index,
                colorscale='RdBu_r',
                zmid=0,
                text=correlation_matrix.round(3).values,
                texttemplate="%{text}",
                textfont={"size": 10},
                hoverongaps=False
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title="Variables",
                yaxis_title="Variables",
                width=800,
                height=600
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib heatmap
            plt.figure(figsize=self.figsize, dpi=self.dpi)
            
            # Create mask for upper triangle
            mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
            
            # Generate heatmap
            sns.heatmap(
                correlation_matrix,
                mask=mask,
                annot=True,
                cmap='RdBu_r',
                center=0,
                square=True,
                linewidths=0.5,
                fmt='.3f',
                cbar_kws={"shrink": 0.8}
            )
            
            plt.title(title, fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def plot_correlation_strength_distribution(self,
                                             correlations: List[float],
                                             title: str = "Correlation Strength Distribution",
                                             save_path: Optional[str] = None) -> None:
        """
        Plot distribution of correlation strengths.
        
        Args:
            correlations: List of correlation coefficients
            title: Plot title
            save_path: Path to save the plot
        """
        plt.figure(figsize=self.figsize, dpi=self.dpi)
        
        # Create histogram
        plt.hist(correlations, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        
        # Add vertical lines for strength categories
        plt.axvline(-0.7, color='red', linestyle='--', alpha=0.7, label='Strong Negative')
        plt.axvline(-0.3, color='orange', linestyle='--', alpha=0.7, label='Weak Negative')
        plt.axvline(0.3, color='orange', linestyle='--', alpha=0.7, label='Weak Positive')
        plt.axvline(0.7, color='green', linestyle='--', alpha=0.7, label='Strong Positive')
        
        plt.xlabel('Correlation Coefficient', fontsize=12)
        plt.ylabel('Frequency', fontsize=12)
        plt.title(title, fontsize=16, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_scatter_with_correlation(self,
                                    data: pd.DataFrame,
                                    var1: str,
                                    var2: str,
                                    title: Optional[str] = None,
                                    save_path: Optional[str] = None,
                                    interactive: bool = False) -> Optional[go.Figure]:
        """
        Create scatter plot with correlation information.
        
        Args:
            data: DataFrame containing the variables
            var1: First variable name
            var2: Second variable name
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        # Clean data
        clean_data = data[[var1, var2]].dropna()
        
        if len(clean_data) == 0:
            print(f"No valid data points for {var1} vs {var2}")
            return None
        
        # Calculate correlation
        correlation = clean_data[var1].corr(clean_data[var2])
        
        if title is None:
            title = f"{var1} vs {var2} (r = {correlation:.3f})"
        
        if interactive:
            # Interactive plotly scatter
            fig = px.scatter(
                clean_data, 
                x=var1, 
                y=var2,
                title=title,
                trendline="ols",
                hover_data={var1: ':.2f', var2: ':.2f'}
            )
            
            fig.update_layout(
                width=800,
                height=600,
                showlegend=True
            )
            
            # Add correlation text
            fig.add_annotation(
                x=0.05, y=0.95,
                xref="paper", yref="paper",
                text=f"Correlation: {correlation:.3f}",
                showarrow=False,
                bgcolor="white",
                bordercolor="black",
                borderwidth=1
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib scatter
            plt.figure(figsize=self.figsize, dpi=self.dpi)
            
            plt.scatter(clean_data[var1], clean_data[var2], alpha=0.6, s=50)
            
            # Add trend line
            z = np.polyfit(clean_data[var1], clean_data[var2], 1)
            p = np.poly1d(z)
            plt.plot(clean_data[var1].sort_values(), 
                    p(clean_data[var1].sort_values()), 
                    "r--", alpha=0.8, linewidth=2)
            
            plt.xlabel(var1, fontsize=12)
            plt.ylabel(var2, fontsize=12)
            plt.title(title, fontsize=16, fontweight='bold')
            
            # Add correlation text
            plt.text(0.05, 0.95, f'Correlation: {correlation:.3f}', 
                    transform=plt.gca().transAxes, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                    fontsize=11)
            
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def plot_lag_correlation_analysis(self,
                                    lag_correlations: Dict[int, float],
                                    var1: str,
                                    var2: str,
                                    title: Optional[str] = None,
                                    save_path: Optional[str] = None,
                                    interactive: bool = False) -> Optional[go.Figure]:
        """
        Plot lag correlation analysis results.
        
        Args:
            lag_correlations: Dictionary of lag -> correlation
            var1: First variable name (leading)
            var2: Second variable name (lagging)
            title: Plot title
            save_path: Path to save the plot
            interactive: Whether to create interactive plot
            
        Returns:
            Plotly figure if interactive, None otherwise
        """
        lags = list(lag_correlations.keys())
        correlations = list(lag_correlations.values())
        
        if title is None:
            title = f"Lag Correlation Analysis: {var1} → {var2}"
        
        if interactive:
            # Interactive plotly line plot
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=lags,
                y=correlations,
                mode='lines+markers',
                name='Correlation',
                line=dict(width=3),
                marker=dict(size=8)
            ))
            
            # Highlight optimal lag
            optimal_lag = max(lag_correlations.keys(), key=lambda k: abs(lag_correlations[k]))
            optimal_corr = lag_correlations[optimal_lag]
            
            fig.add_trace(go.Scatter(
                x=[optimal_lag],
                y=[optimal_corr],
                mode='markers',
                name=f'Optimal Lag: {optimal_lag}',
                marker=dict(size=15, color='red', symbol='star')
            ))
            
            fig.add_hline(y=0, line_dash="dash", line_color="gray")
            
            fig.update_layout(
                title=title,
                xaxis_title="Lag (time periods)",
                yaxis_title="Correlation Coefficient",
                width=800,
                height=600,
                showlegend=True
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
        
        else:
            # Static matplotlib line plot
            plt.figure(figsize=self.figsize, dpi=self.dpi)
            
            plt.plot(lags, correlations, 'b-o', linewidth=2, markersize=6, label='Correlation')
            
            # Highlight optimal lag
            optimal_lag = max(lag_correlations.keys(), key=lambda k: abs(lag_correlations[k]))
            optimal_corr = lag_correlations[optimal_lag]
            
            plt.scatter([optimal_lag], [optimal_corr], 
                       color='red', s=150, marker='*', 
                       label=f'Optimal Lag: {optimal_lag}', zorder=5)
            
            plt.axhline(y=0, color='gray', linestyle='--', alpha=0.7)
            plt.xlabel('Lag (time periods)', fontsize=12)
            plt.ylabel('Correlation Coefficient', fontsize=12)
            plt.title(title, fontsize=16, fontweight='bold')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            
            plt.show()
            return None
    
    def plot_correlation_network(self,
                               correlation_matrix: pd.DataFrame,
                               threshold: float = 0.5,
                               title: str = "Correlation Network",
                               save_path: Optional[str] = None) -> go.Figure:
        """
        Create a network plot of correlations above threshold.
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            threshold: Minimum correlation strength to include
            title: Plot title
            save_path: Path to save the plot
            
        Returns:
            Plotly figure
        """
        # Extract strong correlations
        variables = correlation_matrix.columns.tolist()
        edges = []
        edge_weights = []
        
        for i, var1 in enumerate(variables):
            for j, var2 in enumerate(variables[i+1:], i+1):
                corr = correlation_matrix.loc[var1, var2]
                if abs(corr) >= threshold:
                    edges.append((var1, var2))
                    edge_weights.append(corr)
        
        if not edges:
            print(f"No correlations above threshold {threshold}")
            return go.Figure()
        
        # Create network layout (simple circular layout)
        n_vars = len(variables)
        angles = np.linspace(0, 2*np.pi, n_vars, endpoint=False)
        
        pos = {}
        for i, var in enumerate(variables):
            pos[var] = (np.cos(angles[i]), np.sin(angles[i]))
        
        # Create edge traces
        edge_traces = []
        for i, (var1, var2) in enumerate(edges):
            x0, y0 = pos[var1]
            x1, y1 = pos[var2]
            weight = edge_weights[i]
            
            color = 'red' if weight < 0 else 'blue'
            width = abs(weight) * 5
            
            edge_traces.append(go.Scatter(
                x=[x0, x1, None],
                y=[y0, y1, None],
                mode='lines',
                line=dict(width=width, color=color),
                hoverinfo='none',
                showlegend=False
            ))
        
        # Create node trace
        node_x = [pos[var][0] for var in variables]
        node_y = [pos[var][1] for var in variables]
        
        node_trace = go.Scatter(
            x=node_x,
            y=node_y,
            mode='markers+text',
            text=variables,
            textposition="middle center",
            marker=dict(
                size=30,
                color='lightblue',
                line=dict(width=2, color='black')
            ),
            hoverinfo='text',
            hovertext=variables
        )
        
        # Create figure
        fig = go.Figure(data=edge_traces + [node_trace])
        
        fig.update_layout(
            title=title,
            showlegend=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            width=800,
            height=800,
            annotations=[
                dict(
                    text=f"Threshold: {threshold}<br>Blue: Positive correlation<br>Red: Negative correlation",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.02, y=0.98,
                    bgcolor="white",
                    bordercolor="black",
                    borderwidth=1
                )
            ]
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def plot_correlation_timeline(self,
                                data: pd.DataFrame,
                                var1: str,
                                var2: str,
                                window_size: int = 50,
                                time_column: str = 'timestamp',
                                title: Optional[str] = None,
                                save_path: Optional[str] = None) -> go.Figure:
        """
        Plot how correlation changes over time using rolling windows.
        
        Args:
            data: DataFrame with time series data
            var1: First variable name
            var2: Second variable name
            window_size: Size of rolling window
            time_column: Name of timestamp column
            title: Plot title
            save_path: Path to save the plot
            
        Returns:
            Plotly figure
        """
        # Ensure data is sorted by time
        if time_column in data.columns:
            data = data.sort_values(time_column)
        
        # Calculate rolling correlation
        rolling_corr = data[var1].rolling(window=window_size).corr(data[var2])
        
        if title is None:
            title = f"Rolling Correlation: {var1} vs {var2} (Window: {window_size})"
        
        # Create time axis
        if time_column in data.columns:
            x_axis = data[time_column]
            x_title = "Time"
        else:
            x_axis = data.index
            x_title = "Index"
        
        # Create plot
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=x_axis,
            y=rolling_corr,
            mode='lines',
            name='Rolling Correlation',
            line=dict(width=2, color='blue')
        ))
        
        # Add horizontal reference lines
        fig.add_hline(y=0, line_dash="dash", line_color="gray")
        fig.add_hline(y=0.3, line_dash="dot", line_color="green", 
                     annotation_text="Weak Positive")
        fig.add_hline(y=-0.3, line_dash="dot", line_color="orange", 
                     annotation_text="Weak Negative")
        fig.add_hline(y=0.7, line_dash="dot", line_color="darkgreen", 
                     annotation_text="Strong Positive")
        fig.add_hline(y=-0.7, line_dash="dot", line_color="red", 
                     annotation_text="Strong Negative")
        
        fig.update_layout(
            title=title,
            xaxis_title=x_title,
            yaxis_title="Correlation Coefficient",
            yaxis=dict(range=[-1, 1]),
            width=1000,
            height=600,
            showlegend=True
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def create_correlation_dashboard(self,
                                   data: pd.DataFrame,
                                   correlation_matrix: pd.DataFrame,
                                   significant_correlations: List[Dict],
                                   save_path: Optional[str] = None) -> go.Figure:
        """
        Create a comprehensive correlation analysis dashboard.
        
        Args:
            data: Original data DataFrame
            correlation_matrix: Correlation matrix
            significant_correlations: List of significant correlation results
            save_path: Path to save the dashboard
            
        Returns:
            Plotly figure with subplots
        """
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Correlation Matrix', 'Correlation Distribution', 
                           'Top Correlation', 'Variable Statistics'),
            specs=[[{"type": "xy"}, {"type": "xy"}],
                   [{"type": "xy"}, {"type": "xy"}]]
        )
        
        # 1. Correlation matrix heatmap
        fig.add_trace(
            go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.index,
                colorscale='RdBu_r',
                zmid=0,
                showscale=False
            ),
            row=1, col=1
        )
        
        # 2. Correlation distribution
        # Flatten correlation matrix and remove self-correlations
        corr_values = []
        for i in range(len(correlation_matrix)):
            for j in range(i+1, len(correlation_matrix)):
                corr_values.append(correlation_matrix.iloc[i, j])
        
        fig.add_trace(
            go.Histogram(
                x=corr_values,
                nbinsx=20,
                name="Correlation Distribution"
            ),
            row=1, col=2
        )
        
        # 3. Scatter plot of top correlation
        if significant_correlations:
            top_corr = significant_correlations[0]
            var1, var2 = top_corr['variable_1'], top_corr['variable_2']
            
            clean_data = data[[var1, var2]].dropna()
            
            fig.add_trace(
                go.Scatter(
                    x=clean_data[var1],
                    y=clean_data[var2],
                    mode='markers',
                    name=f"{var1} vs {var2}",
                    opacity=0.6
                ),
                row=2, col=1
            )
        
        # 4. Variable statistics summary
        numeric_cols = data.select_dtypes(include=[np.number]).columns[:5]  # Top 5
        means = [data[col].mean() for col in numeric_cols]
        stds = [data[col].std() for col in numeric_cols]
        
        fig.add_trace(
            go.Bar(
                x=numeric_cols,
                y=means,
                name="Mean Values",
                error_y=dict(type='data', array=stds)
            ),
            row=2, col=2
        )
        
        # Update layout
        fig.update_layout(
            height=800,
            title_text="Manufacturing Data Correlation Dashboard",
            showlegend=False
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig

# Utility functions for quick plotting
def quick_correlation_plot(data: pd.DataFrame, 
                         save_dir: Optional[str] = None,
                         interactive: bool = True) -> Dict[str, Any]:
    """
    Quickly generate all correlation plots for a dataset.
    
    Args:
        data: DataFrame to analyze
        save_dir: Directory to save plots
        interactive: Whether to create interactive plots
        
    Returns:
        Dictionary with plot results
    """
    visualizer = CorrelationVisualizer()
    results = {}
    
    if save_dir:
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
    
    # Calculate correlation matrix
    numeric_data = data.select_dtypes(include=[np.number])
    if len(numeric_data.columns) < 2:
        print("Need at least 2 numeric columns for correlation analysis")
        return results
    
    correlation_matrix = numeric_data.corr()
    
    # 1. Correlation matrix
    matrix_path = str(save_path / "correlation_matrix.html") if save_dir else None
    matrix_fig = visualizer.plot_correlation_matrix(
        correlation_matrix, 
        save_path=matrix_path,
        interactive=interactive
    )
    results['correlation_matrix'] = matrix_fig
    
    # 2. Correlation distribution
    corr_values = []
    for i in range(len(correlation_matrix)):
        for j in range(i+1, len(correlation_matrix)):
            corr_values.append(correlation_matrix.iloc[i, j])
    
    if not interactive:
        dist_path = str(save_path / "correlation_distribution.png") if save_dir else None
        visualizer.plot_correlation_strength_distribution(
            corr_values,
            save_path=dist_path
        )
    
    # 3. Top correlation scatter plot
    if len(corr_values) > 0:
        # Find strongest correlation
        abs_corr_matrix = correlation_matrix.abs()
        np.fill_diagonal(abs_corr_matrix.values, 0)  # Remove self-correlations
        
        max_corr_idx = abs_corr_matrix.stack().idxmax()
        var1, var2 = max_corr_idx
        
        scatter_path = str(save_path / f"scatter_{var1}_{var2}.html") if save_dir else None
        scatter_fig = visualizer.plot_scatter_with_correlation(
            data, var1, var2,
            save_path=scatter_path,
            interactive=interactive
        )
        results['top_scatter'] = scatter_fig
    
    return results

# Example usage
def main():
    """Example usage of correlation visualization tools"""
    
    # Create sample manufacturing data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate correlated manufacturing variables
    speed = np.random.normal(150, 10, n_samples)
    temperature = 80 + 0.1 * speed + np.random.normal(0, 2, n_samples)
    thickness = 12.5 + 0.01 * speed + 0.005 * temperature + np.random.normal(0, 0.2, n_samples)
    pressure = 50 + 0.1 * speed + 0.05 * temperature + np.random.normal(0, 2, n_samples)
    quality_score = 100 - 0.1 * np.abs(speed - 150) - 0.2 * np.abs(temperature - 80) + np.random.normal(0, 1, n_samples)
    
    # Create DataFrame
    data = pd.DataFrame({
        'speed': speed,
        'temperature': temperature,
        'thickness': thickness,
        'pressure': pressure,
        'quality_score': quality_score
    })
    
    print("Sample manufacturing data created")
    print(f"Shape: {data.shape}")
    print(f"Variables: {list(data.columns)}")
    
    # Initialize visualizer
    visualizer = CorrelationVisualizer()
    
    # Calculate correlation matrix
    correlation_matrix = data.corr()
    print(f"\nCorrelation matrix shape: {correlation_matrix.shape}")
    
    # Test visualizations
    print("\n=== Testing Correlation Visualizations ===")
    
    # 1. Correlation matrix heatmap
    print("Creating correlation matrix heatmap...")
    visualizer.plot_correlation_matrix(correlation_matrix, interactive=False)
    
    # 2. Scatter plot with correlation
    print("Creating scatter plot: speed vs thickness...")
    visualizer.plot_scatter_with_correlation(data, 'speed', 'thickness', interactive=False)
    
    # 3. Generate lag correlation data and plot
    print("Creating lag correlation analysis...")
    lag_correlations = {}
    for lag in range(0, 31):
        if lag == 0:
            corr = data['speed'].corr(data['thickness'])
        else:
            lagged_speed = data['speed'].shift(lag)
            corr = lagged_speed.corr(data['thickness'])
        
        if not np.isnan(corr):
            lag_correlations[lag] = corr
    
    visualizer.plot_lag_correlation_analysis(
        lag_correlations, 'speed', 'thickness', interactive=False
    )
    
    # 4. Quick correlation analysis
    print("Running quick correlation analysis...")
    results = quick_correlation_plot(data, interactive=False)
    
    print(f"Generated {len(results)} visualizations")

if __name__ == "__main__":
    main()