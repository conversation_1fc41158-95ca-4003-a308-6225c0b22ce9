#!/usr/bin/env python3
"""
Advanced Multi-Method Correlation Analysis Example

This script demonstrates advanced usage including method selection,
convergence analysis, visualization, and manufacturing-specific insights.

Usage:
    python examples/multi_method_analysis_advanced.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.multi_correlations import MultiMethodCorrelationAnalyzer
from src.data.loader import ManufacturingDataLoader
from src.visualization.multi_plots import MultiMethodCorrelationPlotter
import pandas as pd
import numpy as np
from pathlib import Path


def analyze_manufacturing_processes(unified_data, analyzer):
    """Perform comprehensive manufacturing process analysis"""
    
    print("\n🏭 Manufacturing Process Analysis")
    print("=" * 40)
    
    # Group variables by manufacturing domain
    variable_groups = {
        'Production': [col for col in unified_data.columns if any(
            keyword in col.lower() for keyword in ['speed', 'quantity', 'accepted']
        )],
        'Quality': [col for col in unified_data.columns if any(
            keyword in col.lower() for keyword in ['thickness', 'rejected', 'scrap']
        )],
        'Operations': [col for col in unified_data.columns if any(
            keyword in col.lower() for keyword in ['stop', 'duration', 'time']
        )]
    }
    
    results_by_domain = {}
    
    for domain, variables in variable_groups.items():
        if len(variables) >= 2:
            print(f"\n📊 {domain} Variables Analysis:")
            for var in variables:
                print(f"   • {var}")
            
            try:
                domain_results = analyzer.calculate_multi_method_correlations(
                    unified_data,
                    variables=variables,
                    min_periods=30
                )
                
                results_by_domain[domain] = domain_results
                print(f"   ✅ Found {len(domain_results)} correlation pairs")
                
                # Find strongest correlations in this domain
                strongest_pairs = []
                for pair_key, result in domain_results.items():
                    avg_correlation = np.mean([
                        abs(result.pearson_correlation),
                        abs(result.spearman_correlation),
                        abs(result.kendall_correlation)
                    ])
                    strongest_pairs.append((pair_key, avg_correlation, result))
                
                # Sort by average correlation strength
                strongest_pairs.sort(key=lambda x: x[1], reverse=True)
                
                if strongest_pairs:
                    top_pair = strongest_pairs[0][2]
                    print(f"   🏆 Strongest correlation: {top_pair.variable_1} ↔ {top_pair.variable_2}")
                    print(f"      Recommended method: {top_pair.recommended_method.title()}")
                    print(f"      Convergence score: {top_pair.method_convergence_score:.6f}")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {domain}: {e}")
    
    return results_by_domain


def method_selection_analysis(results):
    """Analyze method selection patterns and recommendations"""
    
    print("\n🎯 Method Selection Analysis")
    print("=" * 35)
    
    method_recommendations = {}
    convergence_levels = {'high': [], 'medium': [], 'low': []}
    
    for pair_key, result in results.items():
        # Count method recommendations
        method = result.recommended_method
        if method not in method_recommendations:
            method_recommendations[method] = 0
        method_recommendations[method] += 1
        
        # Categorize convergence levels
        convergence = result.method_convergence_score
        if convergence >= 0.8:
            convergence_levels['high'].append(pair_key)
        elif convergence >= 0.5:
            convergence_levels['medium'].append(pair_key)
        else:
            convergence_levels['low'].append(pair_key)
    
    # Display method recommendation summary
    print("\n📈 Method Recommendation Summary:")
    total_pairs = len(results)
    for method, count in method_recommendations.items():
        percentage = (count / total_pairs) * 100
        print(f"   • {method.title()}: {count} pairs ({percentage:.1f}%)")
    
    # Display convergence analysis
    print("\n🔄 Convergence Level Distribution:")
    for level, pairs in convergence_levels.items():
        count = len(pairs)
        percentage = (count / total_pairs) * 100
        print(f"   • {level.title()} convergence: {count} pairs ({percentage:.1f}%)")
        
        if count > 0 and level == 'high':
            print("     High convergence examples:")
            for pair_key in pairs[:3]:  # Show first 3 examples
                result = results[pair_key]
                print(f"       - {result.variable_1} ↔ {result.variable_2} "
                      f"(convergence: {result.method_convergence_score:.3f})")
    
    return method_recommendations, convergence_levels


def data_quality_assessment(unified_data, results):
    """Assess data quality and its impact on correlation analysis"""
    
    print("\n🔍 Data Quality Assessment")
    print("=" * 30)
    
    # Basic data quality metrics
    total_rows = len(unified_data)
    numeric_columns = unified_data.select_dtypes(include=[np.number]).columns
    
    print(f"📊 Dataset Overview:")
    print(f"   • Total rows: {total_rows:,}")
    print(f"   • Numeric columns: {len(numeric_columns)}")
    
    # Missing data analysis
    missing_data = unified_data[numeric_columns].isnull().sum()
    missing_percentage = (missing_data / total_rows) * 100
    
    print(f"\n📉 Missing Data Analysis:")
    for col in numeric_columns:
        if missing_data[col] > 0:
            print(f"   • {col}: {missing_data[col]:,} missing ({missing_percentage[col]:.1f}%)")
    
    # Outlier analysis for key variables
    print(f"\n📊 Outlier Analysis (using IQR method):")
    for col in numeric_columns[:5]:  # Analyze first 5 numeric columns
        data = unified_data[col].dropna()
        if len(data) > 0:
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            
            outliers = ((data < (Q1 - 1.5 * IQR)) | (data > (Q3 + 1.5 * IQR))).sum()
            outlier_percentage = (outliers / len(data)) * 100
            
            print(f"   • {col}: {outliers:,} outliers ({outlier_percentage:.1f}%)")
    
    # Sample size analysis for correlations
    print(f"\n📏 Sample Size Analysis for Correlations:")
    sample_sizes = [result.sample_size for result in results.values()]
    if sample_sizes:
        print(f"   • Minimum sample size: {min(sample_sizes):,}")
        print(f"   • Maximum sample size: {max(sample_sizes):,}")
        print(f"   • Average sample size: {np.mean(sample_sizes):,.0f}")
        
        # Correlations with small sample sizes
        small_sample_pairs = [
            (pair_key, result.sample_size) 
            for pair_key, result in results.items() 
            if result.sample_size < 100
        ]
        
        if small_sample_pairs:
            print(f"   ⚠️  Pairs with small samples (<100): {len(small_sample_pairs)}")
            for pair_key, sample_size in small_sample_pairs[:3]:
                result = results[pair_key]
                print(f"       - {result.variable_1} ↔ {result.variable_2}: {sample_size} samples")


def generate_manufacturing_insights(results_by_domain):
    """Generate manufacturing-specific insights from correlation analysis"""
    
    print("\n💡 Manufacturing Insights & Recommendations")
    print("=" * 50)
    
    insights = []
    
    for domain, results in results_by_domain.items():
        print(f"\n🏭 {domain} Domain Insights:")
        
        if domain == 'Production':
            # Look for speed-quality relationships
            for pair_key, result in results.items():
                if 'speed' in result.variable_1.lower() or 'speed' in result.variable_2.lower():
                    if any(keyword in f"{result.variable_1} {result.variable_2}".lower() 
                           for keyword in ['quality', 'accepted', 'rejected']):
                        
                        # Determine correlation strength
                        avg_corr = np.mean([
                            abs(result.pearson_correlation),
                            abs(result.spearman_correlation),
                            abs(result.kendall_correlation)
                        ])
                        
                        if avg_corr > 0.3:
                            direction = "positive" if result.pearson_correlation > 0 else "negative"
                            print(f"   📈 {direction.title()} speed-quality relationship detected")
                            print(f"      Variables: {result.variable_1} ↔ {result.variable_2}")
                            print(f"      Recommended method: {result.recommended_method.title()}")
                            
                            if result.recommended_method == 'pearson':
                                insights.append("Linear speed-quality relationship suggests predictable process control")
                            elif result.recommended_method == 'spearman':
                                insights.append("Non-linear speed-quality relationship requires adaptive control")
                            else:
                                insights.append("Complex speed-quality relationship needs robust monitoring")
        
        elif domain == 'Quality':
            # Look for quality consistency patterns
            thickness_vars = [pair_key for pair_key, result in results.items() 
                             if 'thickness' in f"{result.variable_1} {result.variable_2}".lower()]
            
            if thickness_vars:
                print(f"   📏 Thickness correlation patterns detected ({len(thickness_vars)} pairs)")
                
                high_convergence_thickness = [
                    pair_key for pair_key in thickness_vars 
                    if results[pair_key].method_convergence_score > 0.8
                ]
                
                if high_convergence_thickness:
                    print(f"   ✅ {len(high_convergence_thickness)} thickness relationships show high method convergence")
                    insights.append("Thickness measurements show consistent correlation patterns - reliable quality indicators")
    
    # Summary insights
    print(f"\n🎯 Key Manufacturing Insights:")
    for i, insight in enumerate(insights, 1):
        print(f"   {i}. {insight}")
    
    if not insights:
        print("   💭 Consider running analysis on larger dataset or adjusting correlation thresholds")


def main():
    """Run advanced multi-method correlation analysis example"""
    
    print("🚀 Advanced Multi-Method Correlation Analysis")
    print("=" * 55)
    print("This example demonstrates comprehensive multi-method analysis")
    print("including method selection, convergence analysis, and manufacturing insights.")
    
    # Step 1: Load and prepare data
    print("\n📁 Step 1: Loading and Preparing Manufacturing Data")
    print("-" * 55)
    
    try:
        loader = ManufacturingDataLoader('test-data')
        datasets = loader.load_all_manufacturing_data()
        unified_data = loader.create_unified_dataset()
        
        print(f"✅ Loaded {len(datasets)} datasets")
        print(f"✅ Unified dataset: {unified_data.shape[0]:,} rows, {unified_data.shape[1]} columns")
        
        # Display data timeframe
        if 'timestamp' in unified_data.columns:
            start_time = unified_data['timestamp'].min()
            end_time = unified_data['timestamp'].max()
            print(f"📅 Data timeframe: {start_time} to {end_time}")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # Step 2: Initialize advanced analyzer
    print("\n🧮 Step 2: Initializing Advanced Multi-Method Analyzer")
    print("-" * 60)
    
    analyzer = MultiMethodCorrelationAnalyzer(
        significance_level=0.01   # More stringent significance
    )
    print("✅ Advanced multi-method analyzer initialized")
    print("   • Significance level: 0.01 (99% confidence)")
    print("   • Minimum periods: 50")
    print("   • Bootstrap samples: 100")
    
    # Step 3: Comprehensive correlation analysis
    print("\n🔍 Step 3: Comprehensive Multi-Method Analysis")
    print("-" * 50)
    
    # Get all numeric variables
    numeric_vars = unified_data.select_dtypes(include=[np.number]).columns.tolist()
    
    try:
        # Calculate correlations for all numeric variables
        all_results = analyzer.calculate_multi_method_correlations(
            unified_data,
            variables=numeric_vars,
            min_periods=50
        )
        
        print(f"✅ Calculated correlations for {len(all_results)} variable pairs")
        
    except Exception as e:
        print(f"❌ Error in correlation analysis: {e}")
        return
    
    # Step 4: Manufacturing process analysis
    results_by_domain = analyze_manufacturing_processes(unified_data, analyzer)
    
    # Step 5: Method selection analysis
    method_recommendations, convergence_levels = method_selection_analysis(all_results)
    
    # Step 6: Data quality assessment
    data_quality_assessment(unified_data, all_results)
    
    # Step 7: Advanced convergence analysis
    print("\n🔄 Step 7: Advanced Convergence Analysis")
    print("-" * 45)
    
    try:
        convergence_analysis = analyzer.analyze_method_convergence(all_results)
        
        print(f"📊 Overall convergence score: {convergence_analysis['overall_convergence_score']:.6f}")
        
        # Detailed convergence metrics
        if 'convergence_distribution' in convergence_analysis:
            dist = convergence_analysis['convergence_distribution']
            total_pairs = len(all_results)
            
            print("📈 Detailed convergence distribution:")
            print(f"   • High convergence (≥0.8): {dist.get('high_convergence_pairs', 0)}/{total_pairs}")
            print(f"   • Medium convergence (0.5-0.8): {dist.get('medium_convergence_pairs', 0)}/{total_pairs}")
            print(f"   • Low convergence (<0.5): {dist.get('low_convergence_pairs', 0)}/{total_pairs}")
            print(f"   • Overall convergence rate: {dist.get('convergence_rate', 0):.1%}")
        
    except Exception as e:
        print(f"❌ Error in convergence analysis: {e}")
    
    # Step 8: Manufacturing insights
    generate_manufacturing_insights(results_by_domain)
    
    # Step 9: Visualization (optional)
    print("\n📊 Step 9: Multi-Method Visualization")
    print("-" * 40)
    
    try:
        plotter = MultiMethodCorrelationPlotter()
        
        # Prepare results for visualization
        visualization_results = {
            'multi_method_results': {},
            'convergence_analysis': convergence_analysis
        }
        
        # Convert results to visualization format
        for pair_key, result in list(all_results.items())[:10]:  # Limit to first 10 for demo
            visualization_results['multi_method_results'][pair_key] = {
                'variable_1': result.variable_1,
                'variable_2': result.variable_2,
                'pearson_correlation': result.pearson_correlation,
                'spearman_correlation': result.spearman_correlation,
                'kendall_correlation': result.kendall_correlation,
                'method_convergence_score': result.method_convergence_score,
                'recommended_method': result.recommended_method
            }
        
        # Create output directory
        output_dir = Path('examples/output')
        output_dir.mkdir(exist_ok=True)
        
        # Generate visualizations
        print("🎨 Generating multi-method heatmaps...")
        heatmap_fig = plotter.create_multi_method_heatmaps(
            visualization_results,
            save_path=str(output_dir / 'advanced_multi_method_heatmaps.png')
        )
        
        print("📈 Generating convergence analysis plot...")
        convergence_fig = plotter.plot_method_convergence(
            visualization_results,
            save_path=str(output_dir / 'advanced_convergence_analysis.html')
        )
        
        print(f"✅ Visualizations saved to {output_dir}")
        
    except Exception as e:
        print(f"⚠️  Visualization generation skipped: {e}")
    
    # Final Summary
    print("\n🎉 Advanced Analysis Complete!")
    print("=" * 40)
    print(f"✅ Analyzed {len(all_results)} correlation pairs across multiple domains")
    print(f"✅ Compared 3 correlation methods with advanced convergence analysis")
    print(f"✅ Generated manufacturing-specific insights and recommendations")
    
    # Key statistics
    if method_recommendations:
        most_recommended = max(method_recommendations.items(), key=lambda x: x[1])
        print(f"📊 Most recommended method: {most_recommended[0].title()} ({most_recommended[1]} pairs)")
    
    high_convergence_count = len(convergence_levels.get('high', []))
    print(f"🎯 High convergence pairs: {high_convergence_count} ({high_convergence_count/len(all_results)*100:.1f}%)")
    
    print("\n💡 Advanced Analysis Benefits:")
    print("   • Comprehensive method comparison across manufacturing domains")
    print("   • Data quality assessment for reliable correlation analysis")
    print("   • Manufacturing-specific insights for process optimization")
    print("   • Advanced visualization for stakeholder communication")


if __name__ == "__main__":
    main()