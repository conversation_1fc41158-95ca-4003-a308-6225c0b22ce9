#!/usr/bin/env python3
"""
Emergency Stable Configuration

Creates a conservative, stable configuration after the NaN failure.
Prioritizes numerical stability over exploration.
"""

import json

def create_emergency_stable_config():
    """Create emergency stable configuration to fix NaN predictions"""
    
    print("🚨 CREATING EMERGENCY STABLE CONFIGURATION")
    print("=" * 60)
    print("Previous attempt failed with NaN predictions due to:")
    print("  - Learning rate too high (3e-4)")
    print("  - Insufficient gradient clipping")
    print("  - Training instability")
    print()
    
    # Ultra-conservative configuration for stability
    config = {
        "forecasting_config": {
            "input_variables": [
                # Most reliable features only (reduced set)
                "Speed", "speed_avg_5min", "speed_change_rate",
                "sm_production_rate", "fm_processing_rate",
                "production_efficiency_pct", "sm_scrap_pct", "fm_reject_pct",
                "sm_quality_efficiency", "fm_quality_efficiency",
                "time_since_last_stop", "stop_duration_previous",
                "hour_of_day", "day_of_week", "hour_sin", "hour_cos",
                "speed_stability", "speed_momentum",
                "fm_position_pct", "position_in_stack", "time_since_start",
                "is_restart_period", "has_fm_match", "production_stable"
            ],
            "target_variables": ["fm_reject_pct"],
            "forecast_horizons": [15, 60, 240],
            "lookback_window": 80,      # Moderate lookback
            "patch_size": 8,            # Standard patch size
            "patch_stride": 8,          # No overlap for stability
            "model_params": {
                "d_model": 64,          # Smaller for stability
                "num_attention_heads": 4,
                "num_hidden_layers": 2,  # Fewer layers
                "dropout": 0.3,         # Higher dropout for stability
                "head_dropout": 0.3,
                "attention_dropout": 0.2,
                "norm_type": "layer_norm",
                "activation": "relu",    # ReLU more stable than GELU
                "norm_first": False,     # Post-norm more stable
                "channel_attention": False,  # Disable for simplicity
                "scaling": "std",
                "loss": "mse"
            }
        },
        "training_config": {
            "batch_size": 16,           # Smaller batches
            "learning_rate": 5e-5,      # Much lower LR
            "max_epochs": 25,
            "early_stopping_patience": 12,
            "validation_split": 0.2,
            "test_split": 0.2,
            "save_strategy": "epoch",
            "evaluation_strategy": "epoch",
            "metric_for_best_model": "eval_loss",
            "num_workers": 0,
            "pin_memory": False,
            "model_save_path": "./models/",
            "stability_config": {
                "gradient_clipping": {
                    "enabled": True,
                    "max_norm": 0.5,        # Strong clipping
                    "norm_type": 2.0,
                    "error_if_nonfinite": True
                },
                "learning_rate_schedule": {
                    "initial_lr": 5e-5,
                    "warmup_steps": 200,    # Longer warmup
                    "decay_factor": 0.95,
                    "decay_patience": 5,
                    "min_lr": 1e-6
                },
                "mixed_precision": {
                    "enabled": False,       # Disable for maximum stability
                    "loss_scale": "dynamic"
                },
                "early_stopping": {
                    "patience": 12,
                    "min_delta": 0.01,
                    "restore_best_weights": True
                },
                "numerical_stability": {
                    "eps": 1e-8,
                    "clip_norm": True,
                    "detect_anomaly": True  # Enable anomaly detection
                }
            },
            "transfer_learning": {
                "enabled": False,
                "fallback_to_fresh": True
            }
        }
    }
    
    # Save emergency config
    output_file = "config/emergency_stable_config.json"
    with open(output_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Emergency stable configuration saved to {output_file}")
    print("\nEmergency stability measures:")
    print("  - Ultra-low learning rate (5e-5)")
    print("  - Strong gradient clipping (0.5)")
    print("  - Smaller batch size (16)")
    print("  - Fewer input features (23 vs 36)")
    print("  - Conservative architecture (64 d_model, 2 layers)")
    print("  - High dropout (0.3) for regularization")
    print("  - ReLU activation for stability")
    print("  - Anomaly detection enabled")
    print("  - No mixed precision")
    
    return config

def main():
    """Create emergency stable configuration"""
    
    print("🆘 EMERGENCY MODEL STABILIZATION")
    print("=" * 80)
    print("The final optimized config caused NaN predictions.")
    print("Creating emergency stable configuration...")
    print()
    
    # Create emergency stable config
    config = create_emergency_stable_config()
    
    print("\n🧪 TESTING COMMAND:")
    print("python train.py fm_reject_pct --config config/emergency_stable_config.json")
    
    print("\n📊 EXPECTED STABLE RESULTS:")
    print("  1. NO NaN predictions (critical requirement)")
    print("  2. Finite evaluation loss")
    print("  3. Prediction variance > 1e-6 (better than constant)")
    print("  4. MSE < 10 (reasonable performance)")
    print("  5. Stable gradients throughout training")
    
    print("\n⚖️ TRADE-OFFS:")
    print("  ✅ Stability: Maximum priority")
    print("  ⚠️  Performance: May be lower than previous best")
    print("  ✅ Reliability: High confidence in training completion")
    print("  ✅ Gradients: Well-controlled with strong clipping")
    
    print("\n🎯 SUCCESS CRITERIA:")
    print("  1. Training completes without NaN")
    print("  2. Predictions are finite numbers")
    print("  3. Model learns some variance patterns")
    print("  4. Stable enough for production use")

if __name__ == "__main__":
    main()