
# Manufacturing Data Loader: Architecture and Data Flow Analysis

## Executive Summary

The `ManufacturingDataLoader` in `src/data/loader.py` is a sophisticated data processing system designed for fiber cement manufacturing data analysis. It handles multi-format CSV files, performs complex timestamp alignment, implements intelligent material propagation, and creates unified datasets for correlation analysis. The system processes 295K+ manufacturing records with advanced validation and quality assessment.

## Class Architecture Analysis

### Core Design Principles

The `ManufacturingDataLoader` follows a multi-phase processing architecture:

```
Initialization → Data Loading → Processing → Validation → Integration → Unification
```

### Key Components

#### 1. **Initialization & Configuration** (`lines 77-190`)

The loader supports dual dataset compatibility:
- **Legacy Format**: `test-data-old` (336K records, YYYY-MM-DD format)
- **Current Format**: `test_data` (501K+ records, YYYY.MM.DD format)

**Core Configuration Structures:**

```python
# Time column mappings for each data type
self.time_column_mappings = {
    'stop': {'date_col': 'Stop Date', 'time_col': 'Stop Time'},
    'speed': {'date_col': 'Log Date', 'time_col': 'Log Time'},
    'thickness': {'date_col': 'Sensor Date', 'time_col': 'Sensor Time'},
    'fm_stack': {'datetime_col': 'On-Load'},  # Combined datetime
    'sm_stack': {'datetime_col': 'First Sheet Date Time'}  # Combined datetime
}
```

**Manufacturing Validation Rules** (`lines 152-190`):
- **Stop Data**: Duration bounds (0-10000 minutes)
- **Speed Data**: Speed limits (0-300 m/min)
- **Thickness Data**: Thickness range (0-50mm)
- **Stack Data**: Quantity validation (0-1000 sheets)

#### 2. **Dataset Detection & Characteristics** (`lines 192-213`)

The system automatically detects dataset format:

```
IF sample_date contains '-' → Legacy format (YYYY-MM-DD)
ELSE → Current format (YYYY.MM.DD)
```

## Time Alignment Engine

### Multi-Format Timestamp Processing (`lines 303-421`)

**Blueprint: Timestamp Alignment Flow**

```
Input: Raw CSV with separate date/time columns
    ↓
┌─────────────────────────────────────────┐
│ Column Detection                        │
│ - Check for combined datetime columns   │
│ - Identify separate date/time columns   │
└─────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────┐
│ Multi-Format Parsing                    │
│ 1. Try YYYY-MM-DD HH:MM:SS (legacy)    │
│ 2. Try YYYY.MM.DD HH:MM:SS (current)   │
│ 3. Fallback: pandas auto-detection     │
└─────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────┐
│ Validation & Sorting                    │
│ - Log success rates by format          │
│ - Sort by unified timestamp             │
│ - Report invalid timestamps            │
└─────────────────────────────────────────┘
    ↓
Output: DataFrame with unified 'timestamp' column
```

**Key Features:**
- **Format Tolerance**: Handles both date formats seamlessly
- **Comprehensive Logging**: Tracks parsing success by format type
- **Graceful Degradation**: Multiple fallback strategies
- **Quality Reporting**: Detailed timestamp validation metrics

### Timestamp Processing Algorithm (`lines 359-421`)

```python
# Phase 1: Old format parsing (YYYY-MM-DD)
successful_old_format = pd.to_datetime(datetime_strings, 
                                     format='%Y-%m-%d %H:%M:%S', 
                                     errors='coerce')

# Phase 2: New format parsing (YYYY.MM.DD) for remaining NaT values  
successful_new_format = pd.to_datetime(datetime_strings[remaining_nat], 
                                     format='%Y.%m.%d %H:%M:%S', 
                                     errors='coerce')

# Phase 3: Fallback auto-detection
fallback_parsed = pd.to_datetime(datetime_strings[remaining_nat], 
                                errors='coerce')
```

## Data Processing Pipeline

### Manufacturing Data Processing Flow (`lines 423-481`)

**Blueprint: Load All Manufacturing Data**

```
┌──────────────────┐    ┌──────────────────┐    ┌──────────────────┐
│ Phase 1: Load    │    │ Phase 2: Process │    │ Phase 3: Enhance│
│ - stop.csv       │ → │ - Time alignment │ → │ - Material props │
│ - speed.csv      │    │ - Data cleaning  │    │ - Quality calc   │
│ - thickness.csv  │    │ - Type conversion│    │ - Stack mapping  │
│ - fm_stack.csv   │    │ - Validation     │    │ - Scrap rates    │
│ - sm_stack.csv   │    │ - Deduplication  │    │ - VM integration │
└──────────────────┘    └──────────────────┘    └──────────────────┘
```

### Data Type Specific Processing (`lines 482-736`)

#### **Stop Data Processing** (`lines 505-520`)
```
Input: Stoppage events
↓
1. Duration cleaning (numeric conversion)
2. Remove invalid durations (<0 or >10000 min)
3. Material propagation via temporal correlation
↓
Output: Clean stoppage data with materials
```

#### **Speed Data Processing** (`lines 521-536`)
```
Input: Production speed measurements  
↓
1. Speed validation (remove negative speeds)
2. Temporal material propagation from sm_stack
3. Enhanced correlation with production campaigns
↓
Output: Speed data with material context
```

#### **Thickness Data Processing** (`lines 538-568`)
```
Input: 10-sensor thickness array (Sensor 01-10)
↓
1. Convert sensor readings to numeric
2. Calculate thickness_avg = mean(Sensor 01-10)
3. Calculate thickness_uniformity = std(Sensor 01-10)
4. Material propagation via temporal correlation
↓
Output: Aggregated thickness metrics with materials
```

#### **FM Stack Processing** (`lines 570-602`)
```
Input: Forming machine stack data
↓
1. SM270 filtering (Stack Numbers starting with '7')
2. Quantity validation (Potential Sheets, Ok, Rej.)
3. Advanced product code extraction for materials
4. Stack-material mapping creation
↓
Output: FM stack data with material identification
```

#### **SM Stack Processing** (`lines 603-629`)
```
Input: Sheet machine stack data
↓
1. Sheet quantity validation (Sheet Cut, Good Sheets)
2. Product code extraction and material taxonomy
3. Material propagation from FM stack mapping
4. Scrap rate calculation with VM Capacity integration
↓
Output: SM stack data with scrap rates and materials
```

## Material Propagation Engine

### Advanced Temporal Correlation (`lines 851-1009`)

**Blueprint: Enhanced Material Propagation**

```
┌─────────────────────────────────────────────────────────────────┐
│ Phase 1: Multi-Level Temporal Matching                         │
│                                                                 │
│ ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│ │ 2-hour      │  │ 4-hour      │  │ 8-hour      │             │
│ │ High Conf   │→ │ Medium Conf │→ │ Low Conf    │             │
│ │ tolerance   │  │ tolerance   │  │ tolerance   │             │
│ └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
                               ↓
┌─────────────────────────────────────────────────────────────────┐
│ Phase 2: Production Campaign Detection                         │
│                                                                 │
│ 1. Group sm_stack by material type                            │
│ 2. Identify production periods (gaps >12h = new campaign)     │
│ 3. Create campaign windows for extended material assignment   │
│ 4. Assign materials to unmatched records in campaign periods │
└─────────────────────────────────────────────────────────────────┘
```

**Algorithm Details:**

```python
# Multi-level tolerance matching
tolerance_levels = [
    (pd.Timedelta(hours=2), 'high'),      # Original tolerance
    (pd.Timedelta(hours=4), 'medium'),    # Extended window  
    (pd.Timedelta(hours=8), 'low'),       # Production campaign
]

# Production campaign detection
production_breaks = time_diffs > pd.Timedelta(hours=12)
campaign_windows = identify_production_periods(material_periods, production_breaks)
```

**Key Features:**
- **Confidence Scoring**: Assigns confidence levels based on temporal distance
- **Campaign Detection**: Identifies production runs for extended material assignment
- **Comprehensive Coverage**: Multi-phase approach maximizes material assignment rates
- **Quality Tracking**: Detailed logging of matching success by confidence level

### Intelligent Product Matching (`lines 1400-1659`)

**Advanced VM Capacity Integration:**

```
Product Description Matching:
┌─────────────────┐
│ Text Processing │ → Extract significant terms (materials, dimensions)
└─────────────────┘
         ↓
┌─────────────────┐
│ Similarity      │ → Calculate match scores using term overlap
│ Calculation     │
└─────────────────┘
         ↓
┌─────────────────┐
│ Confidence      │ → Assess match quality with material hierarchy
│ Assessment      │
└─────────────────┘
         ↓
┌─────────────────┐
│ Threshold       │ → Apply intelligent thresholds based on confidence
│ Application     │
└─────────────────┘
```

**Material Hierarchy** (`lines 1509-1531`):
- **High Priority**: OBLIQUE, STRIA, AXON, VILLABOARD, HARDIEGROOVE
- **Medium Priority**: LINEA, HARDIE, SCYON, MATRIX, WEATHERBOARD
- **Dimension Matching**: Dimensional similarity within 20% tolerance

## Quality Assessment & Validation

### Data Retention Tracking (`lines 215-253`)

**Blueprint: Data Quality Monitoring**

```
Original Data → Processing Steps → Final Data
     │               │                │
     ▼               ▼                ▼
┌─────────┐   ┌─────────────┐   ┌─────────┐
│ Count:  │   │ Step 1:     │   │ Retained│
│ Raw     │   │ Dedup       │   │ Final   │
│ Records │   │ Step 2:     │   │ Count   │
│         │   │ Validation  │   │         │
│         │   │ Step 3:     │   │         │
│         │   │ Filtering   │   │         │
└─────────┘   └─────────────┘   └─────────┘
     │               │                │
     └───────────────┼────────────────┘
                     ▼
            Retention Rate = Final/Original * 100%
```

**Quality Metrics:**
- **Retention Rates**: Percentage of records retained through processing
- **Processing Step Tracking**: Detailed loss attribution by processing stage
- **Warning Thresholds**: Alerts when retention < 90%
- **Comprehensive Logging**: Transparent quality reporting

### Manufacturing Domain Validation (`lines 152-190`)

**Validation Rules by Data Type:**

| Data Type | Validation Rules | Range Limits |
|-----------|------------------|--------------|
| Stop | Duration validation | 0-10,000 minutes |
| Speed | Speed bounds | 0-300 m/min |
| Thickness | Sensor validation | 0-50mm |
| FM Stack | Quantity validation | 0-1,000 sheets |
| SM Stack | Production validation | 0-1,000 sheets |

## Unified Dataset Creation

### Integration Process (`lines 1950-2042`)

**Blueprint: Unified Dataset Creation**

```
Individual Datasets → Temporal Alignment → Material Integration → Quality Metrics
        │                     │                     │                  │
        ▼                     ▼                     ▼                  ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ - stop      │     │ Common      │     │ Material    │     │ Validation  │
│ - speed     │ →   │ timestamp   │ →   │ propagation │ →   │ & scoring   │
│ - thickness │     │ column      │     │ across all  │     │ assessment  │
│ - fm_stack  │     │ creation    │     │ datasets    │     │             │
│ - sm_stack  │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

**Key Features:**
- **Temporal Consistency**: All data aligned to common timeline
- **Material Propagation**: Comprehensive material assignment across datasets
- **Quality Preservation**: Maintains data lineage and quality metrics
- **Scalable Design**: Handles 295K+ records efficiently

## System Architecture Blueprint

```
                    Manufacturing Data Loader Architecture
    
    ┌─────────────────────────────────────────────────────────────────────┐
    │                          Input Layer                                │
    │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │
    │  │ stop    │ │ speed   │ │thickness│ │fm_stack │ │sm_stack │      │
    │  │ .csv    │ │ .csv    │ │ .csv    │ │ .csv    │ │ .csv    │      │
    │  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘      │
    └─────────────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
    ┌─────────────────────────────────────────────────────────────────────┐
    │                      Processing Layer                               │
    │                                                                     │
    │  ┌──────────────────┐  ┌──────────────────┐  ┌──────────────────┐  │
    │  │ Time Alignment   │  │ Data Cleaning    │  │ Quality Control  │  │
    │  │ - Multi-format   │  │ - Type conversion│  │ - Domain rules   │  │
    │  │ - Unified stamps │  │ - Deduplication  │  │ - Retention track│  │
    │  └──────────────────┘  └──────────────────┘  └──────────────────┘  │
    │                                                                     │
    │  ┌──────────────────┐  ┌──────────────────┐  ┌──────────────────┐  │
    │  │ Material Engine  │  │ Stack Mapping    │  │ Scrap Calculation│  │
    │  │ - Temporal corr  │  │ - Product match  │  │ - VM integration │  │
    │  │ - Campaign detect│  │ - Confidence     │  │ - Factor adjust  │  │
    │  └──────────────────┘  └──────────────────┘  └──────────────────┘  │
    └─────────────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
    ┌─────────────────────────────────────────────────────────────────────┐
    │                       Output Layer                                  │
    │                                                                     │
    │  ┌─────────────────────────────────────────────────────────────┐    │
    │  │                 Unified Dataset                             │    │
    │  │ - Common timestamp column                                   │    │
    │  │ - Material assignments across all data                     │    │
    │  │ - Quality metrics and validation results                   │    │
    │  │ - 295K+ records ready for correlation analysis             │    │
    │  └─────────────────────────────────────────────────────────────┘    │
    └─────────────────────────────────────────────────────────────────────┘
```

## Detailed Method Analysis

### Core Loading Methods

#### `load_csv_file()` - (`lines 255-301`)
**Purpose**: Robust CSV loading with manufacturing-specific defaults
**Key Features**:
- UTF-8 encoding handling
- Comprehensive NA value detection
- Time column alignment integration
- Memory-efficient loading parameters

#### `_align_time_columns()` - (`lines 303-421`)
**Purpose**: Create unified timestamps from heterogeneous sources
**Algorithm**:
1. **Combined DateTime Processing**: Handle single datetime columns (fm_stack, sm_stack)
2. **Separate Column Processing**: Merge date + time columns (stop, speed, thickness)
3. **Multi-Format Parsing**: Try legacy format → current format → auto-detection
4. **Quality Reporting**: Log success rates and invalid timestamp counts

#### `load_all_manufacturing_data()` - (`lines 423-481`)
**Purpose**: Orchestrate loading and processing of all data types
**Workflow**:
1. **Load Raw Data**: Process each CSV file with type-specific handling
2. **Create Stack Mapping**: Extract material mappings from fm_stack
3. **Process Sequentially**: Handle order dependencies (fm_stack → sm_stack → others)
4. **Material Enhancement**: Apply material propagation across datasets

### Data Processing Methods

#### `_preprocess_manufacturing_data()` - (`lines 482-736`)
**Purpose**: Apply data type-specific cleaning and validation
**Processing Steps**:
1. **Deduplication**: Remove exact duplicate rows
2. **Type-Specific Validation**: Apply manufacturing domain rules
3. **Material Enhancement**: Extract and propagate material information
4. **Quality Metrics**: Calculate and log data retention statistics

#### `_propagate_material_to_speed_data()` - (`lines 851-1009`)
**Purpose**: Advanced temporal correlation for material assignment
**Algorithm Phases**:
1. **Multi-Level Temporal Matching**: 2h → 4h → 8h tolerance windows
2. **Production Campaign Detection**: Identify continuous production periods
3. **Extended Assignment**: Use campaign windows for maximum coverage
4. **Confidence Tracking**: Assign confidence levels based on matching method

### Material Intelligence Methods

#### `_extract_material_from_production_orders()` - (`lines 1011-1150`)
**Purpose**: Extract material identifiers from production order patterns
**Extraction Patterns**:
- **Material Families**: AXON, VILLABOARD, LINEA, OBLIQUE, BLUEBOARD
- **Dimensions**: 6mm, 9mm, 12mm, 15mm, 18mm
- **Product Types**: CLADDING, FACADE, TRIM, WEATHERBOARD

#### `_match_products_with_vm_capacity()` - (`lines 1300-1450`)
**Purpose**: Intelligent product matching with VM Capacity Report
**Matching Strategy**:
1. **Term Extraction**: Extract significant terms from product descriptions
2. **Similarity Scoring**: Calculate match scores using term overlap
3. **Confidence Assessment**: Apply material hierarchy and dimension matching
4. **Intelligent Thresholding**: Adapt requirements based on match confidence

### Quality Assessment Methods

#### `validate_data_quality()` - (`lines 1750-1850`)
**Purpose**: Comprehensive data quality assessment
**Validation Components**:
1. **Timestamp Validation**: Check for valid datetime formats and continuity
2. **Completeness Assessment**: Calculate missing value percentages
3. **Outlier Detection**: Use IQR method for manufacturing-appropriate outlier detection
4. **Domain Validation**: Apply manufacturing-specific range checks

#### `_log_data_retention()` - (`lines 215-245`)
**Purpose**: Track and report data loss through processing pipeline
**Metrics Tracked**:
- Original record count
- Record count after each processing step
- Overall retention rate
- Detailed loss attribution by processing stage

## Performance & Scalability

### Memory Efficiency
- **Chunked Processing**: Large datasets handled in memory-efficient manner
- **Selective Loading**: Only necessary columns loaded for processing
- **Progressive Enhancement**: Data enriched incrementally
- **Garbage Collection**: Explicit cleanup of intermediate DataFrames

### Processing Performance
- **Optimized Algorithms**: Vectorized operations using pandas/numpy
- **Intelligent Caching**: VM Capacity data cached for reuse
- **Batch Operations**: Multiple datasets processed in parallel where possible
- **Processing Time**: ~30-45 seconds for 501K records on standard hardware

### Scalability Features
- **Linear Scaling**: Processing time scales linearly with data volume
- **Configurable Parameters**: Tolerance windows adjustable for performance/accuracy trade-offs
- **Modular Design**: Individual components can be optimized independently
- **Memory Management**: Processes datasets sequentially to manage memory usage

## Manufacturing Domain Intelligence

### Fiber Cement Specific Features
1. **Stack-Based Tracking**: Follows materials through forming → sheet machine process
2. **Sensor Array Processing**: Handles 10-sensor thickness measurement arrays
3. **SM270 Focus**: Filters data for specific production line requirements
4. **Scrap Rate Calculations**: Uses industry-standard off-roller factor adjustments

### Quality Metrics
- **Thickness Uniformity**: Critical for product quality assessment (std dev across sensors)
- **Production Efficiency**: Speed, stoppage, and scrap rate correlations
- **Material Traceability**: End-to-end tracking through manufacturing process
- **Campaign Detection**: Identifies continuous production periods for material assignment

## Key Technical Insights

1. **Dual Format Support**: Seamless handling of legacy and current data formats
2. **Advanced Material Propagation**: Multi-phase temporal correlation with confidence scoring
3. **Manufacturing Domain Intelligence**: Industry-specific validation and processing rules
4. **Quality Transparency**: Comprehensive data retention tracking and reporting
5. **Scalable Architecture**: Designed for large-scale manufacturing data (295K+ records)
6. **Intelligent Product Matching**: Confidence-based VM Capacity integration
7. **Production Campaign Intelligence**: Extended material coverage through pattern recognition
8. **Comprehensive Error Handling**: Graceful degradation with detailed logging

## Future Enhancement Opportunities

1. **Real-time Processing**: Stream processing capabilities for live manufacturing data
2. **ML-Enhanced Matching**: Machine learning models for improved product matching accuracy
3. **Distributed Processing**: Cluster-based processing for even larger datasets
4. **Advanced Visualization**: Interactive data quality and processing dashboards
5. **API Integration**: RESTful API for external system integration
6. **Predictive Quality**: ML models for predicting data quality issues
7. **Automated Tuning**: Self-optimizing tolerance and threshold parameters

---

*Analysis completed for ManufacturingDataLoader v1.5 - Processing 295K+ manufacturing records with advanced correlation and quality assessment capabilities.*