{"model_metadata": {"num_input_channels": 56, "context_length": 240, "prediction_length": 240, "patch_length": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "created_at": "2025-07-14T19:20:36.805676"}, "training_history": {"target_variable": "sm_scrap_pct", "training_loss": 0.5286116123199462, "epochs_trained": 20, "training_time": 88.3902, "prepared_data_metadata": {"total_samples": 122466, "train_samples": 85390, "val_samples": 18298, "test_samples": 18299, "sequence_length": 240, "feature_count": 56}, "gradient_norms": [0.0038104925770312548, 0.008724622428417206, 0.0023685337509959936, 0.0025745669845491648, 0.00171743705868721, 0.002920198254287243, 0.0026257121935486794, 0.005472022574394941, 0.021519474685192108, 0.0035571998450905085, 0.0033930293284356594, 0.00618192832916975, 0.013213460333645344, 0.007135852240025997, 0.002977107185870409, 0.0031453240662813187, 0.003585029859095812, 0.004854185506701469, 0.002827425254508853, 0.002827291376888752], "losses": [0.43266817927360535, 0.6405671834945679, 0.39260247349739075, 0.36515524983406067, 0.4188106656074524, 0.4724082052707672, 0.5625134110450745, 0.41335996985435486, 1.0230157375335693, 0.7154159545898438, 0.6995733380317688, 0.5084731578826904, 0.594182550907135, 0.4485892951488495, 0.48164355754852295, 0.5500165820121765, 0.45480626821517944, 0.5429672598838806, 0.3357129991054535, 0.519750714302063], "stability_scores": [0.4980713499832855, 0.5036349990748104, 0.5102035453923632, 0.5150691193847378, 0.5138966190474736, 0.5132920106196553, 0.5135811235676159, 0.5156373657448989, 0.5148760946969219, 0.5142741324880316]}, "best_model_metrics": {"eval_loss": Infinity, "eval_runtime": 25.3697, "target_variable": "sm_scrap_pct"}, "forecast_config": {"input_variables": ["Speed", "speed_avg_5min", "speed_change_rate", "speed_avg", "speed_std", "speed_cv", "sm_production_rate", "fm_processing_rate", "production_efficiency_pct", "capacity_utilization_pct", "sm_scrap_pct", "fm_reject_pct", "fm_reject_pct_calculated", "sm_quality_efficiency", "fm_quality_efficiency", "sm_good_sheets", "fm_ok_sheets", "fm_reject_sheets", "time_since_last_stop", "stop_duration_previous", "stops_last_hour", "total_stop_duration_minutes", "stops_during_production", "restart_impact_score", "hour_of_day", "day_of_week", "hour_sin", "hour_cos", "speed_stability", "speed_momentum", "speed_acceleration", "sm_duration_minutes", "fm_duration_minutes", "speed_deviation_pct", "hourly_avg_scrap", "scrap_vs_hourly_avg", "shift_avg_efficiency", "efficiency_vs_shift_avg", "scrap_trend_5min", "scrap_trend_direction", "month", "quarter", "week_of_year", "sm_to_fm_gap_minutes", "match_quality_score", "fm_position_pct", "position_in_stack", "stack_progress_pct", "time_since_start", "sheet_sequence", "is_restart_period", "has_fm_match", "is_weekend", "stack_change", "quality_improving", "production_stable", "product_type_code", "product_thickness_mm", "product_length_mm", "product_width_mm", "product_area_m2", "thickness_category", "size_category", "product_complexity", "is_cladding", "is_weatherboard", "is_villaboard", "is_specialty"], "target_variables": ["Speed", "sm_scrap_pct", "fm_reject_pct", "production_efficiency_pct", "capacity_utilization_pct"], "forecast_horizons": [15, 60, 240], "lookback_window": 240, "patch_size": 16, "patch_stride": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"speed_range": [0.0, 100.0], "scrap_rate_range": [0.0, 1.0], "efficiency_range": [0.0, 1.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05, "efficiency_threshold": 0.85, "production_rate_range": [0.0, 1000.0], "stop_duration_range": [0.0, 480.0], "restart_grace_period": 15.0, "quality_control": {"sm_scrap_max": 0.15, "fm_reject_max": 0.15, "efficiency_min": 0.7}, "operational_limits": {"max_stop_duration": 240.0, "max_restart_impact": 1.0, "speed_variance_threshold": 0.2}, "forecasting_constraints": {"min_forecast_horizon": 5, "max_forecast_horizon": 480, "lookback_min": 60, "stability_window": 30}}}, "training_config": {"batch_size": 16, "learning_rate": 5e-05, "max_epochs": 50, "early_stopping_patience": 15, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "steps", "evaluation_strategy": "steps", "metric_for_best_model": "eval_loss", "num_workers": 2, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 1.0, "norm_type": 2.0, "error_if_nonfinite": false}, "learning_rate_schedule": {"initial_lr": 5e-05, "warmup_steps": 100, "decay_factor": 0.95, "decay_patience": 5, "min_lr": 1e-06}, "mixed_precision": {"enabled": true, "loss_scale": "dynamic", "init_scale": 65536.0, "growth_factor": 2.0, "backoff_factor": 0.5, "growth_interval": 2000}, "early_stopping": {"patience": 15, "min_delta": 0.001, "restore_best_weights": true, "baseline": null}, "stability_monitoring": {"enabled": true, "log_frequency": 10, "save_frequency": 100, "instability_threshold": 0.2, "intervention_patience": 5}}, "use_stability_features": true, "gradient_clip_norm": 1.0, "adaptive_clipping": true, "warmup_ratio": 0.1, "weight_decay": 0.01, "gradient_accumulation_steps": 2, "fp16": true, "fp16_opt_level": "O1", "logging_steps": 10, "eval_steps": 50, "save_steps": 100}}