{"model_metadata": {"num_input_channels": 6, "context_length": 240, "prediction_length": 240, "patch_length": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "created_at": "2025-07-09T08:11:46.310155"}, "training_history": {"target_variable": "fm_stack_Total Sheet Rejected", "training_loss": 0.36762688159942625, "epochs_trained": 20, "training_time": 460.3373, "prepared_data_metadata": {"total_samples": 262028, "train_samples": 183084, "val_samples": 39232, "test_samples": 39233, "sequence_length": 240, "feature_count": 6}, "gradient_norms": [0.007489407900720835, 0.0023952354677021503, 0.010593992657959461, 0.0032895018812268972, 0.008216779679059982, 0.013129260390996933, 0.0047920821234583855, 0.007345207966864109, 0.006301976274698973, 0.0036759742069989443, 0.0062531582079827785, 0.002708104904741049, 0.014392228797078133, 0.005960339680314064, 0.010494323447346687, 0.02088199369609356, 0.0010494047310203314, 0.002910116920247674, 0.0064817373640835285, 0.0032673992682248354], "losses": [0.3738921284675598, 0.4733593165874481, 0.36622968316078186, 0.1691173017024994, 0.1650325208902359, 0.16864453256130219, 0.862186849117279, 0.56106036901474, 0.15622983872890472, 0.26462405920028687, 0.2848801910877228, 0.36252644658088684, 0.15011166036128998, 0.4459216296672821, 0.2752392888069153, 0.14730754494667053, 0.5536271929740906, 0.17603076994419098, 0.5452623963356018, 0.8512539267539978], "stability_scores": [0.5742771688120257, 0.5679067577265612, 0.561432985727233, 0.5636002902468508, 0.5668080999518375, 0.550087460586007, 0.5424803364827664, 0.5395423691535909, 0.5414762720193843, 0.5395935276383861]}, "best_model_metrics": {"eval_loss": Infinity, "eval_runtime": 67.4963, "target_variable": "fm_stack_Total Sheet Rejected"}, "forecast_config": {"input_variables": ["thickness_thickness_avg", "thickness_thickness_uniformity", "speed_Speed", "stop_MPS Stop Duration", "fm_stack_Sheet Quantity", "fm_stack_Sheet Acceped"], "target_variables": ["thickness_thickness_avg", "speed_Speed", "fm_stack_Total Sheet Rejected"], "forecast_horizons": [15, 60, 240], "lookback_window": 240, "patch_size": 16, "patch_stride": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"thickness_range": [2.0, 20.0], "speed_range": [0.0, 100.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05}}, "training_config": {"batch_size": 16, "learning_rate": 5e-05, "max_epochs": 50, "early_stopping_patience": 15, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "steps", "evaluation_strategy": "steps", "metric_for_best_model": "eval_loss", "num_workers": 2, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 1.0, "norm_type": 2.0, "error_if_nonfinite": false}, "learning_rate_schedule": {"initial_lr": 5e-05, "warmup_steps": 100, "decay_factor": 0.95, "decay_patience": 5, "min_lr": 1e-06}, "mixed_precision": {"enabled": true, "loss_scale": "dynamic", "init_scale": 65536.0, "growth_factor": 2.0, "backoff_factor": 0.5, "growth_interval": 2000}, "early_stopping": {"patience": 15, "min_delta": 0.001, "restore_best_weights": true, "baseline": null}, "stability_monitoring": {"enabled": true, "log_frequency": 10, "save_frequency": 100, "instability_threshold": 0.2, "intervention_patience": 5}}, "use_stability_features": true, "gradient_clip_norm": 1.0, "adaptive_clipping": true, "warmup_ratio": 0.1, "weight_decay": 0.01, "gradient_accumulation_steps": 2, "fp16": true, "fp16_opt_level": "O1", "logging_steps": 10, "eval_steps": 50, "save_steps": 100}}