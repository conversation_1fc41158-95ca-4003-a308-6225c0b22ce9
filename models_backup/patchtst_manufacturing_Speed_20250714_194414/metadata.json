{"model_metadata": {"num_input_channels": 56, "context_length": 240, "prediction_length": 240, "patch_length": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "created_at": "2025-07-14T19:16:01.212908"}, "training_history": {"target_variable": "Speed", "training_loss": 0.5167008399963379, "epochs_trained": 20, "training_time": 92.0007, "prepared_data_metadata": {"total_samples": 122466, "train_samples": 85390, "val_samples": 18298, "test_samples": 18299, "sequence_length": 240, "feature_count": 56}, "gradient_norms": [0.004126827232539654, 0.008822847157716751, 0.0012244576355442405, 0.0028452975675463676, 0.00185541738756001, 0.002563198795542121, 0.004249190911650658, 0.00587915163487196, 0.0214124396443367, 0.003317485796287656, 0.004327026661485434, 0.006193786393851042, 0.011986196041107178, 0.007811928633600473, 0.003945632372051477, 0.0028963182121515274, 0.004049355164170265, 0.006018845364451408, 0.0026626274921000004, 0.003206915222108364], "losses": [0.4198112189769745, 0.6162546277046204, 0.3855506181716919, 0.3525579273700714, 0.4192552864551544, 0.4690491855144501, 0.5467646718025208, 0.40906190872192383, 0.9679735898971558, 0.7076483368873596, 0.6696985363960266, 0.49542829394340515, 0.5881507396697998, 0.4472223222255707, 0.4703885316848755, 0.5524498224258423, 0.45600929856300354, 0.5147889256477356, 0.3285489082336426, 0.5174042582511902], "stability_scores": [0.5019679306392352, 0.5072757225824243, 0.5141658325278322, 0.5194804688140591, 0.5198395135163686, 0.5185289912888822, 0.5193323960305273, 0.5224201578794212, 0.5210953684837288, 0.5208203222024751]}, "best_model_metrics": {"eval_loss": Infinity, "eval_runtime": 25.8658, "target_variable": "Speed"}, "forecast_config": {"input_variables": ["Speed", "speed_avg_5min", "speed_change_rate", "speed_avg", "speed_std", "speed_cv", "sm_production_rate", "fm_processing_rate", "production_efficiency_pct", "capacity_utilization_pct", "sm_scrap_pct", "fm_reject_pct", "fm_reject_pct_calculated", "sm_quality_efficiency", "fm_quality_efficiency", "sm_good_sheets", "fm_ok_sheets", "fm_reject_sheets", "time_since_last_stop", "stop_duration_previous", "stops_last_hour", "total_stop_duration_minutes", "stops_during_production", "restart_impact_score", "hour_of_day", "day_of_week", "hour_sin", "hour_cos", "speed_stability", "speed_momentum", "speed_acceleration", "sm_duration_minutes", "fm_duration_minutes", "speed_deviation_pct", "hourly_avg_scrap", "scrap_vs_hourly_avg", "shift_avg_efficiency", "efficiency_vs_shift_avg", "scrap_trend_5min", "scrap_trend_direction", "month", "quarter", "week_of_year", "sm_to_fm_gap_minutes", "match_quality_score", "fm_position_pct", "position_in_stack", "stack_progress_pct", "time_since_start", "sheet_sequence", "is_restart_period", "has_fm_match", "is_weekend", "stack_change", "quality_improving", "production_stable", "product_type_code", "product_thickness_mm", "product_length_mm", "product_width_mm", "product_area_m2", "thickness_category", "size_category", "product_complexity", "is_cladding", "is_weatherboard", "is_villaboard", "is_specialty"], "target_variables": ["Speed", "sm_scrap_pct", "fm_reject_pct", "production_efficiency_pct", "capacity_utilization_pct"], "forecast_horizons": [15, 60, 240], "lookback_window": 240, "patch_size": 16, "patch_stride": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"speed_range": [0.0, 100.0], "scrap_rate_range": [0.0, 1.0], "efficiency_range": [0.0, 1.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05, "efficiency_threshold": 0.85, "production_rate_range": [0.0, 1000.0], "stop_duration_range": [0.0, 480.0], "restart_grace_period": 15.0, "quality_control": {"sm_scrap_max": 0.15, "fm_reject_max": 0.15, "efficiency_min": 0.7}, "operational_limits": {"max_stop_duration": 240.0, "max_restart_impact": 1.0, "speed_variance_threshold": 0.2}, "forecasting_constraints": {"min_forecast_horizon": 5, "max_forecast_horizon": 480, "lookback_min": 60, "stability_window": 30}}}, "training_config": {"batch_size": 16, "learning_rate": 5e-05, "max_epochs": 50, "early_stopping_patience": 15, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "steps", "evaluation_strategy": "steps", "metric_for_best_model": "eval_loss", "num_workers": 2, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 1.0, "norm_type": 2.0, "error_if_nonfinite": false}, "learning_rate_schedule": {"initial_lr": 5e-05, "warmup_steps": 100, "decay_factor": 0.95, "decay_patience": 5, "min_lr": 1e-06}, "mixed_precision": {"enabled": true, "loss_scale": "dynamic", "init_scale": 65536.0, "growth_factor": 2.0, "backoff_factor": 0.5, "growth_interval": 2000}, "early_stopping": {"patience": 15, "min_delta": 0.001, "restore_best_weights": true, "baseline": null}, "stability_monitoring": {"enabled": true, "log_frequency": 10, "save_frequency": 100, "instability_threshold": 0.2, "intervention_patience": 5}}, "use_stability_features": true, "gradient_clip_norm": 1.0, "adaptive_clipping": true, "warmup_ratio": 0.1, "weight_decay": 0.01, "gradient_accumulation_steps": 2, "fp16": true, "fp16_opt_level": "O1", "logging_steps": 10, "eval_steps": 50, "save_steps": 100}}