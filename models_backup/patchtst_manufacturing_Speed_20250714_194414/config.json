{"activation_function": "gelu", "architectures": ["UnivariatePatchTSTForPrediction"], "attention_dropout": 0.0, "bias": true, "channel_attention": false, "channel_consistent_masking": false, "context_length": 240, "d_model": 64, "distribution_output": "mse", "do_mask_input": null, "dropout": 0.3, "ff_dropout": 0.0, "ffn_dim": 512, "head_dropout": 0.1, "init_std": 0.02, "loss": "mse", "mask_type": "random", "mask_value": 0, "model_type": "patchtst", "norm_eps": 1e-05, "norm_type": "batchnorm", "num_attention_heads": 4, "num_forecast_mask_patches": [2], "num_hidden_layers": 2, "num_input_channels": 56, "num_parallel_samples": 100, "num_targets": 1, "output_range": null, "patch_length": 16, "patch_stride": 16, "path_dropout": 0.0, "pooling_type": "mean", "positional_dropout": 0.0, "positional_encoding_type": "sincos", "pre_norm": true, "prediction_length": 240, "random_mask_ratio": 0.5, "scaling": "std", "share_embedding": true, "share_projection": true, "torch_dtype": "float32", "transformers_version": "4.51.3", "unmasked_channel_indices": null, "use_cls_token": false}