{"model_metadata": {"num_input_channels": 6, "context_length": 240, "prediction_length": 240, "patch_length": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "created_at": "2025-07-09T08:00:41.326954"}, "training_history": {"target_variable": "speed_Speed", "training_loss": 0.1763103187084198, "epochs_trained": 20, "training_time": 470.3614, "prepared_data_metadata": {"total_samples": 262028, "train_samples": 183084, "val_samples": 39232, "test_samples": 39233, "sequence_length": 240, "feature_count": 6}, "gradient_norms": [0.007489918731153011, 0.0024104162584990263, 0.010603142902255058, 0.0032886750996112823, 0.008215031586587429, 0.013118968345224857, 0.004796658642590046, 0.007370362989604473, 0.0062978677451610565, 0.0036746456753462553, 0.006254732608795166, 0.002707370789721608, 0.014379588887095451, 0.00597025640308857, 0.010491464287042618, 0.02087808959186077, 0.0010618926025927067, 0.002908597467467189, 0.006497597321867943, 0.0032689664512872696], "losses": [0.15210843086242676, 0.07768720388412476, 0.10407677292823792, 0.0879291519522667, 0.0992656722664833, 0.12608571350574493, 0.8072459697723389, 0.1113804429769516, 0.11066388338804245, 0.09418235719203949, 0.1380445659160614, 0.09024980664253235, 0.13138404488563538, 0.11136333644390106, 0.10444236546754837, 0.11243594437837601, 0.07681768387556076, 0.10646651685237885, 0.09059567004442215, 0.7937808632850647], "stability_scores": [0.574389815843867, 0.5679998245078794, 0.5615510481519889, 0.5637284868100803, 0.5669316308867359, 0.5501806424821781, 0.5425924451073132, 0.5396456628011227, 0.5415910827943993, 0.5397052868072261]}, "best_model_metrics": {"eval_loss": Infinity, "eval_runtime": 67.3948, "target_variable": "speed_Speed"}, "forecast_config": {"input_variables": ["thickness_thickness_avg", "thickness_thickness_uniformity", "speed_Speed", "stop_MPS Stop Duration", "fm_stack_Sheet Quantity", "fm_stack_Sheet Acceped"], "target_variables": ["thickness_thickness_avg", "speed_Speed", "fm_stack_Total Sheet Rejected"], "forecast_horizons": [15, 60, 240], "lookback_window": 240, "patch_size": 16, "patch_stride": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"thickness_range": [2.0, 20.0], "speed_range": [0.0, 100.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05}}, "training_config": {"batch_size": 16, "learning_rate": 5e-05, "max_epochs": 50, "early_stopping_patience": 15, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "steps", "evaluation_strategy": "steps", "metric_for_best_model": "eval_loss", "num_workers": 2, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 1.0, "norm_type": 2.0, "error_if_nonfinite": false}, "learning_rate_schedule": {"initial_lr": 5e-05, "warmup_steps": 100, "decay_factor": 0.95, "decay_patience": 5, "min_lr": 1e-06}, "mixed_precision": {"enabled": true, "loss_scale": "dynamic", "init_scale": 65536.0, "growth_factor": 2.0, "backoff_factor": 0.5, "growth_interval": 2000}, "early_stopping": {"patience": 15, "min_delta": 0.001, "restore_best_weights": true, "baseline": null}, "stability_monitoring": {"enabled": true, "log_frequency": 10, "save_frequency": 100, "instability_threshold": 0.2, "intervention_patience": 5}}, "use_stability_features": true, "gradient_clip_norm": 1.0, "adaptive_clipping": true, "warmup_ratio": 0.1, "weight_decay": 0.01, "gradient_accumulation_steps": 2, "fp16": true, "fp16_opt_level": "O1", "logging_steps": 10, "eval_steps": 50, "save_steps": 100}}