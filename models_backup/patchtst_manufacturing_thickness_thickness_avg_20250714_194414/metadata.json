{"model_metadata": {"num_input_channels": 6, "context_length": 240, "prediction_length": 240, "patch_length": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "created_at": "2025-07-09T07:48:56.796140"}, "training_history": {"target_variable": "thickness_thickness_avg", "training_loss": 0.1763298213481903, "epochs_trained": 20, "training_time": 509.7233, "prepared_data_metadata": {"total_samples": 262028, "train_samples": 183084, "val_samples": 39232, "test_samples": 39233, "sequence_length": 240, "feature_count": 6}, "gradient_norms": [0.007349214516580105, 0.002400810830295086, 0.010301429778337479, 0.003261028090491891, 0.008264537900686264, 0.012997710146009922, 0.004757657181471586, 0.007254771888256073, 0.006313726305961609, 0.0036516187246888876, 0.00626242533326149, 0.0027548703365027905, 0.01436926145106554, 0.0058409180492162704, 0.01046676654368639, 0.02081732451915741, 0.001150129595771432, 0.0028506063390523195, 0.006448417901992798, 0.00322115421295166], "losses": [0.15208861231803894, 0.07769650220870972, 0.10404103994369507, 0.08790414035320282, 0.09926192462444305, 0.1260676085948944, 0.8072268962860107, 0.11138749867677689, 0.11070764809846878, 0.09417109936475754, 0.1380877047777176, 0.09031270444393158, 0.1314626932144165, 0.11138541251420975, 0.1045478880405426, 0.11255434155464172, 0.07681752741336823, 0.1064731553196907, 0.09064985811710358, 0.7937523722648621], "stability_scores": [0.5750580723490774, 0.5688908185764188, 0.5618279960407792, 0.5638797607934082, 0.5670244582809907, 0.5499741924051373, 0.5426205143018585, 0.5396034633277486, 0.541542871705217, 0.5396186047428249]}, "best_model_metrics": {"eval_loss": Infinity, "eval_runtime": 67.5921, "target_variable": "thickness_thickness_avg"}, "forecast_config": {"input_variables": ["thickness_thickness_avg", "thickness_thickness_uniformity", "speed_Speed", "stop_MPS Stop Duration", "fm_stack_Sheet Quantity", "fm_stack_Sheet Acceped"], "target_variables": ["thickness_thickness_avg", "speed_Speed", "fm_stack_Total Sheet Rejected"], "forecast_horizons": [15, 60, 240], "lookback_window": 240, "patch_size": 16, "patch_stride": 16, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"thickness_range": [2.0, 20.0], "speed_range": [0.0, 100.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05}}, "training_config": {"batch_size": 16, "learning_rate": 5e-05, "max_epochs": 50, "early_stopping_patience": 15, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "steps", "evaluation_strategy": "steps", "metric_for_best_model": "eval_loss", "num_workers": 2, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 1.0, "norm_type": 2.0, "error_if_nonfinite": false}, "learning_rate_schedule": {"initial_lr": 5e-05, "warmup_steps": 100, "decay_factor": 0.95, "decay_patience": 5, "min_lr": 1e-06}, "mixed_precision": {"enabled": true, "loss_scale": "dynamic", "init_scale": 65536.0, "growth_factor": 2.0, "backoff_factor": 0.5, "growth_interval": 2000}, "early_stopping": {"patience": 15, "min_delta": 0.001, "restore_best_weights": true, "baseline": null}, "stability_monitoring": {"enabled": true, "log_frequency": 10, "save_frequency": 100, "instability_threshold": 0.2, "intervention_patience": 5}}, "use_stability_features": true, "gradient_clip_norm": 1.0, "adaptive_clipping": true, "warmup_ratio": 0.1, "weight_decay": 0.01, "gradient_accumulation_steps": 2, "fp16": true, "fp16_opt_level": "O1", "logging_steps": 10, "eval_steps": 50, "save_steps": 100}}