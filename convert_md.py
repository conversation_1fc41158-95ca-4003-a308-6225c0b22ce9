
import markdown2
import sys

if len(sys.argv) < 3:
    print("Usage: python convert_md.py <input_file> <output_file>")
    sys.exit(1)

input_file = sys.argv[1]
output_file = sys.argv[2]

with open(input_file, "r", encoding="utf-8") as f:
    md_content = f.read()

# Use the "tables" extra to ensure tables are converted correctly
html_content = markdown2.markdown(md_content, extras=["tables"])

with open(output_file, "w", encoding="utf-8") as f:
    f.write(html_content)

print(f"Successfully converted {input_file} to {output_file}")
