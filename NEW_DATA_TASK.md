# NEW_DATA_TASK.md - Enhanced Unified Table Creation for Manufacturing Data Loader

## FEATURE:
**Enhanced Unified Table Creation Algorithm with Stack-Level Manufacturing Intelligence**

Implement a comprehensive unified table creation system that transforms the current `src/data/loader.py` from basic integration to advanced manufacturing intelligence. The enhancement will implement the complete 7-phase methodology from `DATA_WITHOUT_THICKNESS.md` to create a production-ready unified dataset optimized for fiber cement manufacturing correlation analysis.

### Core Enhancements Required:

#### 1. **Advanced SM-FM Stack Matching Algorithm**
- Implement production order correlation with temporal validation
- Add time gap analysis between SM completion and FM processing  
- Create comprehensive stack lifecycle tracking
- Support multiple FM matches per SM stack with intelligent selection

#### 2. **Comprehensive Time-Series Aggregation Engine**
- **Speed Analytics per Stack**: Calculate avg, std, min, max, CV, and reading counts for each stack's production period
- **Stoppage Analytics per Stack**: Track stops during production, stops before production, efficiency metrics
- **Production Context Analysis**: Recovery patterns, stoppage reason categorization
- **Temporal Correlation**: Event-based analysis with configurable time windows

#### 3. **Manufacturing Domain Intelligence Features**
- **Stack-Level Quality Metrics**: SM scrap percentage, FM reject rates, total quality loss
- **Production Efficiency Calculations**: Duration analysis, capacity utilization, throughput metrics
- **Design Parameter Integration**: Actual vs. design performance comparison
- **Operational Deviation Analysis**: Speed deviation from design, capacity utilization percentages

#### 4. **Enhanced Temporal & Sequence Features**
- **Shift Classification**: Night/morning/afternoon with configurable boundaries
- **Cyclical Time Encoding**: Sin/cos hour encoding for ML compatibility
- **Production Sequence Tracking**: Stack numbering, campaign duration analysis
- **Quality Trend Detection**: Deviation from rolling averages, specification compliance

#### 5. **Advanced Data Validation & Quality Framework**
- **Temporal Consistency Validation**: Ensure logical time sequences across data sources
- **Manufacturing Domain Validation**: Speed ranges, duration limits, quality bounds
- **Data Completeness Reporting**: Critical column coverage analysis
- **Range Validation**: Manufacturing-specific value bounds checking

### Technical Requirements:

#### **Performance Optimization**
- Efficient processing of 175K+ speed records with stack-level aggregation
- Vectorized operations using pandas for large dataset handling
- Memory-optimized merge strategies with merge_asof for temporal alignment
- Incremental processing with progress indicators for large operations

#### **Data Integration Robustness**
- Handle missing data sources gracefully with meaningful defaults
- Support partial stack matching with comprehensive reporting
- Robust numeric conversion with manufacturing domain validation
- Multi-format datetime handling maintaining current warning-free processing

#### **Manufacturing Context Preservation**
- Maintain work center context throughout integration process
- Preserve production order lineage and traceability
- Support material changeover analysis and efficiency tracking
- Enable shift-based and temporal pattern analysis

## EXAMPLES:

### Primary Reference Implementation
**File**: `DATA_WITHOUT_THICKNESS.md` - Complete step-by-step methodology (lines 32-488)

Key functions to implement:

#### 1. **Data Preparation Functions**
```python
# Reference: DATA_WITHOUT_THICKNESS.md lines 52-117
prepare_sm_stack_data(sm_stack_df)    # Transform SM data with timestamps and metrics
prepare_fm_stack_data(fm_stack_df)    # Process FM data with duration calculations
```

#### 2. **Aggregation Engine Functions**
```python
# Reference: DATA_WITHOUT_THICKNESS.md lines 122-244
aggregate_speed_data(sm_stack_df, speed_df)      # Stack-level speed analytics
aggregate_stoppage_data(sm_stack_df, stop_df)    # Stoppage impact analysis
```

#### 3. **Stack Matching and Integration**
```python
# Reference: DATA_WITHOUT_THICKNESS.md lines 250-318
match_sm_fm_stacks(sm_stack_df, fm_stack_df)     # Production order correlation
add_product_specifications(unified_df, vm_capacity_df)  # Design parameter integration
```

#### 4. **Feature Engineering Functions**
```python
# Reference: DATA_WITHOUT_THICKNESS.md lines 377-411
add_temporal_features(unified_df)     # Shift classification and time encoding
```

#### 5. **Complete Integration Algorithm**
```python
# Reference: DATA_WITHOUT_THICKNESS.md lines 418-488
create_unified_table(sm_stack_df, fm_stack_df, speed_df, stop_df, vm_capacity_df)
```

#### 6. **Data Validation Framework**
```python
# Reference: DATA_WITHOUT_THICKNESS.md lines 563-611
validate_unified_table(unified_df)    # Comprehensive data quality assessment
```

### Implementation Examples from Documentation
**File**: `DATA_WITHOUT_THICKNESS.md` contains complete working examples including:
- Timestamp parsing with multi-format support (lines 44-49)
- Scrap percentage conversion from strings (lines 78-79)
- Production order base extraction (lines 82-83)
- Speed statistics calculation with proper handling (lines 149-158)
- Stoppage analysis with time windows (lines 197-243)
- Product code extraction and specification matching (lines 335-372)

## DOCUMENTATION:

### Core Reference Documents
- **MANUFACTURING_DATA_COLUMNS.md**: Complete 51-column unified table specification with data types and manufacturing context
- **DATA_LOADER_DOCUMENTATION.md**: Current architecture documentation and production fixes
- **DATA_WITHOUT_THICKNESS.md**: Complete implementation methodology and validation framework

### API Documentation Links
- **Current Loader Architecture**: `src/data/loader.py` lines 221-925 (ManufacturingDataLoader class)
- **Existing Integration Methods**: Lines 415-482 (create_unified_table method)
- **Configuration System**: Lines 320-334 (JSON-based data source management)

### Manufacturing Domain References
- **Thickness Processing**: MANUFACTURING_DATA_COLUMNS.md lines 85-114 (10-sensor array specification)
- **Quality Metrics**: MANUFACTURING_DATA_COLUMNS.md lines 143-168 (SM and FM integration)
- **Temporal Features**: MANUFACTURING_DATA_COLUMNS.md lines 186-210 (shift and sequence analysis)

### Validation Standards
- **Data Quality Framework**: DATA_WITHOUT_THICKNESS.md lines 560-612
- **Manufacturing Domain Validation**: Speed 0-300 m/min, scrap 0-100%, positive durations
- **Temporal Consistency**: First sheet <= Last sheet, FM after SM completion

## OTHER CONSIDERATIONS:

### **Critical Implementation Requirements**

#### **1. Backward Compatibility Maintenance**
- **Preserve Existing API**: Current `create_unified_table()` function signature must remain functional
- **Configuration Compatibility**: Existing `data.config.json` structure must continue working
- **CLI Integration**: Seamless operation with existing analysis tools and interactive mode
- **Import Compatibility**: Maintain both class-based and function-based usage patterns

#### **2. Performance & Scalability Considerations**
- **Large Dataset Handling**: Efficient processing of 175K+ speed records without memory issues
- **Vectorized Operations**: Use pandas-optimized calculations throughout aggregation functions
- **Memory Management**: Implement chunked processing for extremely large datasets
- **Progress Indicators**: Add logging for long-running operations (stack matching, aggregation)

#### **3. Data Quality & Robustness**
- **Graceful Degradation**: Handle missing data sources without breaking the entire pipeline
- **Manufacturing Domain Validation**: Implement realistic bounds checking for all manufacturing metrics
- **Error Recovery**: Provide meaningful error messages with suggested remediation steps
- **Data Lineage**: Maintain traceability of transformations and aggregations

#### **4. Manufacturing Intelligence Features**
- **Stack-Level Analysis**: Enable correlation analysis at production batch level
- **Quality Outcome Prediction**: Support features for ML models predicting scrap rates
- **Process Optimization**: Provide metrics for identifying optimal operating parameters
- **Root Cause Analysis**: Enable investigation of quality issues through comprehensive correlation

#### **5. Common AI Assistant Pitfalls to Avoid**
- **Do NOT break existing functionality**: Test all current loader methods after enhancement
- **Do NOT ignore manufacturing context**: Validate that aggregations make industrial sense
- **Do NOT sacrifice performance**: Use efficient pandas operations, avoid nested loops
- **Do NOT hardcode time windows**: Make analysis windows configurable
- **Do NOT lose data traceability**: Maintain source references throughout transformations
- **Do NOT ignore edge cases**: Handle partial data, missing stacks, malformed timestamps
- **Do NOT break datetime processing**: Preserve current warning-free multi-format support

#### **6. Production Deployment Considerations**
- **Warning-Free Operation**: Maintain current pandas warning elimination fixes
- **Memory Efficiency**: Support deployment on resource-constrained systems
- **Configuration Flexibility**: Support different manufacturing facilities with varying data structures
- **Monitoring Support**: Provide metrics for data pipeline health and performance
- **Integration Testing**: Ensure compatibility with correlation analysis tools and visualization suite

#### **7. Manufacturing Domain Expertise Requirements**
- **Stack Lifecycle Understanding**: Correctly model SM → FM production flow
- **Quality Metrics Accuracy**: Ensure scrap/reject calculations follow manufacturing standards
- **Temporal Alignment Logic**: Properly handle production timing and sequence dependencies
- **Operational Context**: Preserve shift patterns, work center assignments, and production campaigns

#### **8. Advanced Analytics Enablement**
- **Multi-Method Correlation Support**: Optimize data structure for Pearson, Spearman, Kendall analysis
- **Forecasting Readiness**: Provide time series features suitable for PatchTST and other models
- **Causal Analysis Support**: Enable PCMCI and other causal discovery algorithms
- **Visualization Integration**: Ensure compatibility with existing 5-tool visualization suite

### **Success Criteria**
1. ✅ Complete 7-phase methodology implementation matching DATA_WITHOUT_THICKNESS.md
2. ✅ All 51 columns from MANUFACTURING_DATA_COLUMNS.md specification generated correctly
3. ✅ Stack-level aggregation working efficiently for 14K+ stacks 
4. ✅ SM-FM matching achieving >85% success rate with logical temporal constraints
5. ✅ Backward compatibility maintained with existing analysis pipeline
6. ✅ Manufacturing domain validation preventing illogical data combinations
7. ✅ Performance suitable for interactive analysis (< 30 seconds for full dataset)
8. ✅ Comprehensive data quality reporting and validation framework