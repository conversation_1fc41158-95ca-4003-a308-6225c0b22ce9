{"forecasting_config": {"input_variables": ["Speed", "speed_avg_5min", "speed_change_rate", "sm_production_rate", "fm_processing_rate", "production_efficiency_pct", "sm_scrap_pct", "fm_reject_pct", "sm_quality_efficiency", "fm_quality_efficiency", "time_since_last_stop", "stop_duration_previous", "hour_of_day", "day_of_week", "hour_sin", "hour_cos", "speed_stability", "speed_momentum", "speed_acceleration", "sm_duration_minutes", "fm_duration_minutes", "month", "quarter", "week_of_year", "sm_to_fm_gap_minutes", "match_quality_score", "fm_position_pct", "position_in_stack", "stack_progress_pct", "time_since_start", "sheet_sequence", "is_restart_period", "has_fm_match", "is_weekend", "stack_change", "quality_improving", "production_stable"], "target_variables": ["fm_reject_pct"], "forecast_horizons": [15, 60, 240], "lookback_window": 120, "patch_size": 8, "patch_stride": 8, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.2, "head_dropout": 0.2, "attention_dropout": 0.1, "norm_type": "batch_norm", "activation": "relu", "norm_first": false, "channel_attention": false, "scaling": "std", "loss": "mse"}}, "training_config": {"batch_size": 32, "learning_rate": 0.0001, "max_epochs": 50, "early_stopping_patience": 10, "validation_split": 0.2, "test_split": 0.2, "save_strategy": "epoch", "evaluation_strategy": "epoch", "metric_for_best_model": "eval_loss", "num_workers": 0, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 1.0, "norm_type": 2.0, "error_if_nonfinite": true}, "learning_rate_schedule": {"initial_lr": 0.0001, "warmup_steps": 100, "decay_factor": 0.95, "decay_patience": 5, "min_lr": 1e-06}, "mixed_precision": {"enabled": false, "loss_scale": "dynamic"}, "early_stopping": {"patience": 10, "min_delta": 0.01, "restore_best_weights": true}}, "transfer_learning": {"enabled": false, "fallback_to_fresh": true}}}