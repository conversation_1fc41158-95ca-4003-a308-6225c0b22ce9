{"forecasting_config": {"input_variables": ["thickness_thickness_avg", "thickness_thickness_uniformity", "speed_Speed", "stop_MPS Stop Duration", "fm_stack_Sheet Quantity", "fm_stack_Sheet Acceped"], "target_variables": ["thickness_thickness_avg", "speed_Speed", "fm_stack_Total Sheet Rejected"], "forecast_horizons": [15, 60], "lookback_window": 120, "patch_size": 8, "patch_stride": 8, "model_params": {"d_model": 32, "num_attention_heads": 2, "num_hidden_layers": 1, "dropout": 0.4, "head_dropout": 0.4, "attention_dropout": 0.3, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"thickness_range": [2.0, 20.0], "speed_range": [0.0, 100.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05}}, "training_config": {"batch_size": 8, "learning_rate": 1e-05, "max_epochs": 5, "early_stopping_patience": 3, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "steps", "evaluation_strategy": "steps", "eval_steps": 25, "save_steps": 50, "metric_for_best_model": "eval_loss", "num_workers": 1, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 0.5, "norm_type": 2.0, "error_if_nonfinite": true}, "learning_rate_schedule": {"initial_lr": 1e-05, "warmup_steps": 50, "decay_factor": 0.9, "decay_patience": 2, "min_lr": 1e-07}, "mixed_precision": {"enabled": false, "loss_scale": "dynamic", "init_scale": 65536.0, "growth_factor": 2.0, "backoff_factor": 0.5, "growth_interval": 2000}, "early_stopping": {"patience": 3, "min_delta": 0.01, "restore_best_weights": true, "baseline": null}, "numerical_stability": {"eps": 1e-08, "clip_norm": true, "detect_anomaly": true}}, "transfer_learning": {"enabled": false, "pretrained_model": "ibm-granite/granite-timeseries-patchtst", "fallback_to_fresh": true, "linear_probing_epochs": 5, "fine_tuning_epochs": 10, "discriminative_lr": false, "gradual_unfreezing": false}}}