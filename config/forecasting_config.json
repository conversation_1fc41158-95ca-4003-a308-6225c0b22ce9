{"forecasting_config": {"input_variables": ["Speed", "sm_scrap_pct", "fm_reject_pct", "fm_reject_sheets"], "target_variables": ["Speed", "sm_scrap_pct", "fm_reject_pct", "production_efficiency_pct", "capacity_utilization_pct"], "forecast_horizons": [15, 60, 240], "lookback_window": 240, "patch_size": 16, "patch_stride": 16, "model_params": {"d_model": 128, "num_attention_heads": 8, "num_hidden_layers": 3, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "unit_norm", "activation": "gelu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"speed_range": [0.0, 100.0], "scrap_rate_range": [0.0, 1.0], "efficiency_range": [0.0, 1.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05, "efficiency_threshold": 0.85, "production_rate_range": [0.0, 1000.0], "stop_duration_range": [0.0, 480.0], "restart_grace_period": 15.0, "quality_control": {"sm_scrap_max": 0.15, "fm_reject_max": 0.15, "efficiency_min": 0.7}, "operational_limits": {"max_stop_duration": 240.0, "max_restart_impact": 1.0, "speed_variance_threshold": 0.2}, "forecasting_constraints": {"min_forecast_horizon": 5, "max_forecast_horizon": 480, "lookback_min": 60, "stability_window": 30}}}, "training_config": {"batch_size": 16, "learning_rate": 5e-05, "max_epochs": 100, "early_stopping_patience": 15, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "steps", "evaluation_strategy": "steps", "eval_steps": 50, "save_steps": 100, "metric_for_best_model": "eval_loss", "num_workers": 2, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 0.5, "norm_type": 2.0, "error_if_nonfinite": true}, "learning_rate_schedule": {"initial_lr": 5e-05, "warmup_steps": 200, "decay_factor": 0.9, "decay_patience": 8, "min_lr": 1e-07}, "mixed_precision": {"enabled": true, "loss_scale": "dynamic", "init_scale": 65536.0, "growth_factor": 2.0, "backoff_factor": 0.5, "growth_interval": 2000}, "early_stopping": {"patience": 15, "min_delta": 0.001, "restore_best_weights": true, "baseline": null}, "numerical_stability": {"eps": 1e-08, "clip_norm": true, "detect_anomaly": false}}, "transfer_learning": {"enabled": true, "pretrained_model": "ibm-granite/granite-timeseries-patchtst", "fallback_to_fresh": true, "linear_probing_epochs": 10, "fine_tuning_epochs": 20, "discriminative_lr": true, "gradual_unfreezing": true}}}