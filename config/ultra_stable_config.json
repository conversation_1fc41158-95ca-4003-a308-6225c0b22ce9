{"forecasting_config": {"input_variables": ["Speed", "sm_production_rate", "fm_processing_rate", "sm_scrap_pct", "fm_reject_pct", "total_stop_duration_minutes"], "target_variables": ["fm_reject_pct"], "forecast_horizons": [15], "lookback_window": 80, "patch_size": 8, "patch_stride": 8, "model_params": {"d_model": 32, "num_attention_heads": 2, "num_hidden_layers": 1, "dropout": 0.5, "head_dropout": 0.5, "attention_dropout": 0.4, "norm_type": "unit_norm", "activation": "relu", "norm_first": true, "channel_attention": false, "scaling": "std", "loss": "mse"}, "manufacturing_params": {"speed_range": [0.0, 100.0], "scrap_rate_range": [0.0, 1.0], "efficiency_range": [0.0, 1.0], "quality_threshold": 0.95, "scrap_rate_threshold": 0.05, "efficiency_threshold": 0.85, "production_rate_range": [0.0, 1000.0], "stop_duration_range": [0.0, 480.0], "restart_grace_period": 15.0, "quality_control": {"sm_scrap_max": 0.15, "fm_reject_max": 0.15, "efficiency_min": 0.7}, "operational_limits": {"max_stop_duration": 240.0, "max_restart_impact": 1.0, "speed_variance_threshold": 0.2}, "forecasting_constraints": {"min_forecast_horizon": 5, "max_forecast_horizon": 60, "lookback_min": 60, "stability_window": 30}}}, "training_config": {"batch_size": 4, "learning_rate": 1e-06, "max_epochs": 10, "early_stopping_patience": 5, "validation_split": 0.2, "test_split": 0.2, "save_strategy": "epoch", "evaluation_strategy": "epoch", "metric_for_best_model": "eval_loss", "num_workers": 0, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 0.1, "norm_type": 2.0, "error_if_nonfinite": true}, "learning_rate_schedule": {"initial_lr": 1e-06, "warmup_steps": 50, "decay_factor": 0.95, "decay_patience": 3, "min_lr": 1e-08}, "mixed_precision": {"enabled": false, "loss_scale": "dynamic"}, "early_stopping": {"patience": 5, "min_delta": 0.001, "restore_best_weights": true}, "numerical_stability": {"eps": 1e-08, "clip_norm": true, "detect_anomaly": true}, "stability_monitoring": {"enabled": true, "log_frequency": 1, "save_frequency": 10, "instability_threshold": 0.1, "intervention_patience": 2}}, "transfer_learning": {"enabled": false, "pretrained_model": null, "fallback_to_fresh": true, "linear_probing_epochs": 5, "fine_tuning_epochs": 10, "discriminative_lr": false, "gradual_unfreezing": false}, "use_stability_features": true, "gradient_clip_norm": 0.1, "adaptive_clipping": true, "warmup_ratio": 0.2, "weight_decay": 0.1, "gradient_accumulation_steps": 1, "fp16": false, "fp16_opt_level": "O0", "logging_steps": 1, "eval_steps": 5, "save_steps": 10}}