{"forecasting_config": {"input_variables": ["Speed", "speed_avg_5min", "speed_change_rate", "sm_production_rate", "fm_processing_rate", "production_efficiency_pct", "sm_scrap_pct", "fm_reject_pct", "sm_quality_efficiency", "fm_quality_efficiency", "time_since_last_stop", "stop_duration_previous", "hour_of_day", "day_of_week", "hour_sin", "hour_cos", "speed_stability", "speed_momentum", "fm_position_pct", "position_in_stack", "time_since_start", "is_restart_period", "has_fm_match", "production_stable"], "target_variables": ["fm_reject_pct"], "forecast_horizons": [15, 60, 240], "lookback_window": 80, "patch_size": 8, "patch_stride": 8, "model_params": {"d_model": 64, "num_attention_heads": 4, "num_hidden_layers": 2, "dropout": 0.3, "head_dropout": 0.3, "attention_dropout": 0.2, "norm_type": "layer_norm", "activation": "relu", "norm_first": false, "channel_attention": false, "scaling": "std", "loss": "mse"}}, "training_config": {"batch_size": 16, "learning_rate": 5e-05, "max_epochs": 25, "early_stopping_patience": 12, "validation_split": 0.2, "test_split": 0.2, "save_strategy": "epoch", "evaluation_strategy": "epoch", "metric_for_best_model": "eval_loss", "num_workers": 0, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 0.5, "norm_type": 2.0, "error_if_nonfinite": true}, "learning_rate_schedule": {"initial_lr": 5e-05, "warmup_steps": 200, "decay_factor": 0.95, "decay_patience": 5, "min_lr": 1e-06}, "mixed_precision": {"enabled": false, "loss_scale": "dynamic"}, "early_stopping": {"patience": 12, "min_delta": 0.01, "restore_best_weights": true}, "numerical_stability": {"eps": 1e-08, "clip_norm": true, "detect_anomaly": true}}, "transfer_learning": {"enabled": false, "fallback_to_fresh": true}}}