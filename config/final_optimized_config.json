{"forecasting_config": {"input_variables": ["Speed", "speed_avg_5min", "speed_change_rate", "sm_production_rate", "fm_processing_rate", "production_efficiency_pct", "sm_scrap_pct", "fm_reject_pct", "sm_quality_efficiency", "fm_quality_efficiency", "sm_good_sheets", "fm_ok_sheets", "fm_reject_sheets", "time_since_last_stop", "stop_duration_previous", "stops_last_hour", "hour_of_day", "day_of_week", "hour_sin", "hour_cos", "speed_stability", "speed_momentum", "speed_acceleration", "sm_duration_minutes", "fm_duration_minutes", "fm_position_pct", "position_in_stack", "stack_progress_pct", "time_since_start", "sheet_sequence", "is_restart_period", "has_fm_match", "is_weekend", "stack_change", "quality_improving", "production_stable"], "target_variables": ["fm_reject_pct"], "forecast_horizons": [15, 60, 240], "lookback_window": 60, "patch_size": 6, "patch_stride": 3, "model_params": {"d_model": 96, "num_attention_heads": 6, "num_hidden_layers": 3, "dropout": 0.15, "head_dropout": 0.15, "attention_dropout": 0.1, "norm_type": "layer_norm", "activation": "gelu", "norm_first": true, "channel_attention": true, "scaling": "std", "loss": "mse"}}, "training_config": {"batch_size": 64, "learning_rate": 0.0003, "max_epochs": 30, "early_stopping_patience": 8, "validation_split": 0.15, "test_split": 0.15, "save_strategy": "epoch", "evaluation_strategy": "epoch", "metric_for_best_model": "eval_loss", "num_workers": 0, "pin_memory": false, "model_save_path": "./models/", "stability_config": {"gradient_clipping": {"enabled": true, "max_norm": 2.0, "norm_type": 2.0, "error_if_nonfinite": true}, "learning_rate_schedule": {"initial_lr": 0.0003, "warmup_steps": 50, "decay_factor": 0.98, "decay_patience": 3, "min_lr": 1e-05}, "mixed_precision": {"enabled": false, "loss_scale": "dynamic"}, "early_stopping": {"patience": 8, "min_delta": 0.005, "restore_best_weights": true}}, "transfer_learning": {"enabled": false, "fallback_to_fresh": true}}}