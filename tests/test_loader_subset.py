#!/usr/bin/env python3
"""
Subset Test for Optimized Data Loader
Tests the rebuilt loader with a smaller subset to validate functionality.
"""

import time
import logging
from src.data.loader import ManufacturingDataLoader

# Set up logging to see progress
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_subset_performance():
    """Test the optimized loader with a subset of data."""
    logger.info("Starting subset performance test...")
    
    start_time = time.time()
    
    try:
        # Initialize loader
        loader = ManufacturingDataLoader()
        
        # Load data sources first
        logger.info("Loading data sources...")
        datasets = loader.load_all_data_sources()
        
        # Use only first 5000 rows of speed data for testing
        if 'speed' in datasets:
            original_size = len(datasets['speed'])
            datasets['speed'] = datasets['speed'].head(5000)
            loader.loaded_data['speed'] = datasets['speed']
            logger.info(f"Using speed subset: {len(datasets['speed']):,} rows (from {original_size:,})")
        
        load_time = time.time()
        logger.info(f"Data loading completed in {load_time - start_time:.2f} seconds")
        
        # Test unified table creation with subset
        logger.info("Creating unified table with subset data...")
        unified_df = loader.create_unified_table()
        
        unified_time = time.time()
        logger.info(f"Unified table creation completed in {unified_time - load_time:.2f} seconds")
        logger.info(f"Total processing time: {unified_time - start_time:.2f} seconds")
        
        # Validate results
        logger.info(f"Unified table: {len(unified_df):,} rows × {len(unified_df.columns)} columns")
        
        # Check stoppage features
        stoppage_cols = [col for col in unified_df.columns if 'stop' in col or 'restart' in col]
        logger.info(f"Stoppage features created: {stoppage_cols}")
        
        # Check feature coverage
        feature_counts = {}
        for col in unified_df.columns:
            non_null_count = unified_df[col].notna().sum()
            feature_counts[col] = non_null_count
        
        logger.info("Feature coverage:")
        for col, count in sorted(feature_counts.items()):
            pct = (count / len(unified_df)) * 100
            logger.info(f"  {col}: {count:,}/{len(unified_df):,} ({pct:.1f}%)")
        
        # Sample some data
        sample_data = unified_df.head(3)
        logger.info("Sample unified data:")
        key_cols = ['timestamp', 'work_center', 'Speed', 'thickness_avg', 'time_since_last_stop', 'is_restart_period']
        available_cols = [col for col in key_cols if col in sample_data.columns]
        print(sample_data[available_cols].to_string())
        
        return True
        
    except Exception as e:
        logger.error(f"Subset test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_subset_performance()
    if success:
        print("\n✅ Subset test completed successfully!")
        print("🚀 Optimized loader is working correctly - performance issues resolved!")
    else:
        print("\n❌ Subset test failed!")