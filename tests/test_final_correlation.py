#!/usr/bin/env python3
"""
Test script to verify the complete correlation analysis fix
"""

import pandas as pd
import numpy as np
from src.agents.multi_tools import calculate_multi_method_correlations_tool

# Mock context class for testing
class MockContext:
    def __init__(self, data):
        self.deps = MockDeps(data)

class MockDeps:
    def __init__(self, data):
        self.data = data

# Create sample data with strong correlation to get very small p-values
np.random.seed(42)
n = 10000
x = np.random.randn(n)
y = 0.8 * x + 0.2 * np.random.randn(n)  # Strong correlation

data = pd.DataFrame({
    'thickness_avg': x,
    'speed': y,
})

print("🧪 Testing complete correlation analysis with very small p-values...")

try:
    # Create mock context
    ctx = MockContext(data)
    
    # Test the multi-method correlation tool
    result = calculate_multi_method_correlations_tool(
        ctx, 
        variables=['thickness_avg', 'speed'],
        min_periods=30,
        include_robustness=False
    )
    
    if 'error' in result:
        print(f"❌ Error in correlation analysis: {result['error']}")
    else:
        print("✅ Multi-method correlation analysis completed successfully")
        
        # Check the results structure
        if 'significant_correlations' in result:
            sig_corrs = result['significant_correlations']
            print(f"✅ Found {len(sig_corrs)} significant correlations")
            
            # Check p-value structure
            for corr in sig_corrs[:3]:  # Check first 3
                p_val_numeric = corr.get('p_value', 'N/A')
                p_val_formatted = corr.get('p_value_formatted', 'N/A')
                method = corr.get('method', 'unknown')
                correlation = corr.get('correlation_coefficient', 0)
                
                print(f"  - {method}: correlation={correlation:.6f}")
                print(f"    p_value (numeric): {p_val_numeric} (type: {type(p_val_numeric)})")
                print(f"    p_value_formatted: {p_val_formatted} (type: {type(p_val_formatted)})")
                
                # Test comparison operations
                try:
                    if isinstance(p_val_numeric, (int, float)) and p_val_numeric < 0.001:
                        print(f"    ✅ Numeric comparison works: p < 0.001")
                    else:
                        print(f"    ⚠️  Numeric comparison issue or p >= 0.001")
                except Exception as e:
                    print(f"    ❌ Comparison error: {e}")
        
        print("✅ No string comparison errors detected")
        
except Exception as e:
    print(f"❌ Test failed with error: {e}")
    import traceback
    traceback.print_exc()

print("\n🎉 Complete correlation analysis test completed!")