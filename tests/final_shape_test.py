#!/usr/bin/env python3
"""
Final test to confirm shape mismatch is completely resolved
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_shape_compatibility():
    """Test that the shape compatibility fix is working"""
    
    from src.forecasting.patchtst_model import (
        UnivariatePatchTSTForPrediction,
        ManufacturingDataCollator,
        ManufacturingDataset
    )
    from transformers import PatchTSTConfig
    import torch
    import torch.nn as nn
    
    logger.info("🔧 Final Shape Compatibility Test...")
    
    try:
        # Create a PatchTST config
        config = PatchTSTConfig(
            num_input_channels=6,
            context_length=240,
            prediction_length=240,
            patch_length=16,
            patch_stride=16,
            d_model=64,
            num_attention_heads=4,
            num_hidden_layers=2,
            dropout=0.1,
            num_targets=1,
            loss="mse"
        )
        
        # Create the univariate model
        model = UnivariatePatchTSTForPrediction(config, target_channel_idx=0)
        logger.info("✅ Univariate model created successfully")
        
        # Create sample data
        sample_sequences = [
            {
                'features': [[float(i+j) for j in range(6)] for i in range(240)],
                'targets': {'horizon_240': [7.0 + i*0.01 for i in range(240)]}
            },
            {
                'features': [[float(i+j+10) for j in range(6)] for i in range(240)],
                'targets': {'horizon_240': [8.0 + i*0.01 for i in range(240)]}
            }
        ]
        
        # Create dataset and collator
        dataset = ManufacturingDataset(sample_sequences, max_horizon=240)
        data_collator = ManufacturingDataCollator(target_channel_idx=0)
        
        # Create batch
        batch = [dataset[0], dataset[1]]
        collated_batch = data_collator(batch)
        
        logger.info(f"Batch shapes:")
        logger.info(f"  past_values: {collated_batch['past_values'].shape}")
        logger.info(f"  future_values: {collated_batch['future_values'].shape}")
        
        # Test training mode (with targets)
        model.train()
        
        logger.info("Testing training forward pass...")
        outputs = model(
            past_values=collated_batch['past_values'],
            future_values=collated_batch['future_values']
        )
        
        # Check if loss is computed without errors
        if hasattr(outputs, 'loss') and outputs.loss is not None:
            logger.info(f"✅ Training loss computed: {outputs.loss.item():.6f}")
        else:
            logger.warning("⚠️ No loss in training outputs")
        
        # Test inference mode (no targets)
        model.eval()
        
        logger.info("Testing inference forward pass...")
        with torch.no_grad():
            inference_outputs = model(past_values=collated_batch['past_values'])
        
        if hasattr(inference_outputs, 'prediction_outputs'):
            pred_shape = inference_outputs.prediction_outputs.shape
            expected_shape = (2, 240)  # [batch_size, prediction_length]
            
            logger.info(f"Inference prediction shape: {pred_shape}")
            
            if pred_shape == expected_shape:
                logger.info("✅ Inference output shape is correct!")
            else:
                logger.error(f"❌ Wrong inference shape: expected {expected_shape}")
                return False
        
        # Test that we can compute MSE loss manually (simulating training)
        logger.info("Testing manual loss computation...")
        
        # Get predictions in training mode
        model.train()
        pred_outputs = model(past_values=collated_batch['past_values'])
        
        if hasattr(pred_outputs, 'prediction_outputs'):
            predictions = pred_outputs.prediction_outputs
            
            # Extract target values (first channel from multivariate targets)
            targets = collated_batch['future_values'][:, :, 0]  # [batch, pred_len]
            
            logger.info(f"Prediction shape for loss: {predictions.shape}")
            logger.info(f"Target shape for loss: {targets.shape}")
            
            if predictions.shape == targets.shape:
                # Compute MSE loss
                mse_loss = nn.MSELoss()
                loss = mse_loss(predictions, targets)
                logger.info(f"✅ Manual MSE loss computed: {loss.item():.6f}")
                logger.info("✅ Shape compatibility confirmed!")
                return True
            else:
                logger.error(f"❌ Shape mismatch in manual loss: pred {predictions.shape} vs target {targets.shape}")
                return False
        
        return False
        
    except Exception as e:
        logger.error(f"❌ Error during shape test: {str(e)}")
        
        # Check for specific shape errors
        error_str = str(e).lower()
        if "size of tensor" in error_str or "target size" in error_str:
            logger.error("🚨 Shape mismatch error detected!")
        
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    logger.info("=" * 60)
    logger.info("🧪 FINAL SHAPE COMPATIBILITY TEST")
    logger.info("=" * 60)
    
    success = test_shape_compatibility()
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 SUCCESS: Shape mismatch issue is completely resolved!")
        logger.info("✅ Training should work without tensor shape errors.")
        logger.info("📝 You can now run: python train.py thickness_thickness_avg")
        return 0
    else:
        logger.error("❌ FAILURE: Shape compatibility issues still exist.")
        return 1

if __name__ == "__main__":
    exit(main())