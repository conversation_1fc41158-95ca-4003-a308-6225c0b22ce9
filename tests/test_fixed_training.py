#!/usr/bin/env python3
"""
Test the fixed PatchTST training with proper target shapes
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_fixed_model():
    """Test the fixed model with proper dataset"""
    
    from src.forecasting.patchtst_model import ManufacturingDataset, ManufacturingPatchTSTModel
    from src.forecasting.config import ForecastConfig, PatchTSTTrainingConfig
    import torch
    
    logger.info("🔧 Testing Fixed PatchTST Model...")
    
    # Create sample config
    forecast_config = ForecastConfig(
        input_variables=["thickness_thickness_avg", "thickness_thickness_uniformity", "speed_Speed", 
                        "stop_MPS Stop Duration", "fm_stack_Sheet Quantity", "fm_stack_Sheet Acceped"],
        target_variables=["thickness_thickness_avg"],
        forecast_horizons=[240],
        lookback_window=240,
        patch_size=16,
        patch_stride=16,
        model_params={
            "d_model": 64,
            "num_attention_heads": 4,
            "num_hidden_layers": 2,
            "dropout": 0.1
        }
    )
    
    training_config = PatchTSTTrainingConfig(
        batch_size=2,
        max_epochs=1,
        learning_rate=1e-4
    )
    
    # Create model
    model = ManufacturingPatchTSTModel(forecast_config, training_config)
    
    # Initialize model with 6 input channels
    model.initialize_model(6)
    
    logger.info("✅ Model initialized successfully")
    
    # Test with sample data
    sample_sequences = [{
        'features': [[1.0, 2.0, 3.0, 4.0, 5.0, 6.0] for _ in range(240)],
        'targets': {
            'horizon_240': [7.0] * 240
        }
    }]
    
    # Create dataset
    dataset = ManufacturingDataset(sample_sequences, max_horizon=240)
    sample = dataset[0]
    
    logger.info(f"Dataset sample shapes:")
    logger.info(f"  past_values: {sample['past_values'].shape}")
    logger.info(f"  future_values: {sample['future_values'].shape}")
    
    # Test model forward pass
    batch_past_values = sample['past_values'].unsqueeze(0)  # Add batch dimension
    batch_future_values = sample['future_values'].unsqueeze(0)  # Add batch dimension
    
    logger.info(f"Batch shapes:")
    logger.info(f"  Input (past_values): {batch_past_values.shape}")
    logger.info(f"  Target (future_values): {batch_future_values.shape}")
    
    # Forward pass
    model.model.eval()
    with torch.no_grad():
        outputs = model.model(past_values=batch_past_values)
    
    if hasattr(outputs, 'prediction_outputs'):
        pred_shape = outputs.prediction_outputs.shape
        logger.info(f"  Prediction outputs: {pred_shape}")
        
        # Check if shapes are compatible for loss calculation
        target_shape = batch_future_values.shape
        if pred_shape == target_shape:
            logger.info("✅ Output and target shapes match perfectly!")
            
            # Test loss calculation
            loss_fn = torch.nn.MSELoss()
            loss = loss_fn(outputs.prediction_outputs, batch_future_values)
            logger.info(f"✅ Loss calculation successful: {loss.item():.6f}")
            
            return True
        else:
            logger.error(f"❌ Shape mismatch: pred {pred_shape} vs target {target_shape}")
            return False
    else:
        logger.error("❌ No prediction outputs found")
        return False

def main():
    """Main test function"""
    try:
        success = test_fixed_model()
        if success:
            logger.info("🎉 All tests passed! The fix is working correctly.")
            logger.info("📝 You can now run the training with:")
            logger.info("   python train.py thickness_thickness_avg")
            return 0
        else:
            logger.error("❌ Tests failed. More fixes needed.")
            return 1
    except Exception as e:
        logger.error(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())