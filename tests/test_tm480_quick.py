#!/usr/bin/env python3
"""
Quick test script for TM480 integration validation.

Focuses on key integration points without running the full unified table creation.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.loader import ManufacturingDataLoader
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tm480_quick():
    """Quick validation of TM480 integration key features."""
    try:
        logger.info("🚀 Quick TM480 integration validation...")
        
        # Initialize the loader
        config_path = Path(__file__).parent / "data.config.json"
        loader = ManufacturingDataLoader(str(config_path))
        
        # Load data sources
        logger.info("📂 Loading data sources...")
        loader.load_all_data_sources()
        
        # Validate TM480 data loading
        if 'tm480_stack' not in loader.loaded_data:
            logger.error("❌ TM480 data not loaded")
            return False
        
        tm480_raw = loader.loaded_data['tm480_stack']
        logger.info(f"✅ TM480 data loaded: {len(tm480_raw):,} records")
        
        # Test TM480 preparation
        logger.info("⚙️ Testing TM480 data preparation...")
        prepared_tm480 = loader.prepare_tm480_stack_data(tm480_raw)
        logger.info(f"✅ TM480 preparation: {len(prepared_tm480):,} filtered stacks")
        
        # Check key fields
        required_fields = ['tm480_stack_number', 'tm480_start_timestamp', 'tm480_duration_minutes', 
                          'tm480_reject_pct', 'fm_machine_type', 'fm_source_system']
        missing_fields = [field for field in required_fields if field not in prepared_tm480.columns]
        
        if missing_fields:
            logger.error(f"❌ Missing required fields: {missing_fields}")
            return False
        else:
            logger.info(f"✅ All required TM480 fields present")
        
        # Validate machine type tracking
        tm480_machine_types = prepared_tm480['fm_machine_type'].value_counts()
        logger.info(f"🏷️ Machine types: {dict(tm480_machine_types)}")
        
        tm480_source_systems = prepared_tm480['fm_source_system'].value_counts()
        logger.info(f"🏷️ Source systems: {dict(tm480_source_systems)}")
        
        # Test multi-FM matching preparation (without full run)
        logger.info("🔄 Testing multi-FM dataset creation...")
        
        # Prepare original FM data for comparison
        if 'fm_stack' in loader.loaded_data:
            prepared_fm = loader.prepare_fm_stack_data(loader.loaded_data['fm_stack'])
            logger.info(f"✅ Original FM preparation: {len(prepared_fm):,} stacks")
            
            # Test unified FM dataset creation logic
            total_fm_sources = len(prepared_fm) + len(prepared_tm480)
            logger.info(f"📊 Combined FM sources: {total_fm_sources:,} stacks")
            logger.info(f"   • Original FM: {len(prepared_fm):,} stacks")
            logger.info(f"   • TM480: {len(prepared_tm480):,} stacks")
            
            # Check stack number overlap analysis
            fm_stack_numbers = set(prepared_fm['MPS ID'].astype(str))
            tm480_stack_numbers = set(prepared_tm480['tm480_stack_number'].astype(str))
            
            overlap = fm_stack_numbers.intersection(tm480_stack_numbers)
            unique_fm = len(fm_stack_numbers - tm480_stack_numbers) 
            unique_tm480 = len(tm480_stack_numbers - fm_stack_numbers)
            
            logger.info(f"📈 Stack number analysis:")
            logger.info(f"   • Overlapping stack numbers: {len(overlap):,}")
            logger.info(f"   • Unique to FM: {unique_fm:,}")
            logger.info(f"   • Unique to TM480: {unique_tm480:,}")
            logger.info(f"   • Total unique stacks: {len(overlap) + unique_fm + unique_tm480:,}")
            
            # Estimate improvement potential
            if len(overlap) == 0:
                logger.info("💡 No stack number overlap - perfect complementary coverage!")
                potential_improvement = (len(prepared_tm480) / 14380) * 100  # 14380 SM stacks
                logger.info(f"📈 Potential match rate improvement: +{potential_improvement:.1f} percentage points")
            else:
                logger.info(f"⚠️ {len(overlap)} stack numbers appear in both sources")
        
        logger.info("🎉 Quick TM480 integration validation completed successfully!")
        
        # Summary
        logger.info("=" * 50)
        logger.info("📋 INTEGRATION SUMMARY:")
        logger.info(f"   ✅ TM480 data loading: WORKING")
        logger.info(f"   ✅ TM480 data preparation: WORKING") 
        logger.info(f"   ✅ Timestamp processing: WORKING")
        logger.info(f"   ✅ Machine type tracking: WORKING")
        logger.info(f"   ✅ Stack number filtering: WORKING")
        logger.info(f"   📊 TM480 stacks available: {len(prepared_tm480):,}")
        logger.info(f"   🚀 Ready for full multi-FM matching")
        logger.info("=" * 50)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Quick validation failed: {e}")
        import traceback
        logger.error(f"Full error traceback:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_tm480_quick()
    if success:
        print("\n✅ VALIDATION PASSED: TM480 integration ready for production")
    else:
        print("\n❌ VALIDATION FAILED: TM480 integration needs fixes")
        sys.exit(1)