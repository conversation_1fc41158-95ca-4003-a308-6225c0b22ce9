"""
Tests for transfer learning fine-tuning functionality.

Validates linear probing, full fine-tuning, and adaptive freezing strategies.
"""

import pytest
import torch
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch

from src.forecasting.transfer_learning.linear_probing import LinearProbingTrainer
from src.forecasting.transfer_learning.fine_tuning import (
    FullFineTuningTrainer,
    AdaptiveFreezingStrategy,
    LayerFreezingScheduler
)
from src.forecasting import ForecastConfig, PatchTSTTrainingConfig


class TestLinearProbingTrainer:
    """Test linear probing functionality"""
    
    def test_linear_probing_initialization(self):
        """Test LinearProbingTrainer initialization"""
        config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        trainer = LinearProbingTrainer(config, training_config)
        
        assert trainer.forecast_config == config
        assert trainer.training_config == training_config
        assert trainer.freeze_backbone is True
        assert trainer.learning_rate_head > trainer.learning_rate_backbone
    
    def test_backbone_freezing(self):
        """Test that backbone layers are properly frozen"""
        trainer = LinearProbingTrainer(ForecastConfig(), PatchTSTTrainingConfig())
        
        # Create mock model with backbone and head
        mock_model = Mock()
        mock_model.model = Mock()  # Backbone
        mock_model.prediction_head = Mock()  # Head
        
        # Mock parameter iteration
        backbone_param = torch.nn.Parameter(torch.randn(10, 10))
        head_param = torch.nn.Parameter(torch.randn(5, 5))
        
        mock_model.model.parameters.return_value = [backbone_param]
        mock_model.prediction_head.parameters.return_value = [head_param]
        
        # Apply freezing
        trainer.freeze_backbone_layers(mock_model)
        
        # Verify backbone is frozen
        assert backbone_param.requires_grad is False
        # Head should remain trainable
        assert head_param.requires_grad is True
    
    def test_linear_probing_training_setup(self):
        """Test linear probing training configuration"""
        config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        training_config.num_epochs = 10
        
        trainer = LinearProbingTrainer(config, training_config)
        
        # Mock model
        mock_model = Mock()
        
        # Get training arguments
        training_args = trainer.get_training_arguments()
        
        # Verify linear probing specific settings
        assert training_args.learning_rate == trainer.learning_rate_head
        assert training_args.num_train_epochs <= 20  # Should be limited for linear probing
        assert training_args.warmup_ratio == 0.1  # Conservative warmup for linear probing
    
    def test_fast_adaptation_convergence(self):
        """Test that linear probing converges quickly"""
        trainer = LinearProbingTrainer(ForecastConfig(), PatchTSTTrainingConfig())
        
        # Create synthetic convergence data
        loss_history = [2.5, 1.8, 1.2, 0.9, 0.8, 0.7, 0.65, 0.6]  # Rapid convergence
        
        # Test convergence detection
        converged = trainer.check_fast_convergence(loss_history, patience=3, threshold=0.05)
        assert converged is True
        
        # Test non-convergence
        slow_loss = [2.5, 2.4, 2.3, 2.2, 2.1, 2.0, 1.9]  # Slow convergence
        not_converged = trainer.check_fast_convergence(slow_loss, patience=3, threshold=0.05)
        assert not_converged is False
    
    def test_linear_probing_with_manufacturing_data(self):
        """Test linear probing with manufacturing data patterns"""
        np.random.seed(42)
        
        # Create small manufacturing dataset
        n_samples = 100
        manufacturing_data = pd.DataFrame({
            'timestamp': pd.date_range('2024-01-01', periods=n_samples, freq='1min'),
            'thickness_avg': np.random.normal(16.0, 0.5, n_samples),
            'speed': np.random.normal(50.0, 5.0, n_samples),
            'scrap_rate': np.random.exponential(2.0, n_samples)
        })
        
        config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        training_config.num_epochs = 5  # Short for testing
        
        trainer = LinearProbingTrainer(config, training_config)
        
        # Mock pretrained model
        with patch('src.forecasting.transfer_learning.pretrained_loader.load_granite_model') as mock_load:
            mock_model = Mock()
            mock_model.config = Mock()
            mock_model.config.num_input_channels = 3
            mock_load.return_value = mock_model
            
            # Test training setup (should not crash)
            try:
                training_setup = trainer.setup_linear_probing(manufacturing_data, 'thickness_avg')
                assert training_setup is not None
            except Exception as e:
                pytest.skip(f"Linear probing setup failed: {str(e)}")


class TestFullFineTuningTrainer:
    """Test full fine-tuning functionality"""
    
    def test_full_fine_tuning_initialization(self):
        """Test FullFineTuningTrainer initialization"""
        config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        trainer = FullFineTuningTrainer(config, training_config)
        
        assert trainer.forecast_config == config
        assert trainer.training_config == training_config
        assert trainer.adaptive_freezing is True
        assert trainer.layer_freezing_scheduler is not None
    
    def test_gradual_unfreezing_strategy(self):
        """Test gradual layer unfreezing during training"""
        trainer = FullFineTuningTrainer(ForecastConfig(), PatchTSTTrainingConfig())
        
        # Mock model with multiple layers
        mock_model = Mock()
        layers = [Mock() for _ in range(6)]  # 6 transformer layers
        
        for i, layer in enumerate(layers):
            layer.parameters.return_value = [torch.nn.Parameter(torch.randn(5, 5))]
        
        mock_model.model.encoder.layers = layers
        
        # Test freezing schedule
        freezing_schedule = trainer.get_layer_freezing_schedule(total_epochs=12)
        
        # Should gradually unfreeze layers
        assert len(freezing_schedule) > 0
        assert all(epoch >= 0 for epoch in freezing_schedule.values())
        
        # Later layers should unfreeze earlier (closer to head)
        layer_unfreeze_epochs = list(freezing_schedule.values())
        assert layer_unfreeze_epochs == sorted(layer_unfreeze_epochs, reverse=True)
    
    def test_adaptive_learning_rate_scheduling(self):
        """Test adaptive learning rate scheduling"""
        trainer = FullFineTuningTrainer(ForecastConfig(), PatchTSTTrainingConfig())
        
        # Test learning rate calculation for different layer groups
        backbone_lr = trainer.calculate_layer_learning_rate('backbone', base_lr=1e-4)
        middle_lr = trainer.calculate_layer_learning_rate('middle', base_lr=1e-4)
        head_lr = trainer.calculate_layer_learning_rate('head', base_lr=1e-4)
        
        # Head should have highest LR, backbone lowest
        assert head_lr >= middle_lr >= backbone_lr
        assert backbone_lr < 1e-4  # Should be reduced for stability
        assert head_lr <= 1e-3  # Should not be too high
    
    def test_discriminative_fine_tuning(self):
        """Test discriminative fine-tuning with different learning rates"""
        trainer = FullFineTuningTrainer(ForecastConfig(), PatchTSTTrainingConfig())
        
        # Mock model with parameter groups
        mock_model = Mock()
        
        # Mock different parameter groups
        backbone_params = [torch.nn.Parameter(torch.randn(10, 10)) for _ in range(3)]
        head_params = [torch.nn.Parameter(torch.randn(5, 5)) for _ in range(2)]
        
        param_groups = trainer.setup_discriminative_learning_rates(
            backbone_params, head_params, base_lr=1e-4
        )
        
        assert len(param_groups) == 2  # Backbone and head groups
        assert param_groups[0]['lr'] < param_groups[1]['lr']  # Backbone < Head
        assert all('params' in group for group in param_groups)
    
    def test_transfer_learning_curriculum(self):
        """Test transfer learning curriculum strategy"""
        trainer = FullFineTuningTrainer(ForecastConfig(), PatchTSTTrainingConfig())
        
        # Define curriculum phases
        curriculum = trainer.get_training_curriculum(total_epochs=20)
        
        # Should have multiple phases
        assert len(curriculum) >= 2
        
        # Verify phase progression
        phase_names = list(curriculum.keys())
        expected_order = ['linear_probing', 'gradual_unfreezing', 'full_fine_tuning']
        
        for expected_phase in expected_order:
            if expected_phase in phase_names:
                assert phase_names.index(expected_phase) >= 0
    
    def test_stability_monitoring_during_fine_tuning(self):
        """Test stability monitoring during fine-tuning"""
        trainer = FullFineTuningTrainer(ForecastConfig(), PatchTSTTrainingConfig())
        
        # Simulate training loss history
        stable_losses = [2.0, 1.8, 1.6, 1.4, 1.2, 1.0, 0.9, 0.8]
        unstable_losses = [2.0, 1.5, 3.0, 0.5, 5.0, 1.0, 0.8, 2.5]
        
        # Test stability detection
        stable_score = trainer.calculate_training_stability(stable_losses)
        unstable_score = trainer.calculate_training_stability(unstable_losses)
        
        assert stable_score > unstable_score
        assert stable_score > 0.7  # Should be considered stable
        assert unstable_score < 0.5  # Should be considered unstable


class TestAdaptiveFreezingStrategy:
    """Test adaptive freezing strategy"""
    
    def test_adaptive_freezing_initialization(self):
        """Test AdaptiveFreezingStrategy initialization"""
        strategy = AdaptiveFreezingStrategy()
        
        assert strategy.patience == 3
        assert strategy.threshold == 0.01
        assert strategy.min_epochs_per_layer == 2
    
    def test_layer_unfreezing_decision(self):
        """Test decision making for layer unfreezing"""
        strategy = AdaptiveFreezingStrategy(patience=2, threshold=0.05)
        
        # Test with improving loss (should not unfreeze yet)
        improving_losses = [1.0, 0.8, 0.6, 0.4]
        should_unfreeze = strategy.should_unfreeze_layer(improving_losses)
        assert should_unfreeze is False
        
        # Test with plateaued loss (should unfreeze)
        plateaued_losses = [1.0, 0.8, 0.79, 0.78, 0.775]  # Minimal improvement
        should_unfreeze = strategy.should_unfreeze_layer(plateaued_losses)
        assert should_unfreeze is True
    
    def test_freezing_schedule_generation(self):
        """Test automatic freezing schedule generation"""
        strategy = AdaptiveFreezingStrategy()
        
        # Generate schedule for model with 6 layers
        schedule = strategy.generate_adaptive_schedule(
            num_layers=6, 
            estimated_total_epochs=24
        )
        
        assert len(schedule) <= 6  # Should not exceed number of layers
        assert all(epoch > 0 for epoch in schedule.values())
        
        # Later layers should unfreeze earlier
        layer_epochs = [(layer, epoch) for layer, epoch in schedule.items()]
        layer_epochs.sort(key=lambda x: int(x[0].split('_')[-1]))  # Sort by layer number
        
        epochs = [epoch for _, epoch in layer_epochs]
        assert epochs == sorted(epochs, reverse=True)


class TestLayerFreezingScheduler:
    """Test layer freezing scheduler"""
    
    def test_scheduler_initialization(self):
        """Test LayerFreezingScheduler initialization"""
        scheduler = LayerFreezingScheduler()
        
        assert scheduler.current_epoch == 0
        assert scheduler.frozen_layers == set()
        assert scheduler.unfreezing_schedule == {}
    
    def test_epoch_based_unfreezing(self):
        """Test epoch-based layer unfreezing"""
        scheduler = LayerFreezingScheduler()
        
        # Set unfreezing schedule
        schedule = {
            'layer_5': 5,
            'layer_4': 8,
            'layer_3': 12,
            'layer_2': 16,
            'layer_1': 20,
            'layer_0': 24
        }
        scheduler.set_unfreezing_schedule(schedule)
        
        # Mock model layers
        mock_layers = {}
        for layer_name in schedule.keys():
            mock_layer = Mock()
            mock_layer.parameters.return_value = [torch.nn.Parameter(torch.randn(5, 5))]
            mock_layers[layer_name] = mock_layer
        
        # Test unfreezing at different epochs
        for epoch in range(1, 25):
            scheduler.step_epoch(epoch)
            unfrozen_layers = scheduler.apply_freezing_schedule(mock_layers)
            
            # Verify correct layers are unfrozen
            expected_unfrozen = [name for name, unfreeze_epoch in schedule.items() 
                               if epoch >= unfreeze_epoch]
            
            assert len(unfrozen_layers) == len(expected_unfrozen)
    
    def test_performance_based_unfreezing(self):
        """Test performance-based adaptive unfreezing"""
        scheduler = LayerFreezingScheduler()
        
        # Configure performance-based unfreezing
        scheduler.enable_performance_based_unfreezing(patience=3, threshold=0.02)
        
        # Simulate training with plateau
        loss_history = [2.0, 1.5, 1.2, 1.0, 0.95, 0.94, 0.93, 0.925]  # Plateau
        
        should_unfreeze = scheduler.check_performance_plateau(loss_history)
        assert should_unfreeze is True
        
        # Test with continued improvement
        improving_history = [2.0, 1.5, 1.0, 0.7, 0.4, 0.2]
        should_unfreeze = scheduler.check_performance_plateau(improving_history)
        assert should_unfreeze is False


class TestTransferLearningIntegration:
    """Test end-to-end transfer learning integration"""
    
    def test_transfer_learning_pipeline(self):
        """Test complete transfer learning pipeline"""
        config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        training_config.num_epochs = 10
        
        # Create synthetic manufacturing data
        manufacturing_data = pd.DataFrame({
            'timestamp': pd.date_range('2024-01-01', periods=200, freq='1min'),
            'thickness_avg': np.random.normal(16.0, 0.3, 200),
            'speed': np.random.normal(50.0, 3.0, 200)
        })
        
        # Test linear probing first
        linear_trainer = LinearProbingTrainer(config, training_config)
        
        with patch('src.forecasting.transfer_learning.pretrained_loader.load_granite_model') as mock_load:
            mock_model = Mock()
            mock_model.config = Mock()
            mock_model.config.num_input_channels = 2
            mock_load.return_value = mock_model
            
            # Should be able to setup linear probing
            try:
                linear_setup = linear_trainer.setup_linear_probing(manufacturing_data, 'thickness_avg')
                assert linear_setup is not None
            except Exception as e:
                pytest.skip(f"Linear probing integration test failed: {str(e)}")
        
        # Test full fine-tuning
        full_trainer = FullFineTuningTrainer(config, training_config)
        
        try:
            curriculum = full_trainer.get_training_curriculum(total_epochs=10)
            assert len(curriculum) > 0
        except Exception as e:
            pytest.skip(f"Full fine-tuning integration test failed: {str(e)}")
    
    def test_fallback_to_fresh_model(self):
        """Test fallback when pretrained model unavailable"""
        config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        # Mock failed pretrained model loading
        with patch('src.forecasting.transfer_learning.pretrained_loader.load_granite_model') as mock_load:
            mock_load.return_value = None  # No pretrained model available
            
            trainer = FullFineTuningTrainer(config, training_config)
            
            # Should handle gracefully and suggest fresh model training
            fallback_strategy = trainer.get_fallback_strategy()
            
            assert fallback_strategy['use_fresh_model'] is True
            assert fallback_strategy['recommended_epochs'] > training_config.num_epochs


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__])