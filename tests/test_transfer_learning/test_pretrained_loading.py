"""
Tests for transfer learning pretrained model loading functionality.

Validates IBM Granite model loading, compatibility checks, and fallback mechanisms.
"""

import pytest
from unittest.mock import Mock, patch
from pathlib import Path
import tempfile

from src.forecasting.transfer_learning.pretrained_loader import (
    PretrainedModelLoader,
    Model<PERSON>ompatibility<PERSON><PERSON>cker,
    load_granite_model,
    check_model_compatibility
)
from src.forecasting import ForecastConfig


class TestPretrainedModelLoader:
    """Test pretrained model loading functionality"""
    
    def test_loader_initialization(self):
        """Test PretrainedModelLoader initialization"""
        loader = PretrainedModelLoader()
        
        assert loader.supported_models is not None
        assert 'granite-timeseries-patchtst' in loader.supported_models
        assert loader.fallback_enabled is True
    
    def test_granite_model_loading_success(self):
        """Test successful IBM Granite model loading"""
        loader = PretrainedModelLoader()
        
        # Mock successful model loading
        with patch('transformers.PatchTSTForPrediction.from_pretrained') as mock_load:
            mock_model = Mock()
            mock_model.config = Mock()
            mock_model.config.num_input_channels = 5
            mock_model.config.prediction_length = 96
            mock_model.config.d_model = 128
            
            mock_load.return_value = mock_model
            
            # Test loading
            result = loader.load_granite_model()
            
            assert result is not None
            assert result == mock_model
            mock_load.assert_called_once()
    
    def test_granite_model_loading_failure_fallback(self):
        """Test fallback behavior when Granite model loading fails"""
        loader = PretrainedModelLoader()
        
        # Mock failed model loading
        with patch('transformers.PatchTSTForPrediction.from_pretrained') as mock_load:
            mock_load.side_effect = Exception("Model not found")
            
            # Test loading with fallback
            result = loader.load_granite_model()
            
            assert result is None  # Should return None for fallback handling
    
    def test_model_availability_check(self):
        """Test model availability checking"""
        loader = PretrainedModelLoader()
        
        # Mock successful availability check
        with patch('transformers.PatchTSTConfig.from_pretrained') as mock_config:
            mock_config.return_value = Mock()
            
            is_available = loader.check_model_availability('ibm-granite/granite-timeseries-patchtst')
            assert is_available is True
    
    def test_model_availability_check_failure(self):
        """Test model availability check when model not found"""
        loader = PretrainedModelLoader()
        
        # Mock failed availability check
        with patch('transformers.PatchTSTConfig.from_pretrained') as mock_config:
            mock_config.side_effect = Exception("Model not available")
            
            is_available = loader.check_model_availability('non-existent-model')
            assert is_available is False
    
    def test_custom_model_loading(self):
        """Test loading custom pretrained models"""
        loader = PretrainedModelLoader()
        
        with patch('transformers.PatchTSTForPrediction.from_pretrained') as mock_load:
            mock_model = Mock()
            mock_load.return_value = mock_model
            
            result = loader.load_model('custom-model-path')
            
            assert result == mock_model
            mock_load.assert_called_once_with('custom-model-path')


class TestModelCompatibilityChecker:
    """Test model compatibility validation"""
    
    def test_compatibility_checker_initialization(self):
        """Test ModelCompatibilityChecker initialization"""
        config = ForecastConfig()
        checker = ModelCompatibilityChecker(config)
        
        assert checker.forecast_config == config
        assert checker.tolerance_ratio == 0.1  # Default tolerance
    
    def test_channel_compatibility_check(self):
        """Test input channel compatibility validation"""
        config = ForecastConfig()
        config.input_variables = ['thickness_avg', 'speed', 'scrap_rate']  # 3 channels
        
        checker = ModelCompatibilityChecker(config)
        
        # Mock model with compatible channels
        compatible_model = Mock()
        compatible_model.config = Mock()
        compatible_model.config.num_input_channels = 3
        
        is_compatible = checker.check_input_channels(compatible_model)
        assert is_compatible is True
        
        # Mock model with incompatible channels
        incompatible_model = Mock()
        incompatible_model.config = Mock()
        incompatible_model.config.num_input_channels = 7
        
        is_compatible = checker.check_input_channels(incompatible_model)
        assert is_compatible is False
    
    def test_prediction_length_compatibility(self):
        """Test prediction length compatibility validation"""
        config = ForecastConfig()
        config.forecast_horizons = [15, 60, 96]  # Max horizon = 96
        
        checker = ModelCompatibilityChecker(config)
        
        # Compatible model
        compatible_model = Mock()
        compatible_model.config = Mock()
        compatible_model.config.prediction_length = 96
        
        is_compatible = checker.check_prediction_length(compatible_model)
        assert is_compatible is True
        
        # Incompatible model (too short)
        incompatible_model = Mock()
        incompatible_model.config = Mock()
        incompatible_model.config.prediction_length = 30
        
        is_compatible = checker.check_prediction_length(incompatible_model)
        assert is_compatible is False
    
    def test_context_length_compatibility(self):
        """Test context length compatibility validation"""
        config = ForecastConfig()
        config.lookback_window = 240
        
        checker = ModelCompatibilityChecker(config)
        
        # Compatible model
        compatible_model = Mock()
        compatible_model.config = Mock()
        compatible_model.config.context_length = 256
        
        is_compatible = checker.check_context_length(compatible_model)
        assert is_compatible is True
        
        # Incompatible model (too short)
        incompatible_model = Mock()
        incompatible_model.config = Mock()
        incompatible_model.config.context_length = 100
        
        is_compatible = checker.check_context_length(incompatible_model)
        assert is_compatible is False
    
    def test_comprehensive_compatibility_check(self):
        """Test comprehensive model compatibility validation"""
        config = ForecastConfig()
        config.input_variables = ['thickness_avg', 'speed']  # 2 channels
        config.forecast_horizons = [15, 60]  # Max horizon = 60
        config.lookback_window = 200
        
        checker = ModelCompatibilityChecker(config)
        
        # Fully compatible model
        compatible_model = Mock()
        compatible_model.config = Mock()
        compatible_model.config.num_input_channels = 2
        compatible_model.config.prediction_length = 96
        compatible_model.config.context_length = 256
        
        compatibility_result = checker.check_full_compatibility(compatible_model)
        
        assert compatibility_result.is_compatible is True
        assert compatibility_result.channel_compatible is True
        assert compatibility_result.prediction_length_compatible is True
        assert compatibility_result.context_length_compatible is True
        assert len(compatibility_result.incompatibility_reasons) == 0
        
        # Partially incompatible model
        incompatible_model = Mock()
        incompatible_model.config = Mock()
        incompatible_model.config.num_input_channels = 5  # Wrong channels
        incompatible_model.config.prediction_length = 30   # Too short
        incompatible_model.config.context_length = 256     # OK
        
        compatibility_result = checker.check_full_compatibility(incompatible_model)
        
        assert compatibility_result.is_compatible is False
        assert compatibility_result.channel_compatible is False
        assert compatibility_result.prediction_length_compatible is False
        assert compatibility_result.context_length_compatible is True
        assert len(compatibility_result.incompatibility_reasons) == 2
    
    def test_adaptation_recommendations(self):
        """Test model adaptation recommendations"""
        config = ForecastConfig()
        config.input_variables = ['thickness_avg', 'speed', 'scrap_rate']
        
        checker = ModelCompatibilityChecker(config)
        
        # Model needing adaptation
        model = Mock()
        model.config = Mock()
        model.config.num_input_channels = 7
        model.config.prediction_length = 30
        model.config.context_length = 128
        
        compatibility_result = checker.check_full_compatibility(model)
        recommendations = checker.get_adaptation_recommendations(compatibility_result)
        
        assert 'input_projection' in recommendations
        assert 'prediction_head_extension' in recommendations
        assert recommendations['input_projection']['required'] is True
        assert recommendations['prediction_head_extension']['required'] is True


class TestHighLevelIntegration:
    """Test high-level transfer learning integration functions"""
    
    def test_load_granite_model_function(self):
        """Test high-level load_granite_model function"""
        with patch('src.forecasting.transfer_learning.pretrained_loader.PretrainedModelLoader') as mock_loader_class:
            mock_loader = Mock()
            mock_model = Mock()
            mock_loader.load_granite_model.return_value = mock_model
            mock_loader_class.return_value = mock_loader
            
            result = load_granite_model()
            
            assert result == mock_model
            mock_loader.load_granite_model.assert_called_once()
    
    def test_check_model_compatibility_function(self):
        """Test high-level check_model_compatibility function"""
        config = ForecastConfig()
        mock_model = Mock()
        
        with patch('src.forecasting.transfer_learning.pretrained_loader.ModelCompatibilityChecker') as mock_checker_class:
            mock_checker = Mock()
            mock_result = Mock()
            mock_result.is_compatible = True
            mock_checker.check_full_compatibility.return_value = mock_result
            mock_checker_class.return_value = mock_checker
            
            result = check_model_compatibility(mock_model, config)
            
            assert result == mock_result
            mock_checker.check_full_compatibility.assert_called_once_with(mock_model)
    
    def test_model_loading_with_network_issues(self):
        """Test model loading handles network connectivity issues"""
        loader = PretrainedModelLoader()
        
        # Simulate network timeout
        with patch('transformers.PatchTSTForPrediction.from_pretrained') as mock_load:
            mock_load.side_effect = ConnectionError("Network timeout")
            
            result = loader.load_granite_model()
            
            assert result is None  # Should handle gracefully
    
    def test_model_loading_with_authentication_issues(self):
        """Test model loading handles authentication issues"""
        loader = PretrainedModelLoader()
        
        # Simulate authentication error
        with patch('transformers.PatchTSTForPrediction.from_pretrained') as mock_load:
            mock_load.side_effect = Exception("Authentication required")
            
            result = loader.load_granite_model()
            
            assert result is None  # Should handle gracefully
    
    def test_local_model_loading(self):
        """Test loading models from local paths"""
        loader = PretrainedModelLoader()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            model_path = Path(temp_dir) / "local_model"
            model_path.mkdir()
            
            with patch('transformers.PatchTSTForPrediction.from_pretrained') as mock_load:
                mock_model = Mock()
                mock_load.return_value = mock_model
                
                result = loader.load_model(str(model_path))
                
                assert result == mock_model
                mock_load.assert_called_once_with(str(model_path))
    
    def test_model_adaptation_workflow(self):
        """Test complete model adaptation workflow"""
        config = ForecastConfig()
        config.input_variables = ['thickness_avg', 'speed']
        
        loader = PretrainedModelLoader()
        checker = ModelCompatibilityChecker(config)
        
        # Mock incompatible model that needs adaptation
        with patch('transformers.PatchTSTForPrediction.from_pretrained') as mock_load:
            mock_model = Mock()
            mock_model.config = Mock()
            mock_model.config.num_input_channels = 5  # Needs adaptation
            mock_model.config.prediction_length = 96   # OK
            mock_model.config.context_length = 256     # OK
            
            mock_load.return_value = mock_model
            
            # Load model
            loaded_model = loader.load_granite_model()
            assert loaded_model is not None
            
            # Check compatibility
            compatibility = checker.check_full_compatibility(loaded_model)
            assert compatibility.is_compatible is False
            assert compatibility.channel_compatible is False
            
            # Get adaptation recommendations
            recommendations = checker.get_adaptation_recommendations(compatibility)
            assert recommendations['input_projection']['required'] is True


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__])