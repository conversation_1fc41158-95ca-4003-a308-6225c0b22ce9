"""
Unit Tests for Multi-Method Agent Tools

Tests for multi-method correlation agent tools using real API integration
and manufacturing data. Validates complete tool workflow and context handling.
"""

import pytest
import pandas as pd
import numpy as np
import asyncio
import os
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv
from unittest.mock import Mock
from typing import Dict, Any

# Load environment variables
load_dotenv()

# Import the modules to test
from src.agents.multi_tools import (
    calculate_multi_method_correlations_tool,
    analyze_method_convergence_tool,
    recommend_correlation_method_tool,
    calculate_robustness_metrics_tool,
    MULTI_METHOD_TOOLS
)
from src.agents.correlation_agent import ManufacturingDataDependencies
from pydantic_ai import RunContext
from src.data.loader import ManufacturingDataLoader


class TestMultiMethodToolsConfiguration:
    """Test multi-method tools configuration and registration"""
    
    def test_multi_method_tools_list(self):
        """Test that all multi-method tools are properly registered"""
        assert len(MULTI_METHOD_TOOLS) == 4
        
        expected_tools = [
            calculate_multi_method_correlations_tool,
            analyze_method_convergence_tool,
            recommend_correlation_method_tool,
            calculate_robustness_metrics_tool
        ]
        
        for tool in expected_tools:
            assert tool in MULTI_METHOD_TOOLS
    
    def test_tool_function_signatures(self):
        """Test that all tools have correct function signatures"""
        # Test calculate_multi_method_correlations_tool
        import inspect
        sig = inspect.signature(calculate_multi_method_correlations_tool)
        params = list(sig.parameters.keys())
        assert 'ctx' in params
        assert 'variables' in params
        assert 'min_periods' in params
        assert 'include_robustness' in params
        
        # Test analyze_method_convergence_tool
        sig = inspect.signature(analyze_method_convergence_tool)
        params = list(sig.parameters.keys())
        assert 'ctx' in params
        assert 'correlation_results' in params
        assert 'convergence_threshold' in params
        
        # Test recommend_correlation_method_tool
        sig = inspect.signature(recommend_correlation_method_tool)
        params = list(sig.parameters.keys())
        assert 'ctx' in params
        assert 'variable_1' in params
        assert 'variable_2' in params
        assert 'return_assessment' in params
        
        # Test calculate_robustness_metrics_tool
        sig = inspect.signature(calculate_robustness_metrics_tool)
        params = list(sig.parameters.keys())
        assert 'ctx' in params
        assert 'correlation_results' in params
        assert 'bootstrap_samples' in params
        assert 'variable_pairs' in params


class TestMultiMethodToolsWithMockContext:
    """Test multi-method tools with mock context"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Create sample manufacturing data for testing"""
        np.random.seed(42)
        n_samples = 300
        
        # Create correlated variables with different characteristics
        speed = np.random.normal(150, 10, n_samples)
        temperature = 80 + 0.2 * speed + np.random.normal(0, 2, n_samples)
        
        # Non-linear relationship
        pressure = 50 + 0.1 * np.sqrt(np.abs(speed - 100)) + np.random.normal(0, 3, n_samples)
        
        # With outliers
        thickness = 12 + 0.01 * speed + np.random.normal(0, 0.2, n_samples)
        outlier_indices = np.random.choice(n_samples, size=15, replace=False)
        thickness[outlier_indices] += np.random.normal(0, 2, 15)
        
        # Quality score
        quality = 100 - 0.1 * np.abs(speed - 150) - 0.05 * np.abs(temperature - 80) + np.random.normal(0, 1, n_samples)
        
        return pd.DataFrame({
            'speed': speed,
            'temperature': temperature,
            'pressure': pressure,
            'thickness': thickness,
            'quality_score': quality,
            'timestamp': pd.date_range('2024-01-01', periods=n_samples, freq='1min')
        })
    
    @pytest.fixture
    def mock_context(self, sample_manufacturing_data):
        """Create mock RunContext for testing"""
        # Create dependencies
        deps = ManufacturingDataDependencies(
            data=sample_manufacturing_data,
            time_column='timestamp',
            significance_threshold=0.05,
            min_correlation=0.01
        )
        
        # Create mock context
        context = Mock(spec=RunContext)
        context.deps = deps
        
        return context
    
    def test_calculate_multi_method_correlations_tool_success(self, mock_context):
        """Test successful multi-method correlation calculation"""
        result = calculate_multi_method_correlations_tool(mock_context)
        
        assert isinstance(result, dict)
        assert 'multi_method_results' in result
        assert 'convergence_analysis' in result
        assert 'total_correlations_analyzed' in result
        assert 'methods_analyzed' in result
        assert 'analysis_parameters' in result
        assert 'data_summary' in result
        
        # Check methods analyzed
        assert result['methods_analyzed'] == ['pearson', 'spearman', 'kendall']
        
        # Check multi-method results structure
        multi_results = result['multi_method_results']
        assert len(multi_results) > 0
        
        # Check first result structure
        first_result = next(iter(multi_results.values()))
        required_fields = [
            'variable_1', 'variable_2',
            'pearson_correlation', 'spearman_correlation', 'kendall_correlation',
            'pearson_p_value', 'spearman_p_value', 'kendall_p_value',
            'sample_size', 'method_convergence_score', 'recommended_method',
            'data_distribution_assessment', 'interpretation'
        ]
        
        for field in required_fields:
            assert field in first_result
        
        # Check correlation values are valid
        assert -1 <= first_result['pearson_correlation'] <= 1
        assert -1 <= first_result['spearman_correlation'] <= 1
        assert -1 <= first_result['kendall_correlation'] <= 1
        
        # Check convergence score
        assert 0 <= first_result['method_convergence_score'] <= 1
        
        # Check recommended method
        assert first_result['recommended_method'] in ['pearson', 'spearman', 'kendall']
    
    def test_calculate_multi_method_correlations_tool_specific_variables(self, mock_context):
        """Test multi-method correlation tool with specific variables"""
        variables = ['speed', 'temperature', 'thickness']
        result = calculate_multi_method_correlations_tool(
            mock_context, variables=variables
        )
        
        assert isinstance(result, dict)
        assert 'multi_method_results' in result
        
        # Check that only specified variables are included
        multi_results = result['multi_method_results']
        for result_data in multi_results.values():
            assert result_data['variable_1'] in variables
            assert result_data['variable_2'] in variables
    
    def test_calculate_multi_method_correlations_tool_with_robustness(self, mock_context):
        """Test multi-method correlation tool with robustness analysis"""
        result = calculate_multi_method_correlations_tool(
            mock_context, include_robustness=True
        )
        
        assert isinstance(result, dict)
        assert 'robustness_metrics' in result
        
        # Check robustness metrics structure
        robustness = result['robustness_metrics']
        if 'error' not in robustness:
            assert isinstance(robustness, dict)
            # Should have entries for variable pairs
            for pair_metrics in robustness.values():
                if isinstance(pair_metrics, dict):
                    # Check for method-specific metrics
                    for method in ['pearson', 'spearman', 'kendall']:
                        if method in pair_metrics:
                            method_metrics = pair_metrics[method]
                            assert 'bootstrap_mean' in method_metrics
                            assert 'bootstrap_std' in method_metrics
                            assert 'stability_score' in method_metrics
    
    def test_calculate_multi_method_correlations_tool_empty_data(self):
        """Test multi-method correlation tool with empty data"""
        # Create empty data context
        empty_deps = ManufacturingDataDependencies(
            data=pd.DataFrame(),
            time_column='timestamp'
        )
        
        empty_context = Mock(spec=RunContext)
        empty_context.deps = empty_deps
        
        result = calculate_multi_method_correlations_tool(empty_context)
        
        assert isinstance(result, dict)
        assert 'error' in result
        assert 'No data available' in result['error']
    
    def test_analyze_method_convergence_tool_success(self, mock_context):
        """Test successful method convergence analysis"""
        # First calculate correlations
        corr_result = calculate_multi_method_correlations_tool(mock_context)
        
        # Then analyze convergence
        convergence_result = analyze_method_convergence_tool(
            mock_context, correlation_results=corr_result['multi_method_results']
        )
        
        assert isinstance(convergence_result, dict)
        assert 'overall_convergence_score' in convergence_result
        assert 'method_stability' in convergence_result
        assert 'cross_method_correlations' in convergence_result
        assert 'convergence_distribution' in convergence_result
        assert 'insights' in convergence_result
        assert 'method_agreement_summary' in convergence_result
        
        # Check convergence score
        assert 0 <= convergence_result['overall_convergence_score'] <= 1
        
        # Check method stability
        stability = convergence_result['method_stability']
        for method_stability in ['pearson_stability', 'spearman_stability', 'kendall_stability']:
            assert method_stability in stability
            assert 0 <= stability[method_stability] <= 1
        
        # Check insights
        assert isinstance(convergence_result['insights'], list)
        assert len(convergence_result['insights']) > 0
    
    def test_analyze_method_convergence_tool_auto_calculate(self, mock_context):
        """Test method convergence analysis with auto-calculation"""
        # Don't provide correlation results, should calculate automatically
        convergence_result = analyze_method_convergence_tool(mock_context)
        
        assert isinstance(convergence_result, dict)
        if 'error' not in convergence_result:
            assert 'overall_convergence_score' in convergence_result
            assert 'insights' in convergence_result
    
    def test_recommend_correlation_method_tool_success(self, mock_context):
        """Test successful correlation method recommendation"""
        result = recommend_correlation_method_tool(
            mock_context, variable_1='speed', variable_2='temperature'
        )
        
        assert isinstance(result, dict)
        assert 'variable_pair' in result
        assert 'recommended_method' in result
        assert 'recommendation_confidence' in result
        assert 'reasoning' in result
        assert 'method_guidance' in result
        assert 'correlation_comparison' in result
        assert 'data_distribution_assessment' in result
        
        # Check variable pair
        assert result['variable_pair'] == 'speed - temperature'
        
        # Check recommended method
        assert result['recommended_method'] in ['pearson', 'spearman', 'kendall']
        
        # Check confidence level
        assert result['recommendation_confidence'] in ['high', 'medium', 'low']
        
        # Check reasoning
        assert isinstance(result['reasoning'], list)
        assert len(result['reasoning']) > 0
        
        # Check correlation comparison
        comparison = result['correlation_comparison']
        if 'error' not in comparison:
            for method in ['pearson', 'spearman', 'kendall']:
                assert method in comparison
                assert 'correlation' in comparison[method]
                assert 'p_value' in comparison[method]
    
    def test_recommend_correlation_method_tool_invalid_variables(self, mock_context):
        """Test correlation method recommendation with invalid variables"""
        result = recommend_correlation_method_tool(
            mock_context, variable_1='nonexistent', variable_2='temperature'
        )
        
        assert isinstance(result, dict)
        assert 'error' in result
        assert 'not found' in result['error']
    
    def test_recommend_correlation_method_tool_without_assessment(self, mock_context):
        """Test correlation method recommendation without detailed assessment"""
        result = recommend_correlation_method_tool(
            mock_context, variable_1='speed', variable_2='temperature', return_assessment=False
        )
        
        assert isinstance(result, dict)
        assert 'recommended_method' in result
        # Should not include detailed assessment
        assert 'data_distribution_assessment' not in result or result['data_distribution_assessment'] is None
    
    def test_calculate_robustness_metrics_tool_success(self, mock_context):
        """Test successful robustness metrics calculation"""
        # First calculate correlations
        corr_result = calculate_multi_method_correlations_tool(mock_context)
        
        # Calculate robustness with fewer bootstrap samples for testing
        robustness_result = calculate_robustness_metrics_tool(
            mock_context, 
            correlation_results=corr_result['multi_method_results'],
            bootstrap_samples=10
        )
        
        assert isinstance(robustness_result, dict)
        assert 'robustness_metrics' in robustness_result
        assert 'overall_robustness' in robustness_result
        assert 'insights' in robustness_result
        assert 'analysis_parameters' in robustness_result
        
        # Check analysis parameters
        params = robustness_result['analysis_parameters']
        assert params['bootstrap_samples'] == 10
        assert 'variable_pairs_analyzed' in params
        
        # Check overall robustness
        overall = robustness_result['overall_robustness']
        assert 'overall_stability_score' in overall
        assert 'method_robustness_summary' in overall
        
        if overall['overall_stability_score'] > 0:
            assert 0 <= overall['overall_stability_score'] <= 1
    
    def test_calculate_robustness_metrics_tool_specific_pairs(self, mock_context):
        """Test robustness metrics calculation with specific variable pairs"""
        variable_pairs = [('speed', 'temperature'), ('pressure', 'thickness')]
        
        robustness_result = calculate_robustness_metrics_tool(
            mock_context,
            variable_pairs=variable_pairs,
            bootstrap_samples=5
        )
        
        assert isinstance(robustness_result, dict)
        if 'error' not in robustness_result:
            params = robustness_result['analysis_parameters']
            assert params['requested_variable_pairs'] == variable_pairs
    
    def test_calculate_robustness_metrics_tool_auto_calculate(self, mock_context):
        """Test robustness metrics calculation with auto-correlation calculation"""
        # Don't provide correlation results, should calculate automatically
        robustness_result = calculate_robustness_metrics_tool(
            mock_context, bootstrap_samples=5
        )
        
        assert isinstance(robustness_result, dict)
        # Should either succeed or fail gracefully
        if 'error' not in robustness_result:
            assert 'robustness_metrics' in robustness_result


class TestMultiMethodToolsWithRealData:
    """Test multi-method tools with real manufacturing data"""
    
    @pytest.fixture
    def real_manufacturing_data(self):
        """Load real test data if available"""
        test_data_path = Path('test-data')
        
        if test_data_path.exists():
            try:
                loader = ManufacturingDataLoader(str(test_data_path))
                all_data = loader.load_all_manufacturing_data()
                
                if all_data:
                    # Create unified dataset
                    unified_data = loader.create_unified_dataset()
                    if len(unified_data) > 100:  # Ensure sufficient data
                        return unified_data
            except Exception as e:
                pytest.skip(f"Could not load real test data: {e}")
        
        # Create synthetic data if real data not available
        return self._create_synthetic_data()
    
    def _create_synthetic_data(self):
        """Create realistic synthetic manufacturing data"""
        np.random.seed(42)
        n_samples = 500
        
        # Create realistic manufacturing correlations
        base_speed = np.random.normal(150, 12, n_samples)
        base_temp = 75 + 0.18 * base_speed + np.random.normal(0, 3, n_samples)
        
        # Add realistic patterns
        thickness_avg = 12.0 + 0.008 * base_speed + 0.003 * base_temp + np.random.normal(0, 0.25, n_samples)
        thickness_uniformity = 0.1 + 0.002 * np.abs(base_speed - 150) + np.random.exponential(0.05, n_samples)
        
        # Production quality metrics
        scrap_rate = np.where(
            (base_speed < 130) | (base_speed > 170), 
            0.08 + 0.02 * np.random.exponential(1, n_samples),
            0.02 + 0.01 * np.random.exponential(1, n_samples)
        )
        
        return pd.DataFrame({
            'speed': base_speed,
            'temperature': base_temp,
            'thickness_avg': thickness_avg,
            'thickness_uniformity': thickness_uniformity,
            'scrap_rate': scrap_rate,
            'timestamp': pd.date_range('2024-01-01', periods=n_samples, freq='2min')
        })
    
    @pytest.fixture
    def real_data_context(self, real_manufacturing_data):
        """Create context with real manufacturing data"""
        deps = ManufacturingDataDependencies(
            data=real_manufacturing_data,
            time_column='timestamp',
            significance_threshold=0.05,
            min_correlation=0.01
        )
        
        context = Mock(spec=RunContext)
        context.deps = deps
        return context
    
    def test_multi_method_correlations_with_real_data(self, real_data_context):
        """Test multi-method correlation analysis with real manufacturing data"""
        result = calculate_multi_method_correlations_tool(real_data_context)
        
        assert isinstance(result, dict)
        assert 'error' not in result or len(result.get('multi_method_results', {})) > 0
        
        if 'multi_method_results' in result:
            multi_results = result['multi_method_results']
            assert len(multi_results) > 0
            
            # Check that manufacturing variable patterns are detected
            for pair_key, pair_result in multi_results.items():
                # All correlations should be within valid range
                assert -1 <= pair_result['pearson_correlation'] <= 1
                assert -1 <= pair_result['spearman_correlation'] <= 1
                assert -1 <= pair_result['kendall_correlation'] <= 1
                
                # Sample size should be reasonable
                assert pair_result['sample_size'] >= 10
                
                # Convergence score should be computed
                assert 0 <= pair_result['method_convergence_score'] <= 1
    
    def test_method_recommendations_with_real_data(self, real_data_context):
        """Test method recommendations with real manufacturing data"""
        data = real_data_context.deps.data
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        
        if len(numeric_cols) >= 2:
            var1, var2 = numeric_cols[0], numeric_cols[1]
            
            result = recommend_correlation_method_tool(
                real_data_context, variable_1=var1, variable_2=var2
            )
            
            assert isinstance(result, dict)
            if 'error' not in result:
                assert 'recommended_method' in result
                assert result['recommended_method'] in ['pearson', 'spearman', 'kendall']
                assert 'reasoning' in result
                assert len(result['reasoning']) > 0
    
    def test_robustness_analysis_with_real_data(self, real_data_context):
        """Test robustness analysis with real manufacturing data"""
        # Use reduced bootstrap samples for testing performance
        result = calculate_robustness_metrics_tool(
            real_data_context, bootstrap_samples=15
        )
        
        assert isinstance(result, dict)
        
        # Should complete without errors or provide meaningful error message
        if 'error' in result:
            # Error should be descriptive
            assert len(result['error']) > 10
        else:
            assert 'robustness_metrics' in result
            assert 'overall_robustness' in result


class TestMultiMethodToolsEdgeCases:
    """Test edge cases and error conditions for multi-method tools"""
    
    def test_tools_with_minimal_data(self):
        """Test multi-method tools with minimal data"""
        # Create very small dataset
        minimal_data = pd.DataFrame({
            'var1': [1, 2, 3, 4, 5],
            'var2': [2, 3, 4, 5, 6]
        })
        
        deps = ManufacturingDataDependencies(data=minimal_data)
        context = Mock(spec=RunContext)
        context.deps = deps
        
        # Multi-method correlation should handle gracefully
        result = calculate_multi_method_correlations_tool(context, min_periods=10)
        assert isinstance(result, dict)
        # Should either succeed with limited results or provide clear error
    
    def test_tools_with_non_numeric_data(self):
        """Test multi-method tools with non-numeric data"""
        non_numeric_data = pd.DataFrame({
            'category': ['A', 'B', 'C', 'A', 'B'],
            'text': ['hello', 'world', 'test', 'data', 'analysis']
        })
        
        deps = ManufacturingDataDependencies(data=non_numeric_data)
        context = Mock(spec=RunContext)
        context.deps = deps
        
        result = calculate_multi_method_correlations_tool(context)
        assert isinstance(result, dict)
        assert 'error' in result
        assert 'numeric' in result['error'].lower()
    
    def test_recommend_method_with_identical_variables(self):
        """Test method recommendation with identical data"""
        identical_data = pd.DataFrame({
            'var1': [1, 2, 3, 4, 5] * 20,
            'var2': [1, 2, 3, 4, 5] * 20  # Identical pattern
        })
        
        deps = ManufacturingDataDependencies(data=identical_data)
        context = Mock(spec=RunContext)
        context.deps = deps
        
        result = recommend_correlation_method_tool(
            context, variable_1='var1', variable_2='var2'
        )
        
        assert isinstance(result, dict)
        if 'error' not in result:
            # Should detect perfect correlation
            comparison = result.get('correlation_comparison', {})
            if 'error' not in comparison:
                # All methods should show strong correlation
                for method in ['pearson', 'spearman', 'kendall']:
                    if method in comparison:
                        assert abs(comparison[method]['correlation']) > 0.8


class TestMultiMethodToolsIntegration:
    """Integration tests for multi-method tools workflow"""
    
    @pytest.fixture
    def integration_data(self):
        """Create comprehensive test data for integration testing"""
        np.random.seed(42)
        n_samples = 400
        
        # Create manufacturing process with multiple correlation patterns
        
        # Primary process variable
        production_rate = np.random.normal(100, 15, n_samples)
        
        # Linear relationships (good for Pearson)
        energy_consumption = 50 + 0.8 * production_rate + np.random.normal(0, 5, n_samples)
        raw_material_usage = 20 + 0.6 * production_rate + np.random.normal(0, 3, n_samples)
        
        # Non-linear but monotonic (good for Spearman)
        machine_wear = 0.1 + 0.001 * production_rate**1.5 + np.random.normal(0, 0.02, n_samples)
        
        # With outliers and threshold effects (good for Kendall)
        defect_rate = np.where(
            production_rate > 120, 
            0.05 + 0.001 * (production_rate - 120)**2,
            0.02
        ) + np.random.exponential(0.01, n_samples)
        
        # Add some random outliers
        outlier_indices = np.random.choice(n_samples, size=20, replace=False)
        defect_rate[outlier_indices] *= np.random.uniform(2, 5, 20)
        
        return pd.DataFrame({
            'production_rate': production_rate,
            'energy_consumption': energy_consumption,
            'raw_material_usage': raw_material_usage,
            'machine_wear': machine_wear,
            'defect_rate': defect_rate,
            'timestamp': pd.date_range('2024-01-01', periods=n_samples, freq='5min')
        })
    
    def test_complete_multi_method_workflow(self, integration_data):
        """Test complete multi-method analysis workflow"""
        # Create context
        deps = ManufacturingDataDependencies(
            data=integration_data,
            time_column='timestamp',
            significance_threshold=0.05
        )
        context = Mock(spec=RunContext)
        context.deps = deps
        
        # Step 1: Calculate multi-method correlations
        corr_results = calculate_multi_method_correlations_tool(context)
        assert isinstance(corr_results, dict)
        assert 'multi_method_results' in corr_results
        
        # Step 2: Analyze method convergence
        convergence_results = analyze_method_convergence_tool(
            context, correlation_results=corr_results['multi_method_results']
        )
        assert isinstance(convergence_results, dict)
        assert 'overall_convergence_score' in convergence_results
        
        # Step 3: Get method recommendations for specific pairs
        recommendation_results = recommend_correlation_method_tool(
            context, variable_1='production_rate', variable_2='energy_consumption'
        )
        assert isinstance(recommendation_results, dict)
        assert 'recommended_method' in recommendation_results
        
        # Step 4: Calculate robustness metrics
        robustness_results = calculate_robustness_metrics_tool(
            context, 
            correlation_results=corr_results['multi_method_results'],
            bootstrap_samples=20
        )
        assert isinstance(robustness_results, dict)
        
        # Verify workflow consistency
        total_pairs = len(corr_results['multi_method_results'])
        assert total_pairs > 0
        
        # Check that convergence analysis covers all pairs
        if 'error' not in convergence_results:
            convergence_pairs = convergence_results.get('convergence_distribution', {}).get('high_convergence_pairs', 0)
            convergence_pairs += convergence_results.get('convergence_distribution', {}).get('medium_convergence_pairs', 0)
            convergence_pairs += convergence_results.get('convergence_distribution', {}).get('low_convergence_pairs', 0)
            
            # Should analyze all or most pairs
            assert convergence_pairs >= total_pairs * 0.8
    
    def test_performance_with_larger_dataset(self, integration_data):
        """Test tool performance with larger dataset"""
        # Expand dataset
        larger_data = pd.concat([integration_data] * 3, ignore_index=True)
        larger_data['timestamp'] = pd.date_range('2024-01-01', periods=len(larger_data), freq='2min')
        
        deps = ManufacturingDataDependencies(data=larger_data)
        context = Mock(spec=RunContext)
        context.deps = deps
        
        # Test with timeout to ensure reasonable performance
        import time
        start_time = time.time()
        
        result = calculate_multi_method_correlations_tool(context)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within reasonable time (30 seconds)
        assert execution_time < 30
        
        if 'error' not in result:
            assert 'multi_method_results' in result
            assert len(result['multi_method_results']) > 0


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])