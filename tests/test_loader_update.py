#!/usr/bin/env python3
"""
Test script for updated data loader with Cyrus data integration
"""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.append('src')

from data.loader import ManufacturingDataLoader

def test_updated_loader():
    """Test the updated data loader with new Cyrus data files"""
    
    # Set up logging to see what's happening
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    print("=== Testing Updated Manufacturing Data Loader ===")
    print()
    
    try:
        # Initialize loader with test data directory
        print("1. Initializing ManufacturingDataLoader...")
        loader = ManufacturingDataLoader("test-data")
        print("✓ Loader initialized successfully")
        print()
        
        # Test VM Capacity data loading
        print("2. Testing VM Capacity Report loading...")
        vm_data = loader._load_vm_capacity_data()
        if vm_data is not None:
            print(f"✓ VM Capacity data loaded: {len(vm_data)} records")
            print(f"  Columns: {vm_data.columns.tolist()}")
            if 'Sheet Machine' in vm_data.columns:
                sm270_count = (vm_data['Sheet Machine'] == 'SM270').sum()
                print(f"  SM270 records: {sm270_count}")
        else:
            print("⚠ VM Capacity data not found")
        print()
        
        # Test loading individual files  
        print("3. Testing individual file loading...")
        
        # Test FM stack with SM270 filtering
        print("  Loading FM stack (should filter for Stack Numbers beginning with 7)...")
        try:
            fm_data = loader.load_csv_file("data-cyrus/fm_stack.csv", "fm_stack")
            print(f"  ✓ FM stack loaded: {len(fm_data)} records")
            if 'Stack Number' in fm_data.columns:
                stack_7_count = fm_data['Stack Number'].astype(str).str.startswith('7').sum()
                print(f"    Records with Stack Numbers starting with 7: {stack_7_count}")
        except Exception as e:
            print(f"  ✗ FM stack loading failed: {e}")
        
        # Test SM stack
        print("  Loading SM stack...")
        try:
            sm_data = loader.load_csv_file("data-cyrus/sm_stack.csv", "sm_stack")
            print(f"  ✓ SM stack loaded: {len(sm_data)} records")
            if 'Product' in sm_data.columns:
                product_count = sm_data['Product'].notna().sum()
                print(f"    Records with Product info: {product_count}")
        except Exception as e:
            print(f"  ✗ SM stack loading failed: {e}")
        
        print()
        
        # Test full data loading
        print("4. Testing full data loading...")
        try:
            all_data = loader.load_all_manufacturing_data()
            print(f"✓ All data loaded successfully: {len(all_data)} datasets")
            for dataset_name, df in all_data.items():
                print(f"  {dataset_name}: {len(df)} records")
        except Exception as e:
            print(f"✗ Full data loading failed: {e}")
        
        print()
        
        # Test consolidated table creation
        print("5. Testing consolidated table creation...")
        try:
            consolidated_file = loader.create_consolidated_table()
            print(f"✓ Consolidated table created: {consolidated_file}")
        except Exception as e:
            print(f"✗ Consolidated table creation failed: {e}")
        
        print()
        print("=== Test Complete ===")
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_updated_loader()