#!/usr/bin/env python3
"""
Quick test to verify CLI correlation functionality is working.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import():
    """Test if the correlation agent can be imported correctly"""
    try:
        from src.agents.correlation_agent import analyze_manufacturing_correlations
        print("✅ Successfully imported analyze_manufacturing_correlations")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_cli_import():
    """Test if CLI can import the correlation agent"""
    try:
        from src.cli import get_correlation_agent
        agent_func = get_correlation_agent()
        if agent_func is not None:
            print("✅ CLI correlation agent import successful")
            return True
        else:
            print("❌ CLI correlation agent returned None")
            return False
    except Exception as e:
        print(f"❌ CLI correlation agent failed: {e}")
        return False

def main():
    print("🧪 Testing CLI Correlation Agent Import")
    print("=" * 50)
    
    success1 = test_import()
    success2 = test_cli_import()
    
    if success1 and success2:
        print("\n🎉 All imports successful! CLI correlation agent is ready.")
        print("\nYou can now run:")
        print("  python -m src.cli interactive")
        print('\nAnd ask: "What are the key quality correlations across complete SM-FM manufacturing flows?"')
        return True
    else:
        print("\n❌ Import issues detected.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)