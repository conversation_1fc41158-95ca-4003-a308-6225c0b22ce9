"""
Unit Tests for Forecasting Agent Tools

Tests for PatchTST forecasting tools integrated with the correlation agent.
These tests validate agent tool functionality with real API integration.
"""

import pytest
import pandas as pd
import numpy as np
import asyncio
import os
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the modules to test
from src.agents.correlation_agent import (
    correlation_agent,
    ManufacturingDataDependencies
)
from src.agents.forecasting_tools import (
    forecast_manufacturing_parameter_tool,
    multi_horizon_forecast_tool,
    compare_forecast_scenarios_tool,
    train_forecasting_models_tool
)
from src.forecasting import ForecastConfig
from src.data.loader import ManufacturingDataLoader
from pydantic_ai import RunContext


class TestForecastingToolsSetup:
    """Test setup and configuration for forecasting tools"""
    
    def test_environment_variables_loaded(self):
        """Test that required environment variables are available"""
        # Check that API keys are loaded for agent testing
        anthropic_key = os.getenv('ANTHROPIC_API_KEY')
        assert anthropic_key is not None, "ANTHROPIC_API_KEY not found in environment"
        assert anthropic_key.startswith('sk-ant-'), "Invalid Anthropic API key format"
    
    def test_forecasting_config_available(self):
        """Test that forecasting configuration is available"""
        config_path = 'config/forecasting_config.json'
        assert Path(config_path).exists(), f"Forecasting config not found at {config_path}"
        
        # Test loading configuration
        config = ForecastConfig.from_json(config_path)
        assert isinstance(config, ForecastConfig)
        assert len(config.target_variables) > 0
        assert len(config.forecast_horizons) > 0
    
    def test_manufacturing_data_available(self):
        """Test that manufacturing test data is available"""
        data_loader = ManufacturingDataLoader('test-data')
        
        # Check that test data exists
        test_data_path = Path('test-data')
        assert test_data_path.exists(), "Test data directory not found"
        
        # Load sample data
        datasets = data_loader.load_all_manufacturing_data()
        assert len(datasets) > 0, "No manufacturing datasets loaded"
        
        # Create unified dataset
        unified_data = data_loader.create_unified_dataset()
        assert not unified_data.empty, "Unified dataset is empty"
        assert len(unified_data) > 100, "Insufficient data for forecasting tests"


class TestForecastingTools:
    """Test individual forecasting tools functionality"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Create comprehensive sample manufacturing data for testing"""
        # Create 500 data points (about 8 hours at 1-minute intervals)
        n_samples = 500
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(hours=8),
            periods=n_samples,
            freq='1min'
        )
        
        # Simulate realistic manufacturing data
        np.random.seed(42)
        
        # Base patterns with manufacturing characteristics
        time_trend = np.linspace(0, 8, n_samples)
        shift_pattern = np.sin(2 * np.pi * np.arange(n_samples) / 480)  # 8-hour shift cycle
        
        # Manufacturing variables with realistic ranges and correlations
        speed = 45 + 15 * np.sin(time_trend) + 5 * shift_pattern + np.random.normal(0, 2, n_samples)
        temperature = 78 + 8 * np.cos(time_trend) + 3 * shift_pattern + np.random.normal(0, 1.5, n_samples)
        pressure = 95 + 10 * np.sin(time_trend + 0.5) + np.random.normal(0, 2, n_samples)
        
        # Thickness calculation from multiple sensors
        thickness_base = 12 + 3 * np.sin(time_trend) + 1.5 * shift_pattern
        thickness_sensors = []
        for i in range(10):
            sensor_noise = np.random.normal(0, 0.3, n_samples)
            sensor_bias = np.random.normal(0, 0.1)  # Small sensor-specific bias
            thickness_sensors.append(thickness_base + sensor_bias + sensor_noise)
        
        thickness_avg = np.mean(thickness_sensors, axis=0)
        thickness_uniformity = np.std(thickness_sensors, axis=0)
        
        # Quality metrics with realistic manufacturing relationships
        # Scrap rate increases with speed and temperature deviations
        base_scrap = 0.015
        speed_effect = 0.005 * np.maximum(0, (speed - 50) / 20)  # Higher scrap at high speed
        temp_effect = 0.003 * np.abs(temperature - 80) / 10      # Higher scrap away from optimal temp
        scrap_rate = base_scrap + speed_effect + temp_effect + np.random.exponential(0.01, n_samples)
        scrap_rate = np.clip(scrap_rate, 0, 0.2)  # Cap at 20%
        
        # Quality index inversely related to scrap rate and process variation
        quality_base = 0.95
        quality_index = quality_base - 5 * scrap_rate + np.random.normal(0, 0.02, n_samples)
        quality_index = np.clip(quality_index, 0.5, 1.0)
        
        # Stoppage simulation - exponential intervals
        stoppage_intervals = np.random.exponential(120, n_samples // 10)  # Average 2-hour intervals
        stoppage_times = np.cumsum(stoppage_intervals)
        minutes_since_last_stop = np.zeros(n_samples)
        
        current_time = 0
        last_stop = 0
        for i in range(n_samples):
            current_time = i
            # Find most recent stoppage
            recent_stops = stoppage_times[stoppage_times <= current_time]
            if len(recent_stops) > 0:
                last_stop = recent_stops[-1]
            minutes_since_last_stop[i] = current_time - last_stop
        
        data = pd.DataFrame({
            'timestamp': timestamps,
            'speed': speed,
            'temperature': temperature,
            'pressure': pressure,
            'thickness_avg': thickness_avg,
            'thickness_uniformity': thickness_uniformity,
            'scrap_rate': scrap_rate,
            'quality_index': quality_index,
            'minutes_since_last_stop': minutes_since_last_stop
        })
        
        return data
    
    @pytest.fixture
    def run_context(self, sample_manufacturing_data):
        """Create RunContext for tool testing"""
        deps = ManufacturingDataDependencies(
            data=sample_manufacturing_data,
            time_column='timestamp',
            target_variables=['thickness_avg', 'scrap_rate', 'quality_index'],
            significance_threshold=0.05,
            min_correlation=0.01,
            analysis_type='forecasting'
        )
        return RunContext(deps=deps)
    
    def test_forecast_manufacturing_parameter_tool_basic(self, run_context):
        """Test basic forecasting tool functionality"""
        # Test forecasting thickness_avg
        result = forecast_manufacturing_parameter_tool(
            ctx=run_context,
            target_variable='thickness_avg',
            forecast_horizon=30,  # 30 minutes
            lookback_window=120,  # 2 hours
            include_confidence_intervals=True
        )
        
        # Validate result structure
        assert isinstance(result, dict)
        
        if 'error' not in result:
            # Successful forecast
            assert result['target_variable'] == 'thickness_avg'
            assert result['forecast_horizon'] == 30
            assert 'forecast_values' in result
            assert 'forecast_timestamps' in result
            assert 'forecast_summary' in result
            
            # Validate forecast values
            forecast_values = result['forecast_values']
            assert isinstance(forecast_values, list)
            assert len(forecast_values) == 30
            assert all(isinstance(v, (int, float)) for v in forecast_values)
            
            # Validate timestamps
            timestamps = result['forecast_timestamps']
            assert len(timestamps) == 30
            assert all(isinstance(t, str) for t in timestamps)
            
            # Validate summary metrics
            summary = result['forecast_summary']
            assert 'min_value' in summary
            assert 'max_value' in summary
            assert 'mean_value' in summary
            assert 'trend' in summary
            assert summary['trend'] in ['increasing', 'decreasing']
        else:
            # Expected failure due to no pre-trained model
            assert 'error' in result
            assert result['status'] == 'failed'
    
    def test_forecast_invalid_target_variable(self, run_context):
        """Test forecasting with invalid target variable"""
        result = forecast_manufacturing_parameter_tool(
            ctx=run_context,
            target_variable='invalid_variable',
            forecast_horizon=15
        )
        
        # Should return error
        assert 'error' in result
        assert result['status'] == 'failed'
        assert 'invalid_variable' in result['error']
    
    def test_multi_horizon_forecast_tool(self, run_context):
        """Test multi-horizon forecasting tool"""
        result = multi_horizon_forecast_tool(
            ctx=run_context,
            target_variable='thickness_avg',
            forecast_horizons=[15, 30, 60],
            include_comparison=True
        )
        
        # Validate result structure
        assert isinstance(result, dict)
        assert result['target_variable'] == 'thickness_avg'
        assert result['forecast_horizons'] == [15, 30, 60]
        
        if 'error' not in result:
            # Successful multi-horizon forecast
            assert 'horizon_results' in result
            assert 'comparative_analysis' in result
            
            horizon_results = result['horizon_results']
            
            # Check that we have results for each horizon
            for horizon in [15, 30, 60]:
                horizon_key = f'horizon_{horizon}min'
                if horizon_key in horizon_results and 'error' not in horizon_results[horizon_key]:
                    assert 'forecast_values' in horizon_results[horizon_key]
                    assert 'summary' in horizon_results[horizon_key]
                    
                    # Validate forecast length matches horizon
                    forecast_values = horizon_results[horizon_key]['forecast_values']
                    assert len(forecast_values) == horizon
        else:
            # Expected failure due to no pre-trained model
            assert 'error' in result
    
    def test_compare_forecast_scenarios_tool(self, run_context):
        """Test scenario comparison forecasting tool"""
        result = compare_forecast_scenarios_tool(
            ctx=run_context,
            target_variables=['thickness_avg', 'scrap_rate'],
            forecast_horizon=30,
            scenario_analysis=True
        )
        
        # Validate result structure
        assert isinstance(result, dict)
        assert result['target_variables'] == ['thickness_avg', 'scrap_rate']
        assert result['forecast_horizon'] == 30
        
        if 'error' not in result:
            # Successful scenario comparison
            assert 'variable_forecasts' in result
            assert 'scenario_insights' in result
            assert 'cross_variable_analysis' in result
            
            variable_forecasts = result['variable_forecasts']
            
            # Check forecasts for each variable
            for var in ['thickness_avg', 'scrap_rate']:
                if var in variable_forecasts and 'error' not in variable_forecasts[var]:
                    forecast_data = variable_forecasts[var]
                    assert 'forecast_values' in forecast_data
                    assert 'forecast_timestamps' in forecast_data
                    assert len(forecast_data['forecast_values']) == 30
        else:
            # Expected failure due to no pre-trained model
            assert 'error' in result
    
    def test_train_forecasting_models_tool(self, run_context):
        """Test model training tool"""
        result = train_forecasting_models_tool(
            ctx=run_context,
            target_variables=['thickness_avg'],
            retrain_existing=False
        )
        
        # Validate result structure
        assert isinstance(result, dict)
        assert 'training_summary' in result
        assert 'training_results' in result
        
        training_summary = result['training_summary']
        assert 'total_variables' in training_summary
        assert 'successful_models' in training_summary
        assert 'training_timestamp' in training_summary
        
        training_results = result['training_results']
        
        # Check results for thickness_avg
        if 'thickness_avg' in training_results:
            thickness_result = training_results['thickness_avg']
            
            # May succeed or fail depending on system capabilities
            assert 'status' in thickness_result
            assert thickness_result['status'] in ['completed', 'failed', 'skipped']
            
            if thickness_result['status'] == 'completed':
                assert 'model_metrics' in thickness_result
                assert 'validation_results' in thickness_result
                assert 'model_path' in thickness_result
            elif thickness_result['status'] == 'failed':
                assert 'error' in thickness_result
    
    def test_forecasting_tools_with_insufficient_data(self):
        """Test forecasting tools with insufficient data"""
        # Create very small dataset
        small_data = pd.DataFrame({
            'timestamp': pd.date_range(start=datetime.now(), periods=10, freq='1min'),
            'thickness_avg': np.random.normal(10, 1, 10),
            'speed': np.random.normal(50, 5, 10),
            'temperature': np.random.normal(80, 2, 10)
        })
        
        deps = ManufacturingDataDependencies(
            data=small_data,
            time_column='timestamp',
            target_variables=['thickness_avg']
        )
        ctx = RunContext(deps=deps)
        
        # Try forecasting with insufficient data
        result = forecast_manufacturing_parameter_tool(
            ctx=ctx,
            target_variable='thickness_avg',
            forecast_horizon=30,
            lookback_window=120
        )
        
        # Should handle gracefully with error
        assert 'error' in result or result.get('status') == 'failed'


class TestForecastingAgentIntegration:
    """Test integration of forecasting tools with correlation agent"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Reuse sample data from previous test class"""
        test_instance = TestForecastingTools()
        return test_instance.sample_manufacturing_data()
    
    @pytest.mark.asyncio
    async def test_agent_forecasting_query(self, sample_manufacturing_data):
        """Test agent responding to forecasting queries"""
        # Create dependencies
        deps = ManufacturingDataDependencies(
            data=sample_manufacturing_data,
            time_column='timestamp',
            target_variables=['thickness_avg', 'scrap_rate'],
            analysis_type='forecasting'
        )
        
        # Test simple forecasting query
        try:
            result = await correlation_agent.run(
                "Forecast thickness for the next 30 minutes",
                deps=deps
            )
            
            # Agent should attempt to use forecasting tools
            assert result is not None
            
            # The actual forecast may fail due to no pre-trained model,
            # but the agent should handle the request appropriately
            if hasattr(result, 'output'):
                output = result.output
                # Validate that agent returned some analysis
                assert output is not None
                
        except Exception as e:
            # Expected if model training fails or forecasting tools encounter issues
            # But the agent should handle errors gracefully
            assert "forecast" in str(e).lower() or "model" in str(e).lower()
    
    @pytest.mark.asyncio
    async def test_agent_multi_horizon_query(self, sample_manufacturing_data):
        """Test agent responding to multi-horizon forecasting queries"""
        deps = ManufacturingDataDependencies(
            data=sample_manufacturing_data,
            time_column='timestamp',
            target_variables=['thickness_avg']
        )
        
        try:
            result = await correlation_agent.run(
                "Compare forecasts for 15 minutes, 30 minutes, and 1 hour horizons for thickness",
                deps=deps
            )
            
            assert result is not None
            
        except Exception as e:
            # Expected if forecasting models are not available
            assert "forecast" in str(e).lower() or "horizon" in str(e).lower()
    
    @pytest.mark.asyncio
    async def test_agent_training_query(self, sample_manufacturing_data):
        """Test agent responding to model training queries"""
        deps = ManufacturingDataDependencies(
            data=sample_manufacturing_data,
            time_column='timestamp'
        )
        
        try:
            result = await correlation_agent.run(
                "Train forecasting models for thickness and scrap rate prediction",
                deps=deps
            )
            
            assert result is not None
            
        except Exception as e:
            # Expected if training fails due to resource constraints
            assert "train" in str(e).lower() or "model" in str(e).lower()


class TestForecastingToolsErrorHandling:
    """Test error handling in forecasting tools"""
    
    def test_empty_data_handling(self):
        """Test handling of empty data"""
        empty_data = pd.DataFrame()
        deps = ManufacturingDataDependencies(data=empty_data)
        ctx = RunContext(deps=deps)
        
        result = forecast_manufacturing_parameter_tool(
            ctx=ctx,
            target_variable='thickness_avg',
            forecast_horizon=30
        )
        
        assert 'error' in result
        assert 'empty' in result['error'].lower()
    
    def test_missing_columns_handling(self):
        """Test handling of missing required columns"""
        incomplete_data = pd.DataFrame({
            'timestamp': pd.date_range(start=datetime.now(), periods=100, freq='1min'),
            'some_other_column': np.random.normal(0, 1, 100)
        })
        
        deps = ManufacturingDataDependencies(data=incomplete_data)
        ctx = RunContext(deps=deps)
        
        result = forecast_manufacturing_parameter_tool(
            ctx=ctx,
            target_variable='thickness_avg',
            forecast_horizon=30
        )
        
        assert 'error' in result
        assert 'not found' in result['error']
    
    def test_invalid_forecast_parameters(self):
        """Test handling of invalid forecast parameters"""
        sample_data = pd.DataFrame({
            'timestamp': pd.date_range(start=datetime.now(), periods=100, freq='1min'),
            'thickness_avg': np.random.normal(10, 1, 100)
        })
        
        deps = ManufacturingDataDependencies(data=sample_data)
        ctx = RunContext(deps=deps)
        
        # Test negative forecast horizon
        result = forecast_manufacturing_parameter_tool(
            ctx=ctx,
            target_variable='thickness_avg',
            forecast_horizon=-10
        )
        
        assert 'error' in result
    
    def test_configuration_error_handling(self):
        """Test handling of configuration errors"""
        sample_data = pd.DataFrame({
            'timestamp': pd.date_range(start=datetime.now(), periods=100, freq='1min'),
            'thickness_avg': np.random.normal(10, 1, 100)
        })
        
        deps = ManufacturingDataDependencies(data=sample_data)
        ctx = RunContext(deps=deps)
        
        # Test with missing configuration file by temporarily moving it
        original_config_path = Path('config/forecasting_config.json')
        temp_config_path = Path('config/forecasting_config.json.tmp')
        
        config_exists = original_config_path.exists()
        if config_exists:
            original_config_path.rename(temp_config_path)
        
        try:
            result = forecast_manufacturing_parameter_tool(
                ctx=ctx,
                target_variable='thickness_avg',
                forecast_horizon=30
            )
            
            # Should handle missing config gracefully
            # Either succeed with default config or fail gracefully
            assert isinstance(result, dict)
            
        finally:
            # Restore config file
            if config_exists and temp_config_path.exists():
                temp_config_path.rename(original_config_path)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])