#!/usr/bin/env python3
"""
Stable Training Test Script

Tests training with very conservative parameters to ensure the system works.
"""

import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.forecasting.trainer import ManufacturingForecastTrainer
from src.forecasting.config import load_config_from_file

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_stable_training():
    """Test training with ultra-conservative parameters."""
    try:
        logger.info("🔬 Testing Stable Training Configuration")
        
        # Load stable configuration
        forecast_config, training_config = load_config_from_file("config/stable_training_config.json")
        
        # Even more conservative for testing
        training_config.max_epochs = 2
        training_config.eval_steps = 10
        training_config.save_steps = 20
        training_config.early_stopping_patience = 1
        training_config.batch_size = 4
        
        # Test with just one target
        forecast_config.target_variables = ["thickness_thickness_avg"]
        forecast_config.forecast_horizons = [15]  # Just one horizon
        
        logger.info("Configuration:")
        logger.info(f"  Max epochs: {training_config.max_epochs}")
        logger.info(f"  Batch size: {training_config.batch_size}")
        logger.info(f"  Learning rate: {training_config.learning_rate}")
        logger.info(f"  Model d_model: {forecast_config.model_params['d_model']}")
        logger.info(f"  Lookback window: {forecast_config.lookback_window}")
        
        logger.info("Creating ultra-stable trainer...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,
            use_transfer_learning=False  # Disable for stability
        )
        
        logger.info("Starting stable training test...")
        trained_models = trainer.train_all_target_variables(data_path="test-data")
        
        if trained_models:
            logger.info("✅ Stable training successful!")
            for target, model in trained_models.items():
                logger.info(f"   {target}: Model created")
                logger.info(f"   Best eval loss: {model.best_model_metrics.get('eval_loss', 'N/A')}")
            return True
        else:
            logger.error("❌ Stable training failed - no models created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Stable training failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🛡️ Ultra-Stable Training Test")
    print("=" * 40)
    
    success = test_stable_training()
    
    print("=" * 40)
    if success:
        print("🎉 Stable training works!")
        print("✅ The model architecture and training fixes are successful")
        print("💡 You can now use regular training with confidence")
    else:
        print("❌ Stable training still has issues")
        print("🔍 Further debugging needed")
        sys.exit(1)