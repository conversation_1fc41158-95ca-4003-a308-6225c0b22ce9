#!/usr/bin/env python3
"""
Test script for TM480 integration with manufacturing data loader.

Tests the multi-FM matching capability with real TM480 data.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.loader import ManufacturingDataLoader
import logging

# Set up logging to see detailed output
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tm480_integration():
    """Test TM480 data integration and multi-FM matching."""
    try:
        logger.info("🚀 Starting TM480 integration test...")
        
        # Initialize the loader with configuration
        config_path = Path(__file__).parent / "data.config.json"
        loader = ManufacturingDataLoader(str(config_path))
        
        # Test data loading
        logger.info("📂 Loading all data sources including TM480...")
        loader.load_all_data_sources()
        
        # Check if TM480 data was loaded
        if 'tm480_stack' in loader.loaded_data:
            tm480_data = loader.loaded_data['tm480_stack']
            logger.info(f"✅ TM480 data loaded: {len(tm480_data):,} records")
            
            # Show column structure
            logger.info(f"TM480 columns: {list(tm480_data.columns)}")
            
            # Check timestamp processing
            if 'finish_start_timestamp' in tm480_data.columns:
                valid_timestamps = tm480_data['finish_start_timestamp'].notna().sum()
                logger.info(f"📅 Valid start timestamps: {valid_timestamps:,}/{len(tm480_data):,}")
            
            if 'finish_end_timestamp' in tm480_data.columns:
                valid_timestamps = tm480_data['finish_end_timestamp'].notna().sum()
                logger.info(f"📅 Valid end timestamps: {valid_timestamps:,}/{len(tm480_data):,}")
        else:
            logger.error("❌ TM480 data not found in loaded sources")
            return False
        
        # Test TM480 data preparation
        logger.info("⚙️ Testing TM480 data preparation...")
        if 'tm480_stack' in loader.loaded_data:
            prepared_tm480 = loader.prepare_tm480_stack_data(loader.loaded_data['tm480_stack'])
            logger.info(f"✅ TM480 preparation completed: {len(prepared_tm480)} stacks processed")
            
            # Check for filtered stacks (starting with '7')
            if 'tm480_stack_number' in prepared_tm480.columns:
                filtered_count = len(prepared_tm480)
                logger.info(f"📊 TM480 stacks after '7' filtering: {filtered_count:,}")
        
        # Test full unified table creation with multi-FM support
        logger.info("🏭 Testing complete unified table creation with multi-FM matching...")
        unified_data = loader.create_unified_table()
        
        logger.info(f"✅ Unified table created successfully!")
        logger.info(f"📊 Final table dimensions: {len(unified_data):,} rows × {len(unified_data.columns)} columns")
        
        # Check for multi-FM columns
        multi_fm_columns = ['fm_machine_type', 'fm_source_system']
        for col in multi_fm_columns:
            if col in unified_data.columns:
                unique_values = unified_data[col].value_counts()
                logger.info(f"🏷️ {col} distribution: {dict(unique_values)}")
        
        # Analyze matching results
        if 'has_fm_match' in unified_data.columns:
            # Note: This is at unified table level, not SM stack level
            logger.info("📈 Multi-FM matching analysis at unified table level:")
            logger.info("ℹ️  Note: These are time-series records, not SM stack counts")
            
            total_records = len(unified_data)
            matched_records = unified_data['has_fm_match'].sum() if 'has_fm_match' in unified_data.columns else 0
            
            logger.info(f"   • Total time-series records: {total_records:,}")
            logger.info(f"   • Records with FM context: {matched_records:,}")
            
            if 'fm_machine_type' in unified_data.columns and matched_records > 0:
                machine_breakdown = unified_data[unified_data['has_fm_match']]['fm_machine_type'].value_counts()
                logger.info(f"   • Machine type breakdown:")
                for machine, count in machine_breakdown.items():
                    percentage = (count / matched_records * 100) if matched_records > 0 else 0
                    logger.info(f"     - {machine}: {count:,} records ({percentage:.1f}%)")
        
        logger.info("🎉 TM480 integration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ TM480 integration test failed: {e}")
        import traceback
        logger.error(f"Full error traceback:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_tm480_integration()
    if success:
        print("\n✅ Test PASSED: TM480 integration working correctly")
    else:
        print("\n❌ Test FAILED: TM480 integration has issues")
        sys.exit(1)