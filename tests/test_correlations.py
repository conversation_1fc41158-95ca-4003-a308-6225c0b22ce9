"""
Unit Tests for Manufacturing Correlation Analysis

Tests for statistical correlation calculations, significance testing, and 
manufacturing-specific correlation patterns.
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Import the modules to test
from src.data.correlations import (
    ManufacturingCorrelationAnalyzer,
    CorrelationResult,
    LagCorrelationResult,
    CorrelationMatrix
)


class TestCorrelationResult:
    """Test suite for CorrelationResult data model"""
    
    def test_correlation_result_creation(self):
        """Test creation of CorrelationResult"""
        result = CorrelationResult(
            variable_1="speed",
            variable_2="thickness",
            correlation_coefficient=0.75,
            p_value=0.001,
            confidence_interval=(0.65, 0.85),
            sample_size=100,
            method="pearson",
            significance_level="very_significant",
            interpretation="Strong positive correlation"
        )
        
        assert result.variable_1 == "speed"
        assert result.variable_2 == "thickness"
        assert result.correlation_coefficient == 0.75
        assert result.p_value == 0.001
        assert result.sample_size == 100
        assert result.method == "pearson"
        assert result.interpretation == "Strong positive correlation"


class TestLagCorrelationResult:
    """Test suite for LagCorrelationResult data model"""
    
    def test_lag_correlation_result_creation(self):
        """Test creation of LagCorrelationResult"""
        result = LagCorrelationResult(
            variable_1="speed",
            variable_2="thickness",
            optimal_lag=15,
            max_correlation=0.68,
            p_value=0.002,
            lag_range=(0, 60),
            correlation_by_lag={0: 0.45, 15: 0.68, 30: 0.52},
            interpretation="Strong positive correlation with optimal lag of 15 time periods"
        )
        
        assert result.variable_1 == "speed"
        assert result.optimal_lag == 15
        assert result.max_correlation == 0.68
        assert len(result.correlation_by_lag) == 3


class TestManufacturingCorrelationAnalyzer:
    """Test suite for ManufacturingCorrelationAnalyzer"""
    
    @pytest.fixture
    def sample_analyzer(self):
        """Create analyzer instance for testing"""
        return ManufacturingCorrelationAnalyzer(significance_level=0.05)
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Create sample manufacturing data for testing"""
        np.random.seed(42)
        n_samples = 200
        
        # Generate correlated manufacturing variables
        timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='1min')
        
        # Base process variables
        speed = np.random.normal(150, 10, n_samples)
        temperature = 80 + 0.1 * speed + np.random.normal(0, 2, n_samples)
        
        # Thickness with correlation to speed and temperature
        thickness = 12.5 + 0.01 * speed + 0.005 * temperature + np.random.normal(0, 0.1, n_samples)
        
        # Pressure correlated with speed
        pressure = 50 + 0.15 * speed + np.random.normal(0, 3, n_samples)
        
        # Quality score inversely related to variation
        quality_score = 100 - 0.05 * np.abs(speed - 150) - 0.1 * np.abs(temperature - 80) + np.random.normal(0, 2, n_samples)
        
        # Random uncorrelated variable
        random_var = np.random.normal(50, 10, n_samples)
        
        return pd.DataFrame({
            'timestamp': timestamps,
            'speed': speed,
            'temperature': temperature,
            'thickness': thickness,
            'pressure': pressure,
            'quality_score': quality_score,
            'random_var': random_var
        })
    
    def test_analyzer_initialization(self, sample_analyzer):
        """Test analyzer initialization"""
        assert sample_analyzer.significance_level == 0.05
        assert isinstance(sample_analyzer.correlation_cache, dict)
        assert 'thickness' in sample_analyzer.process_relationships
        assert 'speed' in sample_analyzer.process_relationships
    
    def test_calculate_correlation_matrix_basic(self, sample_analyzer, sample_manufacturing_data):
        """Test basic correlation matrix calculation"""
        result = sample_analyzer.calculate_correlation_matrix(sample_manufacturing_data)
        
        assert isinstance(result, CorrelationMatrix)
        assert result.method == 'pearson'
        assert isinstance(result.matrix, dict)
        assert isinstance(result.p_values, dict)
        assert isinstance(result.sample_sizes, dict)
        
        # Check that correlation matrix is symmetric
        matrix_df = pd.DataFrame(result.matrix)
        for col1 in matrix_df.columns:
            for col2 in matrix_df.columns:
                if not pd.isna(matrix_df.loc[col1, col2]) and not pd.isna(matrix_df.loc[col2, col1]):
                    assert abs(matrix_df.loc[col1, col2] - matrix_df.loc[col2, col1]) < 1e-10
    
    def test_calculate_correlation_matrix_methods(self, sample_analyzer, sample_manufacturing_data):
        """Test different correlation methods"""
        methods = ['pearson', 'spearman', 'kendall']
        
        for method in methods:
            result = sample_analyzer.calculate_correlation_matrix(sample_manufacturing_data, method=method)
            assert result.method == method
            assert isinstance(result.matrix, dict)
    
    def test_calculate_correlation_matrix_empty_data(self, sample_analyzer):
        """Test correlation matrix with empty DataFrame"""
        empty_df = pd.DataFrame()
        result = sample_analyzer.calculate_correlation_matrix(empty_df)
        
        assert result.matrix == {}
        assert result.p_values == {}
        assert result.sample_sizes == {}
    
    def test_calculate_correlation_matrix_non_numeric(self, sample_analyzer):
        """Test correlation matrix with non-numeric data"""
        text_df = pd.DataFrame({
            'text_col': ['a', 'b', 'c'],
            'numeric_col': [1, 2, 3]
        })
        
        result = sample_analyzer.calculate_correlation_matrix(text_df)
        
        # Should only include numeric columns
        assert 'numeric_col' in str(result.matrix)
        assert 'text_col' not in str(result.matrix)
    
    def test_find_significant_correlations_basic(self, sample_analyzer, sample_manufacturing_data):
        """Test finding significant correlations"""
        results = sample_analyzer.find_significant_correlations(
            sample_manufacturing_data, 
            min_correlation=0.3
        )
        
        assert isinstance(results, list)
        
        # Should find some correlations in our synthetic data
        assert len(results) > 0
        
        for result in results:
            assert isinstance(result, CorrelationResult)
            assert abs(result.correlation_coefficient) >= 0.3
            assert result.p_value < 0.05  # Significant
            assert result.sample_size > 0
    
    def test_find_significant_correlations_methods(self, sample_analyzer, sample_manufacturing_data):
        """Test significant correlations with different methods"""
        methods = ['pearson', 'spearman', 'kendall']
        
        for method in methods:
            results = sample_analyzer.find_significant_correlations(
                sample_manufacturing_data, 
                method=method,
                min_correlation=0.2
            )
            
            for result in results:
                assert result.method == method
    
    def test_find_significant_correlations_return_all(self, sample_analyzer, sample_manufacturing_data):
        """Test returning all correlations, not just significant ones"""
        significant_only = sample_analyzer.find_significant_correlations(
            sample_manufacturing_data, 
            min_correlation=0.3,
            return_all=False
        )
        
        all_correlations = sample_analyzer.find_significant_correlations(
            sample_manufacturing_data, 
            min_correlation=0.3,
            return_all=True
        )
        
        # Should have more correlations when including non-significant ones
        assert len(all_correlations) >= len(significant_only)
    
    def test_find_significant_correlations_insufficient_data(self, sample_analyzer):
        """Test with insufficient data"""
        small_df = pd.DataFrame({
            'var1': [1, 2],
            'var2': [3, 4]
        })
        
        results = sample_analyzer.find_significant_correlations(small_df)
        
        # Should return empty list due to insufficient data
        assert len(results) == 0
    
    def test_analyze_lag_correlations_basic(self, sample_analyzer, sample_manufacturing_data):
        """Test basic lag correlation analysis"""
        result = sample_analyzer.analyze_lag_correlations(
            sample_manufacturing_data,
            'speed',
            'thickness',
            max_lag=30
        )
        
        assert isinstance(result, LagCorrelationResult)
        assert result.variable_1 == 'speed'
        assert result.variable_2 == 'thickness'
        assert 0 <= result.optimal_lag <= 30
        assert isinstance(result.correlation_by_lag, dict)
        assert len(result.correlation_by_lag) > 0
        assert result.lag_range == (0, 30)
    
    def test_analyze_lag_correlations_missing_variables(self, sample_analyzer, sample_manufacturing_data):
        """Test lag correlations with missing variables"""
        with pytest.raises(ValueError, match="Variables .* not found"):
            sample_analyzer.analyze_lag_correlations(
                sample_manufacturing_data,
                'nonexistent_var1',
                'nonexistent_var2'
            )
    
    def test_analyze_lag_correlations_no_time_column(self, sample_analyzer):
        """Test lag correlations without time column"""
        df = pd.DataFrame({
            'var1': np.random.normal(0, 1, 100),
            'var2': np.random.normal(0, 1, 100)
        })
        
        result = sample_analyzer.analyze_lag_correlations(df, 'var1', 'var2', max_lag=10)
        
        # Should still work without explicit time column
        assert isinstance(result, LagCorrelationResult)
    
    def test_analyze_process_correlations_basic(self, sample_analyzer, sample_manufacturing_data):
        """Test process correlation analysis"""
        results = sample_analyzer.analyze_process_correlations(
            sample_manufacturing_data,
            'quality_score'
        )
        
        assert isinstance(results, dict)
        
        # Should analyze relationships with other variables
        for var_name, result in results.items():
            assert isinstance(result, LagCorrelationResult)
            assert result.variable_2 == 'quality_score'
            assert var_name == result.variable_1
    
    def test_analyze_process_correlations_missing_target(self, sample_analyzer, sample_manufacturing_data):
        """Test process correlations with missing target variable"""
        with pytest.raises(ValueError, match="Target variable .* not found"):
            sample_analyzer.analyze_process_correlations(
                sample_manufacturing_data,
                'nonexistent_target'
            )
    
    def test_analyze_process_correlations_specific_variables(self, sample_analyzer, sample_manufacturing_data):
        """Test process correlations with specific process variables"""
        process_vars = ['speed', 'temperature']
        
        results = sample_analyzer.analyze_process_correlations(
            sample_manufacturing_data,
            'thickness',
            process_variables=process_vars
        )
        
        # Should only analyze specified variables
        for var_name in results.keys():
            assert var_name in process_vars
    
    def test_calculate_partial_correlations_basic(self, sample_analyzer, sample_manufacturing_data):
        """Test partial correlation calculation"""
        control_vars = ['speed', 'temperature']
        
        try:
            partial_corrs = sample_analyzer.calculate_partial_correlations(
                sample_manufacturing_data,
                'thickness',
                control_vars
            )
            
            assert isinstance(partial_corrs, dict)
            
            for var, corr in partial_corrs.items():
                assert var in control_vars
                assert isinstance(corr, (int, float))
                assert -1 <= corr <= 1
                
        except ImportError:
            # Skip if sklearn not available
            pytest.skip("scikit-learn not available for partial correlation testing")
    
    def test_calculate_partial_correlations_insufficient_data(self, sample_analyzer):
        """Test partial correlations with insufficient data"""
        small_df = pd.DataFrame({
            'target': [1, 2, 3],
            'control1': [4, 5, 6],
            'control2': [7, 8, 9]
        })
        
        result = sample_analyzer.calculate_partial_correlations(
            small_df, 'target', ['control1', 'control2']
        )
        
        # Should return empty dict due to insufficient data
        assert result == {}
    
    def test_detect_correlation_patterns_basic(self, sample_analyzer, sample_manufacturing_data):
        """Test correlation pattern detection"""
        # Calculate correlation matrix first
        corr_result = sample_analyzer.calculate_correlation_matrix(sample_manufacturing_data)
        corr_matrix = pd.DataFrame(corr_result.matrix)
        
        patterns = sample_analyzer.detect_correlation_patterns(corr_matrix, threshold=0.5)
        
        assert isinstance(patterns, dict)
        assert 'strong_positive_clusters' in patterns
        assert 'strong_negative_clusters' in patterns
        assert 'isolated_variables' in patterns
        assert 'correlation_chains' in patterns
        
        # All pattern lists should be lists
        for pattern_type, pattern_list in patterns.items():
            assert isinstance(pattern_list, list)
    
    def test_detect_correlation_patterns_high_threshold(self, sample_analyzer, sample_manufacturing_data):
        """Test pattern detection with high threshold"""
        corr_result = sample_analyzer.calculate_correlation_matrix(sample_manufacturing_data)
        corr_matrix = pd.DataFrame(corr_result.matrix)
        
        patterns = sample_analyzer.detect_correlation_patterns(corr_matrix, threshold=0.9)
        
        # With high threshold, should have fewer clusters and more isolated variables
        total_clustered = sum(len(cluster) for cluster in patterns['strong_positive_clusters']) + \
                         sum(len(cluster) for cluster in patterns['strong_negative_clusters'])
        
        # Most variables should be isolated with such a high threshold
        assert len(patterns['isolated_variables']) > 0
    
    def test_confidence_interval_calculation(self, sample_analyzer):
        """Test confidence interval calculation"""
        # Test with various correlation values
        test_cases = [
            (0.5, 100),
            (0.8, 50),
            (-0.6, 200),
            (0.0, 100)
        ]
        
        for r, n in test_cases:
            ci = sample_analyzer._calculate_confidence_interval(r, n)
            
            assert isinstance(ci, tuple)
            assert len(ci) == 2
            
            lower, upper = ci
            
            if not (np.isnan(lower) or np.isnan(upper)):
                assert lower <= r <= upper  # Correlation should be within CI
                assert -1 <= lower <= 1
                assert -1 <= upper <= 1
    
    def test_confidence_interval_edge_cases(self, sample_analyzer):
        """Test confidence interval with edge cases"""
        # Test with small sample size
        ci_small = sample_analyzer._calculate_confidence_interval(0.5, 3)
        assert np.isnan(ci_small[0]) and np.isnan(ci_small[1])
        
        # Test with perfect correlation
        ci_perfect = sample_analyzer._calculate_confidence_interval(1.0, 100)
        assert np.isnan(ci_perfect[0]) and np.isnan(ci_perfect[1])
    
    def test_manufacturing_process_relationships(self, sample_analyzer):
        """Test manufacturing-specific process relationships"""
        # Verify key manufacturing variables are configured
        assert 'thickness' in sample_analyzer.process_relationships
        assert 'speed' in sample_analyzer.process_relationships
        assert 'quality_score' in sample_analyzer.process_relationships
        
        # Check that relationships have expected structure
        for var, config in sample_analyzer.process_relationships.items():
            assert 'related_to' in config
            assert 'typical_lags' in config
            assert isinstance(config['related_to'], list)
            assert isinstance(config['typical_lags'], list)
    
    def test_correlation_interpretation_levels(self, sample_analyzer, sample_manufacturing_data):
        """Test correlation strength interpretation"""
        results = sample_analyzer.find_significant_correlations(
            sample_manufacturing_data, 
            min_correlation=0.1,
            return_all=True
        )
        
        # Check that interpretations are appropriate
        for result in results:
            coeff = abs(result.correlation_coefficient)
            interpretation = result.interpretation.lower()
            
            if coeff > 0.8:
                assert 'very strong' in interpretation
            elif coeff > 0.6:
                assert 'strong' in interpretation
            elif coeff > 0.4:
                assert 'moderate' in interpretation
            elif coeff > 0.2:
                assert 'weak' in interpretation
            else:
                assert 'very weak' in interpretation
            
            # Check direction
            if result.correlation_coefficient > 0:
                assert 'positive' in interpretation
            else:
                assert 'negative' in interpretation
    
    def test_significance_level_interpretation(self, sample_analyzer, sample_manufacturing_data):
        """Test statistical significance level interpretation"""
        results = sample_analyzer.find_significant_correlations(
            sample_manufacturing_data, 
            min_correlation=0.1,
            return_all=True
        )
        
        for result in results:
            p_val = result.p_value
            significance = result.significance_level
            
            if p_val < 0.001:
                assert significance == 'highly_significant'
            elif p_val < 0.01:
                assert significance == 'very_significant'
            elif p_val < 0.05:
                assert significance == 'significant'
            else:
                assert significance == 'not_significant'


class TestCorrelationAnalysisIntegration:
    """Integration tests for correlation analysis with real data patterns"""
    
    def test_manufacturing_data_workflow(self):
        """Test complete workflow with manufacturing-like data"""
        # Create realistic manufacturing scenario
        np.random.seed(123)
        n_samples = 500
        
        # Simulate manufacturing line data
        timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='30s')
        
        # Production speed affects multiple downstream variables
        base_speed = 150
        speed_variation = np.random.normal(0, 5, n_samples)
        speed = base_speed + speed_variation
        
        # Temperature responds to speed changes with some lag
        temperature = np.zeros(n_samples)
        temperature[0] = 80
        for i in range(1, n_samples):
            # Temperature responds to speed with 5-period lag
            lag_idx = max(0, i - 5)
            temperature[i] = 0.95 * temperature[i-1] + 0.1 * speed[lag_idx] + np.random.normal(0, 0.5)
        
        # Thickness affected by both speed and temperature
        thickness = 12.5 + 0.008 * speed + 0.01 * temperature + np.random.normal(0, 0.05, n_samples)
        
        # Pressure correlated with speed
        pressure = 45 + 0.12 * speed + np.random.normal(0, 2, n_samples)
        
        # Quality inversely affected by process instability
        speed_instability = pd.Series(speed).rolling(10).std().fillna(0)
        temp_instability = pd.Series(temperature).rolling(10).std().fillna(0)
        quality = 95 - 2 * speed_instability - 1.5 * temp_instability + np.random.normal(0, 1, n_samples)
        
        # Some random noise variables
        noise1 = np.random.normal(100, 10, n_samples)
        noise2 = np.random.normal(50, 5, n_samples)
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'speed': speed,
            'temperature': temperature,
            'thickness': thickness,
            'pressure': pressure,
            'quality': quality,
            'noise1': noise1,
            'noise2': noise2
        })
        
        analyzer = ManufacturingCorrelationAnalyzer()
        
        # Test correlation matrix
        corr_result = analyzer.calculate_correlation_matrix(df)
        assert len(corr_result.matrix) > 0
        
        # Test significant correlations
        significant_corrs = analyzer.find_significant_correlations(df, min_correlation=0.3)
        
        # Should find strong correlations in our engineered data
        assert len(significant_corrs) > 0
        
        # Verify expected relationships are found
        found_relationships = {
            (corr.variable_1, corr.variable_2) for corr in significant_corrs
        }
        
        # Should find speed-pressure relationship (directly correlated)
        speed_pressure_found = any(
            ('speed' in pair and 'pressure' in pair) for pair in found_relationships
        )
        assert speed_pressure_found, "Expected speed-pressure correlation not found"
        
        # Test lag analysis
        try:
            lag_result = analyzer.analyze_lag_correlations(df, 'speed', 'temperature', max_lag=20)
            
            # Should find some lag relationship (we engineered a 5-period lag)
            assert lag_result.optimal_lag >= 0
            assert len(lag_result.correlation_by_lag) > 0
            
        except Exception as e:
            pytest.fail(f"Lag analysis failed: {e}")
        
        # Test process correlation analysis
        try:
            process_results = analyzer.analyze_process_correlations(df, 'quality')
            
            # Should analyze relationships with other process variables
            assert len(process_results) > 0
            
            # Check that we get results for key variables
            process_vars = set(process_results.keys())
            expected_vars = {'speed', 'temperature', 'thickness', 'pressure'}
            
            # Should have analyzed some of the expected variables
            assert len(process_vars.intersection(expected_vars)) > 0
            
        except Exception as e:
            pytest.fail(f"Process correlation analysis failed: {e}")


class TestCorrelationErrorHandling:
    """Test error handling and edge cases"""
    
    def test_empty_dataframe_handling(self):
        """Test handling of empty DataFrames"""
        analyzer = ManufacturingCorrelationAnalyzer()
        empty_df = pd.DataFrame()
        
        # Should handle empty data gracefully
        corr_result = analyzer.calculate_correlation_matrix(empty_df)
        assert corr_result.matrix == {}
        
        significant_corrs = analyzer.find_significant_correlations(empty_df)
        assert len(significant_corrs) == 0
    
    def test_single_column_dataframe(self):
        """Test handling of single-column DataFrame"""
        analyzer = ManufacturingCorrelationAnalyzer()
        single_col_df = pd.DataFrame({'single_var': [1, 2, 3, 4, 5]})
        
        # Should handle single column gracefully
        corr_result = analyzer.calculate_correlation_matrix(single_col_df)
        # Single variable should have correlation of 1 with itself (if calculated)
        if 'single_var' in corr_result.matrix and 'single_var' in corr_result.matrix['single_var']:
            correlation_val = corr_result.matrix['single_var']['single_var']
            # Allow for NaN in case of insufficient data
            assert correlation_val == 1.0 or pd.isna(correlation_val)
        
        significant_corrs = analyzer.find_significant_correlations(single_col_df)
        # No pairs to correlate
        assert len(significant_corrs) == 0
    
    def test_all_nan_columns(self):
        """Test handling of columns with all NaN values"""
        analyzer = ManufacturingCorrelationAnalyzer()
        nan_df = pd.DataFrame({
            'good_col': [1, 2, 3, 4, 5],
            'nan_col': [np.nan, np.nan, np.nan, np.nan, np.nan],
            'another_good_col': [5, 4, 3, 2, 1]
        })
        
        # Should handle NaN columns gracefully
        corr_result = analyzer.calculate_correlation_matrix(nan_df)
        
        # Should still get correlations for valid columns
        assert 'good_col' in corr_result.matrix
        assert 'another_good_col' in corr_result.matrix
    
    def test_constant_columns(self):
        """Test handling of constant (zero variance) columns"""
        analyzer = ManufacturingCorrelationAnalyzer()
        constant_df = pd.DataFrame({
            'varying_col': [1, 2, 3, 4, 5],
            'constant_col': [10, 10, 10, 10, 10],
            'another_varying_col': [5, 4, 3, 2, 1]
        })
        
        # Should handle constant columns gracefully
        significant_corrs = analyzer.find_significant_correlations(constant_df)
        
        # Should not include correlations with constant columns
        for corr in significant_corrs:
            assert corr.variable_1 != 'constant_col'
            assert corr.variable_2 != 'constant_col'
    
    def test_invalid_correlation_method(self):
        """Test handling of invalid correlation method"""
        analyzer = ManufacturingCorrelationAnalyzer()
        df = pd.DataFrame({'var1': [1, 2, 3], 'var2': [4, 5, 6]})
        
        with pytest.raises(ValueError, match="Unknown correlation method"):
            analyzer.calculate_correlation_matrix(df, method='invalid_method')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])