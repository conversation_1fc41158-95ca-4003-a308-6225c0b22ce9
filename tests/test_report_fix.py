#!/usr/bin/env python3
"""
Test that the report generation fixes work
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_report_fixes():
    """Test that report generation works without errors"""
    
    try:
        from src.forecasting.trainer import ManufacturingForecastTrainer
        from src.forecasting.config import load_config_from_file
        
        logger.info("🧪 Testing Report Generation Fixes...")
        
        # Load configuration
        config_path = "config/forecasting_config.json"
        forecast_config, training_config = load_config_from_file(config_path)
        
        # Override with minimal settings for quick test
        training_config.max_epochs = 1
        training_config.batch_size = 4
        training_config.eval_steps = 2
        training_config.save_steps = 5
        training_config.early_stopping_patience = 3
        
        logger.info("Creating trainer...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,
            use_transfer_learning=False
        )
        
        logger.info("🚀 Testing limited training with report generation...")
        
        # Train with very limited steps
        try:
            results = trainer.train_all_target_variables(
                data_path="test-data",
                target_variables=["thickness_thickness_avg"]
            )
            
            if results:
                logger.info("✅ Training completed, testing report generation...")
                
                # Test both report generation methods
                try:
                    trainer.generate_training_report()
                    logger.info("✅ Basic training report generated successfully")
                except Exception as e:
                    logger.error(f"❌ Basic report error: {str(e)}")
                
                try:
                    trainer.generate_enhanced_training_report()
                    logger.info("✅ Enhanced training report generated successfully")
                except Exception as e:
                    logger.error(f"❌ Enhanced report error: {str(e)}")
                
                # Check if report files were created
                models_dir = Path(training_config.model_save_path)
                basic_report = models_dir / 'training_report.json'
                enhanced_report = models_dir / 'enhanced_training_report.json'
                
                if basic_report.exists():
                    logger.info(f"✅ Basic report file created: {basic_report}")
                else:
                    logger.warning(f"⚠️ Basic report file not found: {basic_report}")
                
                if enhanced_report.exists():
                    logger.info(f"✅ Enhanced report file created: {enhanced_report}")
                else:
                    logger.warning(f"⚠️ Enhanced report file not found: {enhanced_report}")
                
                logger.info("🎉 Report generation test completed successfully!")
                return 0
            else:
                logger.error("❌ No models trained")
                return 1
                
        except Exception as e:
            logger.error(f"❌ Training error: {str(e)}")
            # Check for specific report errors
            error_str = str(e).lower()
            if "stabilityvalidationresult" in error_str and "get" in error_str:
                logger.error("🚨 StabilityValidationResult .get() error still present!")
            elif "bool_" in error_str and "json" in error_str:
                logger.error("🚨 JSON serialization error still present!")
            return 1
    
    except Exception as e:
        logger.error(f"❌ Setup error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """Main test function"""
    logger.info("=" * 60)
    logger.info("🔧 TESTING REPORT GENERATION FIXES")
    logger.info("=" * 60)
    
    return test_report_fixes()

if __name__ == "__main__":
    exit(main())