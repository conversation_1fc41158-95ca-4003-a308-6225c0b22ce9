#!/usr/bin/env python3
"""
Test script for the rebuilt manufacturing data loader.

Tests the new unified table creation algorithm with the available data sources:
- VM Capacity Report.csv
- fm_stack.csv  
- sm_stack.csv
- speed.csv
- stop.csv
"""

import sys
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append('src')

from data.loader import ManufacturingDataLoader, create_unified_table, parse_datetime, calculate_thickness_metrics

def test_config_loading():
    """Test configuration file loading."""
    print("="*60)
    print("Testing Configuration Loading")
    print("="*60)
    
    try:
        loader = ManufacturingDataLoader()
        print(f"✓ Configuration loaded successfully")
        print(f"  Data sources configured: {len(loader.config['data_sources'])}")
        
        for source_name in loader.config['data_sources'].keys():
            print(f"  - {source_name}")
        
        return True
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return False

def test_individual_data_sources():
    """Test loading individual data sources."""
    print("\n" + "="*60)
    print("Testing Individual Data Source Loading")
    print("="*60)
    
    loader = ManufacturingDataLoader()
    success_count = 0
    
    for source_name, source_config in loader.config['data_sources'].items():
        try:
            file_path = Path(source_config['file_path'])
            print(f"\nTesting {source_name} ({file_path})...")
            
            if not file_path.exists():
                print(f"  ✗ File not found: {file_path}")
                continue
            
            # Load the CSV
            df = pd.read_csv(file_path)
            print(f"  ✓ Loaded {len(df):,} rows × {len(df.columns)} columns")
            
            # Test timestamp processing
            df_processed = loader._process_timestamps(df, source_config)
            timestamp_cols = [col for col in df_processed.columns if 'timestamp' in col.lower()]
            if timestamp_cols:
                print(f"  ✓ Timestamp columns created: {timestamp_cols}")
            
            success_count += 1
            
        except Exception as e:
            print(f"  ✗ Failed to load {source_name}: {e}")
    
    print(f"\nSuccessfully loaded {success_count}/{len(loader.config['data_sources'])} data sources")
    return success_count > 0

def test_core_functions():
    """Test core parsing functions."""
    print("\n" + "="*60)
    print("Testing Core Functions")
    print("="*60)
    
    # Test parse_datetime function
    print("Testing parse_datetime()...")
    test_cases = [
        ("2025.03.01", "14:30:15"),
        ("2025-03-01", "14:30:15"),
        ("1-Mar-2025", "14:30:15"),
        (None, "14:30:15"),
        ("2025.03.01", None)
    ]
    
    for date_str, time_str in test_cases:
        result = parse_datetime(date_str, time_str)
        print(f"  parse_datetime('{date_str}', '{time_str}') = {result}")
    
    # Test calculate_thickness_metrics function
    print("\nTesting calculate_thickness_metrics()...")
    test_row = pd.Series({f'sensor_{i:02d}': 7.5 + i*0.1 for i in range(1, 11)})
    metrics = calculate_thickness_metrics(test_row)
    print(f"  Calculated metrics: {dict(metrics)}")
    
    return True

def test_unified_table_creation():
    """Test the unified table creation process."""
    print("\n" + "="*60)
    print("Testing Unified Table Creation")
    print("="*60)
    
    try:
        loader = ManufacturingDataLoader()
        print("Creating unified table...")
        
        # This will test the full pipeline
        unified_df = loader.create_unified_table()
        
        print(f"✓ Unified table created successfully!")
        print(f"  Rows: {len(unified_df):,}")
        print(f"  Columns: {len(unified_df.columns)}")
        print(f"  Column types:")
        
        # Show column summary
        for col in unified_df.columns[:20]:  # Show first 20 columns
            dtype = unified_df[col].dtype
            non_null = unified_df[col].notna().sum()
            print(f"    {col}: {dtype} ({non_null:,} non-null)")
        
        if len(unified_df.columns) > 20:
            print(f"    ... and {len(unified_df.columns) - 20} more columns")
        
        # Save sample for inspection
        sample_file = "unified_table_sample.csv"
        unified_df.head(100).to_csv(sample_file, index=False)
        print(f"  Sample saved to: {sample_file}")
        
        return True
        
    except Exception as e:
        print(f"✗ Unified table creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_standalone_function():
    """Test the standalone create_unified_table function."""
    print("\n" + "="*60)
    print("Testing Standalone Function")
    print("="*60)
    
    try:
        # Load individual data files
        speed_df = pd.read_csv("test-data/speed.csv")
        stop_df = pd.read_csv("test-data/stop.csv")
        sm_stack_df = pd.read_csv("test-data/data-cyrus/sm_stack.csv")
        fm_stack_df = pd.read_csv("test-data/data-cyrus/fm_stack.csv")
        vm_capacity_df = pd.read_csv("test-data/data-cyrus/VM Capacity Report.csv")
        
        print("Loaded individual DataFrames:")
        print(f"  Speed: {len(speed_df):,} rows")
        print(f"  Stop: {len(stop_df):,} rows")
        print(f"  SM Stack: {len(sm_stack_df):,} rows")
        print(f"  FM Stack: {len(fm_stack_df):,} rows")
        print(f"  VM Capacity: {len(vm_capacity_df):,} rows")
        
        # Test standalone function
        print("\nCalling standalone create_unified_table()...")
        unified_df = create_unified_table(
            thickness_df=None,  # Will be derived from speed
            speed_df=speed_df,
            stop_df=stop_df,
            sm_stack_df=sm_stack_df,
            fm_stack_df=fm_stack_df,
            vm_capacity_df=vm_capacity_df
        )
        
        print(f"✓ Standalone function successful!")
        print(f"  Result: {len(unified_df):,} rows × {len(unified_df.columns)} columns")
        
        return True
        
    except Exception as e:
        print(f"✗ Standalone function failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("Manufacturing Data Loader - Rebuild Testing")
    print("="*60)
    
    tests = [
        ("Configuration Loading", test_config_loading),
        ("Individual Data Sources", test_individual_data_sources),
        ("Core Functions", test_core_functions),
        ("Unified Table Creation", test_unified_table_creation),
        ("Standalone Function", test_standalone_function)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("Test Summary")
    print("="*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The rebuilt loader is working correctly.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the output above for details.")

if __name__ == "__main__":
    main()