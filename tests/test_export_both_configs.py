#!/usr/bin/env python3
"""
Test export functionality with both add_thickness configurations.

Validates that CSV exports work correctly with and without thickness data.
"""

import sys
import os
import json
import pandas as pd
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.loader import ManufacturingDataLoader
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_export_configurations():
    """Test CSV export with both thickness configurations."""
    try:
        config_path = Path(__file__).parent / "data.config.json"
        output_dir = "/Users/<USER>/JH/JH PoC/test_1/test-data/consolidated"
        
        # Test 1: Export with add_thickness = false (current default)
        logger.info("=" * 60)
        logger.info("🧪 TEST 1: Export with add_thickness = false")
        logger.info("=" * 60)
        
        loader_clean = ManufacturingDataLoader(str(config_path))
        logger.info(f"Thickness simulation: {'enabled' if loader_clean.add_thickness else 'disabled'}")
        
        # Create and export matched stacks
        export_path_clean = loader_clean.create_and_export_matched_stacks(
            output_dir=output_dir,
            filename="test_clean_export.csv"
        )
        
        logger.info(f"✅ Clean export completed: {export_path_clean}")
        
        # Validate clean export
        clean_df = pd.read_csv(export_path_clean, comment='#', nrows=5)
        thickness_cols_clean = [col for col in clean_df.columns if 'sensor_' in col or 'thickness_' in col]
        
        logger.info(f"Clean export columns: {len(clean_df.columns)}")
        logger.info(f"Thickness columns in clean export: {len(thickness_cols_clean)}")
        
        if len(thickness_cols_clean) == 0:
            logger.info("✅ Clean export contains no simulated thickness data")
        else:
            logger.warning(f"⚠️ Unexpected thickness columns in clean export: {thickness_cols_clean[:5]}")
        
        # Test 2: Export with add_thickness = true
        logger.info("=" * 60)
        logger.info("🧪 TEST 2: Export with add_thickness = true")
        logger.info("=" * 60)
        
        # Temporarily modify config
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        config_data['add_thickness'] = True
        
        # Write temporary config
        temp_config_path = Path(__file__).parent / "temp_export_config.json"
        with open(temp_config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        try:
            # Test export with thickness enabled
            loader_thickness = ManufacturingDataLoader(str(temp_config_path))
            logger.info(f"Thickness simulation: {'enabled' if loader_thickness.add_thickness else 'disabled'}")
            
            # Create and export matched stacks
            export_path_thickness = loader_thickness.create_and_export_matched_stacks(
                output_dir=output_dir,
                filename="test_thickness_export.csv"
            )
            
            logger.info(f"✅ Thickness export completed: {export_path_thickness}")
            
            # Validate thickness export
            thickness_df = pd.read_csv(export_path_thickness, comment='#', nrows=5)
            thickness_cols_full = [col for col in thickness_df.columns if 'sensor_' in col or 'thickness_' in col]
            
            logger.info(f"Thickness export columns: {len(thickness_df.columns)}")
            logger.info(f"Thickness columns in thickness export: {len(thickness_cols_full)}")
            
            if len(thickness_cols_full) > 0:
                logger.info(f"✅ Thickness export contains {len(thickness_cols_full)} thickness columns")
                logger.info(f"Sample thickness columns: {thickness_cols_full[:5]}")
            else:
                logger.warning("⚠️ Expected thickness columns but none found in thickness export")
            
        finally:
            # Clean up temporary config
            if temp_config_path.exists():
                temp_config_path.unlink()
        
        # Comparison and validation
        logger.info("=" * 60)
        logger.info("📊 EXPORT COMPARISON SUMMARY")
        logger.info("=" * 60)
        
        column_diff = len(thickness_df.columns) - len(clean_df.columns)
        logger.info(f"Clean export: {len(clean_df.columns)} columns")
        logger.info(f"Thickness export: {len(thickness_df.columns)} columns")
        logger.info(f"Column difference: {column_diff}")
        
        # Check file sizes
        clean_size = Path(export_path_clean).stat().st_size / (1024 * 1024)  # MB
        thickness_size = Path(export_path_thickness).stat().st_size / (1024 * 1024)  # MB
        
        logger.info(f"Clean export size: {clean_size:.2f} MB")
        logger.info(f"Thickness export size: {thickness_size:.2f} MB")
        logger.info(f"Size difference: {thickness_size - clean_size:.2f} MB")
        
        # Validate metadata in both files
        logger.info("=" * 60)
        logger.info("📋 METADATA VALIDATION")
        logger.info("=" * 60)
        
        # Read metadata from both files
        with open(export_path_clean, 'r') as f:
            clean_metadata = [line.strip() for line in f.readlines()[:10] if line.startswith('#')]
        
        with open(export_path_thickness, 'r') as f:
            thickness_metadata = [line.strip() for line in f.readlines()[:10] if line.startswith('#')]
        
        logger.info("Clean export metadata:")
        for line in clean_metadata:
            logger.info(f"  {line}")
        
        logger.info("Thickness export metadata:")
        for line in thickness_metadata:
            logger.info(f"  {line}")
        
        # Validate that column counts in metadata match actual data
        clean_meta_cols = None
        thickness_meta_cols = None
        
        for line in clean_metadata:
            if "Total Columns:" in line:
                clean_meta_cols = int(line.split(':')[1].strip())
        
        for line in thickness_metadata:
            if "Total Columns:" in line:
                thickness_meta_cols = int(line.split(':')[1].strip())
        
        if clean_meta_cols == len(clean_df.columns):
            logger.info(f"✅ Clean export metadata column count accurate: {clean_meta_cols}")
        else:
            logger.warning(f"⚠️ Clean export metadata mismatch: {clean_meta_cols} vs {len(clean_df.columns)}")
        
        if thickness_meta_cols == len(thickness_df.columns):
            logger.info(f"✅ Thickness export metadata column count accurate: {thickness_meta_cols}")
        else:
            logger.warning(f"⚠️ Thickness export metadata mismatch: {thickness_meta_cols} vs {len(thickness_df.columns)}")
        
        logger.info("=" * 60)
        logger.info("🎉 Export configuration test completed successfully!")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Export test failed: {e}")
        import traceback
        logger.error(f"Full error traceback:\\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_export_configurations()
    if success:
        print("\\n✅ TEST PASSED: Export functionality working correctly with both configurations")
    else:
        print("\\n❌ TEST FAILED: Issues with export functionality")
        sys.exit(1)