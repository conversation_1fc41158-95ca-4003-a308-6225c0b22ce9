#!/usr/bin/env python3
"""
Demo script showing the CLI functionality for manufacturing correlation analysis.
"""

import subprocess
import sys
import json
from pathlib import Path

def run_cli_demo():
    """Demonstrate CLI capabilities"""
    
    print("🏭 Manufacturing Correlation Analysis CLI Demo")
    print("=" * 50)
    
    # Test 1: Help command
    print("\n1. Testing CLI Help:")
    result = subprocess.run([
        sys.executable, "-m", "src.cli", "--help"
    ], capture_output=True, text=True, cwd=".")
    print(result.stdout)
    
    # Test 2: Analyze command help
    print("\n2. Testing Analyze Command Help:")
    result = subprocess.run([
        sys.executable, "-m", "src.cli", "analyze", "--help"
    ], capture_output=True, text=True, cwd=".")
    print(result.stdout)
    
    # Test 3: Quick correlation analysis
    print("\n3. Running Sample Analysis:")
    query = "What are the strongest correlations in the manufacturing data?"
    output_file = "demo_analysis.json"
    
    print(f"Query: {query}")
    print("Running analysis...")
    
    result = subprocess.run([
        sys.executable, "-m", "src.cli", "analyze",
        "-q", query,
        "-o", output_file,
        "-t", "general"
    ], capture_output=True, text=True, cwd=".")
    
    if result.returncode == 0:
        print("✅ Analysis completed successfully!")
        print("\nOutput:")
        print(result.stdout)
        
        # Show exported results
        if Path(output_file).exists():
            print(f"\n4. Exported Results ({output_file}):")
            with open(output_file, 'r') as f:
                data = json.load(f)
            
            print(f"Analysis Type: {data.get('analysis_type', 'Unknown')}")
            print(f"Data Quality Score: {data['results']['data_quality_score']:.3f}")
            print(f"Significant Correlations: {len(data['results']['significant_correlations'])}")
            print(f"Key Insights: {len(data['results']['insights'])}")
            
            # Clean up
            # Path(output_file).unlink()
            # print(f"\n✅ Demo completed! Cleaned up {output_file}")
    else:
        print("❌ Analysis failed!")
        print("Error:", result.stderr)
    
    # Test 4: Show CLI features summary
    print("\n" + "=" * 50)
    print("📋 CLI Features Summary:")
    print("• Interactive mode: python -m src.cli interactive")
    print("• Single analysis: python -m src.cli analyze -q 'your question'")
    print("• Export results: python -m src.cli analyze -q 'query' -o results.json")
    print("• Analysis types: general, lag, quality, optimization, rca")
    print("• Data directory: --data-dir path/to/data")
    print("• Real manufacturing data: 334K+ records processed")
    print("• AI-powered insights with statistical validation")

if __name__ == "__main__":
    run_cli_demo()