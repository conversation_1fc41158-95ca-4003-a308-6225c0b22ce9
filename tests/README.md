# Manufacturing Correlation Analysis - Test Suite

This directory contains comprehensive unit tests for the Manufacturing Correlation Analysis system. The test suite validates all core functionality including agent behavior, statistical correlations, and data loading/preprocessing.

## Test Overview

**Total Tests**: 71 tests across 3 main modules
- **test_agent.py**: 17 tests - Agent functionality and LLM integration
- **test_correlations.py**: 32 tests - Statistical correlation analysis  
- **test_data_loader.py**: 22 tests - Data loading and validation

## Prerequisites

### Environment Setup
```bash
# Activate virtual environment
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### Required Environment Variables
Create a `.env` file in the project root with:
```env
# LLM Provider Selection
LLM_PROVIDER=ANTHROPIC

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Data Configuration
DATA_PATH=test-data/
CACHE_PATH=.cache/
```

## Test Categories

### 1. Agent Tests (`test_agent.py`)
Tests the LLM-powered correlation analysis agents with real API integration.

**Test Classes:**
- `TestAgentConfiguration` - Environment setup and agent initialization
- `TestAgentWithRealData` - End-to-end analysis with real manufacturing data
- `TestSpecializedAgents` - Quality, optimization, lag, and process-specific agents
- `TestAgentErrorHandling` - Robust handling of edge cases and bad data
- `TestAgentPerformance` - Response time and efficiency validation

**Key Features Tested:**
- ✅ LLM integration (Anthropic Claude, Vertex AI)
- ✅ Manufacturing domain knowledge
- ✅ Statistical validation of insights
- ✅ Data quality assessment
- ✅ Specialized analysis types (quality, lag, optimization)

### 2. Correlation Tests (`test_correlations.py`)
Tests statistical correlation calculations and manufacturing-specific patterns.

**Test Classes:**
- `TestCorrelationResult` - Result data models
- `TestManufacturingCorrelationAnalyzer` - Core correlation algorithms
- `TestCorrelationAnalysisIntegration` - End-to-end workflows
- `TestCorrelationErrorHandling` - Edge cases and error scenarios

**Key Features Tested:**
- ✅ Pearson, Spearman, and Kendall correlations
- ✅ Significance testing and confidence intervals
- ✅ Lag correlation analysis
- ✅ Partial correlation calculations
- ✅ Pattern detection and clustering
- ✅ Manufacturing process relationships

### 3. Data Loader Tests (`test_data_loader.py`)
Tests CSV data loading, validation, and time-series alignment.

**Test Classes:**
- `TestManufacturingDataLoader` - Core loading functionality
- `TestDataValidationResult` - Validation result structures  
- `TestManufacturingDataLoaderIntegration` - Real data workflows

**Key Features Tested:**
- ✅ CSV file loading with proper data types
- ✅ Timestamp alignment across multiple data sources
- ✅ Data quality validation and outlier detection
- ✅ Manufacturing-specific validation rules
- ✅ Unified dataset creation for correlation analysis

## Running Tests

### Quick Commands

```bash
# Run all tests (takes ~4-5 minutes due to LLM API calls)
python -m pytest tests/ -v

# Run tests with brief output
python -m pytest tests/ --tb=no -q

# Run specific test categories
python -m pytest tests/test_correlations.py -v      # Fast - no API calls
python -m pytest tests/test_data_loader.py -v      # Fast - no API calls  
python -m pytest tests/test_agent.py -v            # Slow - requires API calls
```

### Targeted Testing

```bash
# Test specific functionality
python -m pytest tests/test_correlations.py::TestManufacturingCorrelationAnalyzer -v
python -m pytest tests/test_agent.py::TestAgentWithRealData::test_quality_focused_analysis -v

# Run tests with specific markers
python -m pytest tests/ -k "not test_agent" -v     # Skip LLM tests
python -m pytest tests/ -k "correlation" -v        # Only correlation tests

# Parallel execution (for faster runs)
python -m pytest tests/test_correlations.py tests/test_data_loader.py -v
```

### Debug and Development

```bash
# Run with detailed output and stop on first failure  
python -m pytest tests/ -v -s -x

# Run specific failing test with full traceback
python -m pytest tests/test_agent.py::TestAgentWithRealData::test_quality_focused_analysis -v -s --tb=long

# Collect tests without running (useful for CI planning)
python -m pytest tests/ --collect-only -q
```

## Test Data

The tests use sample manufacturing data located in `test-data/`:
- `fm_stack.csv` - Forming machine data
- `sm_stack.csv` - Sheet machine data  
- `speed.csv` - Production speed data
- `stop.csv` - Stoppage events
- `thickness.csv` - Thickness measurements

## Performance Notes

- **Correlation tests**: ~30 seconds (no external dependencies)
- **Data loader tests**: ~15 seconds (local file operations)
- **Agent tests**: ~4-5 minutes (requires LLM API calls)

For development workflows, run correlation and data loader tests frequently, and agent tests before commits.

## Continuous Integration

For CI/CD pipelines:

```bash
# Fast validation (skip LLM tests)
python -m pytest tests/test_correlations.py tests/test_data_loader.py --tb=short

# Full validation (requires API keys in CI environment)
python -m pytest tests/ --tb=short --maxfail=3
```

## Troubleshooting

### Common Issues

1. **Missing API Keys**: Ensure `ANTHROPIC_API_KEY` is set in `.env`
2. **Timeout Errors**: LLM tests may timeout - this is expected for slow networks
3. **Data File Errors**: Ensure `test-data/` directory exists with sample CSV files
4. **Import Errors**: Activate venv and install dependencies

### Test Isolation

Each test is designed to be independent. However, for optimal performance:
- Agent tests share the same LLM instance
- Data loader tests use temporary files
- Correlation tests use synthetic data

### Getting Help

For test-specific issues:
- Check the test docstrings for expected behavior
- Review the `CLAUDE.md` file for project conventions
- Examine failing test output for specific assertion details