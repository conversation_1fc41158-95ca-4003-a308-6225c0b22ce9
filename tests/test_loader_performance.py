#!/usr/bin/env python3
"""
Performance Test for Optimized Data Loader
Tests the rebuilt loader with performance optimizations for stoppage features.
"""

import time
import logging
from src.data.loader import ManufacturingDataLoader

# Set up logging to see progress
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_loader_performance():
    """Test the optimized loader performance."""
    logger.info("Starting performance test of optimized data loader...")
    
    start_time = time.time()
    
    try:
        # Initialize loader
        loader = ManufacturingDataLoader()
        
        # Test data loading
        logger.info("Loading data sources...")
        datasets = loader.load_all_data_sources()
        
        load_time = time.time()
        logger.info(f"Data loading completed in {load_time - start_time:.2f} seconds")
        
        # Print dataset sizes
        for name, df in datasets.items():
            logger.info(f"{name}: {len(df):,} rows × {len(df.columns)} columns")
        
        # Test unified table creation with optimizations
        logger.info("Creating unified table with optimized stoppage features...")
        unified_df = loader.create_unified_table()
        
        unified_time = time.time()
        logger.info(f"Unified table creation completed in {unified_time - load_time:.2f} seconds")
        logger.info(f"Total processing time: {unified_time - start_time:.2f} seconds")
        
        # Validate results
        logger.info(f"Unified table: {len(unified_df):,} rows × {len(unified_df.columns)} columns")
        
        # Check stoppage features
        stoppage_cols = [col for col in unified_df.columns if 'stop' in col or 'restart' in col]
        logger.info(f"Stoppage features created: {stoppage_cols}")
        
        # Sample some data
        sample_data = unified_df.head()
        logger.info("Sample unified data:")
        print(sample_data[['timestamp', 'work_center', 'Speed', 'time_since_last_stop', 'is_restart_period']].to_string())
        
        return True
        
    except Exception as e:
        logger.error(f"Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_loader_performance()
    if success:
        print("\n✅ Performance test completed successfully!")
    else:
        print("\n❌ Performance test failed!")