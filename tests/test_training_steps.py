#!/usr/bin/env python3
"""
Test that training can run for several steps without shape errors
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_training_steps():
    """Test training for a few steps"""
    
    from src.forecasting.trainer import ManufacturingForecastTrainer
    from src.forecasting.config import load_config_from_file
    
    logger.info("🧪 Testing Training Steps...")
    
    try:
        # Load configuration
        config_path = "config/forecasting_config.json"
        forecast_config, training_config = load_config_from_file(config_path)
        
        # Override with minimal settings for quick test
        training_config.max_epochs = 1
        training_config.batch_size = 4  # Smaller batch for faster testing
        training_config.eval_steps = 5
        training_config.save_steps = 10
        training_config.early_stopping_patience = 5
        
        logger.info("Creating enhanced trainer...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,
            use_transfer_learning=False  # Skip transfer learning for speed
        )
        
        logger.info("🚀 Starting training for a few steps...")
        
        # Override the train method to stop after a few steps
        original_train_models = trainer.train_models
        
        def limited_train_models(target_variables=None, **kwargs):
            logger.info("⏰ Running limited training (max 10 steps)...")
            kwargs['max_steps'] = 10  # Limit to 10 steps
            return original_train_models(target_variables, **kwargs)
        
        trainer.train_models = limited_train_models
        
        # Train for just one target with limited steps
        results = trainer.train_models(target_variables=["thickness_thickness_avg"])
        
        logger.info("✅ Training steps completed successfully!")
        
        if results:
            logger.info(f"Results: {len(results)} model(s) trained")
            for target, model in results.items():
                logger.info(f"  - {target}: Training completed")
                
                # Check training history
                if hasattr(model, 'training_history'):
                    history = model.training_history
                    if 'epochs_trained' in history:
                        logger.info(f"    Steps completed: {history.get('epochs_trained', 0)}")
                    if 'training_loss' in history:
                        logger.info(f"    Final loss: {history.get('training_loss', 'N/A')}")
        
        logger.info("🎉 Training steps test completed successfully!")
        logger.info("The shape mismatch issue has been resolved.")
        return 0
        
    except Exception as e:
        logger.error(f"❌ Error during training test: {str(e)}")
        
        # Check if it's the specific shape error we were fixing
        error_str = str(e).lower()
        if "size of tensor" in error_str and "must match" in error_str:
            logger.error("🚨 Shape mismatch error still occurring!")
        elif "target size" in error_str and "input size" in error_str:
            logger.error("🚨 Shape mismatch warning still occurring!")
        else:
            logger.info("✅ No shape mismatch errors detected")
        
        import traceback
        traceback.print_exc()
        return 1

def main():
    """Main test function"""
    return test_training_steps()

if __name__ == "__main__":
    exit(main())