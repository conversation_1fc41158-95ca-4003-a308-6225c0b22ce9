"""
Integration Tests for Complete Forecasting Agent Workflow

End-to-end tests for the PatchTST forecasting system integrated with
the correlation analysis agent. Tests complete workflows from data
loading through model training to forecasting with real API integration.
"""

import pytest
import pandas as pd
import numpy as np
import asyncio
import os
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

# Import the modules to test
from src.agents.correlation_agent import (
    correlation_agent,
    analyze_manufacturing_correlations,
    ManufacturingDataDependencies,
    CorrelationAnalysis
)
from src.forecasting import (
    ManufacturingPatchTSTModel,
    ManufacturingForecastTrainer,
    ForecastConfig,
    PatchTSTTrainingConfig
)
from src.data.loader import ManufacturingDataLoader


class TestForecastingAgentWorkflow:
    """Test complete forecasting workflow integration"""
    
    @pytest.fixture
    def production_manufacturing_data(self):
        """Create production-scale manufacturing data for testing"""
        # Create 2000 data points (about 33 hours of data)
        n_samples = 2000
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(hours=33),
            periods=n_samples,
            freq='1min'
        )
        
        # Simulate realistic manufacturing data with multiple patterns
        np.random.seed(123)  # Different seed for production test
        
        # Multi-scale time patterns
        time_trend = np.linspace(0, 33, n_samples)
        daily_cycle = np.sin(2 * np.pi * np.arange(n_samples) / 1440)  # 24-hour cycle
        shift_cycle = np.sin(2 * np.pi * np.arange(n_samples) / 480)   # 8-hour shift cycle
        weekly_trend = np.sin(2 * np.pi * np.arange(n_samples) / (7 * 1440))  # Weekly pattern
        
        # Manufacturing process variables with realistic correlations
        base_speed = 50
        speed = (base_speed + 
                10 * np.sin(time_trend * 0.5) +
                5 * daily_cycle +
                3 * shift_cycle +
                2 * weekly_trend +
                np.random.normal(0, 2.5, n_samples))
        speed = np.clip(speed, 20, 80)  # Realistic speed range
        
        base_temperature = 80
        temperature = (base_temperature +
                      8 * np.cos(time_trend * 0.3) +
                      4 * daily_cycle +
                      2 * shift_cycle +
                      np.random.normal(0, 1.8, n_samples))
        temperature = np.clip(temperature, 65, 95)  # Realistic temperature range
        
        base_pressure = 100
        pressure = (base_pressure +
                   6 * np.sin(time_trend * 0.4 + 1) +
                   3 * shift_cycle +
                   np.random.normal(0, 2.2, n_samples))
        pressure = np.clip(pressure, 85, 115)  # Realistic pressure range
        
        # Thickness measurements with sensor array simulation
        base_thickness = 12
        thickness_trend = (base_thickness +
                          2 * np.sin(time_trend * 0.2) +
                          1 * daily_cycle +
                          0.5 * shift_cycle)
        
        # 10 thickness sensors with individual characteristics
        thickness_sensors = {}
        sensor_biases = np.random.normal(0, 0.15, 10)  # Sensor calibration differences
        
        for i in range(1, 11):
            sensor_noise = np.random.normal(0, 0.35, n_samples)
            process_variation = 0.5 * np.sin(time_trend * 0.1 + i * 0.5)  # Position-dependent variation
            
            thickness_sensors[f'Sensor {i:02d}'] = (
                thickness_trend + 
                sensor_biases[i-1] + 
                process_variation + 
                sensor_noise
            )
        
        # Calculate aggregate thickness metrics
        thickness_values = np.array([thickness_sensors[f'Sensor {i:02d}'] for i in range(1, 11)])
        thickness_avg = np.mean(thickness_values, axis=0)
        thickness_uniformity = np.std(thickness_values, axis=0)
        thickness_range = np.max(thickness_values, axis=0) - np.min(thickness_values, axis=0)
        
        # Quality metrics with process relationships
        # Scrap rate depends on speed, temperature stability, and thickness uniformity
        speed_factor = np.maximum(0, (speed - 60) / 20)  # Increases above 60
        temp_stability = np.abs(np.gradient(temperature))  # Temperature rate of change
        thickness_factor = thickness_uniformity / 2  # Higher uniformity = more scrap risk
        
        base_scrap = 0.018
        scrap_rate = (base_scrap + 
                     0.008 * speed_factor +
                     0.005 * temp_stability +
                     0.003 * thickness_factor +
                     np.random.exponential(0.012, n_samples))
        scrap_rate = np.clip(scrap_rate, 0, 0.25)  # Cap at 25%
        
        # Quality index (inverse relationship with scrap and process variation)
        quality_base = 0.96
        speed_penalty = 0.1 * speed_factor
        temp_penalty = 0.05 * temp_stability
        thickness_penalty = 0.08 * thickness_factor
        scrap_penalty = 3 * scrap_rate
        
        quality_index = (quality_base - 
                        speed_penalty - 
                        temp_penalty - 
                        thickness_penalty - 
                        scrap_penalty +
                        np.random.normal(0, 0.015, n_samples))
        quality_index = np.clip(quality_index, 0.5, 1.0)
        
        # Stoppage simulation with realistic patterns
        # More stoppages during shift changes and maintenance windows
        shift_change_hours = [6, 14, 22]  # 6 AM, 2 PM, 10 PM
        maintenance_probability = np.zeros(n_samples)
        
        for i, timestamp in enumerate(timestamps):
            hour = timestamp.hour
            # Higher probability during shift changes
            if any(abs(hour - shift_hour) <= 1 for shift_hour in shift_change_hours):
                maintenance_probability[i] = 0.02
            # Weekly maintenance (Sunday early morning)
            elif timestamp.weekday() == 6 and 2 <= hour <= 6:
                maintenance_probability[i] = 0.05
            else:
                maintenance_probability[i] = 0.005
        
        # Generate stoppage events
        stoppage_events = np.random.binomial(1, maintenance_probability)
        minutes_since_last_stop = np.zeros(n_samples)
        
        last_stop_index = 0
        for i in range(n_samples):
            if stoppage_events[i]:
                last_stop_index = i
            minutes_since_last_stop[i] = i - last_stop_index
        
        # Create comprehensive dataset
        data = pd.DataFrame({
            'timestamp': timestamps,
            'speed': speed,
            'temperature': temperature,
            'pressure': pressure,
            'thickness_avg': thickness_avg,
            'thickness_uniformity': thickness_uniformity,
            'thickness_range': thickness_range,
            'scrap_rate': scrap_rate,
            'quality_index': quality_index,
            'minutes_since_last_stop': minutes_since_last_stop,
            **thickness_sensors
        })
        
        return data
    
    @pytest.mark.asyncio
    async def test_complete_forecasting_workflow(self, production_manufacturing_data):
        """Test complete end-to-end forecasting workflow"""
        # Step 1: Data preparation and validation
        assert len(production_manufacturing_data) >= 1000, "Insufficient data for workflow test"
        assert 'thickness_avg' in production_manufacturing_data.columns
        assert 'scrap_rate' in production_manufacturing_data.columns
        
        # Step 2: Create dependencies for agent
        deps = ManufacturingDataDependencies(
            data=production_manufacturing_data,
            time_column='timestamp',
            target_variables=['thickness_avg', 'scrap_rate', 'quality_index'],
            significance_threshold=0.05,
            min_correlation=0.01,
            analysis_type='forecasting'
        )
        
        # Step 3: Test correlation analysis first (baseline)
        try:
            correlation_result = await correlation_agent.run(
                "Analyze correlations between manufacturing parameters",
                deps=deps
            )
            
            assert correlation_result is not None
            
            if hasattr(correlation_result, 'output'):
                output = correlation_result.output
                assert hasattr(output, 'significant_correlations')
                
        except Exception as e:
            pytest.skip(f"Correlation analysis failed: {e}")
        
        # Step 4: Test forecasting query
        try:
            forecast_result = await correlation_agent.run(
                "Forecast thickness for the next hour using historical patterns",
                deps=deps
            )
            
            assert forecast_result is not None
            
            # The forecast may fail due to no pre-trained model, but agent should handle gracefully
            
        except Exception as e:
            # Expected if forecasting models are not available
            assert "forecast" in str(e).lower() or "model" in str(e).lower()
        
        # Step 5: Test multi-variable forecasting
        try:
            multi_forecast_result = await correlation_agent.run(
                "Compare forecasts for thickness, scrap rate, and quality index over the next 2 hours",
                deps=deps
            )
            
            assert multi_forecast_result is not None
            
        except Exception as e:
            # Expected behavior when models are not available
            pass
    
    @pytest.mark.asyncio
    async def test_forecasting_with_real_manufacturing_data(self):
        """Test forecasting with actual manufacturing test data"""
        # Load real manufacturing data if available
        data_loader = ManufacturingDataLoader('test-data')
        
        try:
            datasets = data_loader.load_all_manufacturing_data()
            unified_data = data_loader.create_unified_dataset()
            
            if unified_data.empty or len(unified_data) < 500:
                pytest.skip("Insufficient real manufacturing data for forecasting test")
            
            # Create dependencies
            deps = ManufacturingDataDependencies(
                data=unified_data,
                time_column='timestamp',
                target_variables=['thickness_avg'] if 'thickness_avg' in unified_data.columns else None,
                analysis_type='forecasting'
            )
            
            # Test with real data
            result = await correlation_agent.run(
                "Analyze correlations and forecast key manufacturing parameters",
                deps=deps
            )
            
            assert result is not None
            
        except Exception as e:
            pytest.skip(f"Real data test failed: {e}")
    
    def test_model_training_integration(self, production_manufacturing_data):
        """Test model training with production-scale data"""
        # Configure for faster training
        forecast_config = ForecastConfig(
            input_variables=['speed', 'temperature', 'pressure', 'thickness_avg'],
            target_variables=['thickness_avg'],
            forecast_horizons=[15, 30],  # Shorter horizons for testing
            lookback_window=120,  # 2 hours
            model_params={
                'd_model': 64,  # Smaller model for testing
                'num_attention_heads': 4,
                'num_hidden_layers': 2,
                'dropout': 0.2
            }
        )
        
        training_config = PatchTSTTrainingConfig(
            batch_size=16,  # Smaller batch for testing
            max_epochs=5,   # Very few epochs for testing
            early_stopping_patience=2,
            validation_split=0.2,
            test_split=0.2
        )
        
        # Initialize trainer
        trainer = ManufacturingForecastTrainer(forecast_config, training_config)
        
        # Test model initialization
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        model.initialize_model(num_input_channels=len(forecast_config.input_variables))
        
        assert model.model is not None
        assert model.model_metadata['num_input_channels'] == len(forecast_config.input_variables)
        
        # Test preprocessing
        preprocessor = model.preprocessor
        prepared_data = preprocessor.prepare_forecasting_data(
            production_manufacturing_data, 'thickness_avg'
        )
        
        assert prepared_data is not None
        assert len(prepared_data['train_data']['sequences']) > 0
        assert len(prepared_data['val_data']['sequences']) > 0
        assert len(prepared_data['test_data']['sequences']) > 0
        
        # Validate preprocessing
        is_valid = preprocessor.validate_preprocessing_output(prepared_data)
        assert is_valid is True
    
    @pytest.mark.asyncio
    async def test_forecasting_performance_metrics(self, production_manufacturing_data):
        """Test forecasting performance and validation metrics"""
        # Create model with minimal configuration for testing
        config = ForecastConfig(
            input_variables=['speed', 'temperature'],
            target_variables=['thickness_avg'],
            forecast_horizons=[10],  # Very short horizon
            lookback_window=60,      # Short lookback
            model_params={
                'd_model': 32,
                'num_attention_heads': 2,
                'num_hidden_layers': 1
            }
        )
        
        model = ManufacturingPatchTSTModel(config)
        
        # Test performance metrics calculation
        recent_data = production_manufacturing_data.tail(200)
        
        # Simulate forecast values for metrics testing
        forecast_values = np.random.normal(
            recent_data['thickness_avg'].mean(),
            recent_data['thickness_avg'].std() * 0.5,
            10
        )
        
        # Test performance calculation
        performance_metrics = model._calculate_performance_metrics(
            recent_data, 'thickness_avg', forecast_values
        )
        
        assert isinstance(performance_metrics, dict)
        assert 'mse' in performance_metrics
        assert 'mae' in performance_metrics
        assert 'mape' in performance_metrics
        assert all(isinstance(v, (int, float)) for v in performance_metrics.values())
    
    def test_manufacturing_insights_generation(self, production_manufacturing_data):
        """Test manufacturing domain insights generation"""
        config = ForecastConfig()
        model = ManufacturingPatchTSTModel(config)
        
        # Test insights for different variables
        test_cases = [
            ('thickness_avg', np.array([10.5, 10.6, 10.7, 10.8, 10.9])),  # Increasing
            ('scrap_rate', np.array([0.02, 0.025, 0.03, 0.035, 0.04])),   # Increasing scrap
            ('speed', np.array([50, 52, 48, 51, 49]))                      # Variable speed
        ]
        
        for target_var, forecast_values in test_cases:
            insights = model._generate_manufacturing_insights(
                target_var, forecast_values, production_manufacturing_data.tail(100)
            )
            
            assert isinstance(insights, list)
            assert len(insights) > 0
            assert all(isinstance(insight, str) for insight in insights)
            
            # Check for manufacturing-specific content
            insights_text = ' '.join(insights).lower()
            
            if target_var == 'thickness_avg':
                assert any(word in insights_text for word in ['thickness', 'specification', 'range'])
            elif target_var == 'scrap_rate':
                assert any(word in insights_text for word in ['scrap', 'threshold', 'rate'])
            elif target_var == 'speed':
                assert any(word in insights_text for word in ['speed', 'stability', 'variance'])
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, production_manufacturing_data):
        """Test error handling and recovery in forecasting workflow"""
        # Test with corrupted data
        corrupted_data = production_manufacturing_data.copy()
        corrupted_data.loc[100:200, 'thickness_avg'] = np.nan  # Large gap
        corrupted_data.loc[300:350, 'speed'] = -999            # Invalid values
        
        deps = ManufacturingDataDependencies(
            data=corrupted_data,
            time_column='timestamp',
            target_variables=['thickness_avg']
        )
        
        # Agent should handle corrupted data gracefully
        try:
            result = await correlation_agent.run(
                "Analyze data quality and attempt forecasting if possible",
                deps=deps
            )
            
            # Should return some form of analysis even with problematic data
            assert result is not None
            
        except Exception as e:
            # Should be a meaningful error message
            assert len(str(e)) > 10
    
    def test_configuration_management_integration(self):
        """Test configuration management across the system"""
        # Test default configuration
        default_config = ForecastConfig()
        assert len(default_config.input_variables) > 0
        assert len(default_config.target_variables) > 0
        
        # Test JSON configuration if available
        config_path = 'config/forecasting_config.json'
        if Path(config_path).exists():
            json_config = ForecastConfig.from_json(config_path)
            assert isinstance(json_config, ForecastConfig)
            
            # Test configuration consistency
            assert all(var in ['thickness_avg', 'thickness_uniformity', 'speed', 
                              'temperature', 'pressure', 'minutes_since_last_stop'] 
                      for var in json_config.input_variables)
        
        # Test training configuration
        training_config = PatchTSTTrainingConfig()
        assert training_config.batch_size > 0
        assert training_config.learning_rate > 0
        assert training_config.validation_split + training_config.test_split < 1.0


class TestForecastingScalability:
    """Test forecasting system scalability and performance"""
    
    def test_large_dataset_handling(self):
        """Test handling of large manufacturing datasets"""
        # Create large dataset (simulating several days of production)
        n_samples = 5000  # About 3.5 days of minute-level data
        
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(days=4),
            periods=n_samples,
            freq='1min'
        )
        
        # Generate realistic but large dataset
        np.random.seed(456)
        
        large_data = pd.DataFrame({
            'timestamp': timestamps,
            'thickness_avg': 12 + 2 * np.sin(np.arange(n_samples) / 100) + np.random.normal(0, 0.5, n_samples),
            'speed': 50 + 10 * np.sin(np.arange(n_samples) / 200) + np.random.normal(0, 3, n_samples),
            'temperature': 80 + 5 * np.cos(np.arange(n_samples) / 150) + np.random.normal(0, 2, n_samples),
            'scrap_rate': 0.02 + 0.01 * np.random.exponential(1, n_samples)
        })
        
        # Test preprocessing with large dataset
        config = ForecastConfig(
            input_variables=['speed', 'temperature'],
            target_variables=['thickness_avg'],
            lookback_window=240,
            forecast_horizons=[30, 60]
        )
        
        preprocessor = ManufacturingTimeSeriesPreprocessor(config)
        
        start_time = time.time()
        prepared_data = preprocessor.prepare_forecasting_data(large_data, 'thickness_avg')
        processing_time = time.time() - start_time
        
        # Should complete within reasonable time (under 30 seconds)
        assert processing_time < 30, f"Processing took too long: {processing_time:.2f} seconds"
        
        # Should handle large dataset successfully
        assert prepared_data is not None
        assert len(prepared_data['train_data']['sequences']) > 100
        
        # Validate memory usage is reasonable
        import sys
        memory_usage = sys.getsizeof(prepared_data)
        assert memory_usage < 500 * 1024 * 1024, f"Memory usage too high: {memory_usage} bytes"
    
    def test_concurrent_forecasting_requests(self, production_manufacturing_data):
        """Test handling of concurrent forecasting requests"""
        # This test would be more meaningful in a production environment
        # For now, test sequential processing
        
        config = ForecastConfig(
            input_variables=['speed', 'temperature'],
            target_variables=['thickness_avg'],
            forecast_horizons=[15]
        )
        
        # Test multiple forecasting configurations
        test_configs = [
            {'target': 'thickness_avg', 'horizon': 15},
            {'target': 'thickness_avg', 'horizon': 30},
            {'target': 'thickness_avg', 'horizon': 60}
        ]
        
        results = []
        for test_config in test_configs:
            model = ManufacturingPatchTSTModel(config)
            
            # Test that multiple models can be created without conflicts
            model.initialize_model(num_input_channels=2)
            assert model.model is not None
            
            results.append(model)
        
        # All models should be independent
        assert len(results) == len(test_configs)
        assert all(r.model is not None for r in results)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "-x"])  # Stop on first failure for debugging