#!/usr/bin/env python3
"""
Quick test for thickness configuration validation.

Tests that the configuration flag works correctly without full data processing.
"""

import sys
import os
import json
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.loader import ManufacturingDataLoader
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_thickness_flag_quick():
    """Quick test of thickness flag functionality."""
    try:
        config_path = Path(__file__).parent / "data.config.json"
        
        # Test 1: Default configuration should have add_thickness = false
        logger.info("🧪 Testing default configuration...")
        loader = ManufacturingDataLoader(str(config_path))
        
        logger.info(f"✅ Configuration loaded successfully")
        logger.info(f"✅ add_thickness flag: {loader.add_thickness}")
        logger.info(f"✅ Expected: False, Actual: {loader.add_thickness}")
        
        if loader.add_thickness == False:
            logger.info("✅ Default thickness configuration is correct (disabled)")
        else:
            logger.warning("⚠️ Default thickness configuration unexpected (should be disabled)")
            
        # Test 2: Check that the flag can be read correctly
        with open(config_path, 'r') as f:
            config_data = json.load(f)
            
        config_add_thickness = config_data.get('add_thickness', None)
        logger.info(f"✅ add_thickness in config file: {config_add_thickness}")
        
        if config_add_thickness == False:
            logger.info("✅ Configuration file has correct add_thickness setting")
        else:
            logger.warning(f"⚠️ Configuration file add_thickness unexpected: {config_add_thickness}")
            
        # Test 3: Verify that modified config would work
        logger.info("🧪 Testing modified configuration...")
        temp_config = config_data.copy()
        temp_config['add_thickness'] = True
        
        temp_config_path = Path(__file__).parent / "temp_quick_config.json"
        with open(temp_config_path, 'w') as f:
            json.dump(temp_config, f, indent=2)
            
        try:
            loader_modified = ManufacturingDataLoader(str(temp_config_path))
            logger.info(f"✅ Modified configuration loaded successfully")
            logger.info(f"✅ Modified add_thickness flag: {loader_modified.add_thickness}")
            
            if loader_modified.add_thickness == True:
                logger.info("✅ Modified thickness configuration is correct (enabled)")
            else:
                logger.warning("⚠️ Modified thickness configuration unexpected (should be enabled)")
                
        finally:
            if temp_config_path.exists():
                temp_config_path.unlink()
                
        # Test 4: Quick baseline creation test
        logger.info("🧪 Testing baseline creation methods...")
        
        # Load just speed data for testing
        loader.load_all_data_sources()
        speed_df = loader.loaded_data['speed'].head(100).copy()  # Just 100 records for speed
        
        # Test clean baseline
        logger.info("Testing clean speed baseline...")
        clean_baseline = loader._create_speed_baseline(speed_df)
        logger.info(f"✅ Clean baseline: {len(clean_baseline)} records × {len(clean_baseline.columns)} columns")
        
        expected_clean_cols = ['timestamp', 'work_center', 'Speed']
        clean_cols_present = all(col in clean_baseline.columns for col in expected_clean_cols)
        
        if clean_cols_present:
            logger.info("✅ All expected clean columns present")
        else:
            logger.warning("⚠️ Missing expected clean columns")
            
        # Check no thickness columns
        thickness_cols = [col for col in clean_baseline.columns if 'sensor_' in col or 'thickness_' in col]
        if len(thickness_cols) == 0:
            logger.info("✅ Clean baseline contains no thickness columns")
        else:
            logger.warning(f"⚠️ Clean baseline unexpectedly contains thickness columns: {thickness_cols}")
            
        # Test thickness baseline (with temporary flag change)
        logger.info("Testing thickness baseline...")
        original_flag = loader.add_thickness
        loader.add_thickness = True  # Temporarily enable
        
        thickness_baseline = loader._create_thickness_baseline(speed_df)
        logger.info(f"✅ Thickness baseline: {len(thickness_baseline)} records × {len(thickness_baseline.columns)} columns")
        
        # Restore original flag
        loader.add_thickness = original_flag
        
        # Check thickness columns present
        thickness_cols_full = [col for col in thickness_baseline.columns if 'sensor_' in col or 'thickness_' in col]
        if len(thickness_cols_full) > 0:
            logger.info(f"✅ Thickness baseline contains {len(thickness_cols_full)} thickness columns")
        else:
            logger.warning("⚠️ Thickness baseline missing expected thickness columns")
            
        # Summary
        logger.info("=" * 50)
        logger.info("📊 QUICK TEST SUMMARY")
        logger.info("=" * 50)
        logger.info(f"✅ Default configuration: add_thickness = {config_add_thickness}")
        logger.info(f"✅ Loader reads flag correctly: {loader.add_thickness == config_add_thickness}")
        logger.info(f"✅ Clean baseline columns: {len(clean_baseline.columns)}")
        logger.info(f"✅ Thickness baseline columns: {len(thickness_baseline.columns)}")
        logger.info(f"✅ Column difference: {len(thickness_baseline.columns) - len(clean_baseline.columns)}")
        logger.info("=" * 50)
        logger.info("🎉 Quick thickness configuration test completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Quick test failed: {e}")
        import traceback
        logger.error(f"Full error traceback:\\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_thickness_flag_quick()
    if success:
        print("\\n✅ QUICK TEST PASSED: Thickness configuration working correctly")
    else:
        print("\\n❌ QUICK TEST FAILED: Issues with thickness configuration")
        sys.exit(1)