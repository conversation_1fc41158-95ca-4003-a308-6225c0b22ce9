#!/usr/bin/env python3
"""
Test script to validate add_thickness configuration flag functionality.

Tests both add_thickness=true and add_thickness=false to ensure:
1. Thickness columns are included/excluded correctly
2. Column counts are accurate
3. Export system works with both configurations
"""

import sys
import os
import json
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.loader import ManufacturingDataLoader
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_thickness_configuration():
    """Test both thickness configurations."""
    try:
        config_path = Path(__file__).parent / "data.config.json"
        
        # Test 1: Default configuration (add_thickness = false)
        logger.info("=" * 60)
        logger.info("🧪 TEST 1: add_thickness = false (clean data)")
        logger.info("=" * 60)
        
        loader_clean = ManufacturingDataLoader(str(config_path))
        logger.info(f"Thickness flag: {loader_clean.add_thickness}")
        
        # Load data and check what columns are present
        loader_clean.load_all_data_sources()
        
        # Test quick unified creation (first 1000 records for speed)
        speed_df = loader_clean.loaded_data['speed'].head(1000).copy()
        
        if loader_clean.add_thickness:
            unified_clean = loader_clean._create_thickness_baseline(speed_df)
        else:
            unified_clean = loader_clean._create_speed_baseline(speed_df)
            
        logger.info(f"Clean baseline: {len(unified_clean):,} records × {len(unified_clean.columns)} columns")
        
        # Check for thickness columns
        thickness_cols = [col for col in unified_clean.columns if 'sensor_' in col or 'thickness_' in col]
        logger.info(f"Thickness-related columns: {len(thickness_cols)}")
        if thickness_cols:
            logger.info(f"Found thickness columns: {thickness_cols[:5]}{'...' if len(thickness_cols) > 5 else ''}")
        else:
            logger.info("✅ No simulated thickness columns - clean data confirmed!")
            
        # Test 2: Modified configuration (add_thickness = true)
        logger.info("=" * 60)
        logger.info("🧪 TEST 2: add_thickness = true (with simulated thickness)")
        logger.info("=" * 60)
        
        # Temporarily modify config
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        original_add_thickness = config_data.get('add_thickness', False)
        config_data['add_thickness'] = True
        
        # Write temporary config
        temp_config_path = Path(__file__).parent / "temp_config.json"
        with open(temp_config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        try:
            # Test with thickness enabled
            loader_thickness = ManufacturingDataLoader(str(temp_config_path))
            logger.info(f"Thickness flag: {loader_thickness.add_thickness}")
            
            # Use same speed data
            if loader_thickness.add_thickness:
                unified_thickness = loader_thickness._create_thickness_baseline(speed_df)
            else:
                unified_thickness = loader_thickness._create_speed_baseline(speed_df)
                
            logger.info(f"Thickness baseline: {len(unified_thickness):,} records × {len(unified_thickness.columns)} columns")
            
            # Check for thickness columns
            thickness_cols_full = [col for col in unified_thickness.columns if 'sensor_' in col or 'thickness_' in col]
            logger.info(f"Thickness-related columns: {len(thickness_cols_full)}")
            if thickness_cols_full:
                logger.info(f"Found thickness columns: {thickness_cols_full[:10]}{'...' if len(thickness_cols_full) > 10 else ''}")
                logger.info("✅ Simulated thickness columns present as expected!")
            else:
                logger.warning("⚠️ Expected thickness columns but none found")
            
        finally:
            # Clean up temporary config
            if temp_config_path.exists():
                temp_config_path.unlink()
        
        # Comparison summary
        logger.info("=" * 60)
        logger.info("📊 CONFIGURATION COMPARISON SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Clean data (add_thickness=false): {len(unified_clean.columns)} columns")
        logger.info(f"With thickness (add_thickness=true): {len(unified_thickness.columns)} columns")
        logger.info(f"Difference: {len(unified_thickness.columns) - len(unified_clean.columns)} thickness-related columns")
        
        # Validate expected differences
        expected_thickness_cols = 15  # 10 sensors + 5 derived metrics
        actual_difference = len(unified_thickness.columns) - len(unified_clean.columns)
        
        if actual_difference == expected_thickness_cols:
            logger.info(f"✅ Column difference matches expected: {expected_thickness_cols} thickness columns")
        else:
            logger.warning(f"⚠️ Unexpected column difference: {actual_difference} vs expected {expected_thickness_cols}")
            
        # Test column presence
        expected_clean_cols = ['timestamp', 'work_center', 'Speed']
        missing_clean = [col for col in expected_clean_cols if col not in unified_clean.columns]
        if missing_clean:
            logger.warning(f"⚠️ Missing expected clean columns: {missing_clean}")
        else:
            logger.info("✅ All expected clean data columns present")
            
        expected_thickness_cols_list = ['sensor_01', 'sensor_10', 'thickness_avg', 'thickness_range']
        missing_thickness = [col for col in expected_thickness_cols_list if col not in unified_thickness.columns]
        if missing_thickness:
            logger.warning(f"⚠️ Missing expected thickness columns: {missing_thickness}")
        else:
            logger.info("✅ All expected thickness columns present")
        
        logger.info("=" * 60)
        logger.info("🎉 Thickness configuration test completed successfully!")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"Full error traceback:\\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_thickness_configuration()
    if success:
        print("\\n✅ TEST PASSED: add_thickness configuration working correctly")
    else:
        print("\\n❌ TEST FAILED: Issues with add_thickness configuration")
        sys.exit(1)