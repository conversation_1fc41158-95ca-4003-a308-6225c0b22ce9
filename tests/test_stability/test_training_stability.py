"""
Tests for end-to-end training stability.

Validates that the complete training pipeline produces stable, finite results
and meets manufacturing performance requirements.
"""

import pytest
import torch
import numpy as np
import pandas as pd
import tempfile
import shutil

from src.forecasting import (
    ManufacturingPatchTSTModel,
    ManufacturingForecastTrainer,
    ForecastConfig,
    PatchTSTTrainingConfig,
    TrainingStabilityConfig,
    StabilizedPatchTSTConfig,
    get_stabilized_training_config,
    get_stabilized_model_config
)
from src.forecasting.stability import ManufacturingStabilityValidator


class TestTrainingStabilityEnd2End:
    """Test complete training stability pipeline"""
    
    @pytest.fixture
    def manufacturing_test_data(self):
        """Create realistic manufacturing test data"""
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Create 300 data points for reasonable training/validation split
        n_samples = 300
        timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='1min')
        
        # Simulate realistic manufacturing data patterns
        thickness_base = 16.0
        speed_base = 50.0
        
        # Add realistic patterns: trend, seasonality, noise
        time_hours = np.arange(n_samples) / 60.0
        trend = 0.1 * np.sin(2 * np.pi * time_hours / 24)  # Daily cycle
        noise = np.random.normal(0, 0.3, n_samples)
        
        data = {
            'timestamp': timestamps,
            'thickness_01': thickness_base + trend + noise + np.random.normal(0, 0.1, n_samples),
            'thickness_02': thickness_base + trend + noise + np.random.normal(0, 0.1, n_samples),
            'thickness_03': thickness_base + trend + noise + np.random.normal(0, 0.1, n_samples),
            'thickness_avg': None,  # Will be calculated
            'speed': speed_base + 5 * trend + np.random.normal(0, 2, n_samples),
            'scrap_rate': np.maximum(0, np.random.exponential(1.5, n_samples) + 0.5 * np.abs(trend))
        }
        
        df = pd.DataFrame(data)
        
        # Calculate thickness average
        df['thickness_avg'] = df[['thickness_01', 'thickness_02', 'thickness_03']].mean(axis=1)
        
        # Add some realistic correlations
        df.loc[df['speed'] > 55, 'scrap_rate'] *= 1.2  # Higher speed -> more scrap
        df.loc[df['thickness_avg'] < 15.5, 'scrap_rate'] *= 1.3  # Thin sheets -> more scrap
        
        return df
    
    @pytest.fixture
    def temp_model_dir(self):
        """Create temporary directory for model storage"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    def test_stabilized_configuration_creation(self):
        """Test creation of stabilized configurations"""
        # Test stabilized training config
        training_config = get_stabilized_training_config()
        
        assert isinstance(training_config, PatchTSTTrainingConfig)
        assert training_config.stability_config is not None
        assert training_config.stability_config.gradient_clipping['enabled'] is True
        assert training_config.stability_config.gradient_clipping['max_norm'] == 1.0
        assert training_config.num_epochs <= 50  # Conservative for stability
        
        # Test stabilized model config
        model_config = get_stabilized_model_config()
        
        assert isinstance(model_config, StabilizedPatchTSTConfig)
        assert model_config.d_model <= 64  # Conservative for stability
        assert model_config.dropout >= 0.3  # Higher dropout for stability
        assert model_config.norm_type == 'unit_norm'
    
    def test_training_produces_finite_loss(self, manufacturing_test_data, temp_model_dir):
        """Test that training produces finite evaluation loss throughout"""
        # Setup configuration
        forecast_config = ForecastConfig()
        forecast_config.target_variables = ['thickness_avg']
        
        training_config = get_stabilized_training_config()
        training_config.num_epochs = 5  # Short for testing
        training_config.batch_size = 8
        training_config.model_save_path = temp_model_dir
        
        # Create model with stability enhancements
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train model - should not raise infinite loss errors
            trained_model = model.train(manufacturing_test_data, 'thickness_avg')
            
            # Verify training completed
            assert trained_model is not None
            
            # Check evaluation loss is finite
            if hasattr(trained_model, 'best_model_metrics'):
                metrics = trained_model.best_model_metrics
                eval_loss = metrics.get('eval_loss', float('inf'))
                
                assert np.isfinite(eval_loss), f"Evaluation loss is infinite: {eval_loss}"
                assert eval_loss > 0, f"Evaluation loss is non-positive: {eval_loss}"
                assert eval_loss < 1000, f"Evaluation loss too high: {eval_loss}"
            
            # Verify model can make predictions
            test_data = manufacturing_test_data.tail(50)
            forecast_result = model.forecast(test_data, 'thickness_avg', horizon=10)
            
            assert len(forecast_result.forecast_values) == 10
            assert all(np.isfinite(v) for v in forecast_result.forecast_values)
            
        except Exception as e:
            if any(keyword in str(e).lower() for keyword in ['infinite', 'nan', 'exploding']):
                pytest.fail(f"Training instability detected: {str(e)}")
            else:
                pytest.skip(f"Training failed for other reasons: {str(e)}")
    
    def test_prediction_variance_meaningful(self, manufacturing_test_data, temp_model_dir):
        """Test that predictions show meaningful variance (not constant ~16.001)"""
        forecast_config = ForecastConfig()
        training_config = get_stabilized_training_config()
        training_config.num_epochs = 5
        training_config.batch_size = 8
        training_config.model_save_path = temp_model_dir
        
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train model
            trained_model = model.train(manufacturing_test_data, 'thickness_avg')
            
            # Generate multiple forecasts from different starting points
            forecasts = []
            n_forecasts = min(10, len(manufacturing_test_data) - 100)
            
            for i in range(0, n_forecasts * 20, 20):  # Every 20 steps
                test_data = manufacturing_test_data.iloc[i:i+80]
                forecast_result = model.forecast(test_data, 'thickness_avg', horizon=15)
                forecasts.extend(forecast_result.forecast_values)
            
            if forecasts:
                # Analyze prediction variance
                prediction_std = np.std(forecasts)
                prediction_range = np.max(forecasts) - np.min(forecasts)
                unique_values = len(set(np.round(forecasts, 3)))
                
                # Predictions should show meaningful variance
                assert prediction_std > 0.1, f"Prediction variance too low: {prediction_std}"
                assert prediction_range > 0.2, f"Prediction range too narrow: {prediction_range}"
                assert unique_values > 5, f"Too few unique predictions: {unique_values}"
                
                # Predictions should be in reasonable manufacturing range
                assert all(10 < v < 25 for v in forecasts), "Predictions outside reasonable thickness range"
            
        except Exception as e:
            if any(keyword in str(e).lower() for keyword in ['infinite', 'nan', 'constant']):
                pytest.fail(f"Prediction variance issue: {str(e)}")
            else:
                pytest.skip(f"Test skipped due to: {str(e)}")
    
    def test_gradient_norms_remain_bounded(self, manufacturing_test_data, temp_model_dir):
        """Test that gradient norms remain bounded throughout training"""
        forecast_config = ForecastConfig()
        training_config = get_stabilized_training_config()
        training_config.num_epochs = 3
        training_config.batch_size = 8
        training_config.model_save_path = temp_model_dir
        
        # Ensure gradient clipping is enabled
        training_config.stability_config.gradient_clipping['enabled'] = True
        training_config.stability_config.gradient_clipping['max_norm'] = 1.0
        
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train with gradient monitoring
            trained_model = model.train(manufacturing_test_data, 'thickness_avg')
            
            # Check gradient statistics if available
            if hasattr(model, 'trainer') and hasattr(model.trainer, 'gradient_clipper'):
                stats = model.trainer.gradient_clipper.get_gradient_statistics()
                
                if stats['gradient_norms']:
                    max_norm = max(stats['gradient_norms'])
                    mean_norm = np.mean(stats['gradient_norms'])
                    
                    # Gradient norms should be bounded
                    assert max_norm <= 1.1, f"Maximum gradient norm exceeded bounds: {max_norm}"
                    assert mean_norm >= 0, f"Invalid mean gradient norm: {mean_norm}"
                    assert mean_norm <= 1.0, f"Mean gradient norm too high: {mean_norm}"
                    
                    # Check for gradient explosion patterns
                    gradient_increase = np.diff(stats['gradient_norms'])
                    large_jumps = sum(1 for jump in gradient_increase if jump > 0.5)
                    total_steps = len(stats['gradient_norms'])
                    
                    assert large_jumps / total_steps < 0.1, "Too many gradient norm jumps detected"
            
        except Exception as e:
            if any(keyword in str(e).lower() for keyword in ['gradient', 'exploding', 'unstable']):
                pytest.fail(f"Gradient stability issue: {str(e)}")
            else:
                pytest.skip(f"Test skipped due to: {str(e)}")
    
    def test_early_stopping_prevents_overfitting(self, manufacturing_test_data, temp_model_dir):
        """Test that early stopping works correctly"""
        forecast_config = ForecastConfig()
        training_config = get_stabilized_training_config()
        training_config.num_epochs = 20  # Enough to trigger early stopping
        training_config.batch_size = 8
        training_config.model_save_path = temp_model_dir
        
        # Configure early stopping
        training_config.stability_config.early_stopping['patience'] = 5
        training_config.stability_config.early_stopping['min_delta'] = 0.001
        
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train model
            trained_model = model.train(manufacturing_test_data, 'thickness_avg')
            
            # Check that training stopped early if appropriate
            if hasattr(trained_model, 'training_history'):
                history = trained_model.training_history
                epochs_completed = len(history.get('training_loss', []))
                
                # If early stopping worked, should complete fewer than max epochs
                if epochs_completed < training_config.num_epochs:
                    assert epochs_completed >= 5, "Training stopped too early"
                    assert epochs_completed <= 15, "Early stopping didn't engage"
            
        except Exception as e:
            pytest.skip(f"Test skipped due to: {str(e)}")
    
    def test_manufacturing_stability_validation(self, manufacturing_test_data, temp_model_dir):
        """Test manufacturing-specific stability validation"""
        forecast_config = ForecastConfig()
        training_config = get_stabilized_training_config()
        training_config.num_epochs = 3
        training_config.batch_size = 8
        training_config.model_save_path = temp_model_dir
        
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train model
            trained_model = model.train(manufacturing_test_data, 'thickness_avg')
            
            # Use manufacturing stability validator
            validator = ManufacturingStabilityValidator()
            
            # Generate validation data
            test_data = manufacturing_test_data.tail(100)
            forecast_result = model.forecast(test_data, 'thickness_avg', horizon=20)
            
            # Validate model stability
            validation_result = validator.validate_model_stability(
                model=trained_model,
                test_predictions=forecast_result.forecast_values,
                target_variable='thickness_avg'
            )
            
            # Check validation results
            assert validation_result.stability_score >= 0.5, f"Stability score too low: {validation_result.stability_score}"
            assert validation_result.prediction_variance > 0.01, f"Prediction variance too low: {validation_result.prediction_variance}"
            
            # Check manufacturing compliance
            compliance = validation_result.manufacturing_compliance
            assert compliance.get('finite_predictions', False), "Predictions not finite"
            assert compliance.get('reasonable_range', False), "Predictions outside reasonable range"
            
        except Exception as e:
            if 'stability' in str(e).lower():
                pytest.fail(f"Manufacturing stability validation failed: {str(e)}")
            else:
                pytest.skip(f"Test skipped due to: {str(e)}")
    
    def test_trainer_baseline_comparison(self, manufacturing_test_data, temp_model_dir):
        """Test that trainer performs baseline comparison correctly"""
        forecast_config = ForecastConfig()
        forecast_config.target_variables = ['thickness_avg']
        
        training_config = get_stabilized_training_config()
        training_config.num_epochs = 3
        training_config.batch_size = 8
        training_config.model_save_path = temp_model_dir
        
        # Create trainer
        trainer = ManufacturingForecastTrainer(forecast_config, training_config)
        
        try:
            # Train models
            trained_models = trainer.train_all_target_variables(manufacturing_test_data)
            
            assert 'thickness_avg' in trained_models
            
            # Check baseline comparison
            if hasattr(trainer, 'performance_comparison'):
                comparison = trainer.performance_comparison
                
                # Should have baseline methods
                baseline_methods = ['linear_regression', 'persistence', 'moving_average']
                patchtst_results = comparison.get('thickness_avg', {}).get('patchtst', {})
                
                if patchtst_results:
                    rmse = patchtst_results.get('rmse', float('inf'))
                    mae = patchtst_results.get('mae', float('inf'))
                    
                    # Check performance metrics are reasonable
                    assert np.isfinite(rmse), f"RMSE is not finite: {rmse}"
                    assert np.isfinite(mae), f"MAE is not finite: {mae}"
                    assert rmse > 0, f"RMSE should be positive: {rmse}"
                    assert mae > 0, f"MAE should be positive: {mae}"
            
        except Exception as e:
            if any(keyword in str(e).lower() for keyword in ['baseline', 'comparison', 'performance']):
                pytest.fail(f"Baseline comparison failed: {str(e)}")
            else:
                pytest.skip(f"Test skipped due to: {str(e)}")


class TestConfigurationStability:
    """Test configuration-related stability features"""
    
    def test_stability_config_validation(self):
        """Test TrainingStabilityConfig validation"""
        config = TrainingStabilityConfig()
        
        # Check default values are stable
        assert config.gradient_clipping['max_norm'] <= 1.0
        assert config.learning_rate_schedule['initial_lr'] <= 0.001
        assert config.early_stopping['patience'] >= 10
        
        # Test custom configuration
        custom_config = TrainingStabilityConfig()
        custom_config.gradient_clipping['max_norm'] = 0.5
        
        assert custom_config.gradient_clipping['max_norm'] == 0.5
    
    def test_stabilized_model_config_defaults(self):
        """Test StabilizedPatchTSTConfig has conservative defaults"""
        config = get_stabilized_model_config()
        
        # Check conservative defaults for stability
        assert config.d_model <= 64, "d_model too large for stability"
        assert config.num_attention_heads <= 8, "Too many attention heads"
        assert config.num_hidden_layers <= 3, "Too many layers"
        assert config.dropout >= 0.2, "Dropout too low"
        assert config.norm_type == 'unit_norm', "Should use UnitNorm"
    
    def test_training_config_stability_integration(self):
        """Test training config properly integrates stability features"""
        config = get_stabilized_training_config()
        
        # Check stability configuration is included
        assert config.stability_config is not None
        assert isinstance(config.stability_config, TrainingStabilityConfig)
        
        # Check conservative training parameters
        assert config.learning_rate <= 0.001, "Learning rate too high"
        assert config.batch_size <= 32, "Batch size too large"
        assert config.num_epochs <= 50, "Too many epochs"


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__])