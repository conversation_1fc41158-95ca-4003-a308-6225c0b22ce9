"""
Tests for normalization layers and stability enhancements.

Validates UnitNorm layer functionality and other normalization components
for time series transformer stability.
"""

import pytest
import torch
import numpy as np

from src.forecasting.stability.normalization import (
    UnitNorm,
    RobustScaler,
    TimeSeriesNormalizer
)


class TestUnitNorm:
    """Test UnitNorm layer functionality"""
    
    def test_unitnorm_initialization(self):
        """Test UnitNorm layer initialization"""
        d_model = 64
        unit_norm = UnitNorm(d_model=d_model)
        
        assert unit_norm.d_model == d_model
        assert unit_norm.eps == 1e-5
        assert unit_norm.scale.shape == (d_model,)
        assert torch.allclose(unit_norm.scale, torch.ones(d_model))
    
    def test_unitnorm_custom_eps(self):
        """Test UnitNorm with custom epsilon value"""
        eps = 1e-8
        unit_norm = UnitNorm(d_model=32, eps=eps)
        assert unit_norm.eps == eps
    
    def test_unitnorm_forward_pass(self):
        """Test UnitNorm forward pass functionality"""
        d_model = 16
        batch_size = 2
        seq_length = 10
        
        unit_norm = UnitNorm(d_model=d_model)
        
        # Create test input
        x = torch.randn(batch_size, seq_length, d_model)
        
        # Forward pass
        output = unit_norm(x)
        
        # Check output shape
        assert output.shape == x.shape
        
        # Check that output is not NaN or infinite
        assert not torch.isnan(output).any()
        assert not torch.isinf(output).any()
    
    def test_unitnorm_stabilizes_different_scales(self):
        """Test UnitNorm produces similar scales for different input magnitudes"""
        d_model = 32
        unit_norm = UnitNorm(d_model=d_model)
        
        # Test with different input scales
        small_input = torch.randn(1, 50, d_model) * 0.01  # Very small
        normal_input = torch.randn(1, 50, d_model) * 1.0  # Normal scale
        large_input = torch.randn(1, 50, d_model) * 100.0  # Very large
        
        small_output = unit_norm(small_input)
        normal_output = unit_norm(normal_input)
        large_output = unit_norm(large_input)
        
        # Calculate L2 norms along feature dimension
        small_norm = torch.norm(small_output, dim=-1).mean()
        normal_norm = torch.norm(normal_output, dim=-1).mean()
        large_norm = torch.norm(large_output, dim=-1).mean()
        
        # Outputs should have similar scales despite different inputs
        max_diff = max(abs(small_norm - normal_norm), 
                      abs(normal_norm - large_norm),
                      abs(small_norm - large_norm))
        
        assert max_diff < 2.0, f"Output norms too different: small={small_norm}, normal={normal_norm}, large={large_norm}"
    
    def test_unitnorm_preserves_gradients(self):
        """Test UnitNorm preserves gradient flow"""
        d_model = 8
        unit_norm = UnitNorm(d_model=d_model)
        
        # Create input with gradient tracking
        x = torch.randn(1, 5, d_model, requires_grad=True)
        
        # Forward pass
        output = unit_norm(x)
        
        # Create dummy loss and backward pass
        loss = output.sum()
        loss.backward()
        
        # Check gradients exist and are finite
        assert x.grad is not None
        assert not torch.isnan(x.grad).any()
        assert not torch.isinf(x.grad).any()
        
        # Check scale parameter gradients
        assert unit_norm.scale.grad is not None
        assert not torch.isnan(unit_norm.scale.grad).any()
    
    def test_unitnorm_zero_norm_handling(self):
        """Test UnitNorm handles zero-norm vectors gracefully"""
        d_model = 4
        unit_norm = UnitNorm(d_model=d_model, eps=1e-5)
        
        # Create input with zero vectors
        x = torch.zeros(1, 3, d_model)
        
        # Forward pass should not crash
        output = unit_norm(x)
        
        assert output.shape == x.shape
        assert not torch.isnan(output).any()
        assert not torch.isinf(output).any()
    
    def test_unitnorm_batch_consistency(self):
        """Test UnitNorm produces consistent results across batch dimensions"""
        d_model = 16
        seq_length = 20
        unit_norm = UnitNorm(d_model=d_model)
        
        # Create identical sequences in batch
        single_seq = torch.randn(1, seq_length, d_model)
        batch_input = single_seq.repeat(3, 1, 1)  # 3 identical sequences
        
        # Forward pass
        output = unit_norm(batch_input)
        
        # All sequences in batch should produce identical outputs
        for i in range(1, 3):
            assert torch.allclose(output[0], output[i], atol=1e-6), "Batch processing inconsistent"


class TestRobustScaler:
    """Test RobustScaler functionality"""
    
    def test_robust_scaler_initialization(self):
        """Test RobustScaler initialization"""
        scaler = RobustScaler()
        
        assert scaler.quantile_range == (25.0, 75.0)
        assert scaler.with_centering is True
        assert scaler.with_scaling is True
        assert not scaler.is_fitted
    
    def test_robust_scaler_fit_transform(self):
        """Test RobustScaler fit and transform"""
        scaler = RobustScaler()
        
        # Create sample data with outliers
        np.random.seed(42)
        data = np.random.normal(10, 2, (100, 3))
        data[0, :] = 100  # Add outliers
        data[1, :] = -100
        
        X = torch.tensor(data, dtype=torch.float32)
        
        # Fit and transform
        X_transformed = scaler.fit_transform(X)
        
        assert scaler.is_fitted
        assert X_transformed.shape == X.shape
        assert scaler.center_ is not None
        assert scaler.scale_ is not None
        
        # Check that medians are approximately zero after centering
        medians = torch.median(X_transformed, dim=0)[0]
        assert torch.allclose(medians, torch.zeros_like(medians), atol=0.1)
    
    def test_robust_scaler_outlier_resistance(self):
        """Test RobustScaler is resistant to outliers"""
        scaler = RobustScaler()
        
        # Create data with extreme outliers
        normal_data = torch.randn(100, 2) * 2 + 5
        outlier_data = torch.tensor([[1000, -1000], [-500, 500]], dtype=torch.float32)
        data_with_outliers = torch.cat([normal_data, outlier_data], dim=0)
        
        # Fit scaler
        scaler.fit(data_with_outliers)
        
        # Transform and check that outliers don't dominate scaling
        transformed = scaler.transform(data_with_outliers)
        
        # Most of the normal data should be reasonably scaled (within reasonable range)
        normal_transformed = transformed[:100]  # First 100 are normal data
        assert torch.abs(normal_transformed).quantile(0.95) < 10, "Normal data scaled too extremely"
    
    def test_robust_scaler_inverse_transform(self):
        """Test RobustScaler inverse transform"""
        scaler = RobustScaler()
        
        # Original data
        X = torch.randn(50, 4) * 3 + 2
        
        # Fit and transform
        X_transformed = scaler.fit_transform(X)
        
        # Inverse transform
        X_reconstructed = scaler.inverse_transform(X_transformed)
        
        # Should be close to original
        assert torch.allclose(X, X_reconstructed, atol=1e-5)


class TestTimeSeriesNormalizer:
    """Test TimeSeriesNormalizer functionality"""
    
    def test_time_series_normalizer_initialization(self):
        """Test TimeSeriesNormalizer initialization"""
        normalizer = TimeSeriesNormalizer()
        
        assert normalizer.method == 'z_score'
        assert normalizer.axis == -1
        assert normalizer.eps == 1e-8
        assert not normalizer.is_fitted
    
    def test_time_series_normalizer_z_score(self):
        """Test z-score normalization"""
        normalizer = TimeSeriesNormalizer(method='z_score')
        
        # Create time series data (batch, time, features)
        X = torch.randn(2, 100, 5) * 10 + 50
        
        # Fit and transform
        X_normalized = normalizer.fit_transform(X)
        
        assert normalizer.is_fitted
        assert X_normalized.shape == X.shape
        
        # Check approximate z-score properties (mean ≈ 0, std ≈ 1)
        for i in range(X.shape[0]):  # For each sequence
            for j in range(X.shape[2]):  # For each feature
                feature_data = X_normalized[i, :, j]
                mean_val = feature_data.mean()
                std_val = feature_data.std()
                
                assert abs(mean_val) < 0.1, f"Mean not close to 0: {mean_val}"
                assert abs(std_val - 1.0) < 0.1, f"Std not close to 1: {std_val}"
    
    def test_time_series_normalizer_min_max(self):
        """Test min-max normalization"""
        normalizer = TimeSeriesNormalizer(method='min_max')
        
        # Create data with known range
        X = torch.tensor([[[1, 2, 3, 4, 5]]], dtype=torch.float32).transpose(1, 2)  # (1, 5, 1)
        
        # Fit and transform
        X_normalized = normalizer.fit_transform(X)
        
        # Should be scaled to [0, 1]
        assert torch.allclose(X_normalized.min(), torch.tensor(0.0), atol=1e-6)
        assert torch.allclose(X_normalized.max(), torch.tensor(1.0), atol=1e-6)
    
    def test_time_series_normalizer_robust(self):
        """Test robust normalization"""
        normalizer = TimeSeriesNormalizer(method='robust')
        
        # Create data with outliers
        X = torch.randn(1, 100, 3)
        X[0, 0, :] = 100  # Add outlier
        
        # Fit and transform
        X_normalized = normalizer.fit_transform(X)
        
        assert normalizer.is_fitted
        assert X_normalized.shape == X.shape
        
        # Robust normalization should handle outliers better than z-score
        # Check that the outlier doesn't dominate the scaling
        non_outlier_data = X_normalized[0, 1:, :]
        assert torch.abs(non_outlier_data).quantile(0.95) < 5, "Robust scaling failed to handle outliers"
    
    def test_time_series_normalizer_feature_axis(self):
        """Test normalization along feature axis"""
        normalizer = TimeSeriesNormalizer(method='z_score', axis=-1)  # Along features
        
        # Create data (batch=1, time=50, features=4)
        X = torch.randn(1, 50, 4) * [1, 2, 3, 4] + torch.tensor([10, 20, 30, 40])
        
        # Fit and transform
        X_normalized = normalizer.fit_transform(X)
        
        # Each time step should be normalized across features
        for t in range(X.shape[1]):
            time_step = X_normalized[0, t, :]
            mean_val = time_step.mean()
            std_val = time_step.std()
            
            assert abs(mean_val) < 0.1, f"Mean at time {t} not close to 0: {mean_val}"
            if len(time_step) > 1:  # Only check std if more than one feature
                assert abs(std_val - 1.0) < 0.1, f"Std at time {t} not close to 1: {std_val}"
    
    def test_time_series_normalizer_time_axis(self):
        """Test normalization along time axis"""
        normalizer = TimeSeriesNormalizer(method='z_score', axis=1)  # Along time
        
        # Create data (batch=1, time=100, features=3)
        X = torch.randn(1, 100, 3) * 5 + 25
        
        # Fit and transform
        X_normalized = normalizer.fit_transform(X)
        
        # Each feature should be normalized across time
        for f in range(X.shape[2]):
            feature_series = X_normalized[0, :, f]
            mean_val = feature_series.mean()
            std_val = feature_series.std()
            
            assert abs(mean_val) < 0.1, f"Mean of feature {f} not close to 0: {mean_val}"
            assert abs(std_val - 1.0) < 0.1, f"Std of feature {f} not close to 1: {std_val}"
    
    def test_time_series_normalizer_inverse_transform(self):
        """Test inverse transformation"""
        normalizer = TimeSeriesNormalizer(method='z_score')
        
        # Original data
        X = torch.randn(2, 50, 3) * 10 + 100
        
        # Transform and inverse transform
        X_normalized = normalizer.fit_transform(X)
        X_reconstructed = normalizer.inverse_transform(X_normalized)
        
        # Should recover original data
        assert torch.allclose(X, X_reconstructed, atol=1e-4)
    
    def test_time_series_normalizer_manufacturing_data(self):
        """Test with manufacturing-like data patterns"""
        normalizer = TimeSeriesNormalizer(method='z_score')
        
        # Simulate manufacturing sensor data
        batch_size, time_steps, n_sensors = 1, 200, 10
        
        # Create realistic manufacturing data patterns
        base_values = torch.tensor([16.0, 15.8, 16.2, 15.9, 16.1, 15.7, 16.3, 15.8, 16.0, 15.9])
        noise = torch.randn(batch_size, time_steps, n_sensors) * 0.5
        trend = torch.linspace(0, 2, time_steps).unsqueeze(0).unsqueeze(-1)
        
        X = base_values + noise + trend * 0.1
        
        # Normalize
        X_normalized = normalizer.fit_transform(X)
        
        # Validate normalization quality
        for sensor in range(n_sensors):
            sensor_data = X_normalized[0, :, sensor]
            assert abs(sensor_data.mean()) < 0.1
            assert abs(sensor_data.std() - 1.0) < 0.2  # Allow some tolerance for realistic data


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__])