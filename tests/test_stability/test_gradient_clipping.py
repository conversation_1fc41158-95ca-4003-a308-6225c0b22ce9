"""
Tests for gradient clipping and stabilization functionality.

Validates that gradient clipping prevents training instability and gradient explosions
in PatchTST models for manufacturing forecasting.
"""

import pytest
import torch
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch
import os

from src.forecasting.stability.gradient_utils import Grad<PERSON><PERSON>lipper
from src.forecasting.stability.training_utils import StabilizedTrainer
from src.forecasting import (
    ManufacturingPatchTSTModel,
    ForecastConfig,
    PatchTSTTrainingConfig,
    TrainingStabilityConfig
)


class TestGradientClipper:
    """Test gradient clipping functionality"""
    
    def test_gradient_clipping_initialization(self):
        """Test GradientClipper initialization with various parameters"""
        # Test default initialization
        clipper = GradientClipper()
        assert clipper.max_norm == 1.0
        assert clipper.norm_type == 2.0
        assert clipper.error_if_nonfinite is False
        
        # Test custom initialization
        clipper = GradientClipper(max_norm=0.5, norm_type=1.0, error_if_nonfinite=True)
        assert clipper.max_norm == 0.5
        assert clipper.norm_type == 1.0
        assert clipper.error_if_nonfinite is True
    
    def test_gradient_clipping_prevents_explosions(self):
        """Test that gradient clipping prevents exploding gradients"""
        clipper = GradientClipper(max_norm=1.0)
        
        # Create a simple model for testing
        model = torch.nn.Linear(10, 1)
        
        # Simulate exploding gradients by setting large gradient values
        with torch.no_grad():
            model.weight.grad = torch.randn_like(model.weight) * 100  # Large gradients
            model.bias.grad = torch.randn_like(model.bias) * 100
        
        # Check gradients before clipping
        grad_norm_before = torch.norm(torch.cat([model.weight.grad.flatten(), model.bias.grad.flatten()]))
        assert grad_norm_before > 1.0, "Gradients should be large before clipping"
        
        # Apply gradient clipping
        clipped_norm = clipper.clip_gradients(model.parameters())
        
        # Check gradients after clipping
        grad_norm_after = torch.norm(torch.cat([model.weight.grad.flatten(), model.bias.grad.flatten()]))
        assert grad_norm_after <= 1.0, f"Gradients should be clipped to <= 1.0, got {grad_norm_after}"
        assert clipped_norm == grad_norm_after, "Returned norm should match actual gradient norm"
    
    def test_gradient_monitoring_and_logging(self):
        """Test gradient monitoring functionality"""
        clipper = GradientClipper(max_norm=1.0)
        
        # Create model
        model = torch.nn.Linear(5, 1)
        
        # Test multiple gradient clipping steps
        gradient_norms = []
        for i in range(5):
            # Simulate different gradient magnitudes
            with torch.no_grad():
                scale = (i + 1) * 0.5  # Varying gradient scales
                model.weight.grad = torch.randn_like(model.weight) * scale
                model.bias.grad = torch.randn_like(model.bias) * scale
            
            norm = clipper.clip_gradients(model.parameters())
            gradient_norms.append(norm)
        
        # Check that gradient norms are recorded
        assert len(gradient_norms) == 5
        assert all(norm <= 1.0 for norm in gradient_norms), "All gradient norms should be <= max_norm"
        
        # Test gradient statistics
        stats = clipper.get_gradient_statistics()
        assert 'mean_norm' in stats
        assert 'max_norm' in stats
        assert 'min_norm' in stats
        assert stats['num_clips'] >= 0  # Some gradients might have been clipped
    
    def test_gradient_visualization(self):
        """Test gradient norm visualization functionality"""
        clipper = GradientClipper(max_norm=1.0)
        
        # Simulate training with varying gradient norms
        gradient_norms = [2.5, 1.8, 0.5, 3.0, 0.8, 1.2, 0.3]
        
        # Add gradient norms to clipper history
        for norm in gradient_norms:
            clipper.gradient_norms.append(min(norm, clipper.max_norm))
        
        # Test visualization creation (should not raise errors)
        try:
            clipper.plot_gradient_norms(save_path="test_gradient_plot.png")
            # Clean up if file was created
            if os.path.exists("test_gradient_plot.png"):
                os.remove("test_gradient_plot.png")
        except Exception as e:
            pytest.fail(f"Gradient visualization failed: {str(e)}")


class TestStabilizedTrainer:
    """Test stabilized trainer functionality"""
    
    def test_stabilized_trainer_initialization(self):
        """Test StabilizedTrainer initialization"""
        stability_config = TrainingStabilityConfig()
        
        # Mock training arguments and model
        mock_args = Mock()
        mock_model = Mock()
        mock_train_dataset = Mock()
        
        trainer = StabilizedTrainer(
            model=mock_model,
            args=mock_args,
            train_dataset=mock_train_dataset,
            stability_config=stability_config
        )
        
        assert trainer.stability_config == stability_config
        assert trainer.gradient_clipper is not None
        assert trainer.gradient_clipper.max_norm == stability_config.gradient_clipping['max_norm']
    
    def test_training_step_with_gradient_clipping(self):
        """Test training step includes gradient clipping"""
        stability_config = TrainingStabilityConfig()
        
        # Create a simple model for testing
        model = torch.nn.Linear(10, 1)
        
        # Mock training arguments
        mock_args = Mock()
        mock_args.gradient_accumulation_steps = 1
        mock_args.max_grad_norm = 1.0
        
        # Mock dataset
        mock_dataset = Mock()
        mock_dataset.__len__ = Mock(return_value=100)
        
        trainer = StabilizedTrainer(
            model=model,
            args=mock_args,
            train_dataset=mock_dataset,
            stability_config=stability_config
        )
        
        # Mock inputs with loss
        inputs = {
            'input_ids': torch.randn(2, 10),
            'labels': torch.randn(2, 1)
        }
        
        # Mock model outputs
        mock_outputs = Mock()
        mock_outputs.loss = torch.tensor(1.0, requires_grad=True)
        
        with patch.object(model, 'forward', return_value=mock_outputs):
            # Simulate training step
            try:
                # This would be called by the training loop
                loss = trainer.compute_loss(model, inputs)
                assert isinstance(loss, torch.Tensor)
                assert loss.requires_grad
            except Exception:
                # Training step might fail due to mocking, but gradient clipping should work
                pass
    
    def test_gradient_accumulation_with_clipping(self):
        """Test gradient accumulation works with clipping"""
        stability_config = TrainingStabilityConfig()
        stability_config.gradient_clipping['max_norm'] = 0.5
        
        # Create model
        model = torch.nn.Linear(5, 1)
        
        # Mock training arguments with gradient accumulation
        mock_args = Mock()
        mock_args.gradient_accumulation_steps = 2
        mock_args.max_grad_norm = 0.5
        
        trainer = StabilizedTrainer(
            model=model,
            args=mock_args,
            train_dataset=Mock(),
            stability_config=stability_config
        )
        
        # Verify gradient clipper is configured correctly
        assert trainer.gradient_clipper.max_norm == 0.5
        assert trainer.stability_config.gradient_clipping['max_norm'] == 0.5


class TestTrainingStabilityIntegration:
    """Test end-to-end training stability"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Create sample manufacturing data for testing"""
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Create synthetic time series data
        n_samples = 100
        data = {
            'timestamp': pd.date_range('2024-01-01', periods=n_samples, freq='1min'),
            'thickness_01': np.random.normal(16, 1, n_samples),
            'thickness_02': np.random.normal(16, 1, n_samples),
            'thickness_03': np.random.normal(16, 1, n_samples),
            'speed': np.random.normal(50, 5, n_samples),
            'scrap_rate': np.random.exponential(2, n_samples)
        }
        return pd.DataFrame(data)
    
    def test_finite_evaluation_loss(self, sample_manufacturing_data):
        """Test that model produces finite evaluation loss"""
        # Use stability-enhanced configuration
        forecast_config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        # Add stability configuration
        training_config.stability_config = TrainingStabilityConfig()
        training_config.num_epochs = 3  # Short training for testing
        training_config.batch_size = 8  # Small batch for testing
        
        # Create model with stability enhancements
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train model (should not raise infinite loss)
            trained_model = model.train(sample_manufacturing_data, 'thickness_01')
            
            # Validate evaluation loss is finite
            if hasattr(trained_model, 'best_model_metrics'):
                eval_loss = trained_model.best_model_metrics.get('eval_loss', float('inf'))
                assert np.isfinite(eval_loss), f"Evaluation loss is infinite: {eval_loss}"
                assert eval_loss > 0, f"Evaluation loss is non-positive: {eval_loss}"
            
        except Exception as e:
            # Training might fail due to small dataset, but check if it's stability-related
            if "infinite" in str(e).lower() or "nan" in str(e).lower():
                pytest.fail(f"Training failed due to stability issues: {str(e)}")
            else:
                pytest.skip(f"Training failed for other reasons: {str(e)}")
    
    def test_prediction_variance_nonzero(self, sample_manufacturing_data):
        """Test that model produces varied predictions (not constant)"""
        forecast_config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        # Use stability configuration
        training_config.stability_config = TrainingStabilityConfig()
        training_config.num_epochs = 3
        training_config.batch_size = 8
        
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train model
            trained_model = model.train(sample_manufacturing_data, 'thickness_01')
            
            # Generate multiple forecasts
            forecasts = []
            for i in range(min(5, len(sample_manufacturing_data) - 50)):
                forecast_result = model.forecast(
                    sample_manufacturing_data.iloc[i:i+40], 
                    'thickness_01', 
                    horizon=10
                )
                forecasts.extend(forecast_result.forecast_values)
            
            if forecasts:
                # Validate predictions show variance
                prediction_std = np.std(forecasts)
                assert prediction_std > 0.01, f"Prediction variance too low: {prediction_std}"
                
                # Check for non-constant predictions
                unique_predictions = len(set(np.round(forecasts, 2)))
                assert unique_predictions > 2, f"Too few unique predictions: {unique_predictions}"
                
        except Exception as e:
            if "infinite" in str(e).lower() or "nan" in str(e).lower():
                pytest.fail(f"Model stability issue: {str(e)}")
            else:
                pytest.skip(f"Test skipped due to: {str(e)}")
    
    def test_gradient_norms_bounded(self, sample_manufacturing_data):
        """Test that gradient norms remain bounded during training"""
        forecast_config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        # Configure stability with gradient monitoring
        stability_config = TrainingStabilityConfig()
        stability_config.gradient_clipping['max_norm'] = 1.0
        training_config.stability_config = stability_config
        training_config.num_epochs = 2
        training_config.batch_size = 8
        
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        try:
            # Train with gradient monitoring
            trained_model = model.train(sample_manufacturing_data, 'thickness_01')
            
            # Check if gradient statistics are available
            if hasattr(model, 'trainer') and hasattr(model.trainer, 'gradient_clipper'):
                stats = model.trainer.gradient_clipper.get_gradient_statistics()
                
                # Validate gradient norms are bounded
                if stats['max_norm'] is not None:
                    assert stats['max_norm'] <= 1.0, f"Maximum gradient norm exceeded: {stats['max_norm']}"
                
                if stats['mean_norm'] is not None:
                    assert stats['mean_norm'] >= 0, f"Invalid mean gradient norm: {stats['mean_norm']}"
                    
        except Exception as e:
            if "gradient" in str(e).lower() or "exploding" in str(e).lower():
                pytest.fail(f"Gradient stability issue: {str(e)}")
            else:
                pytest.skip(f"Test skipped due to: {str(e)}")


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__])