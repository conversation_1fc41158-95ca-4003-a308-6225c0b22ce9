#!/usr/bin/env python3
"""
Test script for rate limiting functionality

Tests the rate limiting, retry logic, and fallback behavior
of the Anthropic API wrapper system.
"""

import asyncio
import time
import logging
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.rate_limit_manager import RateLimitManager, api_retry
from src.utils.api_wrapper import RateLimitedAgent, get_rate_limited_agent
from src.config.api_config import get_api_config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_rate_limit_manager():
    """Test the basic rate limit manager functionality"""
    print("=" * 50)
    print("Testing Rate Limit Manager")
    print("=" * 50)
    
    # Create a rate limit manager with very low limits for testing
    rate_manager = RateLimitManager(
        requests_per_minute=5,  # Very low for testing
        tokens_per_minute=1000,
        enable_adaptive_throttling=False  # Disable for predictable testing
    )
    
    print(f"Initial status: {rate_manager.get_current_status()}")
    
    # Test token acquisition
    start_time = time.time()
    for i in range(3):
        print(f"\nAcquiring tokens for request {i+1}...")
        async with rate_manager.acquire(estimated_tokens=100):
            print(f"  Request {i+1} acquired tokens")
            await asyncio.sleep(0.1)  # Simulate work
        
        rate_manager.record_request_result(success=True)
        print(f"  Request {i+1} completed successfully")
    
    elapsed = time.time() - start_time
    print(f"\nCompleted 3 requests in {elapsed:.2f} seconds")
    print(f"Final status: {rate_manager.get_current_status()}")
    
    return True

async def test_retry_decorator():
    """Test the retry decorator functionality"""
    print("\n" + "=" * 50)
    print("Testing Retry Decorator")
    print("=" * 50)
    
    rate_manager = RateLimitManager(
        requests_per_minute=10,
        tokens_per_minute=2000
    )
    
    call_count = 0
    
    @api_retry(rate_manager, max_retries=3, base_delay=0.5)
    async def mock_api_call_that_fails():
        nonlocal call_count
        call_count += 1
        print(f"  Mock API call attempt #{call_count}")
        
        if call_count < 3:
            # Simulate rate limit error
            raise Exception("RateLimitError: Too many requests")
        else:
            return {"success": True, "attempt": call_count}
    
    try:
        print("Testing retry behavior with eventual success...")
        result = await mock_api_call_that_fails()
        print(f"Success after {call_count} attempts: {result}")
    except Exception as e:
        print(f"Failed after retries: {e}")
    
    return True

async def test_config_system():
    """Test the configuration system"""
    print("\n" + "=" * 50)
    print("Testing Configuration System")
    print("=" * 50)
    
    # Test getting configuration
    config = get_api_config()
    print(f"Environment: {config.environment}")
    print(f"Requests per minute: {config.requests_per_minute}")
    print(f"Tokens per minute: {config.tokens_per_minute}")
    print(f"Enable fallbacks: {config.enable_fallbacks}")
    print(f"Enable adaptive throttling: {config.enable_adaptive_throttling}")
    
    # Test configuration summary
    from src.config.api_config import get_config_summary, validate_api_credentials
    
    summary = get_config_summary()
    print(f"\nConfiguration summary: {summary}")
    
    validation = validate_api_credentials()
    print(f"Credential validation: {validation}")
    
    return True

async def test_fallback_behavior():
    """Test fallback response behavior"""
    print("\n" + "=" * 50)
    print("Testing Fallback Behavior")
    print("=" * 50)
    
    # This would normally test with a real agent, but for now we'll just test the structure
    try:
        from src.agents.correlation_agent import CorrelationAnalysis, CorrelationInsight
        
        # Create a mock fallback response
        fallback = CorrelationAnalysis(
            dataset_summary={
                "shape": [100, 5],
                "columns": ["var1", "var2", "var3", "var4", "var5"],
                "fallback_response": True
            },
            significant_correlations=[],
            correlation_matrix=None,
            insights=[
                CorrelationInsight(
                    insight_type="error",
                    description="Analysis temporarily unavailable due to API limitations",
                    supporting_evidence={"fallback": True},
                    confidence_level="low",
                    actionable_recommendation="Try again in a few minutes"
                )
            ],
            recommendations=["Check API rate limits", "Verify connectivity"],
            data_quality_score=0.0,
            analysis_metadata={
                "fallback_response": True,
                "analysis_type": "fallback"
            }
        )
        
        print("Fallback response structure created successfully")
        print(f"Is fallback: {fallback.analysis_metadata.get('fallback_response', False)}")
        print(f"Insights: {len(fallback.insights)}")
        
        return True
        
    except ImportError as e:
        print(f"Could not test fallback structure (import error): {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Rate Limiting System Tests")
    print("=" * 50)
    
    tests = [
        ("Rate Limit Manager", test_rate_limit_manager),
        ("Retry Decorator", test_retry_decorator),
        ("Configuration System", test_config_system),
        ("Fallback Behavior", test_fallback_behavior)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 Running {test_name} test...")
            success = await test_func()
            results[test_name] = "✅ PASSED" if success else "❌ FAILED"
        except Exception as e:
            print(f"💥 Test {test_name} crashed: {e}")
            results[test_name] = f"💥 CRASHED: {e}"
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    passed = sum(1 for r in results.values() if "PASSED" in r)
    total = len(results)
    
    print(f"\n🏆 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Rate limiting system is ready.")
    else:
        print("⚠️ Some tests failed. Check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())