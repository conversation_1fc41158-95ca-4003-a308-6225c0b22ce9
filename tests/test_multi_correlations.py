"""
Unit Tests for Multi-Method Manufacturing Correlation Analysis

Tests for multi-method correlation calculations (<PERSON>, <PERSON>, <PERSON>),
method convergence analysis, data distribution assessment, and intelligent
method selection for manufacturing data.
"""

import pytest
import pandas as pd
import numpy as np

# Import the modules to test
from src.data.multi_correlations import (
    MultiMethodCorrelation<PERSON><PERSON><PERSON><PERSON>,
    MultiMethodCorrelationResult,
    create_sample_multi_method_analysis
)


class TestMultiMethodCorrelationResult:
    """Test suite for MultiMethodCorrelationResult data model"""
    
    def test_multi_method_correlation_result_creation(self):
        """Test creation of MultiMethodCorrelationResult with all three methods"""
        result = MultiMethodCorrelationResult(
            variable_1="speed",
            variable_2="thickness",
            pearson_correlation=0.75,
            spearman_correlation=0.73,
            kendall_correlation=0.68,
            pearson_p_value=0.001,
            spearman_p_value=0.002,
            kendall_p_value=0.003,
            sample_size=100,
            method_convergence_score=0.85,
            recommended_method="pearson",
            data_distribution_assessment={"normality": {"x_variable": {"is_normal": True}}},
            pearson_confidence_interval=(0.65, 0.85),
            spearman_confidence_interval=(0.63, 0.83),
            kendall_confidence_interval=(0.58, 0.78),
            interpretation={
                "pearson": "Strong positive linear relationship",
                "spearman": "Strong positive monotonic relationship",
                "kendall": "Moderate positive ordinal relationship"
            }
        )
        
        assert result.variable_1 == "speed"
        assert result.variable_2 == "thickness"
        assert result.pearson_correlation == 0.75
        assert result.spearman_correlation == 0.73
        assert result.kendall_correlation == 0.68
        assert result.method_convergence_score == 0.85
        assert result.recommended_method == "pearson"
        assert result.sample_size == 100
        assert len(result.interpretation) == 3
    
    def test_multi_method_result_validation(self):
        """Test validation of MultiMethodCorrelationResult fields"""
        # Test invalid correlation values
        with pytest.raises(ValueError):
            MultiMethodCorrelationResult(
                variable_1="speed",
                variable_2="thickness",
                pearson_correlation=1.5,  # Invalid: > 1
                spearman_correlation=0.5,
                kendall_correlation=0.4,
                pearson_p_value=0.01,
                spearman_p_value=0.02,
                kendall_p_value=0.03,
                sample_size=100,
                method_convergence_score=0.8,
                recommended_method="pearson",
                data_distribution_assessment={},
                pearson_confidence_interval=(0.4, 0.6),
                spearman_confidence_interval=(0.3, 0.7),
                kendall_confidence_interval=(0.2, 0.6),
                interpretation={}
            )


class TestMultiMethodCorrelationAnalyzer:
    """Test suite for MultiMethodCorrelationAnalyzer class"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Create sample manufacturing data for testing"""
        np.random.seed(42)
        n_samples = 200
        
        # Create correlated variables with different characteristics
        speed = np.random.normal(150, 10, n_samples)
        
        # Linear relationship (good for Pearson)
        temperature = 80 + 0.2 * speed + np.random.normal(0, 2, n_samples)
        
        # Non-linear but monotonic (good for Spearman)
        pressure = 50 + 0.1 * np.sqrt(np.abs(speed - 100)) + np.random.normal(0, 3, n_samples)
        
        # With outliers (good for Kendall)
        thickness = 12 + 0.01 * speed + np.random.normal(0, 0.2, n_samples)
        # Add some outliers
        outlier_indices = np.random.choice(n_samples, size=10, replace=False)
        thickness[outlier_indices] += np.random.normal(0, 2, 10)
        
        # Quality score with mixed relationships
        quality = 100 - 0.1 * np.abs(speed - 150) - 0.05 * np.abs(temperature - 80) + np.random.normal(0, 1, n_samples)
        
        return pd.DataFrame({
            'speed': speed,
            'temperature': temperature,
            'pressure': pressure,
            'thickness': thickness,
            'quality_score': quality,
            'timestamp': pd.date_range('2024-01-01', periods=n_samples, freq='1min')
        })
    
    @pytest.fixture
    def analyzer(self):
        """Create MultiMethodCorrelationAnalyzer instance"""
        return MultiMethodCorrelationAnalyzer(significance_level=0.05)
    
    def test_analyzer_initialization(self, analyzer):
        """Test MultiMethodCorrelationAnalyzer initialization"""
        assert analyzer.significance_level == 0.05
        assert hasattr(analyzer, 'method_weights')
        assert 'pearson' in analyzer.method_weights
        assert 'spearman' in analyzer.method_weights
        assert 'kendall' in analyzer.method_weights
        assert hasattr(analyzer, 'normality_threshold')
        assert hasattr(analyzer, 'outlier_threshold')
    
    def test_calculate_multi_method_correlations_success(self, analyzer, sample_manufacturing_data):
        """Test successful multi-method correlation calculation"""
        results = analyzer.calculate_multi_method_correlations(sample_manufacturing_data)
        
        # Check that results were generated
        assert len(results) > 0
        assert isinstance(results, dict)
        
        # Check first result structure
        first_result = next(iter(results.values()))
        assert isinstance(first_result, MultiMethodCorrelationResult)
        assert hasattr(first_result, 'pearson_correlation')
        assert hasattr(first_result, 'spearman_correlation')
        assert hasattr(first_result, 'kendall_correlation')
        assert hasattr(first_result, 'method_convergence_score')
        assert hasattr(first_result, 'recommended_method')
        
        # Check correlation values are within valid range
        assert -1 <= first_result.pearson_correlation <= 1
        assert -1 <= first_result.spearman_correlation <= 1
        assert -1 <= first_result.kendall_correlation <= 1
        
        # Check convergence score is within [0, 1]
        assert 0 <= first_result.method_convergence_score <= 1
        
        # Check recommended method is valid
        assert first_result.recommended_method in ['pearson', 'spearman', 'kendall']
    
    def test_calculate_multi_method_correlations_specific_variables(self, analyzer, sample_manufacturing_data):
        """Test multi-method correlation calculation with specific variables"""
        variables = ['speed', 'temperature', 'thickness']
        results = analyzer.calculate_multi_method_correlations(
            sample_manufacturing_data, variables=variables
        )
        
        assert len(results) > 0
        
        # Check that only specified variables are included
        for result in results.values():
            assert result.variable_1 in variables
            assert result.variable_2 in variables
    
    def test_calculate_multi_method_correlations_empty_data(self, analyzer):
        """Test multi-method correlation calculation with empty data"""
        empty_df = pd.DataFrame()
        results = analyzer.calculate_multi_method_correlations(empty_df)
        
        assert len(results) == 0
    
    def test_calculate_multi_method_correlations_insufficient_data(self, analyzer):
        """Test multi-method correlation calculation with insufficient data"""
        # Create very small dataset
        small_df = pd.DataFrame({
            'var1': [1, 2, 3],
            'var2': [4, 5, 6]
        })
        
        results = analyzer.calculate_multi_method_correlations(small_df, min_periods=30)
        
        # Should return empty results due to insufficient data
        assert len(results) == 0
    
    def test_analyze_method_convergence_success(self, analyzer, sample_manufacturing_data):
        """Test successful method convergence analysis"""
        # First calculate multi-method correlations
        correlation_results = analyzer.calculate_multi_method_correlations(sample_manufacturing_data)
        
        # Then analyze convergence
        convergence_analysis = analyzer.analyze_method_convergence(correlation_results)
        
        assert isinstance(convergence_analysis, dict)
        assert 'overall_convergence_score' in convergence_analysis
        assert 'method_stability' in convergence_analysis
        assert 'cross_method_correlations' in convergence_analysis
        assert 'convergence_distribution' in convergence_analysis
        
        # Check convergence score is valid
        assert 0 <= convergence_analysis['overall_convergence_score'] <= 1
        
        # Check method stability scores
        stability = convergence_analysis['method_stability']
        assert 'pearson_stability' in stability
        assert 'spearman_stability' in stability
        assert 'kendall_stability' in stability
        
        for method_stability in stability.values():
            assert 0 <= method_stability <= 1
    
    def test_analyze_method_convergence_empty_results(self, analyzer):
        """Test method convergence analysis with empty correlation results"""
        convergence_analysis = analyzer.analyze_method_convergence({})
        
        assert 'error' in convergence_analysis
    
    def test_assess_data_distribution(self, analyzer, sample_manufacturing_data):
        """Test data distribution assessment"""
        x = sample_manufacturing_data['speed']
        y = sample_manufacturing_data['temperature']
        
        assessment = analyzer._assess_data_distribution(x, y)
        
        assert isinstance(assessment, dict)
        assert 'normality' in assessment
        assert 'outliers' in assessment
        assert 'linearity' in assessment
        assert 'monotonicity' in assessment
        
        # Check normality test structure
        if 'error' not in assessment['normality']:
            normality = assessment['normality']
            assert 'x_variable' in normality
            assert 'y_variable' in normality
            assert 'is_normal' in normality['x_variable']
            assert 'is_normal' in normality['y_variable']
        
        # Check outlier detection structure
        if 'error' not in assessment['outliers']:
            outliers = assessment['outliers']
            assert 'x_variable' in outliers
            assert 'y_variable' in outliers
            assert 'outlier_count' in outliers['x_variable']
            assert 'outlier_percentage' in outliers['x_variable']
    
    def test_recommend_correlation_method(self, analyzer, sample_manufacturing_data):
        """Test correlation method recommendation"""
        x = sample_manufacturing_data['speed']
        y = sample_manufacturing_data['temperature']  # Should have linear relationship
        
        # Assess distribution first
        assessment = analyzer._assess_data_distribution(x, y)
        
        # Get recommendation
        recommended_method = analyzer._recommend_correlation_method(x, y, assessment)
        
        assert recommended_method in ['pearson', 'spearman', 'kendall']
        
        # For linear relationship with normal data, should often recommend Pearson
        # (though this depends on the specific data characteristics)
    
    def test_detect_outliers_iqr(self, analyzer):
        """Test IQR outlier detection"""
        # Create data with known outliers
        data = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 100])  # 100 is clear outlier
        
        outlier_indices = analyzer._detect_outliers_iqr(data)
        
        assert len(outlier_indices) > 0
        assert 9 in outlier_indices  # Index of the outlier value 100
    
    def test_calculate_robustness_metrics_success(self, analyzer, sample_manufacturing_data):
        """Test successful robustness metrics calculation"""
        # First calculate multi-method correlations
        correlation_results = analyzer.calculate_multi_method_correlations(sample_manufacturing_data)
        
        # Calculate robustness metrics with fewer bootstrap samples for testing
        robustness_metrics = analyzer.calculate_robustness_metrics(
            sample_manufacturing_data, correlation_results, bootstrap_samples=10
        )
        
        assert isinstance(robustness_metrics, dict)
        assert len(robustness_metrics) > 0
        
        # Check structure of robustness metrics
        for pair_metrics in robustness_metrics.values():
            for method in ['pearson', 'spearman', 'kendall']:
                if method in pair_metrics:
                    method_metrics = pair_metrics[method]
                    assert 'bootstrap_mean' in method_metrics
                    assert 'bootstrap_std' in method_metrics
                    assert 'bootstrap_ci_lower' in method_metrics
                    assert 'bootstrap_ci_upper' in method_metrics
                    assert 'stability_score' in method_metrics
                    
                    # Check stability score is valid
                    assert 0 <= method_metrics['stability_score'] <= 1
    
    def test_calculate_robustness_metrics_insufficient_data(self, analyzer):
        """Test robustness metrics calculation with insufficient data"""
        # Create small dataset
        small_df = pd.DataFrame({
            'var1': [1, 2, 3, 4, 5],
            'var2': [2, 3, 4, 5, 6]
        })
        
        # Create minimal correlation results
        correlation_results = {}
        
        robustness_metrics = analyzer.calculate_robustness_metrics(
            small_df, correlation_results, bootstrap_samples=5
        )
        
        # Should return empty or minimal results
        assert isinstance(robustness_metrics, dict)
    
    def test_generate_interpretation(self, analyzer):
        """Test correlation interpretation generation"""
        # Test strong positive correlation
        interpretation = analyzer._generate_interpretation(0.85, 0.001, 'pearson')
        assert 'strong' in interpretation.lower()
        assert 'positive' in interpretation.lower()
        assert 'significant' in interpretation.lower()
        
        # Test weak negative correlation
        interpretation = analyzer._generate_interpretation(-0.2, 0.1, 'spearman')
        assert 'weak' in interpretation.lower()
        assert 'negative' in interpretation.lower()
        
        # Test non-significant correlation
        interpretation = analyzer._generate_interpretation(0.1, 0.5, 'kendall')
        assert 'not significant' in interpretation.lower()


class TestMultiMethodIntegration:
    """Integration tests for multi-method correlation analysis"""
    
    def test_create_sample_multi_method_analysis(self):
        """Test the sample data creation and analysis function"""
        results = create_sample_multi_method_analysis()
        
        assert isinstance(results, dict)
        assert 'sample_data' in results
        assert 'correlation_results' in results
        assert 'convergence_analysis' in results
        assert 'robustness_metrics' in results
        assert 'analyzer' in results
        
        # Check sample data
        sample_data = results['sample_data']
        assert isinstance(sample_data, pd.DataFrame)
        assert len(sample_data) == 1000
        assert 'speed' in sample_data.columns
        assert 'temperature' in sample_data.columns
        assert 'thickness' in sample_data.columns
        
        # Check correlation results
        correlation_results = results['correlation_results']
        assert len(correlation_results) > 0
        
        # Check convergence analysis
        convergence_analysis = results['convergence_analysis']
        assert 'overall_convergence_score' in convergence_analysis
        
        # Check robustness metrics
        robustness_metrics = results['robustness_metrics']
        assert isinstance(robustness_metrics, dict)
    
    def test_multi_method_with_manufacturing_patterns(self):
        """Test multi-method analysis with manufacturing-specific patterns"""
        # Create manufacturing data with known patterns
        np.random.seed(42)
        n_samples = 300
        
        # Production speed affects everything
        speed = np.random.normal(150, 10, n_samples)
        
        # Temperature has linear relationship with speed
        temperature = 80 + 0.15 * speed + np.random.normal(0, 2, n_samples)
        
        # Thickness with lag and non-linear relationship
        thickness = 12 + 0.005 * speed**1.2 + np.random.normal(0, 0.3, n_samples)
        
        # Quality inversely related with threshold effects
        quality = np.where(
            speed > 160, 90 - 0.1 * (speed - 160),  # Degradation at high speed
            np.where(
                speed < 140, 85 - 0.2 * (140 - speed),  # Degradation at low speed
                95 + np.random.normal(0, 1, n_samples)  # Optimal range
            )
        )
        
        manufacturing_data = pd.DataFrame({
            'speed': speed,
            'temperature': temperature, 
            'thickness': thickness,
            'quality_score': quality
        })
        
        # Analyze with multi-method approach
        analyzer = MultiMethodCorrelationAnalyzer()
        results = analyzer.calculate_multi_method_correlations(manufacturing_data)
        
        assert len(results) > 0
        
        # Find speed-temperature correlation (should be linear, recommend Pearson)
        speed_temp_key = None
        for key, result in results.items():
            if (result.variable_1 == 'speed' and result.variable_2 == 'temperature') or \
               (result.variable_1 == 'temperature' and result.variable_2 == 'speed'):
                speed_temp_key = key
                break
        
        assert speed_temp_key is not None
        speed_temp_result = results[speed_temp_key]
        
        # Should have strong positive correlation
        assert speed_temp_result.pearson_correlation > 0.5
        assert speed_temp_result.pearson_p_value < 0.05
        
        # Methods should converge for linear relationship
        assert speed_temp_result.method_convergence_score > 0.5


class TestMultiMethodEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_correlation_with_constant_variable(self):
        """Test correlation calculation with constant variable"""
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Create data with one constant variable
        df = pd.DataFrame({
            'constant': [5.0] * 100,  # Constant variable
            'variable': np.random.normal(0, 1, 100)
        })
        
        results = analyzer.calculate_multi_method_correlations(df)
        
        # Should handle constant variable gracefully (likely return no results or NaN)
        # The behavior depends on scipy's handling of constant variables
        assert isinstance(results, dict)
    
    def test_correlation_with_identical_variables(self):
        """Test correlation calculation with identical variables"""
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Create data with identical variables
        data = np.random.normal(0, 1, 100)
        df = pd.DataFrame({
            'var1': data,
            'var2': data.copy()  # Identical data
        })
        
        results = analyzer.calculate_multi_method_correlations(df)
        
        if len(results) > 0:
            result = next(iter(results.values()))
            # Should have perfect correlation (r = 1.0)
            assert abs(result.pearson_correlation - 1.0) < 0.001
            assert abs(result.spearman_correlation - 1.0) < 0.001
            assert abs(result.kendall_correlation - 1.0) < 0.001
    
    def test_correlation_with_missing_values(self):
        """Test correlation calculation with missing values"""
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Create data with missing values
        df = pd.DataFrame({
            'var1': [1, 2, np.nan, 4, 5, 6, np.nan, 8, 9, 10],
            'var2': [2, np.nan, 4, 5, 6, np.nan, 8, 9, 10, 11]
        })
        
        results = analyzer.calculate_multi_method_correlations(df)
        
        # Should handle missing values by using pairwise complete observations
        assert isinstance(results, dict)
        
        if len(results) > 0:
            result = next(iter(results.values()))
            # Sample size should reflect only complete pairs
            assert result.sample_size < 10
    
    def test_convergence_analysis_with_single_pair(self):
        """Test convergence analysis with only one variable pair"""
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Create single correlation result
        single_result = MultiMethodCorrelationResult(
            variable_1="var1",
            variable_2="var2",
            pearson_correlation=0.5,
            spearman_correlation=0.52,
            kendall_correlation=0.48,
            pearson_p_value=0.01,
            spearman_p_value=0.01,
            kendall_p_value=0.02,
            sample_size=100,
            method_convergence_score=0.8,
            recommended_method="pearson",
            data_distribution_assessment={},
            pearson_confidence_interval=(0.3, 0.7),
            spearman_confidence_interval=(0.32, 0.72),
            kendall_confidence_interval=(0.28, 0.68),
            interpretation={}
        )
        
        correlation_results = {"var1_var2": single_result}
        convergence_analysis = analyzer.analyze_method_convergence(correlation_results)
        
        assert isinstance(convergence_analysis, dict)
        assert 'overall_convergence_score' in convergence_analysis
        # Should handle single pair gracefully
    
    def test_recommend_method_with_edge_case_data(self):
        """Test method recommendation with edge case data distributions"""
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Create data with extreme characteristics
        x = pd.Series([1, 1, 1, 1, 1, 2, 3, 4, 5, 100])  # Heavy outlier
        y = pd.Series([1, 1, 1, 1, 1, 2, 3, 4, 5, 10])
        
        assessment = analyzer._assess_data_distribution(x, y)
        recommendation = analyzer._recommend_correlation_method(x, y, assessment)
        
        # Should recommend robust method due to outliers
        assert recommendation in ['spearman', 'kendall']


class TestMultiMethodPerformance:
    """Performance and scalability tests"""
    
    def test_performance_with_large_dataset(self):
        """Test performance with larger dataset"""
        # Create larger dataset
        np.random.seed(42)
        n_samples = 2000
        n_variables = 8
        
        # Generate correlated variables
        base_data = np.random.multivariate_normal(
            mean=np.zeros(n_variables),
            cov=np.eye(n_variables) + 0.3 * np.ones((n_variables, n_variables)),
            size=n_samples
        )
        
        df = pd.DataFrame(
            base_data,
            columns=[f'var_{i}' for i in range(n_variables)]
        )
        
        analyzer = MultiMethodCorrelationAnalyzer()
        
        # Time the analysis (should complete in reasonable time)
        import time
        start_time = time.time()
        results = analyzer.calculate_multi_method_correlations(df)
        end_time = time.time()
        
        # Should complete within reasonable time (less than 30 seconds)
        assert (end_time - start_time) < 30
        
        # Should generate results for all variable pairs
        expected_pairs = n_variables * (n_variables - 1) // 2
        assert len(results) <= expected_pairs  # May be less due to filtering
    
    def test_memory_usage_with_bootstrap(self):
        """Test memory usage during bootstrap analysis"""
        # Create medium-sized dataset
        np.random.seed(42)
        n_samples = 500
        
        df = pd.DataFrame({
            'var1': np.random.normal(0, 1, n_samples),
            'var2': np.random.normal(0, 1, n_samples),
            'var3': np.random.normal(0, 1, n_samples)
        })
        
        analyzer = MultiMethodCorrelationAnalyzer()
        correlation_results = analyzer.calculate_multi_method_correlations(df)
        
        # Bootstrap analysis with moderate sample size
        robustness_metrics = analyzer.calculate_robustness_metrics(
            df, correlation_results, bootstrap_samples=20
        )
        
        # Should complete without memory issues
        assert isinstance(robustness_metrics, dict)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])