#!/usr/bin/env python3
"""
Quick Stability Test

Fast validation of the critical stability fixes without full data loading.
Tests the core tensor shape and model initialization fixes.
"""

import sys
import logging
import torch
import numpy as np
from pathlib import Path
import pandas as pd

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.forecasting.config import load_config_from_file
from src.forecasting.patchtst_model import ManufacturingDataCollator, ManufacturingPatchTSTModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_data_collator_fixes():
    """Test that ManufacturingDataCollator produces correct tensor shapes"""
    logger.info("🔍 Testing ManufacturingDataCollator fixes...")
    
    try:
        collator = ManufacturingDataCollator(target_channel_idx=0)
        
        # Create test batch
        batch_size = 2
        seq_len = 80
        num_features = 24
        pred_len = 15
        
        test_batch = [
            {
                'past_values': torch.randn(seq_len, num_features),
                'future_values': torch.randn(pred_len)  # 1D target for univariate
            },
            {
                'past_values': torch.randn(seq_len, num_features),
                'future_values': torch.randn(pred_len)  # 1D target for univariate
            }
        ]
        
        # Test collator
        collated_batch = collator(test_batch)
        
        past_shape = collated_batch['past_values'].shape
        future_shape = collated_batch['future_values'].shape
        
        logger.info(f"📊 Collated shapes: past_values={past_shape}, future_values={future_shape}")
        
        # Validate shapes
        expected_past = (batch_size, seq_len, num_features)
        expected_future = (batch_size, pred_len)  # Should be 2D for univariate
        
        if past_shape == expected_past and future_shape == expected_future:
            logger.info("✅ ManufacturingDataCollator produces correct tensor shapes")
            return True
        else:
            logger.error(f"❌ Shape mismatch: expected past={expected_past}, future={expected_future}")
            logger.error(f"❌ Got: past={past_shape}, future={future_shape}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Data collator test failed: {str(e)}")
        return False

def test_model_initialization():
    """Test that the model initializes correctly without wrapper conflicts"""
    logger.info("🤖 Testing model initialization without wrapper conflicts...")
    
    try:
        # Load ultra-stable configuration
        forecast_config, training_config = load_config_from_file("config/ultra_stable_config.json")
        
        # Create test model
        model = ManufacturingPatchTSTModel(forecast_config, training_config)
        
        # Test model initialization
        num_channels = len(forecast_config.input_variables)
        logger.info(f"🔄 Initializing model with {num_channels} input channels...")
        
        model.initialize_model(num_channels)
        
        # Check model configuration
        if hasattr(model.model, 'config'):
            config = model.model.config
            logger.info(f"📊 Model config: num_input_channels={config.num_input_channels}")
            logger.info(f"📊 Model config: num_targets={getattr(config, 'num_targets', 'not_set')}")
            
            # Validate configuration
            if hasattr(config, 'num_targets') and config.num_targets == 1:
                logger.info("✅ Model correctly configured for univariate prediction")
                return True
            else:
                logger.error("❌ Model not configured for univariate prediction")
                return False
        else:
            logger.error("❌ Model missing configuration")
            return False
        
    except Exception as e:
        logger.error(f"❌ Model initialization test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_tensor_validation():
    """Test the tensor validation logic in StabilizedTrainer"""
    logger.info("🛡️ Testing tensor validation logic...")
    
    try:
        from src.forecasting.stability.training_utils import StabilizedTrainer
        
        # Create a mock trainer to test validation methods
        class MockTrainer(StabilizedTrainer):
            def __init__(self):
                # Initialize without calling super() to avoid trainer setup
                pass
        
        trainer = MockTrainer()
        
        # Test valid tensors
        valid_inputs = {
            'past_values': torch.randn(2, 80, 24),    # [batch, seq_len, features]
            'future_values': torch.randn(2, 15)      # [batch, pred_len] - correct for univariate
        }
        
        logger.info("🔄 Testing valid tensor shapes...")
        trainer._validate_input_tensors(valid_inputs)
        logger.info("✅ Valid tensor validation passed")
        
        # Test invalid tensors (should raise error)
        invalid_inputs = {
            'past_values': torch.randn(2, 80, 24),
            'future_values': torch.randn(2, 15, 1)   # 3D - wrong for univariate
        }
        
        logger.info("🔄 Testing invalid tensor shapes...")
        try:
            trainer._validate_input_tensors(invalid_inputs)
            logger.error("❌ Invalid tensor validation should have failed")
            return False
        except ValueError as e:
            logger.info("✅ Invalid tensor validation correctly failed")
            return True
        
    except Exception as e:
        logger.error(f"❌ Tensor validation test failed: {str(e)}")
        return False

def test_loss_clamping():
    """Test loss validation and clamping"""
    logger.info("🔒 Testing loss validation and clamping...")
    
    try:
        from src.forecasting.stability.training_utils import StabilizedTrainer
        
        class MockTrainer(StabilizedTrainer):
            def __init__(self):
                pass
        
        trainer = MockTrainer()
        
        # Test normal loss
        normal_loss = torch.tensor(0.5, requires_grad=True)
        clamped_loss = trainer._validate_and_clamp_loss(normal_loss)
        logger.info(f"📊 Normal loss: {normal_loss.item():.6f} -> {clamped_loss.item():.6f}")
        
        # Test infinite loss
        inf_loss = torch.tensor(float('inf'), requires_grad=True)
        clamped_inf_loss = trainer._validate_and_clamp_loss(inf_loss)
        logger.info(f"📊 Infinite loss: {inf_loss.item()} -> {clamped_inf_loss.item():.6f}")
        
        if torch.isfinite(clamped_inf_loss) and clamped_inf_loss.item() <= 1000.0:
            logger.info("✅ Loss clamping works correctly")
            return True
        else:
            logger.error("❌ Loss clamping failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ Loss clamping test failed: {str(e)}")
        return False

def main():
    """Run quick stability tests"""
    logger.info("🧪 Quick Stability Test Suite")
    logger.info("=" * 50)
    
    tests = [
        ("Data Collator Fixes", test_data_collator_fixes),
        ("Model Initialization", test_model_initialization),
        ("Tensor Validation", test_tensor_validation),
        ("Loss Clamping", test_loss_clamping),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"💥 {test_name}: ERROR - {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("🎯 Quick Test Results")
    logger.info("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name:.<30} {status}")
    
    logger.info("-" * 50)
    logger.info(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All stability fixes validated!")
        logger.info("🚀 Ready for full training")
        return True
    else:
        logger.error("⚠️  Some fixes need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)