#!/usr/bin/env python3
"""
Quick verification that the training fix is working
"""

import subprocess
import sys
import time
import signal

def main():
    print("🔧 Verifying PatchTST training fix...")
    
    # Start the training process
    cmd = [sys.executable, "train.py", "thickness_thickness_avg"]
    proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                           text=True, bufsize=1, universal_newlines=True)
    
    # Monitor output for 60 seconds
    start_time = time.time()
    success_indicators = []
    error_indicators = []
    
    try:
        while proc.poll() is None and (time.time() - start_time) < 60:
            line = proc.stdout.readline()
            if line:
                print(line.strip())
                
                # Check for success indicators
                if "Starting model training..." in line:
                    success_indicators.append("Model training started")
                if "✅" in line:
                    success_indicators.append(line.strip())
                
                # Check for error indicators
                if any(error in line.lower() for error in ["error", "mismatch", "failed"]):
                    if "Using a target size" not in line:  # Skip the specific shape warning we're fixing
                        error_indicators.append(line.strip())
                        
        # Terminate the process
        if proc.poll() is None:
            proc.terminate()
            proc.wait(timeout=5)
            
    except KeyboardInterrupt:
        proc.terminate()
    
    # Report results
    print("\n" + "="*50)
    print("🔍 VERIFICATION RESULTS:")
    print("="*50)
    
    if success_indicators:
        print("✅ SUCCESS INDICATORS:")
        for indicator in success_indicators:
            print(f"  - {indicator}")
    
    if error_indicators:
        print("❌ ERROR INDICATORS:")
        for indicator in error_indicators:
            print(f"  - {indicator}")
    else:
        print("✅ No critical errors detected!")
    
    # Final assessment
    if success_indicators and not error_indicators:
        print("\n🎉 VERDICT: Training fix is working correctly!")
        print("📝 You can now run full training with: python train.py")
        return 0
    elif success_indicators:
        print("\n⚠️ VERDICT: Training started but some errors detected")
        return 1
    else:
        print("\n❌ VERDICT: Training failed to start properly")
        return 1

if __name__ == "__main__":
    exit(main())