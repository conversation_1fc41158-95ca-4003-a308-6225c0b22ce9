#!/usr/bin/env python3
"""
Quick test of the fixed training - just a few steps to verify no errors
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.forecasting.trainer import ManufacturingForecastTrainer
from src.forecasting.config import load_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__main__)

def main():
    """Quick test of fixed training"""
    
    logger.info("🧪 Quick Test of Fixed PatchTST Training")
    logger.info("="*50)
    
    try:
        # Load configuration
        config_path = "config/forecasting_config.json"
        forecast_config, training_config = load_config(config_path)
        
        # Override with minimal settings for quick test
        training_config.max_epochs = 1
        training_config.batch_size = 8
        training_config.eval_steps = 10
        training_config.save_steps = 20
        
        logger.info("Creating enhanced trainer...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,
            use_transfer_learning=False  # Skip transfer learning for speed
        )
        
        logger.info("🚀 Starting quick test training...")
        results = trainer.train_models(
            target_variables=["thickness_thickness_avg"],
            max_steps=20  # Just 20 steps to test
        )
        
        logger.info("✅ Training completed successfully!")
        logger.info(f"Results: {results}")
        
        # Check if model was saved
        model_dir = Path("models/patchtst_manufacturing_thickness_thickness_avg")
        if model_dir.exists():
            logger.info(f"✅ Model saved successfully to {model_dir}")
        else:
            logger.warning(f"⚠️ Model directory not found: {model_dir}")
        
        logger.info("🎉 All tests passed! The training fix is working correctly.")
        return 0
        
    except Exception as e:
        logger.error(f"❌ Error during training test: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())