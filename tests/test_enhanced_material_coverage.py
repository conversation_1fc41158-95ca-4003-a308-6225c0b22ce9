#!/usr/bin/env python3
"""
Test Enhanced Material Coverage Improvements

This script tests the new enhanced material coverage features implemented in Phase 1 and Phase 2:
- Enhanced temporal material propagation with extended windows
- Advanced product code extraction and material taxonomy
- Production campaign detection
- Multi-level fallback strategies

Expected improvements:
- Material Coverage: 21.2% → 45-60%
- Stack Coverage: 23.7% → 35-45%
- Enhanced temporal correlation with confidence scoring
"""

import sys
from pathlib import Path
import pandas as pd
import logging

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.data.loader import ManufacturingDataLoader

# Set up logging to see detailed output
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_material_coverage():
    """Test the enhanced material coverage improvements."""
    
    print("🔬 Testing Enhanced Material Coverage Improvements")
    print("=" * 60)
    
    # Initialize loader with current dataset
    print("\n📊 Initializing ManufacturingDataLoader...")
    loader = ManufacturingDataLoader("test-data")
    
    print("\n🔄 Loading all manufacturing data with enhancements...")
    datasets = loader.load_all_manufacturing_data()
    
    print(f"\n✅ Loaded {len(datasets)} datasets:")
    for name, df in datasets.items():
        print(f"  • {name}: {len(df):,} records")
        
        # Check for enhanced material columns
        enhanced_cols = [col for col in df.columns if any(x in col.lower() for x in ['material', 'confidence', 'source', 'taxonomy', 'extracted'])]
        if enhanced_cols:
            print(f"    Enhanced columns: {enhanced_cols}")
            
            # Report material coverage for this dataset
            if 'Enhanced_Material' in df.columns:
                coverage = df['Enhanced_Material'].notna().sum()
                print(f"    Enhanced material coverage: {coverage:,}/{len(df):,} ({coverage/len(df)*100:.1f}%)")
                
                # Show confidence distribution if available
                if 'Material_Confidence' in df.columns:
                    conf_counts = df['Material_Confidence'].value_counts()
                    print(f"    Confidence distribution: {dict(conf_counts)}")
                
                # Show source distribution if available
                if 'Material_Source' in df.columns:
                    source_counts = df['Material_Source'].value_counts()
                    print(f"    Source distribution: {dict(source_counts)}")
    
    print("\n🔄 Creating unified dataset...")
    unified_data = loader.create_unified_dataset()
    
    print(f"\n📈 Unified Dataset Analysis:")
    print(f"  Total records: {len(unified_data):,}")
    print(f"  Columns: {len(unified_data.columns)}")
    
    # Check overall material coverage
    material_cols = [col for col in unified_data.columns if 'material' in col.lower()]
    print(f"  Material-related columns: {material_cols}")
    
    if 'material' in unified_data.columns:
        total_material_coverage = unified_data['material'].notna().sum()
        print(f"  Overall material coverage: {total_material_coverage:,}/{len(unified_data):,} ({total_material_coverage/len(unified_data)*100:.1f}%)")
    
    # Check stack coverage
    stack_cols = [col for col in unified_data.columns if 'stack' in col.lower()]
    if stack_cols:
        print(f"  Stack-related columns: {stack_cols}")
        for col in stack_cols:
            if col in unified_data.columns:
                stack_coverage = unified_data[col].notna().sum()
                print(f"    {col} coverage: {stack_coverage:,}/{len(unified_data):,} ({stack_coverage/len(unified_data)*100:.1f}%)")
    
    # Generate detailed coverage report
    print("\n📊 Detailed Coverage Analysis:")
    print("-" * 40)
    
    # Analyze by data source
    source_analysis = {}
    for col in unified_data.columns:
        if '_' in col and col.split('_')[0] in ['fm', 'sm', 'speed', 'thickness', 'stop']:
            source = col.split('_')[0]
            if source not in source_analysis:
                source_analysis[source] = {'total_cols': 0, 'material_cols': 0}
            source_analysis[source]['total_cols'] += 1
            if 'material' in col.lower():
                source_analysis[source]['material_cols'] += 1
    
    for source, stats in source_analysis.items():
        print(f"  {source.upper()} source: {stats['total_cols']} columns, {stats['material_cols']} material-related")
    
    print("\n🎯 Enhancement Success Metrics:")
    print("-" * 40)
    
    # Calculate improvement metrics (compared to baseline 21.2% material, 23.7% stack)
    baseline_material = 0.212
    baseline_stack = 0.237
    
    if 'material' in unified_data.columns:
        current_material = unified_data['material'].notna().sum() / len(unified_data)
        improvement_material = ((current_material - baseline_material) / baseline_material) * 100
        print(f"  📈 Material Coverage: {baseline_material*100:.1f}% → {current_material*100:.1f}% ({improvement_material:+.1f}% improvement)")
    
    # Try to calculate stack coverage improvement
    stack_coverage_found = False
    for col in stack_cols:
        if col in unified_data.columns:
            current_stack = unified_data[col].notna().sum() / len(unified_data)
            improvement_stack = ((current_stack - baseline_stack) / baseline_stack) * 100
            print(f"  📈 Stack Coverage ({col}): {baseline_stack*100:.1f}% → {current_stack*100:.1f}% ({improvement_stack:+.1f}% improvement)")
            stack_coverage_found = True
            break
    
    if not stack_coverage_found:
        print("  ⚠️  Stack coverage metrics not available in unified dataset")
    
    # Test specific enhancements
    print("\n🔍 Testing Specific Enhancement Features:")
    print("-" * 40)
    
    # Test enhanced temporal propagation
    speed_dataset = datasets.get('speed')
    if speed_dataset is not None and 'Material_Confidence' in speed_dataset.columns:
        conf_counts = speed_dataset['Material_Confidence'].value_counts()
        print(f"  ✅ Enhanced Temporal Propagation (Speed data):")
        for conf, count in conf_counts.items():
            print(f"    • {conf} confidence: {count:,} records ({count/len(speed_dataset)*100:.1f}%)")
    
    thickness_dataset = datasets.get('thickness')
    if thickness_dataset is not None and 'Material_Confidence' in thickness_dataset.columns:
        conf_counts = thickness_dataset['Material_Confidence'].value_counts()
        print(f"  ✅ Enhanced Temporal Propagation (Thickness data):")
        for conf, count in conf_counts.items():
            print(f"    • {conf} confidence: {count:,} records ({count/len(thickness_dataset)*100:.1f}%)")
    
    # Test product code extraction
    fm_dataset = datasets.get('fm_stack')
    if fm_dataset is not None and 'Enhanced_Material' in fm_dataset.columns:
        enhanced_count = fm_dataset['Enhanced_Material'].notna().sum()
        print(f"  ✅ Product Code Extraction (FM Stack): {enhanced_count:,}/{len(fm_dataset):,} ({enhanced_count/len(fm_dataset)*100:.1f}%)")
        
        if 'Material_Source' in fm_dataset.columns:
            source_counts = fm_dataset['Material_Source'].value_counts()
            for source, count in source_counts.items():
                print(f"    • {source} source: {count:,} records")
    
    sm_dataset = datasets.get('sm_stack') 
    if sm_dataset is not None and 'Enhanced_Material' in sm_dataset.columns:
        enhanced_count = sm_dataset['Enhanced_Material'].notna().sum()
        print(f"  ✅ Product Code Extraction (SM Stack): {enhanced_count:,}/{len(sm_dataset):,} ({enhanced_count/len(sm_dataset)*100:.1f}%)")
    
    print("\n🎉 Enhanced Material Coverage Test Complete!")
    print("=" * 60)
    
    return unified_data, datasets

if __name__ == "__main__":
    try:
        unified_data, datasets = test_enhanced_material_coverage()
        print("\n✅ All tests completed successfully!")
        
        # Save results for further analysis
        output_file = "enhanced_material_coverage_results.csv"
        unified_data.to_csv(output_file, index=False)
        print(f"📁 Unified dataset saved to: {output_file}")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)