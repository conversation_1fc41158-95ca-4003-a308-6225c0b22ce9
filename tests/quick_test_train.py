#!/usr/bin/env python3
"""
Quick Test Training Script

Tests the training system with reduced parameters for faster validation.
"""

import sys
import logging
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.forecasting.trainer import ManufacturingForecastTrainer
from src.forecasting.config import load_config_from_file

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def quick_test():
    """Test training with minimal parameters for faster validation."""
    try:
        logger.info("🔬 Quick Test Training - Validating System")
        
        # Load configuration
        forecast_config, training_config = load_config_from_file("config/forecasting_config.json")
        
        # Reduce training for quick test
        training_config.max_epochs = 2  # Just 2 epochs
        training_config.eval_steps = 10  # Quick evaluation
        training_config.save_steps = 20  # Less frequent saving
        training_config.early_stopping_patience = 2  # Quick stop
        
        # Test with just one target
        forecast_config.target_variables = ["thickness_thickness_avg"]
        
        logger.info("Creating trainer for quick test...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=True,
            use_transfer_learning=False  # Skip transfer learning for speed
        )
        
        logger.info("Starting quick training test...")
        trained_models = trainer.train_all_target_variables(data_path="test-data")
        
        if trained_models:
            logger.info("✅ Training system is working!")
            for target, model in trained_models.items():
                logger.info(f"   {target}: Model created successfully")
                logger.info(f"   Path: {model.model_save_path}")
            return True
        else:
            logger.error("❌ Training test failed - no models created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Training test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n🎉 Training system validated successfully!")
        print("You can now run full training with: python train.py")
    else:
        print("\n❌ Training system needs debugging")
        sys.exit(1)