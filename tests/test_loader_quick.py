#!/usr/bin/env python3
"""
Quick test script for the rebuilt manufacturing data loader.
Tests with a smaller subset to verify functionality.
"""

import sys
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append('src')

from data.loader import ManufacturingDataLoader, create_unified_table

def test_quick_unified_table():
    """Test unified table creation with limited data."""
    print("Testing Unified Table Creation (Quick Test)")
    print("="*60)
    
    try:
        # Load subset of data for quick testing
        speed_df = pd.read_csv("test-data/speed.csv").head(1000)
        stop_df = pd.read_csv("test-data/stop.csv").head(100)
        sm_stack_df = pd.read_csv("test-data/data-cyrus/sm_stack.csv").head(100)
        
        print(f"Loaded data subsets:")
        print(f"  Speed: {len(speed_df):,} rows")
        print(f"  Stop: {len(stop_df):,} rows")
        print(f"  SM Stack: {len(sm_stack_df):,} rows")
        
        # Test the core algorithm with subset
        print("\nTesting standalone create_unified_table()...")
        unified_df = create_unified_table(
            thickness_df=None,  # Will be derived from speed
            speed_df=speed_df,
            stop_df=stop_df,
            sm_stack_df=sm_stack_df,
            fm_stack_df=None,  # Skip FM for quick test
            vm_capacity_df=None
        )
        
        print(f"✓ Unified table created successfully!")
        print(f"  Rows: {len(unified_df):,}")
        print(f"  Columns: {len(unified_df.columns)}")
        
        # Show some key columns
        key_columns = ['timestamp', 'work_center', 'Speed', 'thickness_avg', 'sm_scrap_pct']
        available_columns = [col for col in key_columns if col in unified_df.columns]
        
        print(f"\nKey columns available: {available_columns}")
        print(f"Sample data:")
        print(unified_df[available_columns].head(3).to_string())
        
        # Save a sample
        unified_df.head(50).to_csv("unified_table_quick_sample.csv", index=False)
        print(f"\n✓ Sample saved to: unified_table_quick_sample.csv")
        
        return True
        
    except Exception as e:
        print(f"✗ Quick test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loader_functionality():
    """Test core loader functionality."""
    print("\nTesting Core Loader Functionality")
    print("="*60)
    
    try:
        loader = ManufacturingDataLoader()
        
        # Load just speed data to test basic functionality
        speed_df = pd.read_csv("test-data/speed.csv").head(500)
        loader.loaded_data['speed'] = loader._process_timestamps(
            speed_df, 
            loader.config['data_sources']['speed']
        )
        
        print(f"✓ Speed data processed: {len(loader.loaded_data['speed'])} rows")
        print(f"  Timestamp column created: {'timestamp' in loader.loaded_data['speed'].columns}")
        
        # Test thickness baseline creation
        baseline_df = loader._create_thickness_baseline(loader.loaded_data['speed'])
        print(f"✓ Thickness baseline created: {len(baseline_df)} rows")
        
        # Check for key columns
        expected_cols = ['thickness_avg', 'sensor_01', 'work_center']
        available = [col for col in expected_cols if col in baseline_df.columns]
        print(f"  Key columns present: {available}")
        
        return True
        
    except Exception as e:
        print(f"✗ Core loader test failed: {e}")
        return False

def main():
    """Run quick tests."""
    print("Manufacturing Data Loader - Quick Testing")
    print("="*60)
    
    tests = [
        ("Core Loader Functionality", test_loader_functionality),
        ("Quick Unified Table Creation", test_quick_unified_table)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("Quick Test Summary")
    print("="*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Quick tests passed! The rebuilt loader core functionality is working.")
        print("   The full test may be slow due to the large dataset size (175K+ records).")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")

if __name__ == "__main__":
    main()