#!/usr/bin/env python3
"""
Test script for matched stacks filtering and CSV export functionality.

Tests the complete pipeline from TM480 integration through matched filtering to CSV export.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.loader import ManufacturingDataLoader
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_matched_export():
    """Test matched stacks filtering and CSV export."""
    try:
        logger.info("🚀 Testing matched stacks filtering and CSV export...")
        
        # Initialize loader
        config_path = Path(__file__).parent / "data.config.json"
        loader = ManufacturingDataLoader(str(config_path))
        
        # Set output directory
        output_dir = "/Users/<USER>/JH/JH PoC/test_1/test-data/consolidated"
        
        # Test the complete pipeline using the convenience method
        logger.info("📊 Testing complete pipeline with create_and_export_matched_stacks()...")
        
        export_path = loader.create_and_export_matched_stacks(
            output_dir=output_dir,
            filename="test_matched_stacks.csv"
        )
        
        logger.info(f"✅ Export completed successfully!")
        logger.info(f"📁 File saved to: {export_path}")
        
        # Validate the exported file
        logger.info("🔍 Validating exported CSV file...")
        
        import pandas as pd
        
        # Read the CSV (skip comment lines)
        validation_df = pd.read_csv(export_path, comment='#')
        
        logger.info("=" * 50)
        logger.info("📋 EXPORTED CSV VALIDATION:")
        logger.info(f"   • Records in file: {len(validation_df):,}")
        logger.info(f"   • Columns in file: {len(validation_df.columns)}")
        
        # Check key columns
        key_columns = ['timestamp', 'work_center', 'Speed', 'sm_stack_number', 'has_fm_match', 'fm_machine_type']
        present_columns = [col for col in key_columns if col in validation_df.columns]
        missing_columns = [col for col in key_columns if col not in validation_df.columns]
        
        logger.info(f"   • Key columns present: {len(present_columns)}/{len(key_columns)}")
        if missing_columns:
            logger.warning(f"   • Missing columns: {missing_columns}")
        
        # Validate data quality
        if 'has_fm_match' in validation_df.columns:
            match_rate = validation_df['has_fm_match'].mean() * 100
            logger.info(f"   • FM match rate: {match_rate:.1f}% (should be 100%)")
            
            if match_rate != 100:
                logger.warning("⚠️ Not all records have FM matches - filtering may need adjustment")
        
        if 'sm_stack_number' in validation_df.columns:
            null_sm_stacks = validation_df['sm_stack_number'].isna().sum()
            logger.info(f"   • Null SM stack numbers: {null_sm_stacks:,}")
            
            if null_sm_stacks > 0:
                logger.warning("⚠️ Some records have null SM stack numbers")
        
        if 'fm_machine_type' in validation_df.columns:
            machine_types = validation_df['fm_machine_type'].value_counts()
            logger.info(f"   • Machine type distribution:")
            for machine_type, count in machine_types.items():
                percentage = (count / len(validation_df) * 100) if len(validation_df) > 0 else 0
                logger.info(f"     - {machine_type}: {count:,} ({percentage:.1f}%)")
        
        # Check file size
        file_size = Path(export_path).stat().st_size / (1024 * 1024)  # MB
        logger.info(f"   • File size: {file_size:.2f} MB")
        
        # Show sample data
        logger.info("📖 Sample data (first 3 rows):")
        sample_columns = ['timestamp', 'work_center', 'Speed', 'sm_stack_number', 'has_fm_match', 'fm_machine_type']
        available_sample_cols = [col for col in sample_columns if col in validation_df.columns]
        
        if available_sample_cols:
            sample_data = validation_df[available_sample_cols].head(3)
            for idx, row in sample_data.iterrows():
                logger.info(f"   Row {idx + 1}: {dict(row)}")
        
        logger.info("=" * 50)
        logger.info("🎉 Matched stacks filtering and CSV export test completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"Full error traceback:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_matched_export()
    if success:
        print("\n✅ Test PASSED: Matched stacks filtering and CSV export working correctly")
    else:
        print("\n❌ Test FAILED: Issues with matched stacks filtering or CSV export")
        sys.exit(1)