"""
Unit Tests for Multi-Method Visualization Module

Tests for multi-method correlation visualization capabilities including
side-by-side heatmaps, method convergence plots, and comprehensive dashboards.
"""

import pytest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from pathlib import Path
import tempfile
import os
from unittest.mock import Mock, patch

# Import the modules to test
from src.visualization.multi_plots import (
    MultiMethodCorrelationPlotter,
    quick_multi_method_heatmaps,
    quick_method_convergence_plot
)
from src.data.multi_correlations import create_sample_multi_method_analysis


class TestMultiMethodCorrelationPlotter:
    """Test suite for MultiMethodCorrelationPlotter class"""
    
    @pytest.fixture
    def plotter(self):
        """Create MultiMethodCorrelationPlotter instance"""
        return MultiMethodCorrelationPlotter()
    
    @pytest.fixture
    def sample_correlation_results(self):
        """Create sample multi-method correlation results for testing"""
        # Generate sample results using the analyzer
        analysis_results = create_sample_multi_method_analysis()
        
        # Convert to the expected format for visualization
        correlation_results = {
            'multi_method_results': {},
            'convergence_analysis': analysis_results['convergence_analysis']
        }
        
        # Convert MultiMethodCorrelationResult objects to serializable format
        for pair_key, result in analysis_results['correlation_results'].items():
            correlation_results['multi_method_results'][pair_key] = {
                'variable_1': result.variable_1,
                'variable_2': result.variable_2,
                'pearson_correlation': result.pearson_correlation,
                'spearman_correlation': result.spearman_correlation,
                'kendall_correlation': result.kendall_correlation,
                'pearson_p_value': result.pearson_p_value,
                'spearman_p_value': result.spearman_p_value,
                'kendall_p_value': result.kendall_p_value,
                'sample_size': result.sample_size,
                'method_convergence_score': result.method_convergence_score,
                'recommended_method': result.recommended_method,
                'data_distribution_assessment': result.data_distribution_assessment,
                'pearson_confidence_interval': list(result.pearson_confidence_interval),
                'spearman_confidence_interval': list(result.spearman_confidence_interval),
                'kendall_confidence_interval': list(result.kendall_confidence_interval),
                'interpretation': result.interpretation
            }
        
        return correlation_results
    
    @pytest.fixture
    def minimal_correlation_results(self):
        """Create minimal correlation results for edge case testing"""
        return {
            'multi_method_results': {
                'var1_var2': {
                    'variable_1': 'var1',
                    'variable_2': 'var2',
                    'pearson_correlation': 0.75,
                    'spearman_correlation': 0.73,
                    'kendall_correlation': 0.68,
                    'pearson_p_value': 0.001,
                    'spearman_p_value': 0.002,
                    'kendall_p_value': 0.003,
                    'sample_size': 100,
                    'method_convergence_score': 0.85,
                    'recommended_method': 'pearson',
                    'data_distribution_assessment': {},
                    'pearson_confidence_interval': [0.65, 0.85],
                    'spearman_confidence_interval': [0.63, 0.83],
                    'kendall_confidence_interval': [0.58, 0.78],
                    'interpretation': {
                        'pearson': 'Strong positive correlation',
                        'spearman': 'Strong positive correlation',
                        'kendall': 'Moderate positive correlation'
                    }
                }
            },
            'convergence_analysis': {
                'overall_convergence_score': 0.85,
                'method_stability': {
                    'pearson_stability': 0.9,
                    'spearman_stability': 0.88,
                    'kendall_stability': 0.82
                },
                'cross_method_correlations': {
                    'pearson_spearman': 0.95,
                    'pearson_kendall': 0.88,
                    'spearman_kendall': 0.90
                }
            }
        }
    
    def test_plotter_initialization(self, plotter):
        """Test MultiMethodCorrelationPlotter initialization"""
        assert plotter is not None
        assert hasattr(plotter, 'method_colors')
        assert hasattr(plotter, 'convergence_colors')
        
        # Check method colors are defined
        assert 'pearson' in plotter.method_colors
        assert 'spearman' in plotter.method_colors
        assert 'kendall' in plotter.method_colors
        
        # Check convergence colors are defined
        assert 'high' in plotter.convergence_colors
        assert 'medium' in plotter.convergence_colors
        assert 'low' in plotter.convergence_colors
    
    def test_create_multi_method_heatmaps_success(self, plotter, sample_correlation_results):
        """Test successful creation of multi-method heatmaps"""
        fig = plotter.create_multi_method_heatmaps(sample_correlation_results)
        
        assert isinstance(fig, plt.Figure)
        
        # Check that figure has expected structure (3 subplots for 3 methods + 1 colorbar)
        axes = fig.get_axes()
        assert len(axes) == 4  # 3 subplots + 1 colorbar
        
        # Check subplot titles (first 3 axes are plots, 4th is colorbar)
        plot_axes = axes[:3]  # First 3 are the actual plot axes
        titles = [ax.get_title() for ax in plot_axes]
        assert 'Pearson' in titles[0]
        assert 'Spearman' in titles[1]
        assert 'Kendall' in titles[2]
        
        plt.close(fig)  # Clean up
    
    def test_create_multi_method_heatmaps_with_save(self, plotter, sample_correlation_results):
        """Test multi-method heatmaps creation with file saving"""
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = os.path.join(temp_dir, "test_heatmaps.png")
            
            fig = plotter.create_multi_method_heatmaps(
                sample_correlation_results, 
                save_path=save_path
            )
            
            # Check that file was created
            assert os.path.exists(save_path)
            
            # Check file size is reasonable (not empty)
            assert os.path.getsize(save_path) > 1000  # At least 1KB
            
            plt.close(fig)
    
    def test_create_multi_method_heatmaps_custom_params(self, plotter, minimal_correlation_results):
        """Test multi-method heatmaps with custom parameters"""
        fig = plotter.create_multi_method_heatmaps(
            minimal_correlation_results,
            title="Custom Multi-Method Analysis",
            figsize=(20, 8),
            show_convergence=False
        )
        
        assert isinstance(fig, plt.Figure)
        
        # Check custom figure size
        fig_size = fig.get_size_inches()
        assert abs(fig_size[0] - 20) < 1  # Allow some tolerance
        assert abs(fig_size[1] - 8) < 1
        
        plt.close(fig)
    
    def test_create_multi_method_heatmaps_empty_data(self, plotter):
        """Test multi-method heatmaps with empty correlation results"""
        empty_results = {'multi_method_results': {}}
        
        with pytest.raises(ValueError):
            plotter.create_multi_method_heatmaps(empty_results)
    
    def test_plot_method_convergence_success(self, plotter, sample_correlation_results):
        """Test successful method convergence plotting"""
        fig = plotter.plot_method_convergence(sample_correlation_results)
        
        assert isinstance(fig, go.Figure)
        
        # Check that figure has data
        assert len(fig.data) > 0
        
        # Check subplot structure (should have 4 subplots in 2x2 layout)
        # This is implicit in plotly subplot structure
        assert fig.layout.annotations is not None  # Subplot titles
    
    def test_plot_method_convergence_with_save(self, plotter, sample_correlation_results):
        """Test method convergence plotting with file saving"""
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = os.path.join(temp_dir, "convergence_plot.html")
            
            fig = plotter.plot_method_convergence(
                sample_correlation_results,
                save_path=save_path
            )
            
            # Check that HTML file was created
            assert os.path.exists(save_path)
            
            # Check file contains plotly content
            with open(save_path, 'r') as f:
                content = f.read()
                assert 'plotly' in content.lower()
                assert 'convergence' in content.lower()
    
    def test_plot_method_convergence_missing_data(self, plotter):
        """Test method convergence plotting with missing data"""
        incomplete_results = {'convergence_analysis': {}}
        
        with pytest.raises(ValueError):
            plotter.plot_method_convergence(incomplete_results)
    
    def test_create_multi_method_dashboard_success(self, plotter, sample_correlation_results):
        """Test successful creation of multi-method dashboard"""
        fig = plotter.create_multi_method_dashboard(sample_correlation_results)
        
        assert isinstance(fig, go.Figure)
        
        # Check that dashboard has multiple traces (various plot types)
        assert len(fig.data) > 5  # Should have many traces for comprehensive dashboard
        
        # Check layout has appropriate height for dashboard
        assert fig.layout.height >= 1000  # Should be tall for multiple rows
    
    def test_create_multi_method_dashboard_with_save(self, plotter, sample_correlation_results):
        """Test multi-method dashboard with file saving"""
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = os.path.join(temp_dir, "dashboard.html")
            
            fig = plotter.create_multi_method_dashboard(
                sample_correlation_results,
                save_path=save_path
            )
            
            # Check that HTML file was created
            assert os.path.exists(save_path)
            
            # Check file size is substantial (dashboard should be large)
            assert os.path.getsize(save_path) > 5000  # At least 5KB
    
    def test_plot_method_comparison_matrix_success(self, plotter, sample_correlation_results):
        """Test successful method comparison matrix plotting"""
        fig = plotter.plot_method_comparison_matrix(sample_correlation_results)
        
        assert isinstance(fig, plt.Figure)
        
        # Check that figure has expected structure (2x2 subplots + 1 colorbar)
        axes = fig.get_axes()
        assert len(axes) == 5  # 4 subplots + 1 colorbar
        
        plt.close(fig)
    
    def test_plot_method_comparison_matrix_custom_methods(self, plotter, sample_correlation_results):
        """Test method comparison matrix with custom method pair"""
        fig = plotter.plot_method_comparison_matrix(
            sample_correlation_results,
            method_pair=('spearman', 'kendall'),
            title="Spearman vs Kendall Comparison"
        )
        
        assert isinstance(fig, plt.Figure)
        
        # Check custom title
        suptitle = fig._suptitle
        assert suptitle is not None
        assert 'Spearman vs Kendall' in suptitle.get_text()
        
        plt.close(fig)
    
    def test_plot_method_comparison_matrix_with_save(self, plotter, minimal_correlation_results):
        """Test method comparison matrix with file saving"""
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = os.path.join(temp_dir, "comparison_matrix.png")
            
            fig = plotter.plot_method_comparison_matrix(
                minimal_correlation_results,
                save_path=save_path
            )
            
            # Check that file was created
            assert os.path.exists(save_path)
            assert os.path.getsize(save_path) > 1000
            
            plt.close(fig)
    
    def test_extract_correlation_matrices_success(self, plotter, sample_correlation_results):
        """Test successful extraction of correlation matrices"""
        pearson_matrix, spearman_matrix, kendall_matrix = plotter._extract_correlation_matrices(
            sample_correlation_results
        )
        
        # Check that matrices are DataFrames
        assert isinstance(pearson_matrix, pd.DataFrame)
        assert isinstance(spearman_matrix, pd.DataFrame)
        assert isinstance(kendall_matrix, pd.DataFrame)
        
        # Check that matrices have same shape
        assert pearson_matrix.shape == spearman_matrix.shape
        assert spearman_matrix.shape == kendall_matrix.shape
        
        # Check that diagonal elements are 1.0
        for matrix in [pearson_matrix, spearman_matrix, kendall_matrix]:
            if not matrix.empty:
                for var in matrix.index:
                    assert abs(matrix.loc[var, var] - 1.0) < 0.001
        
        # Check that matrices are symmetric
        for matrix in [pearson_matrix, spearman_matrix, kendall_matrix]:
            if not matrix.empty:
                assert matrix.equals(matrix.T)
    
    def test_extract_correlation_matrices_empty_data(self, plotter):
        """Test extraction of correlation matrices with empty data"""
        empty_results = {'multi_method_results': {}}
        
        pearson_matrix, spearman_matrix, kendall_matrix = plotter._extract_correlation_matrices(
            empty_results
        )
        
        # Should return empty DataFrames
        assert pearson_matrix.empty
        assert spearman_matrix.empty
        assert kendall_matrix.empty
    
    def test_save_all_multi_method_plots_success(self, plotter, sample_correlation_results):
        """Test saving all multi-method plots"""
        with tempfile.TemporaryDirectory() as temp_dir:
            plotter.save_all_multi_method_plots(
                sample_correlation_results,
                output_dir=temp_dir
            )
            
            # Check that expected files were created
            expected_files = [
                "multi_method_heatmaps.png",
                "method_convergence.html",
                "multi_method_dashboard.html",
                "pearson_vs_spearman_comparison.png",
                "pearson_vs_kendall_comparison.png",
                "spearman_vs_kendall_comparison.png"
            ]
            
            created_files = os.listdir(temp_dir)
            
            # Check that most expected files were created
            created_count = sum(1 for f in expected_files if f in created_files)
            assert created_count >= 4  # Allow for some failures in test environment


class TestConvenienceFunctions:
    """Test suite for convenience functions"""
    
    @pytest.fixture
    def sample_results(self):
        """Create sample results for convenience function testing"""
        return create_sample_multi_method_analysis()
    
    def test_quick_multi_method_heatmaps(self, sample_results):
        """Test quick multi-method heatmaps function"""
        # Convert to expected format
        correlation_results = {
            'multi_method_results': {}
        }
        
        for pair_key, result in sample_results['correlation_results'].items():
            correlation_results['multi_method_results'][pair_key] = {
                'variable_1': result.variable_1,
                'variable_2': result.variable_2,
                'pearson_correlation': result.pearson_correlation,
                'spearman_correlation': result.spearman_correlation,
                'kendall_correlation': result.kendall_correlation,
                'method_convergence_score': result.method_convergence_score
            }
        
        fig = quick_multi_method_heatmaps(correlation_results)
        
        assert isinstance(fig, plt.Figure)
        
        plt.close(fig)
    
    def test_quick_method_convergence_plot(self, sample_results):
        """Test quick method convergence plot function"""
        # Convert to expected format
        correlation_results = {
            'multi_method_results': {}
        }
        
        for pair_key, result in sample_results['correlation_results'].items():
            correlation_results['multi_method_results'][pair_key] = {
                'variable_1': result.variable_1,
                'variable_2': result.variable_2,
                'pearson_correlation': result.pearson_correlation,
                'spearman_correlation': result.spearman_correlation,
                'kendall_correlation': result.kendall_correlation,
                'method_convergence_score': result.method_convergence_score,
                'recommended_method': result.recommended_method
            }
        
        fig = quick_method_convergence_plot(correlation_results)
        
        assert isinstance(fig, go.Figure)


class TestVisualizationWithRealData:
    """Test visualization with real manufacturing data patterns"""
    
    @pytest.fixture
    def manufacturing_correlation_results(self):
        """Create realistic manufacturing correlation results"""
        # Simulate realistic manufacturing correlations
        variable_pairs = [
            ('speed', 'energy_consumption'),
            ('speed', 'temperature'),
            ('temperature', 'defect_rate'),
            ('pressure', 'thickness'),
            ('thickness', 'quality_score')
        ]
        
        correlation_results = {
            'multi_method_results': {},
            'convergence_analysis': {
                'overall_convergence_score': 0.78,
                'method_stability': {
                    'pearson_stability': 0.85,
                    'spearman_stability': 0.82,
                    'kendall_stability': 0.75
                },
                'cross_method_correlations': {
                    'pearson_spearman': 0.92,
                    'pearson_kendall': 0.78,
                    'spearman_kendall': 0.84
                },
                'convergence_distribution': {
                    'high_convergence_pairs': 3,
                    'medium_convergence_pairs': 1,
                    'low_convergence_pairs': 1,
                    'convergence_rate': 0.6
                }
            }
        }
        
        # Add realistic correlations for each pair
        np.random.seed(42)
        for i, (var1, var2) in enumerate(variable_pairs):
            # Create realistic correlation patterns
            if 'speed' in var1 and 'energy' in var2:
                # Strong linear relationship
                pearson_r, spearman_r, kendall_r = 0.85, 0.83, 0.72
                convergence_score = 0.9
                recommended = 'pearson'
            elif 'temperature' in var1 and 'defect' in var2:
                # Non-linear but monotonic
                pearson_r, spearman_r, kendall_r = 0.62, 0.78, 0.68
                convergence_score = 0.7
                recommended = 'spearman'
            elif 'pressure' in var1 and 'thickness' in var2:
                # With outliers
                pearson_r, spearman_r, kendall_r = 0.45, 0.68, 0.72
                convergence_score = 0.55
                recommended = 'kendall'
            else:
                # Random but reasonable correlations
                base_corr = np.random.uniform(0.3, 0.8)
                pearson_r = base_corr + np.random.normal(0, 0.05)
                spearman_r = base_corr + np.random.normal(0, 0.03)
                kendall_r = base_corr * 0.8 + np.random.normal(0, 0.04)
                convergence_score = np.random.uniform(0.6, 0.9)
                recommended = np.random.choice(['pearson', 'spearman', 'kendall'])
            
            pair_key = f"{var1}_{var2}"
            correlation_results['multi_method_results'][pair_key] = {
                'variable_1': var1,
                'variable_2': var2,
                'pearson_correlation': round(pearson_r, 6),
                'spearman_correlation': round(spearman_r, 6),
                'kendall_correlation': round(kendall_r, 6),
                'pearson_p_value': np.random.uniform(0.001, 0.01),
                'spearman_p_value': np.random.uniform(0.001, 0.01),
                'kendall_p_value': np.random.uniform(0.001, 0.02),
                'sample_size': np.random.randint(200, 1000),
                'method_convergence_score': round(convergence_score, 3),
                'recommended_method': recommended,
                'data_distribution_assessment': {},
                'pearson_confidence_interval': [pearson_r - 0.1, pearson_r + 0.1],
                'spearman_confidence_interval': [spearman_r - 0.1, spearman_r + 0.1],
                'kendall_confidence_interval': [kendall_r - 0.1, kendall_r + 0.1],
                'interpretation': {
                    'pearson': f"{'Strong' if abs(pearson_r) > 0.7 else 'Moderate'} linear relationship",
                    'spearman': f"{'Strong' if abs(spearman_r) > 0.7 else 'Moderate'} monotonic relationship",
                    'kendall': f"{'Strong' if abs(kendall_r) > 0.7 else 'Moderate'} ordinal relationship"
                }
            }
        
        return correlation_results
    
    def test_manufacturing_heatmaps(self, manufacturing_correlation_results):
        """Test heatmaps with realistic manufacturing data"""
        plotter = MultiMethodCorrelationPlotter()
        
        fig = plotter.create_multi_method_heatmaps(
            manufacturing_correlation_results,
            title="Manufacturing Process Correlations"
        )
        
        assert isinstance(fig, plt.Figure)
        
        # Check that manufacturing variables are represented (3 plots + 1 colorbar)
        axes = fig.get_axes()
        assert len(axes) == 4  # 3 subplots + 1 colorbar
        
        plt.close(fig)
    
    def test_manufacturing_convergence_analysis(self, manufacturing_correlation_results):
        """Test convergence analysis with manufacturing patterns"""
        plotter = MultiMethodCorrelationPlotter()
        
        fig = plotter.plot_method_convergence(manufacturing_correlation_results)
        
        assert isinstance(fig, go.Figure)
        
        # Should have multiple traces for comprehensive analysis
        assert len(fig.data) >= 4
    
    def test_manufacturing_dashboard(self, manufacturing_correlation_results):
        """Test dashboard with manufacturing correlation patterns"""
        plotter = MultiMethodCorrelationPlotter()
        
        fig = plotter.create_multi_method_dashboard(manufacturing_correlation_results)
        
        assert isinstance(fig, go.Figure)
        
        # Dashboard should be comprehensive
        assert len(fig.data) > 5
        assert fig.layout.height >= 1000


class TestVisualizationEdgeCases:
    """Test edge cases and error conditions for visualization"""
    
    def test_heatmaps_with_single_pair(self):
        """Test heatmaps with only one correlation pair"""
        single_pair_results = {
            'multi_method_results': {
                'var1_var2': {
                    'variable_1': 'var1',
                    'variable_2': 'var2',
                    'pearson_correlation': 0.5,
                    'spearman_correlation': 0.48,
                    'kendall_correlation': 0.42,
                    'method_convergence_score': 0.8
                }
            }
        }
        
        plotter = MultiMethodCorrelationPlotter()
        fig = plotter.create_multi_method_heatmaps(single_pair_results)
        
        assert isinstance(fig, plt.Figure)
        
        plt.close(fig)
    
    def test_convergence_plot_with_missing_methods(self):
        """Test convergence plot with missing correlation methods"""
        incomplete_results = {
            'multi_method_results': {
                'var1_var2': {
                    'variable_1': 'var1',
                    'variable_2': 'var2',
                    'pearson_correlation': 0.5,
                    'spearman_correlation': 0.48,
                    # Missing Kendall correlation
                    'method_convergence_score': 0.8,
                    'recommended_method': 'pearson'
                }
            }
        }
        
        plotter = MultiMethodCorrelationPlotter()
        
        # Should handle missing data gracefully
        try:
            fig = plotter.plot_method_convergence(incomplete_results)
            assert isinstance(fig, go.Figure)
        except (KeyError, ValueError):
            # Acceptable to fail with missing required data
            pass
    
    def test_dashboard_with_minimal_convergence_data(self):
        """Test dashboard with minimal convergence analysis data"""
        minimal_results = {
            'multi_method_results': {
                'var1_var2': {
                    'variable_1': 'var1',
                    'variable_2': 'var2',
                    'pearson_correlation': 0.5,
                    'spearman_correlation': 0.48,
                    'kendall_correlation': 0.42,
                    'pearson_p_value': 0.01,
                    'spearman_p_value': 0.01,
                    'kendall_p_value': 0.02,
                    'method_convergence_score': 0.8,
                    'recommended_method': 'pearson'
                }
            },
            'convergence_analysis': {
                'overall_convergence_score': 0.8
                # Missing other convergence data
            }
        }
        
        plotter = MultiMethodCorrelationPlotter()
        fig = plotter.create_multi_method_dashboard(minimal_results)
        
        assert isinstance(fig, go.Figure)
        # Should handle missing data gracefully
    
    def test_comparison_matrix_with_extreme_correlations(self):
        """Test comparison matrix with extreme correlation values"""
        extreme_results = {
            'multi_method_results': {
                'perfect_positive': {
                    'variable_1': 'var1',
                    'variable_2': 'var2',
                    'pearson_correlation': 1.0,
                    'spearman_correlation': 1.0,
                    'kendall_correlation': 1.0,
                    'method_convergence_score': 1.0
                },
                'perfect_negative': {
                    'variable_1': 'var3',
                    'variable_2': 'var4',
                    'pearson_correlation': -1.0,
                    'spearman_correlation': -1.0,
                    'kendall_correlation': -1.0,
                    'method_convergence_score': 1.0
                },
                'no_correlation': {
                    'variable_1': 'var5',
                    'variable_2': 'var6',
                    'pearson_correlation': 0.0,
                    'spearman_correlation': 0.0,
                    'kendall_correlation': 0.0,
                    'method_convergence_score': 1.0
                }
            }
        }
        
        plotter = MultiMethodCorrelationPlotter()
        fig = plotter.plot_method_comparison_matrix(extreme_results)
        
        assert isinstance(fig, plt.Figure)
        
        plt.close(fig)


class TestVisualizationPerformance:
    """Test visualization performance with larger datasets"""
    
    def test_heatmaps_with_many_variables(self):
        """Test heatmaps with many variable pairs"""
        # Create results with many variable pairs
        many_pairs_results = {
            'multi_method_results': {},
            'convergence_analysis': {'overall_convergence_score': 0.75}
        }
        
        # Generate 20 variable pairs
        variables = [f'var_{i}' for i in range(10)]
        np.random.seed(42)
        
        for i, var1 in enumerate(variables):
            for var2 in variables[i+1:]:
                pair_key = f"{var1}_{var2}"
                pearson_r = np.random.uniform(-0.8, 0.8)
                spearman_r = pearson_r + np.random.normal(0, 0.05)
                kendall_r = pearson_r * 0.8 + np.random.normal(0, 0.05)
                
                many_pairs_results['multi_method_results'][pair_key] = {
                    'variable_1': var1,
                    'variable_2': var2,
                    'pearson_correlation': pearson_r,
                    'spearman_correlation': spearman_r,
                    'kendall_correlation': kendall_r,
                    'method_convergence_score': np.random.uniform(0.5, 0.9)
                }
        
        plotter = MultiMethodCorrelationPlotter()
        
        # Should handle many pairs without performance issues
        import time
        start_time = time.time()
        
        fig = plotter.create_multi_method_heatmaps(many_pairs_results)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within reasonable time (10 seconds)
        assert execution_time < 10
        
        plt.close(fig)
    
    def test_dashboard_generation_time(self):
        """Test dashboard generation time with moderate dataset"""
        # Use sample data for performance testing
        sample_results = create_sample_multi_method_analysis()
        
        # Convert to visualization format
        correlation_results = {
            'multi_method_results': {},
            'convergence_analysis': sample_results['convergence_analysis']
        }
        
        for pair_key, result in sample_results['correlation_results'].items():
            correlation_results['multi_method_results'][pair_key] = {
                'variable_1': result.variable_1,
                'variable_2': result.variable_2,
                'pearson_correlation': result.pearson_correlation,
                'spearman_correlation': result.spearman_correlation,
                'kendall_correlation': result.kendall_correlation,
                'pearson_p_value': result.pearson_p_value,
                'spearman_p_value': result.spearman_p_value,
                'kendall_p_value': result.kendall_p_value,
                'method_convergence_score': result.method_convergence_score,
                'recommended_method': result.recommended_method
            }
        
        plotter = MultiMethodCorrelationPlotter()
        
        import time
        start_time = time.time()
        
        fig = plotter.create_multi_method_dashboard(correlation_results)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Dashboard should generate within reasonable time (15 seconds)
        assert execution_time < 15
        
        assert isinstance(fig, go.Figure)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])