#!/usr/bin/env python3
"""
Test script for enhanced correlation agent functionality with unified table.

Tests the new analysis types and tools implemented for the 83-column unified manufacturing table.
"""

import pandas as pd
import numpy as np
import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import the enhanced correlation agent functions directly
try:
    from src.agents.tools import analyze_basic_correlations, analyze_stratified_correlations
    from src.agents.prompts import get_correlation_prompt
    print("✅ Successfully imported enhanced correlation tools")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("⚠️ Will test core functionality only")

class ManufacturingDataDependencies:
    """Mock dependencies for testing"""
    def __init__(self, data):
        self.data = data
        self.time_column = 'timestamp'
        self.significance_threshold = 0.05
        self.min_correlation = 0.01
        self.analysis_type = 'general'

class MockRunContext:
    """Mock run context for testing"""
    def __init__(self, data):
        self.deps = ManufacturingDataDependencies(data)

def create_sample_unified_data():
    """Create sample data that mimics the unified table structure"""
    np.random.seed(42)
    n_records = 1000
    
    # Create timestamps
    timestamps = pd.date_range('2025-03-01', periods=n_records, freq='1min')
    
    # Core manufacturing data
    data = {
        'timestamp': timestamps,
        'work_center': ['SM270'] * n_records,
        'Speed': np.random.normal(130, 15, n_records),
        
        # Quality metrics (key for correlation analysis)
        'sm_scrap_pct': np.random.exponential(2.5, n_records),
        'fm_reject_pct': np.random.exponential(1.8, n_records),
        'production_efficiency_pct': np.random.normal(92, 8, n_records),
        
        # Speed analytics
        'speed_avg': np.random.normal(128, 12, n_records),
        'speed_std': np.random.exponential(8, n_records),
        'speed_cv': np.random.exponential(0.08, n_records),
        'speed_deviation_pct': np.random.normal(0, 5, n_records),
        
        # Stoppage analytics
        'stops_during_production': np.random.poisson(1.2, n_records),
        'total_stop_duration_minutes': np.random.exponential(15, n_records),
        'restart_impact_score': np.random.normal(0.3, 0.2, n_records),
        
        # SM-FM integration (unified table specific)
        'has_fm_match': np.random.choice([True, False], n_records, p=[0.8, 0.2]),
        'fm_machine_type': np.random.choice(['FM', 'TM480'], n_records, p=[0.24, 0.76]),
        'sm_to_fm_gap_minutes': np.random.exponential(720, n_records),  # ~12 hours average
        
        # Temporal features
        'production_shift': np.random.choice(['Day', 'Night', 'Weekend'], n_records, p=[0.5, 0.4, 0.1]),
        'hour_of_day': [ts.hour for ts in timestamps],
        'day_of_week': [ts.dayofweek for ts in timestamps],
        
        # Production context
        'sm_production_rate': np.random.normal(45, 8, n_records),
        'capacity_utilization_pct': np.random.normal(88, 12, n_records),
    }
    
    # Add correlations to make testing meaningful
    # SM scrap affects FM reject with some lag and noise
    for i in range(1, len(data['sm_scrap_pct'])):
        data['fm_reject_pct'][i] += 0.3 * data['sm_scrap_pct'][i-1] + np.random.normal(0, 0.5)
    
    # Speed variability affects quality
    for i in range(len(data['speed_cv'])):
        data['sm_scrap_pct'][i] += 0.5 * data['speed_cv'][i] * 10
        
    # Efficiency affects quality
    for i in range(len(data['production_efficiency_pct'])):
        if data['production_efficiency_pct'][i] < 85:
            data['sm_scrap_pct'][i] += np.random.exponential(1.0)
    
    # Ensure positive values and realistic ranges
    data['sm_scrap_pct'] = np.clip(data['sm_scrap_pct'], 0, 15)
    data['fm_reject_pct'] = np.clip(data['fm_reject_pct'], 0, 12)
    data['production_efficiency_pct'] = np.clip(data['production_efficiency_pct'], 60, 100)
    data['speed_cv'] = np.clip(data['speed_cv'], 0.01, 0.3)
    
    return pd.DataFrame(data)

def test_basic_correlations():
    """Test basic correlation analysis"""
    print("🔍 Testing basic correlation analysis...")
    
    # Create sample data
    data = create_sample_unified_data()
    ctx = MockRunContext(data)
    
    # Test basic correlation analysis
    try:
        result = analyze_basic_correlations(ctx, filter_matched_flows=True)
        
        if "error" in result:
            print(f"❌ Basic correlation analysis failed: {result['error']}")
            return False
        
        print(f"✅ Basic correlation analysis successful!")
        print(f"   📊 Analyzed {result['matched_records']} matched records from {result['total_records']} total")
        print(f"   📈 Match rate: {result['match_rate']:.1%}")
        
        if "direct_sm_fm_correlation" in result:
            corr_coef = result["direct_sm_fm_correlation"]["correlation_coefficient"]
            sample_size = result["direct_sm_fm_correlation"]["sample_size"]
            print(f"   🔗 Direct SM-FM correlation: {corr_coef:.3f} (n={sample_size})")
        
        if "machine_type_distribution" in result:
            print(f"   🏭 Machine types: {result['machine_type_distribution']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic correlation analysis failed with exception: {e}")
        return False

def test_stratified_correlations():
    """Test stratified correlation analysis"""
    print("\n🔍 Testing stratified correlation analysis...")
    
    # Create sample data
    data = create_sample_unified_data()
    ctx = MockRunContext(data)
    
    # Test stratified correlation analysis
    try:
        result = analyze_stratified_correlations(
            ctx, 
            target_variable_1='sm_scrap_pct',
            target_variable_2='fm_reject_pct',
            stratification_types=['production_shift', 'machine_type', 'efficiency']
        )
        
        if "error" in result:
            print(f"❌ Stratified correlation analysis failed: {result['error']}")
            return False
        
        print(f"✅ Stratified correlation analysis successful!")
        print(f"   📊 Analyzed {result['total_records']} records")
        
        for strat_type, strat_result in result["stratification_results"].items():
            print(f"   📈 {strat_type.title()} stratification:")
            if "correlations" in strat_result:
                for category, corr in strat_result["correlations"].items():
                    sample_size = strat_result["sample_sizes"].get(category, 0)
                    if corr is not None:
                        print(f"      {category}: {corr:.3f} (n={sample_size})")
                    else:
                        print(f"      {category}: insufficient data (n={sample_size})")
        
        return True
        
    except Exception as e:
        print(f"❌ Stratified correlation analysis failed with exception: {e}")
        return False

def test_prompt_system():
    """Test the enhanced prompt system"""
    print("\n🔍 Testing enhanced prompt system...")
    
    try:
        # Test different analysis types
        analysis_types = ['unified_table', 'stratified', 'pattern_identification', 'ml_prediction', 'time_series']
        
        for analysis_type in analysis_types:
            prompt = get_correlation_prompt(analysis_type)
            if len(prompt) > 100:  # Basic check that prompt exists and is substantial
                print(f"   ✅ {analysis_type} prompt: {len(prompt)} characters")
            else:
                print(f"   ❌ {analysis_type} prompt missing or too short")
                return False
        
        print("✅ Enhanced prompt system working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Prompt system test failed with exception: {e}")
        return False

def test_data_quality():
    """Test data quality with unified table structure"""
    print("\n🔍 Testing data quality with unified table structure...")
    
    try:
        data = create_sample_unified_data()
        
        # Check expected columns exist
        expected_cols = ['timestamp', 'sm_scrap_pct', 'fm_reject_pct', 'has_fm_match', 'fm_machine_type', 'production_shift']
        missing_cols = [col for col in expected_cols if col not in data.columns]
        
        if missing_cols:
            print(f"❌ Missing expected columns: {missing_cols}")
            return False
        
        # Check data quality
        print(f"   📊 Dataset shape: {data.shape}")
        print(f"   🔗 Has FM match rate: {data['has_fm_match'].mean():.1%}")
        print(f"   🏭 Machine type distribution: {dict(data['fm_machine_type'].value_counts())}")
        print(f"   ⏰ Shift distribution: {dict(data['production_shift'].value_counts())}")
        
        # Check correlations exist
        corr_matrix = data[['sm_scrap_pct', 'fm_reject_pct', 'speed_cv', 'production_efficiency_pct']].corr()
        print(f"   📈 SM-FM correlation: {corr_matrix.loc['sm_scrap_pct', 'fm_reject_pct']:.3f}")
        
        print("✅ Data quality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Data quality test failed with exception: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Enhanced Correlation Agent Functionality")
    print("=" * 60)
    
    # List of tests to run
    tests = [
        ("Data Quality", test_data_quality),
        ("Basic Correlations", test_basic_correlations),
        ("Stratified Correlations", test_stratified_correlations),
        ("Prompt System", test_prompt_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🏃 Running {test_name} test...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} - {test_name}")
        if success:
            passed += 1
    
    print(f"\n📊 Overall: {passed}/{len(results)} tests passed ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("🎉 All tests passed! Enhanced correlation agent is ready for the unified table.")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)