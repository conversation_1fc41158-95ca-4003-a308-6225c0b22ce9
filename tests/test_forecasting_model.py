"""
Unit Tests for Manufacturing PatchTST Forecasting Model

Tests for PatchTST model functionality using real data and actual model training.
These tests validate the complete forecasting workflow with production data.
"""

import pytest
import pandas as pd
import numpy as np
import os
import torch
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the modules to test
from src.forecasting import (
    ManufacturingPatchTSTModel,
    ManufacturingForecastTrainer,
    ForecastConfig,
    PatchTSTTrainingConfig,
    ManufacturingTimeSeriesPreprocessor
)
from src.data.loader import ManufacturingDataLoader


class TestForecastConfiguration:
    """Test forecasting configuration management"""
    
    def test_default_forecast_config(self):
        """Test default configuration creation"""
        config = ForecastConfig()
        
        assert isinstance(config.input_variables, list)
        assert len(config.input_variables) > 0
        assert isinstance(config.target_variables, list)
        assert len(config.target_variables) > 0
        assert isinstance(config.forecast_horizons, list)
        assert all(h > 0 for h in config.forecast_horizons)
        assert config.lookback_window > 0
        assert config.patch_size > 0
    
    def test_config_from_json(self):
        """Test configuration loading from JSON file"""
        config_path = 'config/forecasting_config.json'
        if Path(config_path).exists():
            config = ForecastConfig.from_json(config_path)
            
            assert isinstance(config, ForecastConfig)
            assert 'thickness_avg' in config.input_variables
            assert len(config.forecast_horizons) >= 2
            assert config.model_params['d_model'] > 0
    
    def test_config_validation(self):
        """Test configuration validation"""
        # Test invalid forecast horizons
        with pytest.raises(ValueError):
            ForecastConfig(forecast_horizons=[])
        
        with pytest.raises(ValueError):
            ForecastConfig(forecast_horizons=[-1, 60])
        
        # Test invalid lookback window
        with pytest.raises(ValueError):
            ForecastConfig(lookback_window=0)
        
        # Test invalid patch size
        with pytest.raises(ValueError):
            ForecastConfig(patch_size=0)
    
    def test_training_config_validation(self):
        """Test training configuration validation"""
        # Test valid configuration
        config = PatchTSTTrainingConfig()
        assert config.batch_size > 0
        assert config.learning_rate > 0
        assert 0 < config.validation_split < 1
        assert 0 < config.test_split < 1
        
        # Test invalid configurations
        with pytest.raises(ValueError):
            PatchTSTTrainingConfig(batch_size=0)
        
        with pytest.raises(ValueError):
            PatchTSTTrainingConfig(learning_rate=0)
        
        with pytest.raises(ValueError):
            PatchTSTTrainingConfig(validation_split=1.5)


class TestTimeSeriesPreprocessing:
    """Test time series preprocessing functionality"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Create sample manufacturing data for testing"""
        # Create 1000 data points (about 16 hours at 1-minute intervals)
        n_samples = 1000
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(hours=16),
            periods=n_samples,
            freq='1min'
        )
        
        # Simulate manufacturing data with realistic patterns
        np.random.seed(42)
        
        # Base patterns
        time_trend = np.linspace(0, 10, n_samples)
        daily_cycle = np.sin(2 * np.pi * np.arange(n_samples) / 1440)  # Daily cycle
        
        # Manufacturing variables
        speed = 50 + 10 * np.sin(time_trend) + 5 * daily_cycle + np.random.normal(0, 2, n_samples)
        temperature = 80 + 5 * np.cos(time_trend) + 3 * daily_cycle + np.random.normal(0, 1, n_samples)
        pressure = 100 + 8 * np.sin(time_trend + 1) + np.random.normal(0, 1.5, n_samples)
        
        # Thickness sensors (10 sensors)
        thickness_base = 10 + 2 * np.sin(time_trend) + 1 * daily_cycle
        sensor_data = {}
        for i in range(1, 11):
            sensor_data[f'Sensor {i:02d}'] = (
                thickness_base + np.random.normal(0, 0.5, n_samples)
            )
        
        # Calculate thickness average and uniformity
        thickness_avg = np.mean([sensor_data[f'Sensor {i:02d}'] for i in range(1, 11)], axis=0)
        thickness_uniformity = np.std([sensor_data[f'Sensor {i:02d}'] for i in range(1, 11)], axis=0)
        
        # Quality metrics
        scrap_rate = 0.02 + 0.01 * np.random.exponential(1, n_samples)
        quality_index = 0.95 - 0.1 * (scrap_rate - 0.02) + np.random.normal(0, 0.02, n_samples)
        
        # Stoppage simulation
        minutes_since_last_stop = np.cumsum(np.random.exponential(30, n_samples)) % 480  # Max 8 hours
        
        data = pd.DataFrame({
            'timestamp': timestamps,
            'speed': speed,
            'temperature': temperature,
            'pressure': pressure,
            'thickness_avg': thickness_avg,
            'thickness_uniformity': thickness_uniformity,
            'scrap_rate': scrap_rate,
            'quality_index': quality_index,
            'minutes_since_last_stop': minutes_since_last_stop,
            **sensor_data
        })
        
        return data
    
    def test_preprocessor_initialization(self):
        """Test preprocessor initialization"""
        config = ForecastConfig()
        preprocessor = ManufacturingTimeSeriesPreprocessor(config)
        
        assert preprocessor.config == config
        assert hasattr(preprocessor, 'data_loader')
        assert hasattr(preprocessor, 'feature_scalers')
        assert hasattr(preprocessor, 'target_scalers')
    
    def test_prepare_forecasting_data(self, sample_manufacturing_data):
        """Test data preparation for forecasting"""
        config = ForecastConfig(
            input_variables=['speed', 'temperature', 'pressure', 'thickness_avg'],
            target_variables=['thickness_avg'],
            forecast_horizons=[15, 60],
            lookback_window=240
        )
        
        preprocessor = ManufacturingTimeSeriesPreprocessor(config)
        
        # Prepare data
        prepared_data = preprocessor.prepare_forecasting_data(
            sample_manufacturing_data, 'thickness_avg'
        )
        
        # Validate structure
        assert 'train_data' in prepared_data
        assert 'val_data' in prepared_data
        assert 'test_data' in prepared_data
        assert 'feature_columns' in prepared_data
        assert 'target_variable' in prepared_data
        assert 'metadata' in prepared_data
        
        # Validate data splits
        assert len(prepared_data['train_data']['sequences']) > 0
        assert len(prepared_data['val_data']['sequences']) > 0
        assert len(prepared_data['test_data']['sequences']) > 0
        
        # Validate metadata
        metadata = prepared_data['metadata']
        assert metadata['feature_count'] == len(config.input_variables)
        assert metadata['sequence_length'] == config.lookback_window
        assert metadata['target_variable'] == 'thickness_avg'
    
    def test_missing_value_handling(self, sample_manufacturing_data):
        """Test missing value handling in preprocessing"""
        # Introduce missing values
        data_with_missing = sample_manufacturing_data.copy()
        data_with_missing.loc[100:110, 'speed'] = np.nan
        data_with_missing.loc[200:205, 'thickness_avg'] = np.nan
        
        config = ForecastConfig(
            input_variables=['speed', 'temperature', 'thickness_avg'],
            target_variables=['thickness_avg'],
            lookback_window=120
        )
        
        preprocessor = ManufacturingTimeSeriesPreprocessor(config)
        
        # Should handle missing values without error
        prepared_data = preprocessor.prepare_forecasting_data(
            data_with_missing, 'thickness_avg'
        )
        
        assert prepared_data is not None
        assert len(prepared_data['train_data']['sequences']) > 0
    
    def test_validation_function(self, sample_manufacturing_data):
        """Test preprocessing validation"""
        config = ForecastConfig()
        preprocessor = ManufacturingTimeSeriesPreprocessor(config)
        
        prepared_data = preprocessor.prepare_forecasting_data(
            sample_manufacturing_data, 'thickness_avg'
        )
        
        # Validate the processed data
        is_valid = preprocessor.validate_preprocessing_output(prepared_data)
        assert is_valid is True


class TestPatchTSTModel:
    """Test PatchTST model functionality"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Reuse sample data fixture"""
        return TestTimeSeriesPreprocessing().sample_manufacturing_data(self)
    
    def test_model_initialization(self):
        """Test model initialization"""
        config = ForecastConfig()
        model = ManufacturingPatchTSTModel(config)
        
        assert model.forecast_config == config
        assert model.model is None  # Should be None until initialized
        assert hasattr(model, 'preprocessor')
        assert isinstance(model.preprocessor, ManufacturingTimeSeriesPreprocessor)
    
    def test_model_creation_with_config(self):
        """Test model creation from configuration"""
        config_path = 'config/forecasting_config.json'
        if Path(config_path).exists():
            model = ManufacturingPatchTSTModel.from_config(config_path)
            assert isinstance(model, ManufacturingPatchTSTModel)
            assert model.forecast_config is not None
    
    def test_model_initialize_architecture(self):
        """Test model architecture initialization"""
        config = ForecastConfig(
            input_variables=['speed', 'temperature', 'thickness_avg'],
            model_params={
                'd_model': 64,  # Smaller for testing
                'num_attention_heads': 4,
                'num_hidden_layers': 2
            }
        )
        
        model = ManufacturingPatchTSTModel(config)
        model.initialize_model(num_input_channels=3)
        
        assert model.model is not None
        assert hasattr(model.model, 'config')
        assert model.model.config.num_input_channels == 3
        assert model.model.config.d_model == 64
    
    @pytest.mark.skipif(not torch.cuda.is_available(), reason="GPU not available for full training test")
    def test_model_training_small_dataset(self, sample_manufacturing_data):
        """Test model training with small dataset (GPU required)"""
        # Use smaller configuration for faster testing
        config = ForecastConfig(
            input_variables=['speed', 'temperature', 'thickness_avg'],
            target_variables=['thickness_avg'],
            forecast_horizons=[15],
            lookback_window=60,
            model_params={
                'd_model': 32,
                'num_attention_heads': 2,
                'num_hidden_layers': 1
            }
        )
        
        training_config = PatchTSTTrainingConfig(
            batch_size=8,
            max_epochs=2,  # Very few epochs for testing
            early_stopping_patience=1
        )
        
        model = ManufacturingPatchTSTModel(config, training_config)
        
        # Use subset of data for faster training
        small_data = sample_manufacturing_data.head(300)
        
        # Train model
        trained_model = model.train(small_data, 'thickness_avg')
        
        assert trained_model.model is not None
        assert len(trained_model.training_history) > 0
        assert 'training_loss' in trained_model.training_history
    
    def test_model_metadata_generation(self):
        """Test model metadata generation"""
        config = ForecastConfig()
        model = ManufacturingPatchTSTModel(config)
        model.initialize_model(num_input_channels=5)
        
        metadata = model.model_metadata
        assert 'num_input_channels' in metadata
        assert 'context_length' in metadata
        assert 'prediction_length' in metadata
        assert 'created_at' in metadata
        assert metadata['num_input_channels'] == 5


class TestModelTraining:
    """Test comprehensive model training functionality"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Reuse sample data fixture"""
        return TestTimeSeriesPreprocessing().sample_manufacturing_data(self)
    
    def test_trainer_initialization(self):
        """Test trainer initialization"""
        forecast_config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        trainer = ManufacturingForecastTrainer(forecast_config, training_config)
        
        assert trainer.forecast_config == forecast_config
        assert trainer.training_config == training_config
        assert hasattr(trainer, 'data_loader')
        assert isinstance(trainer.trained_models, dict)
        assert isinstance(trainer.performance_comparison, dict)
    
    def test_baseline_comparison_setup(self, sample_manufacturing_data):
        """Test baseline comparison functionality"""
        forecast_config = ForecastConfig(
            target_variables=['thickness_avg'],
            forecast_horizons=[15],
            lookback_window=60
        )
        training_config = PatchTSTTrainingConfig()
        
        trainer = ManufacturingForecastTrainer(forecast_config, training_config)
        
        # Create a simple mock model for testing baseline comparison
        class MockModel:
            def __init__(self):
                self.best_model_metrics = {'eval_loss': 0.1}
        
        mock_model = MockModel()
        
        # Test baseline comparison
        baseline_results = trainer._compare_with_baselines(
            mock_model, sample_manufacturing_data, 'thickness_avg'
        )
        
        assert isinstance(baseline_results, dict)
        assert 'target_variable' in baseline_results
        assert baseline_results['target_variable'] == 'thickness_avg'
    
    def test_improvement_validation(self):
        """Test 15% improvement validation logic"""
        forecast_config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        trainer = ManufacturingForecastTrainer(forecast_config, training_config)
        
        # Mock performance comparison data
        mock_performance = {
            'thickness_avg': {
                'baseline_comparison': {
                    'baseline_results': {
                        'horizon_15': {
                            'linear_regression': {
                                'improvement_vs_patchtst': 20.0  # 20% improvement
                            },
                            'persistence': {
                                'improvement_vs_patchtst': 18.0  # 18% improvement
                            }
                        }
                    }
                }
            },
            'scrap_rate': {
                'baseline_comparison': {
                    'baseline_results': {
                        'horizon_15': {
                            'linear_regression': {
                                'improvement_vs_patchtst': 10.0  # Only 10% improvement
                            }
                        }
                    }
                }
            }
        }
        
        validation_results = trainer.validate_15_percent_improvement(mock_performance)
        
        assert validation_results['thickness_avg'] is True  # Passes 15% threshold
        assert validation_results['scrap_rate'] is False   # Fails 15% threshold


class TestForecastingIntegration:
    """Test integration between components"""
    
    @pytest.fixture
    def sample_manufacturing_data(self):
        """Reuse sample data fixture"""
        return TestTimeSeriesPreprocessing().sample_manufacturing_data(self)
    
    def test_end_to_end_small_workflow(self, sample_manufacturing_data):
        """Test minimal end-to-end workflow"""
        # Use very small configuration for testing
        config = ForecastConfig(
            input_variables=['speed', 'temperature'],
            target_variables=['thickness_avg'],
            forecast_horizons=[5],  # Very short horizon
            lookback_window=30,     # Short lookback
            model_params={
                'd_model': 16,      # Minimal model
                'num_attention_heads': 2,
                'num_hidden_layers': 1
            }
        )
        
        # Create model
        model = ManufacturingPatchTSTModel(config)
        
        # Initialize architecture
        model.initialize_model(num_input_channels=2)
        
        # Test preprocessor
        preprocessor = model.preprocessor
        prepared_data = preprocessor.prepare_forecasting_data(
            sample_manufacturing_data.head(100), 'thickness_avg'
        )
        
        # Validate preprocessing worked
        assert prepared_data is not None
        assert len(prepared_data['train_data']['sequences']) > 0
    
    def test_configuration_consistency(self):
        """Test consistency between different configuration objects"""
        forecast_config = ForecastConfig()
        training_config = PatchTSTTrainingConfig()
        
        # Test that configurations are compatible
        assert forecast_config.forecast_horizons[0] > 0
        assert training_config.batch_size > 0
        assert training_config.validation_split + training_config.test_split < 1.0


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])