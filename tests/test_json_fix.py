#!/usr/bin/env python3
"""
Test JSON serialization fix for numpy types
"""

import json
import numpy as np
import logging
from dataclasses import dataclass
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MockStabilityValidationResult:
    """Mock version of StabilityValidationResult for testing"""
    is_stable: bool
    stability_score: float
    gradient_health: Dict[str, float]
    prediction_variance: float
    manufacturing_compliance: Dict[str, bool]
    improvement_over_baseline: float
    validation_details: Dict[str, Any]
    recommendations: List[str]

def test_json_serialization():
    """Test that the JSON serialization fixes work correctly"""
    
    logger.info("🧪 Testing JSON Serialization Fixes...")
    
    # Create test data with numpy types that cause serialization issues
    test_data = {
        'basic_bool': True,
        'numpy_bool': np.bool_(True),
        'basic_int': 42,
        'numpy_int32': np.int32(123),
        'numpy_int64': np.int64(456),
        'basic_float': 3.14,
        'numpy_float32': np.float32(2.71),
        'numpy_float64': np.float64(1.41),
        'numpy_array': np.array([1, 2, 3, 4, 5]),
        'nested_data': {
            'inner_numpy_bool': np.bool_(False),
            'inner_numpy_float': np.float64(9.81)
        }
    }
    
    # Create mock stability result
    stability_result = MockStabilityValidationResult(
        is_stable=np.bool_(True),
        stability_score=np.float64(0.85),
        gradient_health={'norm': np.float32(0.5)},
        prediction_variance=np.float64(0.1),
        manufacturing_compliance={'threshold': np.bool_(True)},
        improvement_over_baseline=np.float64(18.5),
        validation_details={'test': np.int32(100)},
        recommendations=['Test recommendation']
    )
    
    # Test the custom JSON serializer functions
    def convert_numpy_types(obj):
        """Convert numpy types to Python native types for JSON serialization"""
        # Handle numpy boolean types
        if hasattr(np, 'bool_') and isinstance(obj, np.bool_):
            return bool(obj)
        # Handle numpy integer types
        elif isinstance(obj, (np.integer, np.int8, np.int16, np.int32, np.int64, np.uint8, np.uint16, np.uint32, np.uint64)):
            return int(obj)
        # Handle numpy float types
        elif isinstance(obj, (np.floating, np.float16, np.float32, np.float64)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return str(obj)
    
    def convert_types_enhanced(obj):
        """Convert complex types to JSON serializable format"""
        # Handle numpy boolean types
        if hasattr(np, 'bool_') and isinstance(obj, np.bool_):
            return bool(obj)
        # Handle numpy integer types
        elif isinstance(obj, (np.integer, np.int8, np.int16, np.int32, np.int64, np.uint8, np.uint16, np.uint32, np.uint64)):
            return int(obj)
        # Handle numpy float types
        elif isinstance(obj, (np.floating, np.float16, np.float32, np.float64)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, '__dict__'):
            # Convert dataclass objects to dict
            return obj.__dict__
        return str(obj)
    
    try:
        # Test basic numpy types serialization
        logger.info("Testing basic numpy types...")
        json_str = json.dumps(test_data, default=convert_numpy_types)
        parsed_data = json.loads(json_str)
        logger.info("✅ Basic numpy types serialization successful")
        
        # Verify data integrity
        assert parsed_data['basic_bool'] == True
        assert parsed_data['numpy_bool'] == True
        assert parsed_data['basic_int'] == 42
        assert parsed_data['numpy_int32'] == 123
        assert abs(parsed_data['numpy_float32'] - 2.71) < 0.001  # Float precision tolerance
        assert parsed_data['numpy_array'] == [1, 2, 3, 4, 5]
        logger.info("✅ Data integrity verified")
        
        # Test stability result serialization
        logger.info("Testing StabilityValidationResult serialization...")
        stability_dict = {'stability_result': stability_result}
        json_str = json.dumps(stability_dict, default=convert_types_enhanced)
        parsed_stability = json.loads(json_str)
        logger.info("✅ StabilityValidationResult serialization successful")
        
        # Verify stability result data
        result_data = parsed_stability['stability_result']
        assert result_data['is_stable'] == True
        assert isinstance(result_data['stability_score'], float)
        assert result_data['stability_score'] == 0.85
        logger.info("✅ StabilityValidationResult data integrity verified")
        
        # Test attribute access (fix for .get() issue)
        logger.info("Testing attribute access on StabilityValidationResult...")
        is_stable = getattr(stability_result, 'is_stable', False)
        stability_score = getattr(stability_result, 'stability_score', 0.0)
        assert is_stable == True
        assert stability_score == 0.85
        logger.info("✅ Attribute access working correctly")
        
        logger.info("🎉 All JSON serialization tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ JSON serialization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    logger.info("=" * 60)
    logger.info("🔧 TESTING JSON SERIALIZATION FIXES")
    logger.info("=" * 60)
    
    success = test_json_serialization()
    
    logger.info("=" * 60)
    if success:
        logger.info("✅ JSON serialization fixes are working correctly!")
        logger.info("📝 Training reports should now generate without errors.")
        return 0
    else:
        logger.error("❌ JSON serialization fixes need more work.")
        return 1

if __name__ == "__main__":
    exit(main())