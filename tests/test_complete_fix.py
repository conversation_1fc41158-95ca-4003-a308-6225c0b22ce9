#!/usr/bin/env python3
"""
Test the complete fix with data collator and univariate wrapper
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_complete_fix():
    """Test the complete fix with data collator"""
    
    from src.forecasting.patchtst_model import (
        ManufacturingDataset, 
        ManufacturingPatchTSTModel,
        ManufacturingDataCollator
    )
    from src.forecasting.config import ForecastConfig, PatchTSTTrainingConfig
    import torch
    
    logger.info("🔧 Testing Complete PatchTST Fix...")
    
    # Create sample config
    forecast_config = ForecastConfig(
        input_variables=["thickness_thickness_avg", "thickness_thickness_uniformity", "speed_Speed", 
                        "stop_MPS Stop Duration", "fm_stack_Sheet Quantity", "fm_stack_Sheet Acceped"],
        target_variables=["thickness_thickness_avg"],
        forecast_horizons=[240],
        lookback_window=240,
        patch_size=16,
        patch_stride=16,
        model_params={
            "d_model": 64,
            "num_attention_heads": 4,
            "num_hidden_layers": 2,
            "dropout": 0.1
        }
    )
    
    training_config = PatchTSTTrainingConfig(
        batch_size=2,
        max_epochs=1,
        learning_rate=1e-4
    )
    
    # Create model
    model = ManufacturingPatchTSTModel(forecast_config, training_config)
    model._current_target_variable = "thickness_thickness_avg"
    model.initialize_model(6)
    
    logger.info("✅ Model initialized successfully")
    
    # Test with sample data
    sample_sequences = [
        {
            'features': [[float(i+j) for j in range(6)] for i in range(240)],
            'targets': {'horizon_240': [7.0 + i*0.1 for i in range(240)]}
        },
        {
            'features': [[float(i+j+10) for j in range(6)] for i in range(240)],
            'targets': {'horizon_240': [8.0 + i*0.1 for i in range(240)]}
        }
    ]
    
    # Create dataset and data collator
    dataset = ManufacturingDataset(sample_sequences, max_horizon=240)
    data_collator = ManufacturingDataCollator(target_channel_idx=0)
    
    # Create batch
    batch = [dataset[0], dataset[1]]
    collated_batch = data_collator(batch)
    
    logger.info(f"Collated batch shapes:")
    logger.info(f"  past_values: {collated_batch['past_values'].shape}")
    logger.info(f"  future_values: {collated_batch['future_values'].shape}")
    
    # Test model forward pass with collated batch
    model.model.train()
    
    # Test training forward pass (with future_values)
    outputs = model.model(
        past_values=collated_batch['past_values'],
        future_values=collated_batch['future_values']
    )
    
    logger.info(f"Training outputs:")
    if hasattr(outputs, 'loss') and outputs.loss is not None:
        logger.info(f"  Loss: {outputs.loss.item():.6f}")
        logger.info(f"✅ Training forward pass successful!")
    else:
        logger.warning("⚠️ No loss computed")
    
    if hasattr(outputs, 'prediction_outputs'):
        logger.info(f"  Prediction shape: {outputs.prediction_outputs.shape}")
    
    # Test inference forward pass (no future_values)
    model.model.eval()
    with torch.no_grad():
        inference_outputs = model.model(past_values=collated_batch['past_values'])
    
    logger.info(f"Inference outputs:")
    if hasattr(inference_outputs, 'prediction_outputs'):
        pred_shape = inference_outputs.prediction_outputs.shape
        logger.info(f"  Prediction shape: {pred_shape}")
        
        # Should be [batch_size, prediction_length] for univariate
        expected_shape = (2, 240)
        if pred_shape == expected_shape:
            logger.info("✅ Inference output shape is correct!")
            return True
        else:
            logger.error(f"❌ Wrong inference shape: got {pred_shape}, expected {expected_shape}")
            return False
    
    return False

def main():
    """Main test function"""
    try:
        success = test_complete_fix()
        if success:
            logger.info("🎉 Complete fix test passed! Training should work now.")
            logger.info("📝 Try running: python train.py thickness_thickness_avg")
            return 0
        else:
            logger.error("❌ Fix test failed.")
            return 1
    except Exception as e:
        logger.error(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())