"""
Comprehensive test suite for enhanced manufacturing data loader.

Tests the complete 10-step enhanced unified table creation algorithm
with real manufacturing data scenarios following PRP specifications.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.loader import ManufacturingDataLoader, DataValidationResult


class TestEnhancedUnifiedTableCreation:
    """Test suite for enhanced unified table creation with stack-level intelligence."""
    
    @pytest.fixture
    def sample_data_dir(self, tmp_path):
        """Create sample manufacturing data for testing."""
        data_dir = tmp_path / "test_data"
        data_dir.mkdir()
        
        # Create sample speed data (base dataset)
        speed_data = pd.DataFrame({
            'Plant': ['Plant1'] * 100,
            'Work Center/Resource': ['SM270'] * 50 + ['SM300'] * 50,
            'Log Date': ['2025.01.15'] * 100,
            'Log Time': [f'{h:02d}:{m:02d}:00' for h in range(10, 20) for m in range(0, 60, 6)],
            'Speed': np.random.normal(90, 5, 100),  # Speed around 90 m/min
            'Rev Speed': np.random.normal(85, 3, 100),
            'Update Date': ['2025.01.15'] * 100,
            'Update Time': ['12:00:00'] * 100,
            'Update Name': ['System'] * 100,
            'Update Data Time': ['2025.01.15 12:00:00'] * 100,
            'MPS Update Date Time': ['2025.01.15 12:00:00'] * 100,
            'MPS Log Date Time': ['2025.01.15 12:00:00'] * 100,
            'MPS Log Date Time.1': ['2025.01.15 12:00:00'] * 100
        })
        speed_data.to_csv(data_dir / 'speed.csv', index=False)
        
        # Create sample SM stack data
        sm_stack_data = pd.DataFrame({
            'Stack Number': [f'STK{i:04d}' for i in range(1, 11)],
            'First Sheet Date Time': [
                '2025.01.15 10:00:00', '2025.01.15 11:30:00', '2025.01.15 13:00:00',
                '2025.01.15 14:30:00', '2025.01.15 16:00:00', '2025.01.15 17:30:00',
                '2025.01.15 19:00:00', '2025.01.15 20:30:00', '2025.01.15 22:00:00',
                '2025.01.15 23:30:00'
            ],
            'Last Sheet Date Time': [
                '2025.01.15 11:15:00', '2025.01.15 12:45:00', '2025.01.15 14:15:00',
                '2025.01.15 15:45:00', '2025.01.15 17:15:00', '2025.01.15 18:45:00',
                '2025.01.15 20:15:00', '2025.01.15 21:45:00', '2025.01.15 23:15:00',
                '2025.01.16 00:45:00'
            ],
            'Sheet Cut': [1200, 1150, 1300, 1100, 1250, 1180, 1220, 1160, 1280, 1140],
            'Good Sheets': [1150, 1100, 1250, 1050, 1200, 1130, 1170, 1110, 1230, 1090],
            'Scrap%': ['4.2%', '4.3%', '3.8%', '4.5%', '4.0%', '4.2%', '4.1%', '4.3%', '3.9%', '4.4%'],
            'Reject Reason': ['Normal'] * 10,
            'Production Order': [f'150861{i}/1' for i in range(1, 11)],
            'Product': [f'7777{i:03d} Test Product {i}' for i in range(1, 11)],
            'Work Center/Resource': ['SM270'] * 10
        })
        sm_stack_data.to_csv(data_dir / 'sm_stack.csv', index=False)
        
        # Create sample FM stack data
        fm_stack_data = pd.DataFrame({
            'MPS ID': [f'FM{i:04d}' for i in range(1, 11)],
            'Branded': ['Yes'] * 10,
            'On-Load': [
                '2025.01.15 12:00:00', '2025.01.15 13:30:00', '2025.01.15 15:00:00',
                '2025.01.15 16:30:00', '2025.01.15 18:00:00', '2025.01.15 19:30:00',
                '2025.01.15 21:00:00', '2025.01.15 22:30:00', '2025.01.16 00:00:00',
                '2025.01.16 01:30:00'
            ],
            'Off-Load': [
                '2025.01.15 12:45:00', '2025.01.15 14:15:00', '2025.01.15 15:45:00',
                '2025.01.15 17:15:00', '2025.01.15 18:45:00', '2025.01.15 20:15:00',
                '2025.01.15 21:45:00', '2025.01.15 23:15:00', '2025.01.16 00:45:00',
                '2025.01.16 02:15:00'
            ],
            'Production Order': [f'150861{i}/1' for i in range(1, 11)],
            'TM Index': [1] * 10,
            'Potential Sheets': [1200, 1150, 1300, 1100, 1250, 1180, 1220, 1160, 1280, 1140],
            'Products': [1] * 10,
            'Ok': [1145, 1095, 1245, 1045, 1195, 1125, 1165, 1105, 1225, 1085],
            'Rej.': [5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
            'Product Description': [f'Test Product {i}' for i in range(1, 11)]
        })
        fm_stack_data.to_csv(data_dir / 'fm_stack.csv', index=False)
        
        # Create sample stop data
        stop_data = pd.DataFrame({
            'Stop ID': [f'STP{i:03d}' for i in range(1, 6)],
            'Plant': ['Plant1'] * 5,
            'Work Center/Resource': ['SM270'] * 5,
            'Stop Date': ['2025.01.15'] * 5,
            'Stop Time': ['10:30:00', '12:15:00', '14:45:00', '16:20:00', '18:10:00'],
            'Restart Date': ['2025.01.15'] * 5,
            'Restart Time': ['10:45:00', '12:30:00', '15:00:00', '16:35:00', '18:25:00'],
            'MPS Stop Duration': [15, 15, 15, 15, 15],  # 15 minutes each
            'Stoppage Reason': ['Maintenance', 'Quality Check', 'Material Change', 'Cleaning', 'Adjustment'],
            'MPS Category': ['Planned'] * 5,
            'MPS Type': ['Maintenance'] * 5,
            'Felt Code': ['FC001'] * 5,
            'Work Order': ['WO001'] * 5,
            'Production Order': [f'150861{i}/1' for i in range(1, 6)],
            'Line': ['Line1'] * 5,
            'Section Code': ['SEC001'] * 5,
            'Section': ['Section 1'] * 5,
            'POS': [1] * 5,
            'Work Group': ['WG001'] * 5,
            'Stoppage Reason LTXT': ['Planned maintenance stop'] * 5,
            'Update Date': ['2025.01.15'] * 5,
            'Update Time': ['20:00:00'] * 5,
            'Update Name': ['System'] * 5,
            'Work Center': ['SM270'] * 5,
            'Employee': ['EMP001'] * 5,
            'Count': [1] * 5,
            'MPS Restart Date Time': ['2025.01.15 20:00:00'] * 5,
            'MPS Stop Date Time': ['2025.01.15 19:45:00'] * 5,
            'Restart Timestamp': ['2025.01.15 20:00:00'] * 5,
            'Stop Timestamp': ['2025.01.15 19:45:00'] * 5,
            'Stop Duration': [15] * 5,
            'Stoppage Category': ['Planned'] * 5,
            'Stoppage Type': ['Maintenance'] * 5,
            'Section Description': ['Production Section 1'] * 5
        })
        stop_data.to_csv(data_dir / 'stop.csv', index=False)
        
        # Create sample VM capacity data
        vm_capacity_data = pd.DataFrame({
            'Vitual Material Description': [f'Product {i} Description' for i in range(1, 11)],
            'SAP Code': [f'7777{i:03d}' for i in range(1, 11)],
            'Sheet Machine': ['SM270'] * 10,
            'Finishing Machine': ['FM001'] * 10,
            'OffRoller Factor': [1.0] * 10,
            'Design Capacity': [100, 95, 105, 90, 102, 98, 101, 96, 104, 92],
            'Design Felt Speed': [88, 85, 92, 82, 90, 87, 89, 84, 91, 83],
            'Design Rev Speed': [83, 80, 87, 77, 85, 82, 84, 79, 86, 78],
            'Active': ['Yes'] * 10
        })
        vm_capacity_data.to_csv(data_dir / 'vm_capacity.csv', index=False)
        
        return data_dir
    
    @pytest.fixture
    def sample_config(self, sample_data_dir, tmp_path):
        """Create sample configuration for testing."""
        config = {
            "description": "Test configuration for enhanced loader",
            "version": "2.0-enhanced",
            "data_sources": {
                "speed": {
                    "file_path": str(sample_data_dir / "speed.csv"),
                    "description": "Production line speed measurements",
                    "key_columns": ["Work Center/Resource", "Log Date", "Log Time", "Speed"],
                    "timestamp_columns": {"log_date": "Log Date", "log_time": "Log Time"},
                    "data_type": "continuous"
                },
                "stop": {
                    "file_path": str(sample_data_dir / "stop.csv"),
                    "description": "Machine stoppage events",
                    "key_columns": ["Work Center/Resource", "Stop Date", "Stop Time"],
                    "timestamp_columns": {"stop_date": "Stop Date", "stop_time": "Stop Time", 
                                        "restart_date": "Restart Date", "restart_time": "Restart Time"},
                    "data_type": "event"
                },
                "sm_stack": {
                    "file_path": str(sample_data_dir / "sm_stack.csv"),
                    "description": "Sheet machine stack data",
                    "key_columns": ["Stack Number", "Production Order", "Scrap%"],
                    "timestamp_columns": {},
                    "data_type": "batch"
                },
                "fm_stack": {
                    "file_path": str(sample_data_dir / "fm_stack.csv"),
                    "description": "Forming machine stack data",
                    "key_columns": ["MPS ID", "Production Order"],
                    "timestamp_columns": {},
                    "data_type": "batch"
                },
                "vm_capacity": {
                    "file_path": str(sample_data_dir / "vm_capacity.csv"),
                    "description": "VM capacity specifications",
                    "key_columns": ["SAP Code", "Design Capacity"],
                    "timestamp_columns": {},
                    "data_type": "reference"
                }
            },
            "unified_schema": {
                "base_table": "speed",
                "timestamp_column": "timestamp",
                "work_center_column": "work_center"
            }
        }
        
        config_path = tmp_path / "test_config.json"
        import json
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        return config_path
    
    def test_enhanced_loader_initialization(self, sample_config):
        """Test enhanced loader initialization with configuration."""
        loader = ManufacturingDataLoader(str(sample_config))
        
        assert loader.config is not None
        assert len(loader.config['data_sources']) == 5
        assert 'speed' in loader.config['data_sources']
        assert 'sm_stack' in loader.config['data_sources']
        assert 'fm_stack' in loader.config['data_sources']
    
    def test_stack_data_preparation(self, sample_config):
        """Test SM and FM stack data preparation functions."""
        loader = ManufacturingDataLoader(str(sample_config))
        loader.load_all_data_sources()
        
        # Test SM stack preparation
        sm_stack_df = loader.loaded_data['sm_stack']
        prepared_sm_df = loader.prepare_sm_stack_data(sm_stack_df)
        
        # Verify enhanced SM fields
        assert 'sm_duration_minutes' in prepared_sm_df.columns
        assert 'sm_scrap_pct' in prepared_sm_df.columns
        assert 'base_production_order' in prepared_sm_df.columns
        assert 'sm_production_rate' in prepared_sm_df.columns
        assert 'sm_quality_efficiency' in prepared_sm_df.columns
        
        # Verify data quality
        assert prepared_sm_df['sm_scrap_pct'].max() <= 100
        assert prepared_sm_df['sm_scrap_pct'].min() >= 0
        assert prepared_sm_df['sm_duration_minutes'].min() > 0
        
        # Test FM stack preparation
        fm_stack_df = loader.loaded_data['fm_stack']
        prepared_fm_df = loader.prepare_fm_stack_data(fm_stack_df)
        
        # Verify enhanced FM fields
        assert 'fm_duration_minutes' in prepared_fm_df.columns
        assert 'fm_reject_pct' in prepared_fm_df.columns
        assert 'base_production_order' in prepared_fm_df.columns
        assert 'fm_processing_rate' in prepared_fm_df.columns
        assert 'fm_quality_efficiency' in prepared_fm_df.columns
        
        # Verify temporal consistency
        valid_durations = prepared_fm_df['fm_duration_minutes'] > 0
        assert valid_durations.sum() > 0  # At least some valid durations
    
    def test_speed_aggregation_engine(self, sample_config):
        """Test speed aggregation per manufacturing stack."""
        loader = ManufacturingDataLoader(str(sample_config))
        loader.load_all_data_sources()
        
        # Prepare SM stack data
        sm_stack_df = loader.loaded_data['sm_stack']
        prepared_sm_df = loader.prepare_sm_stack_data(sm_stack_df)
        
        # Test speed aggregation
        speed_df = loader.loaded_data['speed']
        enhanced_sm_df = loader.aggregate_speed_data(prepared_sm_df, speed_df)
        
        # Verify speed aggregation fields
        expected_speed_cols = ['speed_avg', 'speed_std', 'speed_cv', 'speed_min', 'speed_max',
                              'speed_readings_count', 'speed_range', 'speed_p10', 'speed_p90']
        
        for col in expected_speed_cols:
            assert col in enhanced_sm_df.columns
        
        # Verify reasonable speed statistics
        assert enhanced_sm_df['speed_avg'].between(70, 120).all()  # Reasonable speed range
        assert enhanced_sm_df['speed_std'].min() >= 0  # Standard deviation is non-negative
        assert enhanced_sm_df['speed_min'].le(enhanced_sm_df['speed_max']).all()  # Min <= Max
    
    def test_stoppage_analysis_engine(self, sample_config):
        """Test stoppage analysis with production impact."""
        loader = ManufacturingDataLoader(str(sample_config))
        loader.load_all_data_sources()
        
        # Prepare SM stack data
        sm_stack_df = loader.loaded_data['sm_stack']
        prepared_sm_df = loader.prepare_sm_stack_data(sm_stack_df)
        
        # Test stoppage aggregation
        stop_df = loader.loaded_data['stop']
        enhanced_sm_df = loader.aggregate_stoppage_data(prepared_sm_df, stop_df)
        
        # Verify stoppage analysis fields
        expected_stoppage_cols = ['stops_during_production', 'total_stop_duration_minutes',
                                 'production_efficiency_pct', 'primary_stoppage_reason',
                                 'restart_impact_score', 'max_single_stop_duration']
        
        for col in expected_stoppage_cols:
            assert col in enhanced_sm_df.columns
        
        # Verify reasonable stoppage statistics
        assert enhanced_sm_df['production_efficiency_pct'].between(0, 100).all()
        assert enhanced_sm_df['total_stop_duration_minutes'].min() >= 0
        assert enhanced_sm_df['stops_during_production'].min() >= 0
    
    def test_stack_matching_algorithm(self, sample_config):
        """Test SM-FM stack matching with temporal validation."""
        loader = ManufacturingDataLoader(str(sample_config))
        loader.load_all_data_sources()
        
        # Prepare both SM and FM stack data
        sm_stack_df = loader.loaded_data['sm_stack']
        prepared_sm_df = loader.prepare_sm_stack_data(sm_stack_df)
        
        fm_stack_df = loader.loaded_data['fm_stack']
        prepared_fm_df = loader.prepare_fm_stack_data(fm_stack_df)
        
        # Test stack matching
        matched_sm_df = loader.match_sm_fm_stacks(prepared_sm_df, prepared_fm_df)
        
        # Verify matching fields
        expected_match_cols = ['has_fm_match', 'fm_production_order', 'fm_on_load_timestamp',
                              'sm_to_fm_gap_minutes', 'match_quality_score']
        
        for col in expected_match_cols:
            assert col in matched_sm_df.columns
        
        # Verify matching logic
        matched_records = matched_sm_df[matched_sm_df['has_fm_match'] == True]
        if len(matched_records) > 0:
            # All matched records should have positive time gaps
            assert matched_records['sm_to_fm_gap_minutes'].min() >= 0
            # Match quality scores should be between 0-100
            assert matched_records['match_quality_score'].between(0, 100).all()
    
    def test_enhanced_unified_table_creation(self, sample_config):
        """Test the complete enhanced unified table creation algorithm."""
        loader = ManufacturingDataLoader(str(sample_config))
        
        # Create enhanced unified table
        unified_df = loader.create_unified_table()
        
        # Verify basic structure
        assert len(unified_df) > 0
        assert len(unified_df.columns) >= 51  # Should have 51+ columns as per PRP
        
        # Verify core columns
        core_columns = ['timestamp', 'work_center', 'Speed']
        for col in core_columns:
            assert col in unified_df.columns
        
        # Verify enhanced SM stack features
        enhanced_sm_cols = ['sm_scrap_pct', 'sm_production_rate', 'speed_avg', 'production_efficiency_pct']
        for col in enhanced_sm_cols:
            if col in unified_df.columns:  # Some may not be present if no stack data
                assert not unified_df[col].isna().all()  # Should have some non-null values
        
        # Verify enhanced temporal features
        temporal_cols = ['production_shift', 'manufacturing_state', 'day_type']
        for col in temporal_cols:
            if col in unified_df.columns:
                assert not unified_df[col].isna().all()
    
    def test_manufacturing_validation_framework(self, sample_config):
        """Test comprehensive manufacturing data validation."""
        loader = ManufacturingDataLoader(str(sample_config))
        unified_df = loader.create_unified_table()
        
        # Test validation framework
        validation_result = loader.validate_unified_table(unified_df)
        
        # Verify validation result structure
        assert isinstance(validation_result, DataValidationResult)
        assert hasattr(validation_result, 'is_valid')
        assert hasattr(validation_result, 'quality_score')
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert hasattr(validation_result, 'recommendations')
        
        # Verify quality score range
        assert 0 <= validation_result.quality_score <= 100
        
        # Log validation results for debugging
        print(f"\\nValidation Results:")
        print(f"Valid: {validation_result.is_valid}")
        print(f"Quality Score: {validation_result.quality_score}%")
        print(f"Errors: {len(validation_result.errors)}")
        print(f"Warnings: {len(validation_result.warnings)}")
        
        if validation_result.errors:
            print("Errors:", validation_result.errors)
        if validation_result.warnings:
            print("Warnings:", validation_result.warnings[:3])  # Show first 3 warnings
    
    def test_performance_with_large_dataset(self, sample_config):
        """Test performance with larger dataset (basic benchmark)."""
        import time
        
        loader = ManufacturingDataLoader(str(sample_config))
        
        # Measure unified table creation time
        start_time = time.time()
        unified_df = loader.create_unified_table()
        creation_time = time.time() - start_time
        
        # Measure validation time
        start_time = time.time()
        validation_result = loader.validate_unified_table(unified_df)
        validation_time = time.time() - start_time
        
        # Performance assertions (should complete within reasonable time)
        assert creation_time < 30  # Should complete within 30 seconds
        assert validation_time < 10  # Validation should be fast
        
        print(f"\\nPerformance Metrics:")
        print(f"Unified table creation: {creation_time:.2f}s")
        print(f"Validation: {validation_time:.2f}s")
        print(f"Rows processed: {len(unified_df):,}")
        print(f"Columns created: {len(unified_df.columns)}")
    
    def test_backward_compatibility(self, sample_config):
        """Test backward compatibility with existing API."""
        # Test that the standalone function still works
        from src.data.loader import create_unified_table
        
        loader = ManufacturingDataLoader(str(sample_config))
        loader.load_all_data_sources()
        
        # Test backward compatible function call
        unified_df = create_unified_table(
            speed_df=loader.loaded_data.get('speed'),
            stop_df=loader.loaded_data.get('stop'),
            sm_stack_df=loader.loaded_data.get('sm_stack'),
            fm_stack_df=loader.loaded_data.get('fm_stack'),
            vm_capacity_df=loader.loaded_data.get('vm_capacity')
        )
        
        # Verify it produces a valid result
        assert len(unified_df) > 0
        assert 'timestamp' in unified_df.columns
        assert 'Speed' in unified_df.columns


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v", "--tb=short"])