#!/usr/bin/env python3
"""
Test CLI initialization without running the interactive loop.
"""

import sys
sys.path.append('src')

def test_cli_init():
    """Test CLI initialization."""
    try:
        from cli import ManufacturingCorrelationCLI
        
        print("Testing CLI initialization...")
        cli = ManufacturingCorrelationCLI('test-data')
        
        print("✓ CLI initialized successfully")
        print(f"✓ Data directory: {cli.data_dir}")
        print(f"✓ Loader configured with {len(cli.loader.config['data_sources'])} data sources:")
        
        for source_name in cli.loader.config['data_sources'].keys():
            print(f"  - {source_name}")
        
        # Test basic functionality
        print("\nTesting basic CLI methods...")
        
        # Test load_data method
        try:
            cli._load_data()
            print(f"✓ Data loading successful: {len(cli.loaded_data)} datasets loaded")
        except Exception as e:
            print(f"⚠ Data loading issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ CLI initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cli_init()
    if success:
        print("\n🎉 CLI is ready for interactive use!")
    else:
        print("\n❌ CLI has initialization issues.")