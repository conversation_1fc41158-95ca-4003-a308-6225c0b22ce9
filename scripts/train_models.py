#!/usr/bin/env python3
"""
Manufacturing PatchTST Model Training Script

Run this script as a module from the project root:
    python -m scripts.train_models --help
    python -m scripts.train_models --target thickness_thickness_avg
    python -m scripts.train_models --all-targets
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

from src.forecasting.trainer import ManufacturingForecastTrainer
from src.forecasting.config import load_config_from_file

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('training.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Set up the training environment and validate requirements."""
    logger.info("Setting up training environment...")
    
    # Validate data directory
    data_dir = Path("test-data")
    if not data_dir.exists():
        logger.error(f"Data directory {data_dir} not found!")
        return False
    
    required_files = ["thickness.csv", "speed.csv", "fm_stack.csv", "sm_stack.csv", "stop.csv"]
    missing_files = [f for f in required_files if not (data_dir / f).exists()]
    if missing_files:
        logger.error(f"Missing required data files: {missing_files}")
        return False
    
    # Create models directory if it doesn't exist
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    logger.info("✅ Environment setup complete")
    return True

def train_models(
    target_variable: Optional[str] = None,
    config_path: Optional[str] = None,
    use_stability_features: bool = True,
    use_transfer_learning: bool = True,
    data_path: str = "test-data"
):
    """Train PatchTST models."""
    try:
        # Load configuration
        config_file = config_path or "config/forecasting_config.json"
        logger.info(f"Loading configuration from {config_file}")
        forecast_config, training_config = load_config_from_file(config_file)
        
        # Update config for single target if specified
        if target_variable:
            if target_variable not in forecast_config.target_variables:
                logger.error(f"Target '{target_variable}' not in config: {forecast_config.target_variables}")
                return False
            forecast_config.target_variables = [target_variable]
            logger.info(f"Training single target: {target_variable}")
        else:
            logger.info(f"Training all targets: {forecast_config.target_variables}")
        
        # Create trainer
        logger.info("Creating enhanced trainer...")
        trainer = ManufacturingForecastTrainer(
            forecast_config=forecast_config,
            training_config=training_config,
            use_stability_features=use_stability_features,
            use_transfer_learning=use_transfer_learning
        )
        
        # Train models
        logger.info("Starting training...")
        trained_models = trainer.train_all_target_variables(data_path=data_path)
        
        # Report results
        if trained_models:
            logger.info(f"✅ Successfully trained {len(trained_models)} model(s)")
            
            # Display results
            for target, model in trained_models.items():
                logger.info(f"📊 {target}:")
                logger.info(f"   Model saved: {model.model_save_path}")
                
                # Performance metrics
                if target in trainer.performance_comparison:
                    perf = trainer.performance_comparison[target]
                    
                    # Validation results
                    val_results = perf.get('validation_results', {})
                    if 'horizon_results' in val_results:
                        for horizon, metrics in val_results['horizon_results'].items():
                            mse = metrics.get('mse', 'N/A')
                            mae = metrics.get('mae', 'N/A')
                            logger.info(f"   {horizon}: MSE={mse:.4f}, MAE={mae:.4f}")
                    
                    # Stability results
                    stability = perf.get('stability_validation', {})
                    is_stable = stability.get('is_stable', False)
                    stability_score = stability.get('stability_score', 0.0)
                    logger.info(f"   Stability: {'✅ STABLE' if is_stable else '❌ UNSTABLE'} (score: {stability_score:.3f})")
            
            # Improvement validation
            improvement_results = trainer.validate_15_percent_improvement(trainer.performance_comparison)
            overall_passed = all(improvement_results.values())
            logger.info(f"15% improvement validation: {'✅ ALL PASSED' if overall_passed else '❌ SOME FAILED'}")
            
            for target, passed in improvement_results.items():
                status = "✅ PASS" if passed else "❌ FAIL"
                logger.info(f"   {target}: {status}")
            
            return True
        else:
            logger.error("❌ No models were trained successfully")
            return False
            
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Train PatchTST forecasting models for manufacturing data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m scripts.train_models --target thickness_thickness_avg
  python -m scripts.train_models --all-targets
  python -m scripts.train_models --config custom_config.json
        """
    )
    
    # Target selection
    target_group = parser.add_mutually_exclusive_group(required=True)
    target_group.add_argument(
        "--target", 
        type=str,
        help="Train model for specific target variable"
    )
    target_group.add_argument(
        "--all-targets",
        action="store_true",
        help="Train models for all configured target variables"
    )
    
    # Configuration options
    parser.add_argument(
        "--config",
        type=str,
        help="Path to custom configuration file"
    )
    parser.add_argument(
        "--data-path",
        type=str,
        default="test-data",
        help="Path to manufacturing data directory"
    )
    
    # Feature toggles
    parser.add_argument(
        "--disable-stability",
        action="store_true",
        help="Disable stability enhancements"
    )
    parser.add_argument(
        "--disable-transfer-learning",
        action="store_true",
        help="Disable transfer learning"
    )
    
    # Logging options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("🚀 Starting PatchTST Manufacturing Model Training")
    logger.info("=" * 60)
    
    # Setup environment
    if not setup_environment():
        logger.error("❌ Environment setup failed")
        sys.exit(1)
    
    # Determine settings
    target = args.target if not args.all_targets else None
    use_stability = not args.disable_stability
    use_transfer = not args.disable_transfer_learning
    
    logger.info(f"🔧 Training configuration:")
    logger.info(f"   Target: {target or 'ALL TARGETS'}")
    logger.info(f"   Stability: {'✅ ENABLED' if use_stability else '❌ DISABLED'}")
    logger.info(f"   Transfer learning: {'✅ ENABLED' if use_transfer else '❌ DISABLED'}")
    logger.info(f"   Data path: {args.data_path}")
    logger.info("=" * 60)
    
    # Train models
    success = train_models(
        target_variable=target,
        config_path=args.config,
        use_stability_features=use_stability,
        use_transfer_learning=use_transfer,
        data_path=args.data_path
    )
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 Training completed successfully!")
    else:
        logger.error("❌ Training failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()