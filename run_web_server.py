#!/usr/bin/env python3
"""
Manufacturing Correlation Analysis Web Server Launcher

Starts the FastAPI web server for the manufacturing correlation analysis UI
"""

import sys
import os
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Start the web server"""
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. Consider activating venv first.")
    
    # Check for required environment variables
    env_file = project_root / '.env'
    if not env_file.exists():
        print("⚠️  Warning: .env file not found. Some features may not work.")
        print("   Create .env file with API keys (see .env.example)")
    
    # Check if data directory exists
    data_dir = project_root / 'test-data'
    if not data_dir.exists():
        print("⚠️  Warning: test-data directory not found.")
        print(f"   Expected: {data_dir}")
    
    print("🚀 Starting Manufacturing Correlation Analysis Web Server...")
    print("📊 Frontend: http://localhost:3000 (if React dev server is running)")
    print("🔧 API: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/docs")
    print()
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        # Start the FastAPI server
        uvicorn.run(
            "src.api.server:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            reload_dirs=[str(project_root / "src")],
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()