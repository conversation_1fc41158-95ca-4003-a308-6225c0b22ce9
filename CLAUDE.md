### 🔄 Project Awareness & Context
- **Always read `PLANNING.md`** at the start of a new conversation to understand the project's architecture, goals, style, and constraints.
- **Check `TASK.md`** before starting a new task. If the task isn't listed, add it with a brief description and today's date.
- **Review `INITIAL.md`** to understand the industrial data analysis requirements and multi-agent system architecture.
- **Use consistent naming conventions, file structure, and architecture patterns** as described in `PLANNING.md`.
- **Use venv** (the virtual environment) whenever executing Python commands, including for unit tests.

### 🧱 Code Structure & Modularity
- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.
- **Organize code into clearly separated modules**, grouped by feature or responsibility.
  For agents this looks like:
    - `agent.py` - Main agent definition and execution logic 
    - `tools.py` - Tool functions used by the agent 
    - `prompts.py` - System prompts
  For data processing:
    - `pipeline.py` - Data ingestion and transformation logic
    - `features.py` - Feature engineering functions
    - `models.py` - ML model definitions and training
- **Use clear, consistent imports** (prefer relative imports within packages).
- **Use python_dotenv and load_dotenv()** for environment variables.

### 📊 Data Processing & Analysis
- **Always validate industrial sensor data** before processing (check for missing values, outliers, timestamp gaps).
- **Maintain data lineage** - document transformations applied to raw sensor data.
- **Use pandas for time-series alignment** with proper handling of different frequencies (event-based vs continuous).
- **Create reproducible data pipelines** with clear input/output specifications.
- **Cache processed datasets** to avoid recomputing expensive transformations.
- **📁 Multi-Dataset Support**: Default data loader now uses `test_data_3_to_6_2025` (501K+ records) with backward compatibility for `test-data` (336K+ records).
- **🔄 Auto-Format Detection**: Data loader automatically detects and handles both `YYYY-MM-DD` and `YYYY.MM.DD` date formats.
- **🏷️ Material Validation**: Ensure all unified dataset rows have material information for downstream analysis by material type.

### 🤖 Agent Development
- **✅ Multi-Method Correlation Agent**: Production-ready with 262K+ record validation, Pearson/Spearman/Kendall support, and intelligent method selection
- **🔬 Multi-Method Analysis**: Support all three correlation methods (Pearson, Spearman, Kendall) with comparative analysis and method convergence scoring
- **🧠 Intelligent Method Selection**: Implement data distribution assessment (normality, outliers, linearity) for optimal method recommendation
- **📊 Method Convergence**: Calculate convergence scores across methods and provide robustness analysis with bootstrap sampling
- **🎯 High-Precision Analysis**: Use 8-decimal internal precision, 6-decimal display, scientific notation for small p-values
- **📏 Thickness Integration**: Agents understand thickness = average of Sensor 01-10 with uniformity calculations
- **🎨 Complete Visualization Suite**: 5 visualization tools fully integrated with correlation agent for automatic visualization generation
- **🔮 Advanced Forecasting System (Phase 3.1)**: PatchTST-based forecasting with stability enhancements, transfer learning, and manufacturing intelligence
- **✅ Stability-Enhanced Training**: Implement gradient clipping, UnitNorm layers, and comprehensive stability validation
- **🚀 Transfer Learning**: IBM Granite model integration with adaptive fine-tuning strategies
- **🛡️ Manufacturing Compliance**: Automatic validation of 15% improvement requirement and manufacturing-specific metrics
- **Research Agent** should focus on statistical correlations and causal discovery using established methods (PCMCI).
- **RCA Agent** must only provide insights based on provided data and causal graphs - no speculation.
- **Use Pydantic AI** for agent implementation with proper type hints and validation.
- **Critical**: Use `output_type` instead of deprecated `result_type` in PydanticAI agents
- **Pattern**: Load environment variables with `load_dotenv(override=True)` for reliable configuration
- **⚠️ Strict Configuration**: All required environment variables must be set - the system will fail with clear error messages instead of using fallback values
- **Implement proper error handling** for API failures and data anomalies.
- **Log all agent decisions and outputs** for audit trails.

### 🧪 Testing & Reliability
- **Always create Pytest unit tests for new features** (functions, classes, routes, etc).
- **After updating any logic**, check whether existing unit tests need to be updated. If so, do it.
- **Tests should live in a `/tests` folder** mirroring the main app structure.
  - Include at least:
    - 1 test for expected use
    - 1 edge case
    - 1 failure case
- **Test with real data samples** from `test-data/` folder to ensure compatibility.
- **Use REAL API KEYS in tests** - no mocking for LLM services to ensure production readiness.
- **✅ Phase 2.1 Complete**: 73 comprehensive tests implemented with real API integration, complete visualization suite, and robust error handling.
- **🔬 Multi-Method Testing Requirements**:
  - Test all three correlation methods (Pearson, Spearman, Kendall) in every test suite
  - Validate method convergence scores and robustness metrics
  - Test intelligent method selection with different data distributions
  - Include bootstrap sampling validation in robustness tests
  - Verify Pydantic validation for correlation ranges (-1 to 1) and p-values (0 to 1)
- **🎨 Visualization Testing Requirements**:
  - Test all 5 visualization tools with agent integration
  - Validate PNG and HTML output generation
  - Test side-by-side multi-method heatmaps and dashboards
  - Verify agent automatically calls visualization tools when appropriate
- **🔮 Forecasting Testing Requirements (Phase 3.1)**:
  - Test PatchTST model training and inference with manufacturing data
  - Validate stability enhancements (gradient clipping, UnitNorm layers)
  - Test transfer learning with IBM Granite model integration
  - Verify manufacturing compliance (15% improvement validation)
  - Test multi-horizon forecasting capabilities (15min, 1h, 4h, 24h)
  - Validate forecasting agent tools with real API integration

### 🔧 Critical Debugging & Error Handling Patterns (Phase 2.1 Lessons + Latest Production Fixes)

#### **Latest Production Fixes (Data Loading)**
- **✅ Pandas DateTime Warning Elimination**: Always use `parse_single_datetime()` and `parse_datetime()` functions instead of `pd.to_datetime(..., errors='coerce')` without format specification
- **✅ Explicit Format Specification**: Use format patterns list in `parse_single_datetime()` to try multiple formats before pandas inference
- **✅ FutureWarning Prevention**: Always use `pd.to_numeric(..., errors='coerce')` for mixed string/numeric data before dtype assignment
- **✅ Clean Enterprise Deployment**: Ensure zero warnings during data loading and processing for production environments
- **✅ Production-Ready Error Handling**: Implement robust type conversion patterns that eliminate deprecation warnings

#### **Production Issue Resolution Patterns**
- **🖥️ GUI Threading Issues**: Always use `matplotlib.use('Agg')` before ANY matplotlib imports in server/agent contexts
- **📊 Data Structure Validation**: Create targeted validation functions - don't use generic validators on mixed data types
- **🔢 Display vs Calculation Separation**: Keep numeric values for calculations, format only for display output
- **🛡️ None Value Safety**: Add explicit None/NoneType checks before mathematical operations in visualization functions
- **🎯 Type-Specific Error Handling**: Use different validation approaches for MultiMethodCorrelationResult objects vs dictionaries

#### **Advanced Error Prevention**
- **Safe Data Access Pattern**: Always use `safe_get_dict_value()` for mixed object/dictionary access
- **P-value Formatting Strategy**: Maintain both `p_value` (numeric) and `p_value_formatted` (string) in data structures
- **Visualization Robustness**: Add fallback values (0.0) for None correlations before plotting operations
- **Backend Safety Utilities**: Create `backend_utils.py` with environment detection and safe plotting functions
- **Targeted Validation**: Use `validate_correlation_results_only()` for actual correlation data, not response metadata

#### **Production Deployment Checklist**
- ✅ Matplotlib backend configured for headless operation (`matplotlib.use('Agg')`)
- ✅ All data structure access uses safe methods with type checking
- ✅ P-value formatting handles extremely small values (< 1e-15) properly
- ✅ Visualization functions include None checks before mathematical operations
- ✅ Error messages include type information and data structure details for debugging
- ✅ String vs float comparisons eliminated by maintaining separate numeric/formatted values

#### **Cross-Platform Compatibility**
- **macOS**: Fixed NSWindow threading issues with proper matplotlib backend configuration
- **Linux/Windows**: Ensured consistent behavior across different OS environments
- **Container Deployment**: Headless matplotlib configuration works in Docker/cloud environments
- **CLI vs Web**: Proper backend selection based on execution context

### ✅ Task Completion
- **Mark completed tasks in `TASK.md`** immediately after finishing them.
- Add new sub-tasks or TODOs discovered during development to `TASK.md` under a "Discovered During Work" section.
- **Update analysis results** in appropriate documentation when completing data experiments.

### 📎 Style & Conventions
- **Use Python** as the primary language.
- **Follow PEP8**, use type hints, and format with `black`.
- **Use `pydantic` for data validation**.
- Use `FastAPI` for APIs if building web services.
- Use `pandas` and `numpy` for data manipulation, `scikit-learn` for ML baselines.
- Use `pytorch-forecasting` for Temporal Fusion Transformer implementation.
- Write **docstrings for every function** using the Google style:
  ```python
  def example():
      """
      Brief summary.

      Args:
          param1 (type): Description.

      Returns:
          type: Description.
      """
  ```

### 📚 Documentation & Explainability
- **Update `README.md`** when new features are added, dependencies change, or setup steps are modified.
- **Document data assumptions** (e.g., "speed is in meters/minute", "thickness in millimeters").
- **Include visualization examples** in documentation for key insights.
- **Comment non-obvious code** and ensure everything is understandable to a mid-level developer.
- When writing complex logic, **add an inline `# Reason:` comment** explaining the why, not just the what.

### 🏭 Industrial Data Specifics
- **Handle mixed-frequency data properly** - align event-based and continuous streams to common timeline.
- **Create event windows** for analysis (e.g., 15 min before, 60 min after stoppage).
- **Calculate recovery metrics** after production disruptions.
- **Maintain production context** - consider shift patterns, maintenance schedules.
- **Respect data privacy** - anonymize operator information, facility details.

#### Critical Time Alignment for Test Data
✅ **Manufacturing data requires careful timestamp handling with warning-free multi-format support**:
- **fm_stack.csv**: Combine `'Finish Start Date'` + `'Finish Start ime'` (note typo in column name)
- **sm_stack.csv**: Combine `'First Sheet Date'` + `'First Sheet Time'`  
- **speed.csv**: Combine `'Log Date'` + `'Log Time'`
- **thickness.csv**: Combine `'Sensor Date'` + `'Sensor Time'`
- **stop.csv**: Combine `'Stop Date'` + `'Stop Time'`
- **✅ Warning-Free Multi-format date support**: Automatically handles `YYYY-MM-DD` (legacy) and `YYYY.MM.DD` (current) formats without pandas warnings
- **✅ Enhanced DateTime Parsing**: Use `parse_single_datetime()` and `parse_datetime()` functions with explicit format specifications
- **Create unified datetime column** for proper temporal correlation analysis across all manufacturing variables

#### 📏 Critical Thickness Data Processing
⚠️ **Thickness measurements require specialized handling**:
- **10 Sensor Array**: thickness.csv contains Sensor 01, Sensor 02, ..., Sensor 10 columns
- **Automatic Averaging**: Calculate `thickness_avg` = mean(Sensor 01-10) as primary thickness measure
- **Uniformity Metrics**: Calculate `thickness_uniformity` = std(Sensor 01-10) for quality assessment
- **Range Analysis**: Calculate `thickness_range` = max(Sensor 01-10) - min(Sensor 01-10)
- **Manufacturing Context**: Thickness = average across product width, uniformity indicates quality consistency

#### 🔬 Multi-Method Correlation Development Patterns
⚠️ **Multi-method analysis requires specialized implementation**:
- **Three Method Support**: Always implement Pearson, Spearman, and Kendall together
- **Data Distribution Assessment**: Automatically assess normality, outliers, and linearity before method selection
- **Method Convergence Scoring**: Calculate variance across methods and normalize to [0,1] convergence score
- **Intelligent Recommendation**: Use decision tree logic for method selection based on data characteristics
- **Bootstrap Robustness**: Implement stability analysis with minimum 50 bootstrap samples for production
- **Pydantic Validation**: Use field validators for correlation ranges (-1,1) and p-values (0,1)
- **Precision Requirements**: 8-decimal internal calculations, 6-decimal display, scientific notation for p < 1e-6
- **Visualization Standards**: Side-by-side heatmaps with coolwarm colormap, consistent axis handling

#### 🎨 Complete Visualization Suite Development Patterns
⚠️ **Visualization integration requires agent tool implementation**:
- **5 Visualization Tools**: Multi-method heatmaps, convergence dashboards, comparison matrices, comprehensive dashboards, and batch generation
- **Agent Integration**: All visualization tools registered with @correlation_agent.tool decorators for automatic use
- **Professional Output**: Generate both PNG and HTML formats with manufacturing-specific styling
- **Automatic Generation**: Agent automatically calls visualization tools during correlation analysis
- **File Management**: Save visualizations to organized directories with descriptive filenames
- **User Communication**: Always provide file paths and descriptions of generated visualizations to users

### 🧠 AI Behavior Rules
- **Never assume missing context. Ask questions if uncertain.**
- *⁠*ALWAYs before install any components or libraries do a check if there is a latest stable version available. Make sure you update existing library if already installed to newer version or install the latest version!
- **Never hallucinate libraries or functions** – only use known, verified Python packages.
- **Always confirm file paths and module names** exist before referencing them in code or tests.
- **Never delete or overwrite existing code** unless explicitly instructed to or if part of a task from `TASK.md`.
- **Validate industrial domain assumptions** with explicit checks (e.g., production line can't have negative speed).