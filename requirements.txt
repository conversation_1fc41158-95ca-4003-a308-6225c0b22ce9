# Core AI and ML Dependencies
pydantic-ai>=0.0.12
pydantic>=2.0.0
anthropic>=0.7.0
google-cloud-aiplatform>=1.42.0

# Data Processing and Analysis
pandas>=1.5.0
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Time Series Forecasting
transformers[torch]>=4.35.0
torch>=2.0.0
accelerate>=0.26.0
datasets>=2.14.0
evaluate>=0.4.0

# Statistical Analysis
statsmodels>=0.14.0

# CLI and User Interface
click>=8.1.0
rich>=13.0.0

# Environment Management
python-dotenv>=1.0.0

# Visualization
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.15.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
pytest-cov>=4.1.0

# Code Quality
ruff>=0.1.0
mypy>=1.5.0

# Jupyter Integration (optional)
jupyter>=1.0.0
ipykernel>=6.25.0

# Development Tools
pre-commit>=3.3.0

# Web API Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0

# Type Stubs
types-requests>=2.31.0
pandas-stubs>=2.0.0